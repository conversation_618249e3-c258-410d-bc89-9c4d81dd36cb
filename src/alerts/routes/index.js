/*
    Router class to define actions against urls
 */
    import { body, header, query, validationResult} from 'express-validator';
    import _ from 'lodash'
    export default class {
    
        constructor(app, { controller }) {
            this.app = app
            this.bindRoutes = this.bindRoutes.bind(this);
            this.controller = controller;
        }
    
        bindRoutes() {
            let self = this;

            // Home endpoint
            self.app.get('/', (req, res) => {
                self.controller.handleAlertHomeEndpoint(req, res);
            });

            // Slack events endpoint
            self.app.post('/slack/events', (req, res) => {
                self.controller.handleAlertSlackEvents(req, res);
            });

            // OAuth redirect endpoint
            self.app.get('/slack/oauth_redirect', (req, res) => {
                self.controller.handleAlertOAuthRedirect(req, res);
            });
        }
    }
    