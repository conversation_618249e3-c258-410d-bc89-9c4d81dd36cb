from flask import Flask, request, jsonify
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route("/", methods=["GET"])
def home():
    logger.info("Home endpoint hit")
    return "Server is running!", 200

@app.route("/slack/events", methods=["POST"])
def slack_events():
    try:
        logger.info("Slack events endpoint hit")
        data = request.get_json(force=True)
        logger.info("Received data: %s", data)
        
        if data and data.get("type") == "url_verification":
            challenge = data.get("challenge")
            logger.info("Challenge received: %s", challenge)
            return jsonify({"challenge": challenge})
            
        return jsonify({"status": "ok"}), 200
    except Exception as e:
        logger.exception("Error occurred:")
        return jsonify({"error": str(e)}), 500

@app.route("/slack/oauth_redirect", methods=["GET"])
def slack_oauth_redirect():
    logger.info("OAuth redirect endpoint hit")
    # Get the authorization code from the query parameters
    code = request.args.get('code')
    
    if code:
        logger.info("Received OAuth code")
        # Here you would typically exchange the code for an access token
        # For now, just return a success message
        return "Successfully authenticated with Slack!", 200
    else:
        logger.error("No OAuth code received")
        return "Authentication failed", 400

if __name__ == "__main__":
    logger.info("Starting Flask server...")
    app.run(host='0.0.0.0', port=5001, debug=True)