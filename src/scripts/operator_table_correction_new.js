import startup from '../lib/startup';
import utility from '../lib'
import MOMENT from 'moment';
import request from 'request'
import _ from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';


/*
 * Script for https://jira.mypaytm.com/browse/IN-47124
 * local => npm run build && VAULT=0 node dist/scripts/operator_table_correction_new.js
 * staging => NODE_ENV=staging node dist/scripts/operator_table_correction_new.js
 * greyscale => NODE_ENV=production node dist/scripts/operator_table_correction_new.js
*/

class csv_table_correction {

    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.client = options.cassandraDbClient;
        this.config = options.config;
        this.operators = ['pvvnl', 'dvvnl', 'mvvnl', 'puvvnl'];
        this.operator_mapping = {
            'pvvnl' : {
                'product_id' : 467044907,
                'name' : 'paschimanchal vidyut vitran nigam limited (pvvnl)'
            },
            'dvvnl' : {
                'product_id' : 467045545,
                'name' : 'dakshinanchal vidyut vitran nigam limited (dvvnl)'
            }, 
            'mvvnl' : {
                'product_id' : 467045320,
                'name' : 'madhyanchal vidyut vitran nigam limited (mvvnl)'
            }, 
            'puvvnl' : {
                'product_id' : 467045319,
                'name' : 'purvanchal vidyut vitaran limited (puvvnl)'
            }, 
            'uppcl' : {
                'product_id' : 318747167,
                'name' : 'uttar pradesh power corporation ltd. (uppcl)'
            }
        };
        this.totalRow = 0;
        this.inputFileName = 'src/scripts/files/input.csv';
        this.outputFileName = 'src/scripts/files/output.csv';
        this.headers = 'product_id,customer_id,recharge_number,operator,error';
        this.retryAllowed = ['PROCESSING_ERROR', 'NO_OPERATOR_FOUND', 'DB_WRITE_ERROR', ''];
        this.writeStream = fs.createWriteStream(this.outputFileName);
        this.writeStream.write(`${this.headers}\n`);
    }
    
    async processRecord(cb, data) {
        let self = this, requestData = self.transformDataForAPI(data);
        ++self.totalRow;
        self.L.log(`processRecord :: Processing Data : ${JSON.stringify(data)}`);
        let errorData = _.get(data, 'error', null);
        if (errorData != null && !self.retryAllowed.includes(errorData)) {
            await self.writeData(requestData, errorData);
            return cb();
        }
        if (!self.isValidRequest(requestData)) {
            await self.writeData(requestData, 'INVALID_CSV_DATA');
            return cb('INVALID_CSV_DATA');
        }
        try { 
            let existingRecord = await self.checkInCassandraTable(requestData);
            self.L.verbose("processRecord :: existingRecord from CassandraTable :: ", existingRecord);
            if(existingRecord && _.isArray(existingRecord) && existingRecord.length == 1 && _.get(existingRecord[0],'newoperator',null) != null) {
                let correctOperator =  _.get(existingRecord[0],'newoperator'),
                    incorrectOperator = self.operator_mapping[requestData.operator]['name'],
                    incorrectTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', incorrectOperator], null),
                    correctTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', correctOperator], null);
                if (correctOperator != incorrectOperator) {
                    let dbData = await self.readDatabase(requestData, correctOperator, correctTable);
                    self.L.verbose("processRecord :: dbData from new table:: ", dbData);
                    let dbDataForCustomerId = dbData.filter(row => row.customer_id == requestData.customer_id);
                    if (dbDataForCustomerId && _.isArray(dbDataForCustomerId) && dbDataForCustomerId.length > 0) {
                        self.L.log(`processRecord :: Data already in correct table. So, updating only incorrect table.`);
                        await self.publishMetric('SUCCESS', 'CORRECT_DATA_ALREADY');
                        try {
                            await self.updateDatabase(requestData, incorrectTable);
                            self.L.log(`processRecord :: processed succesfully !!`);
                            await self.publishMetric('SUCCESS', 'DATA_UPDATED');
                            return cb();
                        } catch (error) {
                            await self.writeData(requestData, 'DB_WRITE_ERROR');
                            return cb('DB_WRITE_ERROR');
                        }
                    } else if (dbData && _.isArray(dbData) && dbData.length > 0) {
                        self.L.log(`processRecord :: Data with different customerId already in correct table. So inserting in correct table by changing cutomerId and updating incorrect table.`);
                        try {
                            await self.insertDatabase(dbData[0], correctTable, requestData);
                            await self.updateDatabase(requestData, incorrectTable);
                            self.L.log(`processRecord :: processed succesfully !!`);
                            await self.publishMetric('SUCCESS', 'DATA_UPDATED');
                            return cb();
                        } catch (error) {
                            await self.writeData(requestData, 'DB_WRITE_ERROR');
                            return cb('DB_WRITE_ERROR');
                        }
                    } else {
                        try {
                            self.L.log(`processRecord :: Data with recharge number not present in correct table. So inserting in correct table with next day nbfd and updating incorrect table.`);
                            let dataToStore = {'operator' : correctOperator};
                            await self.insertDatabase(dataToStore, correctTable, requestData);
                            await self.updateDatabase(requestData, incorrectTable);
                            self.L.log(`processRecord :: processed succesfully !!`);
                            await self.publishMetric('SUCCESS', 'DATA_UPDATED');
                            return cb();
                        } catch (error) {
                            await self.writeData(requestData, 'DB_WRITE_ERROR');
                            return cb('DB_WRITE_ERROR');
                        }
                    }
                } else {
                    self.L.log(`processRecord :: Data already in correct table !!`);
                    await self.publishMetric('SUCCESS', 'CORRECT_DATA_ALREADY');
                    return cb();
                }
            } else {
                self.L.log(`processRecord :: No record in cassandra check. So processing with API calls.`);
            }
        } catch (error) {
            self.L.log(`processRecord :: ${error} occurred in cassandra check and insert for ${JSON.stringify(data)}. So processing with API calls.`);
        }
        try {
            let responseData = await self.callFFRValidationAPI(requestData);
            self.L.verbose("processRecord :: responseData :: ", responseData);
            let correctOperators = self.getCorrectOperatorsNames(responseData);
            self.L.verbose("processRecord :: correctOperators :: ", correctOperators);
            if (correctOperators.length == 1) {
                let correctOperator = self.operator_mapping[correctOperators[0]]['name'],
                    incorrectOperator = self.operator_mapping[requestData.operator]['name'],
                    correctProductId = self.operator_mapping[requestData.operator]['product_id'],
                    incorrectTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', incorrectOperator], null),
                    correctTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', correctOperator], null);
                await self.insertInCassandraTable(requestData, correctOperator, incorrectOperator, correctProductId);
                self.L.verbose("processRecord :: incorrectTable :: ", incorrectTable, "correctTable :: ", correctTable) ;
                let dbData = await self.readDatabaseForCustomerId(requestData, correctOperator, correctTable);
                self.L.verbose("processRecord :: dbData from correctTable :: ", dbData);
                if (dbData && _.isArray(dbData) && dbData.length == 1) {
                    self.L.log(`processRecord :: Data already in correct table. So, updating only incorrect table.`);
                    await self.publishMetric('SUCCESS', 'CORRECT_DATA_ALREADY');
                    try {
                        await self.updateDatabase(requestData, incorrectTable);
                        self.L.log(`processRecord :: processed succesfully !!`);
                        await self.publishMetric('SUCCESS', 'DATA_UPDATED');
                        return cb();
                    } catch (error) {
                        await self.writeData(requestData, 'DB_WRITE_ERROR');
                        return cb('DB_WRITE_ERROR');
                    }
                } else {
                    self.L.log(`processRecord :: Data with recharge number not present in correct table. So inserting in correct table with next day nbfd and updating incorrect table.`);
                    try {
                        let dataToStore = {'operator' : correctOperator};
                        await self.insertDatabase(dataToStore, correctTable, requestData);
                        await self.updateDatabase(requestData, incorrectTable);
                        self.L.log(`processRecord :: processed succesfully !!`);
                        await self.publishMetric('SUCCESS', 'DATA_UPDATED');
                        return cb();
                    } catch (error) {
                        await self.writeData(requestData, 'DB_WRITE_ERROR');
                        return cb('DB_WRITE_ERROR');
                    }  
                }   
            } else if (correctOperators.length == 0) {
                let isCaseOf1033 = 0;
                self.operators.forEach(operator => {
                    if (responseData[operator]['statusCode'] == '200' && responseData[operator]['errorMessageCode'] == 1033) {
                        isCaseOf1033 += 1;
                    }
                });
                if (isCaseOf1033 == 4) {
                    self.L.log(`processRecord :: 1033 found for all operators. So updating only incorrect table.`);
                    try {
                        let incorrectTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', self.operator_mapping[requestData.operator]['name']], null);
                        await self.updateDatabase(requestData, incorrectTable);
                        self.L.log(`processRecord :: processed succesfully for 1033 !!`);
                        await self.publishMetric('SUCCESS', '1033_FOUND');
                        return cb();
                    } catch (error) {
                        await self.writeData(requestData, 'DB_WRITE_ERROR');
                        return cb('DB_WRITE_ERROR');
                    }
                } else {
                    self.L.log(`processRecord :: No operator found.`);
                    await self.publishMetric('SUCCESS', 'NO_OPERATOR_FOUND');
                    await self.writeData(requestData, 'NO_OPERATOR_FOUND');
                    return cb();
                }
            } else {
                self.L.log(`processRecord :: Multi operator found.`);
                await self.publishMetric('SUCCESS', 'MULTI_OPERATOR_FOUND');
                await self.writeData(requestData, 'MULTI_OPERATOR_FOUND');
                return cb();
            }
        } catch (error) {
            await self.writeData(requestData, 'PROCESSING_ERROR');
            return cb(error);
        }    
    }

    async writeData(requestData, error) {
        let self = this;
        self.writeStream.write(`${requestData.product_id},${requestData.customer_id},${requestData.recharge_number},${requestData.operator},${error}\n`, 'utf8');
    }

    async publishMetric(status, type) {
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPPCL_CORRECTION', `STATUS:${status}`, `TYPE:${type}`]);
    }

    transformDataForAPI(data) {
        let params =
        {
            'recharge_number': _.get(data, 'recharge_number', null),
            'operator': _.get(data, 'operator', 'pvvnl'),
            'product_id': _.get(data, 'product_id', null),
            'customer_id': _.get(data, 'customer_id', null),
        }
        try {
            params.customer_id = parseInt(_.get(data, 'customer_id', null), 10);
        }
        catch (e) {
            console.log("Error in parsing customer_id", e);
        }
        return params;
    }

    isValidRequest(requestData) {
        let self = this;
        if(!requestData.recharge_number){
            self.L.log("isValidRequest :: Got recharge_number as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'RECHARGE_NUMBER_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.operator){
            self.L.log("isValidRequest :: Got operator as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'OPERATOR_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.product_id){
            self.L.log("isValidRequest :: Got product_id as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'PRODUCT_ID_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.customer_id || requestData.customer_id == 0 || typeof requestData.customer_id == 'string'){
            self.L.log("isValidRequest :: Got customer_id as null or 0 or string from CSV File");
            self.publishMetric('PARAMS MISSING', 'CUSTOMER_ID_NOT_FOUND_FROM_CSV');
            return false;
        }
        return true;
    }

    async checkInCassandraTable(requestData) {
        let self = this,
            operatorName = self.operator_mapping[requestData.operator]['name'],
            query = `SELECT newoperator FROM pid_mapping WHERE recharge_number = ? AND service = ? AND operator = ?`,
            param = [requestData.recharge_number, 'electricity', operatorName];
        self.L.verbose("checkInCassandraTable :: Query :: ", query, " :: Param :: ", param);
        return new Promise((resolve, reject) => {
            self.client.execute(query, param, {prepare : true})
            .then(result =>{
                self.L.verbose(`checkInCassandraTable :: record fetched of length ${result.rows.length} for ${JSON.stringify(param)}`);
                resolve(result.rows);
            })
            .catch(error=>{
                self.L.log(`checkInCassandraTable :: DB exception! for ${JSON.stringify(param)} with error ${error}`);
                resolve('');
            })
        });
    }

    async insertInCassandraTable(requestData, correctOperator, incorrectOperator, correctProductId) {
        let self = this,
            query = 'INSERT into pid_mapping (recharge_number, service, operator, newoperator, source, product_id, updated_at, created_at) values(?,?,?,?,?,?,?,?)',
            param = [requestData.recharge_number, 'electricity', incorrectOperator, correctOperator, 
                    'dev-script', correctProductId, Number(MOMENT().format('x')), Number(MOMENT().format('x'))];
        self.L.verbose("insertInCassandraTable :: Query :: ", query, " :: Param :: ", param);
        return new Promise((resolve, reject) => {
            self.client.execute(query, param, {prepare : true})
            .then(result =>{
                self.L.verbose(`insertInCassandraTable :: record fetched of length ${result.rows.length} for ${JSON.stringify(param)}`);
                resolve(result.rows);
            })
            .catch(error=>{
                self.L.log(`insertInCassandraTable :: DB exception! for ${JSON.stringify(param)} with error ${error}`);
                resolve('');
            })
        });
    }

    async callFFRValidationAPI(requestData) {
        let self = this, responseData = {};
        await Promise.all(self.operators.map(operator => {
            return new Promise((resolve, reject) => {
                self.validateCall(requestData, operator, (error, response, body) => {
                    if (error) {
                        self.L.log(`callFFRValidationAPI :: ${requestData.recharge_number} with ${operator} occurred error ${error}`);
                        self.publishMetric('ERROR', 'FFR_VALIDATION_API');
                        responseData[operator] = {'statusCode': '400'};
                    } else {
                        if (body && typeof body === 'string') {
                            try {
                                body = JSON.parse(body);
                            } catch (e) {
                                body = null;
                                self.L.log(`callFFRValidationAPI :: Error parsing data received for recharge_number ${requestData.recharge_number} with ${operator}`, e);
                            }
                        }
                        if (body != null) {
                            let errorMessageCode = _.get(body, 'cart_items.0.validationGwResponse.errorMessageCode', null);
                            responseData[operator] = {'statusCode': '200', 'errorMessageCode' : errorMessageCode};
                        } else {
                            responseData[operator] = {'statusCode': '400'};
                        }
                        self.L.log(`callFFRValidationAPI :: ${requestData.recharge_number} with ${operator} processed succesfully !!`);
                        self.publishMetric('SUCCESS', 'FFR_VALIDATION_API');
                    }
                    resolve();
                });
            });
        }));
        return responseData;
    }

    validateCall(requestData, operator, cb) {
        let self = this,
            apiOpts = {
            "uri"       : _.get(self.config, ['FFR', 'VALIDATION_URL'], null),
            "method"    : "POST",
            "timeout"   : 60000,
            'json' : {
                'cart_items' : [
                    {
                        'price'           : 1234,
                        'product_id'      : self.operator_mapping[operator]['product_id'],
                        'quantity'        : 1,
                        'fulfillment_req' : {
                            'recharge_number': requestData.recharge_number
                        }
                    }
                ],
                'customer_id': requestData.customer_id,
                'channel_id' : 'digital-reminder-uppcl-script',
            }
        };
    
        self.L.verbose(`validateCall :: Hitting FFR Api: ${JSON.stringify(apiOpts)}`);
        request(apiOpts, function (err, res, body) {
            // self.L.verbose('validateCall :: RESPONSE BODY: ', JSON.stringify(body, null, 4));
            return cb(err, res, body);
        });
    }
    
    getCorrectOperatorsNames(apiResponseData) {
        let self = this, correctOperators = [];
        self.operators.forEach(operator => {
            if (apiResponseData[operator]['statusCode'] == '200' && (apiResponseData[operator]['errorMessageCode'] == 1030 || apiResponseData[operator]['errorMessageCode'] == null) ) {
                correctOperators.push(operator);
            }
        });
        return correctOperators;
    }

    async readDatabase(requestData, operatorName, tableName) {
        let self = this,
            query = `SELECT * FROM ${tableName} WHERE recharge_number = ? and service = ? and operator = ?;`,
            param = [
                requestData.recharge_number,
                'electricity',
                operatorName
            ];

        self.L.verbose("readDatabase :: Query :: ", self.dbInstance.format(query, param));

        return await new Promise((resolve, reject) => {
            self.dbInstance.exec((err, records) => {
                if (err) {
                    self.L.critical('readDatabase :: ', query, err);
                    self.publishMetric('ERROR', 'DB_READ');
                    resolve('');
                } else {
                    resolve(records);
                }
            }, "DIGITAL_REMINDER_SLAVE", query, param);
        });
    }

    async readDatabaseForCustomerId(requestData, operatorName, tableName) {
        let self = this,
            query = `SELECT * FROM ${tableName} WHERE customer_id = ? and recharge_number = ? and service = ? and operator = ?;`,
            param = [
                requestData.customer_id,
                requestData.recharge_number,
                'electricity',
                operatorName
            ];

        self.L.verbose("readDatabaseForCustomerId :: Query :: ", self.dbInstance.format(query, param));

        return await new Promise((resolve, reject) => {
            self.dbInstance.exec((err, records) => {
                if (err) {
                    self.L.critical('readDatabaseForCustomerId :: ', query, err);
                    self.publishMetric('ERROR', 'DB_READ');
                    resolve('');
                } else {
                    resolve(records);
                }
            }, "DIGITAL_REMINDER_SLAVE", query, param);
        });
    }
    
    async insertDatabase(dbData, correctTable, requestData) {
        let self = this;

        let query = `INSERT INTO ${correctTable} \ 
            (customer_id,recharge_number,product_id,operator,amount,bill_date,due_date,bill_fetch_date,next_bill_fetch_date,gateway, \
            paytype,service,circle,customer_mobile,customer_email,payment_channel,retry_count,status,reason,extra,published_date, \
            user_data,notification_status,payment_date,service_id,customerOtherInfo,is_automatic,old_bill_fetch_date,remind_later_date) \
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE next_bill_fetch_date=VALUES(next_bill_fetch_date);`,
            param = [
                requestData.customer_id,
                _.get(dbData, 'recharge_number', requestData.recharge_number),
                _.get(dbData, 'product_id', requestData.product_id),
                _.get(dbData, 'operator', null),
                _.get(dbData, 'amount', 0.00),
                _.get(dbData, 'bill_date', null),
                _.get(dbData, 'due_date', null),
                _.get(dbData, 'bill_fetch_date', null),
                _.get(dbData, 'next_bill_fetch_date', MOMENT().add(1, 'day').format('YYYY-MM-DD')),
                _.get(dbData, 'gateway', null),
                _.get(dbData, 'paytype', null),
                _.get(dbData, 'service', 'electricity'),
                _.get(dbData, 'circle', null),
                null,
                null,
                _.get(dbData, 'payment_channel', null),
                _.get(dbData, 'retry_count', '0'),
                _.get(dbData, 'status', '0'),
                _.get(dbData, 'reason', null),
                _.get(dbData, 'extra', null),
                _.get(dbData, 'published_date', null),
                _.get(dbData, 'user_data', null),
                _.get(dbData, 'notification_status', '1'),
                null,
                _.get(dbData, 'service_id', '0'),
                null,
                _.get(dbData, 'is_automatic', '0'),
                _.get(dbData, 'old_bill_fetch_date', null),
                _.get(dbData, 'remind_later_date', null),
            ];

        self.L.verbose("insertDatabase :: Query :: ", self.dbInstance.format(query, param));

        return await new Promise((resolve, reject) => {
            self.dbInstance.exec((error, res) => {
                if (error) {
                    self.L.critical('insertDatabase :: Error :: ', error);
                    self.publishMetric('ERROR', 'DB_INSERT'); 
                    resolve('');           
                } else {
                    self.L.verbose('insertDatabase :: Success');
                    resolve('');
                }
            }, 'DIGITAL_REMINDER_MASTER', query, param);
        });
    };

    async updateDatabase(requestData, incorrectTable) {
        let self = this,
            query = `UPDATE ${incorrectTable} SET status = 13 WHERE customer_id = ? and recharge_number = ? and service = ? and operator = ?;`,
            param = [
                requestData.customer_id,
                requestData.recharge_number,
                'electricity',
                self.operator_mapping[requestData.operator]['name']
            ];

        self.L.verbose("updateDatabase :: Query :: ", self.dbInstance.format(query, param));
        return await new Promise((resolve, reject) => {
            self.dbInstance.exec((error, res) => {
                if (error) {
                    self.L.critical('updateDatabase :: Error :: ', error);
                    self.publishMetric('ERROR', 'DB_UPDATE');
                    resolve('');
                } else {
                    self.L.verbose('updateDatabase :: Success');
                    resolve('');
                }
            }, 'DIGITAL_REMINDER_MASTER', query, param);
        });
    };

    getCSV_Data(cb) {
        let self = this;
        try {
            const Stream = fs.createReadStream(self.inputFileName);
            const csvStream = parseStream(Stream, { headers: true });
            csvStream
                .on('data', (data) => {
                    csvStream.pause();
                    self.processRecord((err) => {
                        if (err) {
                            self.publishMetric('ERROR', err);                                
                            self.L.error('getCSV_Data :: Error in processing data', err, JSON.stringify(data));
                        }
                        csvStream.resume();
                    }, data);
                })
                .on('end', rowCount => {
                    setTimeout(() => {
                        self.L.log(`getCSV_Data :: ${rowCount} Data rows processed`);
                        return cb();
                    }, 1000);
                })
                .on('error', error => {
                    setTimeout(() => {
                        self.L.error("getCSV_Data :: processCSV data", "Error", error);
                        return cb(error);
                    }, 1000);
                });
        }
        catch (err) {
            return cb(err);
        }
    }

    async start(cb, opts) {
        let self = this;
        if (opts.verbose) {
            self.L.setLevel('verbose');
        }
        await self.getCSV_Data(function (err) {
            if (err) {
                self.L.error('start::Error', err);
            } else {
                self.L.log('start::completed, totalRowCount ', self.totalRow);
            }
            setTimeout(function () {
                self.writeStream.end();
                return cb(err);
            }, 2000);
        });
    }
}

(function main() {
    if (require.main === module) {
        var commander = require('commander');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .parse(process.argv);
        startup.init({
        }, function (err, options) {
            let script = new csv_table_correction(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                }, commander)
        });
    }
})();
