import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
import OAuth from '../lib/oauth';
import ASYNC from 'async'
import BILLS from '../models/bills'

let serviceName = "smart_fetch_old_data"
let progressFilePath = `/var/log/digital-notification/progress-${MOMENT().format('MMYYYY')}.json`
let progressTracker = {}
import BillPush from '../lib/billPush';
import { param } from 'express-validator';
/** Maintain offset and processed files in a path */
class SmartFetchOldData {
    constructor(options) {

        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.rechargeSagaCassandraDbRecentKeySpace = options.rechargeSagaCassandraDbRecentKeySpace;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        this.csvIngester = new AWSCsvIngester(options, this.updateProgress.bind(this))
        this.logPrefix = serviceName
        this.bills = new BILLS(options);
        this.batchSize = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'batch_size', 'value'], 10)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.productIdMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {}
        this.currentFile = ""
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
        this.folderPath = `${_.get(options.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_OLD_DATA', 'path', 'value'], 'digital-reminder/SmartFetch')}`
        this.recentTableName = 'recent';
        this.files = []
        /** As per product requirement the CSV will have operator name without spaces
         * So we need to create a mapping of Operator name(with removed spaces ) -> table_name
         */
        for (const [key, value] of Object.entries(_.get(this.config, 'OPERATOR_TABLE_REGISTRY', {}))) {
            this.operatorMapping[key.replace(/\s/g, '')] = key
            this.operatorMapping[key] = key
        }

    }

    getProgressObject() {
        let progress = {}
        progressFilePath = `/var/log/digital-notification/progress-${MOMENT().format('MMYYYY')}.json`
        this.L.info("Loading progress object from", progressFilePath)
        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded", progress)
        return progress
    }

    updateProgress(filename, count) {
        if (_.get(progressTracker, [filename], 0) == -1) return;
        _.set(progressTracker, [filename], count)
        this.L.info("Updated progess Object", JSON.stringify(progressTracker), count)
        fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2))
    }

    filterFileByMonth(filename) {
        try {
            let date = filename.split('$')[1].split('.')[0].slice(0, 7)
            if (date == MOMENT().format('YYYY-MM')) return true

        } catch (err) {
            return false
        }
        return false

    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                self.checkActiveUserKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CHECK_ACTIVE_USERS.HOSTS
                });
                this.checkActiveUserKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ARCHIVAL_CRON", 'STATUS:ERROR', 'TYPE:CHECK_ACTIVE_USERS_PUBLISHER', 'SOURCE:INITIALIZE_PUBLISHER']);
                    }
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    start(callback) {

        let self = this;
        progressTracker = this.getProgressObject()

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('CommonCAAS :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('CommonCAAS :: start', 'Kafka Configured successfully !!');
            }
        });

        try {
            this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize)
        } catch (error) {
            self.L.critical(this.logPrefix, "Cannot initialize AWS")
            return callback(error)
        }

        self.L.info("Getting Files in the folder")


        this.csvIngester.getFileNames(this.folderPath, function (err, data) {
            if (err) {
                self.L.error("Error while getting files")
                return callback(err)
            } else {
                data = _.filter(data, self.filterFileByMonth)
                return eachSeries(data, self.processEachFile.bind(self), callback)
            }
        })

    }

    processEachFile(filename, callback) {
        if (_.get(progressTracker, filename, null) == -1) {
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ", filename, "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ", filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker, [filename], 0)
        this.csvIngester.start(this.processRecordinBatch.bind(this), filename, function (error, data) {
            return callback()
        }, skipRows)
    }

    async processRecordinBatch(data) {
        let self = this;
        return new Promise((resolve, reject) => {
            eachLimit(data, self.batchSize, function (record, cb) {
                self.processRecord(function (err) {
                    cb()
                }, record)
            }, function (error) {
                if (error) {
                    self.L.error(self.logPrefix, "Error while processing batch", error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }

    getBillDefault(cb, tableName, operator, customerId, service, rechargeNumber) {
        let
            self = this;
        let query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`,
            queryParams = [
                customerId,
                _.toLower(operator),
                _.toLower(service),
                rechargeNumber
            ];

        L.log('getBill', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBill' });
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBill`]);
                L.critical('getBill::', 'error occurred while getting data from DB: ', err);
                return cb(err);
            }
            else {
                L.log('getBill :: response from DB', data);
                return cb(null, data);
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    processRecord(cb, data) {
        let self = this;

        let service = _.get(data, 'service', null),
            operator = _.get(data, 'operator', null),
            productId = _.get(data, 'product_id', null);
        let extraInfo = {};
        let operatorFromProductId = _.toLower(_.get(self.config, ['CVR_DATA', productId, 'operator'], null));
        let serviceFromProductId = _.toLower(_.get(self.config, ['CVR_DATA', productId, 'service'], null));
        if (operatorFromProductId && serviceFromProductId) { 
            if (operatorFromProductId != operator || serviceFromProductId != service) { 
                self.L.error(`Operator or service mismatch for ${productId} - ${operatorFromProductId} - ${operator} - ${serviceFromProductId} - ${service}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:OPERATOR_SERVICE_MISMATCH', `SERVICE:${service}`, `OPERATOR:${operator}`, 'TYPE:OPERATOR_SERVICE_MISMATCH']);
                return cb();
            }
        }
        if (operator != null) {
            operator = _.get(self.operatorMapping, operator, null);
        }
        extraInfo.eventState = "bill_gen";
        extraInfo.billSource = "archivalCronsExpiredUser";
        extraInfo.updated_data_source = "smartFetchOldData";
        extraInfo.created_source = "archivalCronsExpiredUser";
        let params = {
            'customerId': _.get(data, 'customer_id', null),
            'rechargeNumber': _.get(data, 'recharge_number', null),
            'operator': operator,
            'paytype': 'postpaid',
            'service': service,
            'productId': _.get(data, 'product_id', null),
            'status': _.get(self.config, 'COMMON.bills_status.PENDING', 1),
            'customerMobile': _.get(data, 'customer_mobile', null),
            'customerEmail': _.get(data, 'customer_email', null),
            'categoryId': _.get(self.config, ['CVR_DATA', data.product_id, 'category_id'], null),
            'notificationStatus': _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            'source': "archivalCronsExpiredUser",
            'extra': JSON.stringify(extraInfo),
            'dbEvent': "upsert",
            'partialSmsFound': true
        }
        params.debugKey = `${params.customerId}_${params.rechargeNumber}_${params.productId}_${params.operator}_${params.service}`;

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
        if (!params.customerId || params.customerId == '0' || params.customerId == 0) {
            self.L.error(`Customer Id missing for ${params.debugKey}`);
            self.customerIdMissing++
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:customerId_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if (!params.rechargeNumber) {
            self.L.error(self.currentFile, 'Recharge number missing', params)
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if (!params.operator) {
            self.L.error(self.currentFile, 'Opertator missing', params, _.get(data, 'operator', null))
            self.operatorMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:OPERATOR_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if (!params.service) {
            self.L.error(self.currentFile, 'Service name missing', params)
            self.serviceMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:SERVICE_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if (!params.productId) {
            self.L.error(self.currentFile, 'Product Id missing', params)
            self.productIdMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV']);
            return cb();
        }

        if (!tableName) {
            self.L.error(self.currentFile, 'Table name not found for operator : ', params.operator)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:TABLE_NOT FOUND']);
            return cb();
        }

        ASYNC.waterfall([
            next => {
                self.getBillDefault((error, result) => {
                    if (error) {
                        self.L.error(self.currentFile, 'Error while getting default bill details', error);
                        return next(error);
                    } else {
                        if (result.length > 0) {
                            self.L.error(`Record already exists in ${tableName} for ${params.debugKey}`);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMART_FETCH_OLD_DATA', 'STATUS:RECORDS_ALREADY_EXISTS', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'TYPE:RECORDS_ALREADY_EXISTS_IN_TABLE']);
                            return next("Record Already Exist in table");
                        } else {
                            self.L.log(`Record not found in ${tableName} for ${params.debugKey}`);
                            return next();
                        }
                    }
                }, tableName, operator, params.customerId, params.service, params.rechargeNumber);
            },
            next => {
                self.pushRecordToCheckActiveUsersTopic((error) => {
                    if (error)
                        return next(error);
                    else {
                        self.L.log('SMART_FETCH_OLD_DATA :: pushRecordToCheckActiveUsersTopic', 'Record pushed to check active users topic', params.debugKey);
                        return next();   
                    }
                }, params);
            }
        ], (error) => {
            return cb(error);
        });
    }

    pushRecordToCheckActiveUsersTopic(done, payload) {
        let self = this;
        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.CHECK_ACTIVE_USERS.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMART_FETCH_OLD_DATA", 'STATUS:ERROR', "TYPE:CHECK_ACTIVE_USERS_PUBLISHER"]);
                self.L.critical('SMART_FETCH_OLD_DATA :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka on topic NON_PAYTM_RECORDS - MSG:- ' + JSON.stringify(payload), error);
                return done(error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMART_FETCH_OLD_DATA", 'STATUS:SUCCESS', "TYPE:CHECK_ACTIVE_USERS_PUBLISHER"]);
                self.L.log('SMART_FETCH_OLD_DATA :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                return done(null);
            }
        })
    }

    isServiceOperatorDisabled(service, operator) {
        service = service?.toLowerCase();
        operator = operator?.toLowerCase();
        return this.disabledCategoriesOperatorMap[service] && this.disabledCategoriesOperatorMap[service].includes(operator);
    }

}



(function main() {
    if (require.main === module) {
        startup.init({

        }, function (err, options) {
            let script;
            try {
                script = new SmartFetchOldData(options);
                script.start(
                    function (err) {
                        setTimeout(function () {
                            if (err) {
                                console.log("main::Error" + err);
                                process.exit(1);
                            } else {
                                console.log("main::completed");
                                process.exit(0);
                            }
                        }, 1000);
                    })
            } catch (err) {
                options.L.error(err)
                process.exit(1)
            }

        });
    }
})();


export default SmartFetchOldData