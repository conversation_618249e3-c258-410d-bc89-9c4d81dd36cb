import L from 'lgr'
import _ from 'lodash'
import MOMENT from 'moment';

import ASYNC from 'async'
import startup from '../lib/startup'
import utility from '../lib'
import FS from 'fs';
let recordNumber = 0;
import { parse } from 'fast-csv';




class update_bbpsbillfetch {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.startLine = 0;
        this.CSV_PARSE_BATCH = 40;
        this.tableName = 'bills_creditcard';
        this.csvRecords = [];
        this.path = "/tmp/bbpsbillfetch.csv";
    }

    start(cb, opts) {
        let self = this;

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }
        ASYNC.waterfall([
            next => {
                return self.processCSV(next, self.path);
            }
        ], function (error) {
            if (error) {
                self.L.critical('VILCSVLoader', 'Error - ', error);
            }
            return cb(error);
        });
    }
    processCSV(done, filePath) {
        let self = this;

        if (!FS.existsSync(filePath)) {
            return done(`Invalid path: ${filePath}`);
        }


        let stream = FS.createReadStream(filePath)
            .pipe(parse({ ltrim: true, rtrim: true, headers: true, ignoreEmpty: true }))
            .on('data', data => {
                if (recordNumber >= self.startLine) {
                    // self.L.log("Csv data", data);
                    if (self.csvRecords.length < self.CSV_PARSE_BATCH) {
                        recordNumber++;
                        self.csvRecords.push(data);
                    } else {
                        recordNumber++;
                        self.csvRecords.push(data);
                        stream.pause();
                        self.processRecords(() => {
                            self.L.info('wait started', recordNumber, "record processed", self.CSV_PARSE_BATCH);
                            setTimeout(() => {
                                self.L.info('wait end record processed', recordNumber, "staring next");
                                stream.resume();
                            }, 100);
                        });
                    }
                }
            })
            .on('end', rowCount => {
                self.processRecords(() => {
                    self.L.log(`${rowCount} records processed succesfully !!`);
                    return done();
                });
            })
            .on('error', error => {
                self.L.critical("processCSV", "Error", error);
                done(error);
            });
    }
    processRecords(done) {
        let self = this;

        ASYNC.each(self.csvRecords, ASYNC.ensureAsync(self._processRecord.bind(self)), function (err) {
            if (err) {
                self.L.error("Error processing record:", err);
            }
            self.csvRecords = [];
            return done();
        });
    }

    _processRecord(record, done) {
        let self = this;
        self.queryDB(record, (err, dbRecord) => {
            if (!err && dbRecord && dbRecord.length > 0) {
                for (let i = 0; i < dbRecord.length; i++) {
                    self.updateRecord((err) => {
                        if (err) {
                            return done(err);
                        }
                        return done();
                    }, self.tableName, dbRecord[i]);
                }

            } else if (!err && dbRecord && dbRecord.length == 0) {
                self.L.log("_processRecord: No Records");
                done();
            } else {
                self.L.log("_processRecord: Error in query", err);
                done();
            }
        });

    }

    updateRecord(cb, tableName, params) {
        let
            self = this;

            let extraDetails = _.get(params, 'extra', {});

            if(_.get(extraDetails, 'BBPSBillFetch', true) == false) {
                
                _.set(extraDetails, 'BBPSBillFetch', true);
                extraDetails = JSON.stringify(extraDetails);

                const query = `UPDATE ${tableName} \
                    SET extra = ? where id = ? AND customer_id = ? ANDrecharge_number = ?`;

                self.L.log("Updating extra for : ", params.id, params.customer_id, params.recharge_number, query);
                var latencyStart = new Date().getTime();

                self.dbInstance.exec((error, res) => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateDBByScript' });
                    if (error) {
                        self.L.critical('writeCustomerDetails::DB update error for ID:', params.id, params.customer_id, params.recharge_number, error);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY_ERROR', 'STATUS:RECORDS_NOT_UPDATE_DB_ERROR', 'TYPE:updateDBByScript']);
                        return cb('DB_UPDATE_ERROR');
                    } else {
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_UPDATE', 'TYPE:updateDBByScript']);
                        self.L.log("successful updated for ", params);
                        return cb(null, res);
                    }
                }, 'DIGITAL_REMINDER_MASTER', query, [extraDetails, params.id, params.customer_id, params.recharge_number]);
            } else {
                self.L.log("Didn't find BBPSBillFetch flag as false" , extraDetails, params);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_NOT_UPDATE_WITHIN_5', 'TYPE:updateDBByScript']);
                return cb();
            }
    }

    queryDB(record, cb) {
        let self = this;
        let customerId = _.get(record, 'customer_id', ''),
            rechargeNumber = _.get(record, 'recharge_number', ''),
            productId = _.get(record, 'product_id', ''),
            dbRecord = [];
            
        self.L.log("Quering DB for", customerId, rechargeNumber, productId);
        let bankAttributes, bankName;

        try{
            bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
            bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));
        } catch (err) {
            self.L.error("update_bbpsbillfetch", "Trouble reading attributes data ", err);
        }
        try {
            const query = 'SELECT id, status, extra from bills_creditcard where customer_id =? AND recharge_number=?  AND bank_name = ?';
            var latencyStart = new Date().getTime();

            self.dbInstance.exec((error, res) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateDBByScript' });
                self.L.log("Recived DB response for", customerId, rechargeNumber, productId);

                if (error) {
                    self.L.critical('writeCustomerDetails DB read error::', customerId, rechargeNumber, productId);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY_ERROR', 'STATUS:RECORDS_NOT_READ_DB_ERROR', 'TYPE:updateDBByScript']);
                    return cb(error);
                } else {
                    if (res != null && res.length > 0) {
                        for (let i = 0; i < res.length; i++) {
                            let extraDetails = _.get(res[i], 'extra', '{}');
                            try {
                                extraDetails = JSON.parse(extraDetails);
                            } catch(err) {
                                self.L.error('unable to parse extra', customerId, rechargeNumber, productId);
                                extraDetails = {};
                            }

                            self.L.log("DB row selected");
                            dbRecord.push({
                                'id': res[i].id,
                                'customer_id': customerId,
                                'recharge_number': rechargeNumber,
                                'extra': extraDetails,
                                'status': res[i].status,
                            });
                        }
                    } else {
                        self.L.log("No record found for", customerId, rechargeNumber, productId);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:NO_DB_RECORD_FOUND', 'TYPE:updateDBByScript']);
                    }
                    return cb(null, dbRecord);

                }
            }, 'DIGITAL_REMINDER_SLAVE', query, [customerId, rechargeNumber, productId]);
        }
        catch (error) {
            self.L.critical("unknow error while doing DB query", customerId, rechargeNumber, productId);
            return cb(error);
        }

    }
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --path <value>', 'path', String)
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try {
                let script = new update_bbpsbillfetch({ ...options, startingID: commander.startingID });
                script.start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 5000);
                }, options);
            } catch (err) {
                console.log(err)
            }
        });
    }
})();


