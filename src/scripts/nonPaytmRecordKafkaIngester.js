import startup from '../lib/startup';
import MOMENT from 'moment';
import _ from "lodash";
import L from 'lgr';
import fs from 'fs'
import {eachLimit, eachSeries} from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
import BILLS from '../models/bills'
import ASYNC from 'async'

let serviceName = "NON_PAYTM_RECORD_KAFKA_INGESTER"
let progressFilePath = `/var/log/digital-reminder/progress-nonPaytmRecordKafkaIngester-${MOMENT().format('YYYY-MM')}.json`
let progressTracker = {}


class NonPaytmRecordKafkaIngester {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject();
        this.csvIngester = new AWSCsvIngester(options, this.updateProgress);
        this.logPrefix = serviceName;
        this.batchSize = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'batch_size', 'value'], 2);
        this.serviceMissing = 0;
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.currentFile = "";
        this.folderPath = `${_.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'path', 'value'], 'digital-reminder/NON_PAYTM_DWH_KAFKA_INGESTER')}`;
        this.files = [];
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
    }

    getProgressObject() {
        let progress = {}
        progressFilePath = `/var/log/digital-reminder/progress-nonPaytmRecordKafkaIngester-${MOMENT().format('YYYY-MM')}.json`
        this.L.info("Loading progress object from", progressFilePath)
        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded", progress)
        return progress
    }

    updateProgress(filename, count) {
        if (_.get(progressTracker, [filename], 0) == -1) return;
        _.set(progressTracker, [filename], count)
        this.L.info("Updated progess Object", JSON.stringify(progressTracker), count)
        fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2))
    }

    filterFileByMonth(filename) {
        try {
            let date = filename.split('$')[1].split('.')[0].slice(0, 7)
            if (date == MOMENT().format('YYYY-MM')) return true
        } catch (err) {
            return false
        }
        return false
    }

    start(callback) {
        let self = this;

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('NON_PAYTM_RECORD_KAFKA_INGESTER :: start', 'unable to configure kafka', error);
                return callback('Unable to configure kafka');
            }
            else {
                self.L.log('NON_PAYTM_RECORD_KAFKA_INGESTER :: start', 'Kafka Configured successfully !!');
            }
        });

        progressTracker = this.getProgressObject()
        try {
            this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize)
        } catch (error) {
            self.L.critical(this.logPrefix, "Cannot initialize AWS")
            return callback(error)
        }

        // Uncomment this to test on staging for custom file and put pushKafkaTestDataFileToS3 method in awscsvingester file
        // self.L.info("Getting Files in the folder")
        // this.csvIngester.pushKafkaTestDataFileToS3();
        this.csvIngester.getFileNames(this.folderPath, function (err, data) {
            if (err) {
                self.L.error("Error while getting files")
                return callback(err)
            } else {
                data = _.filter(data, self.filterFileByMonth)
                return eachSeries(data, self.processEachFile.bind(self), callback)
            }
        })

    }

    processEachFile(filename, callback) {
        if (_.get(progressTracker, filename, null) == -1) {
            this.L.info("Skipping file ", filename, " as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ", filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker, [filename], 0)
        this.csvIngester.start(this.processRecordinBatch.bind(this), filename, function (error, data) {
            return callback()
        }, skipRows)
    }

    async processRecordinBatch(data) {
        let self = this;
        return new Promise((resolve, reject) => {
            eachLimit(data, self.batchSize, function (record, cb) {
                self.processRecord(function () {
                    cb()
                }, record)
            }, function (error) {
                setTimeout(() => self.L.log("Wating for 500ms before starting next batch"), 500);
                if (error) {
                    self.L.error(self.logPrefix, "Error while processing batch", error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }

    processRecord(done, record) {
        let self = this;

        self.validateRecord(function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_RECORD_KAFKA_INGESTER",'STATUS:ERROR',"TYPE:INVALID_RECORD"]);
                self.L.error('NON_PAYTM_RECORD_KAFKA_INGESTER :: _processRecord', `Record Validation failed for ${record}, Error ${error}`);
                return done();
            } else {
                let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.operator], null);
                self.bills.getBillsRecord(function (error, data) {
                    if (error || !data || (_.isArray(data) && _.isEmpty(data))) {
                        let payload = self.preparePayload(record);
                        self.L.log(`NON_PAYTM_RECORD_KAFKA_INGESTER :: _processRecord`, `Going to push the payload to NON_PAYTM_RECORDS_DWH - ${record.traceKey}`);
                        return self.pushRecordToNONPAYTM(done, payload);
                    } else {
                        self.L.log('NON_PAYTM_RECORD_KAFKA_INGESTER :: _processRecord', 'RU Record found');   
                        return done();
                    }
                }, tableName, record.operator, record.service, record.recharge_number, record.customer_id, null);
            }
        }, record);
    }

    validateRecord(done, record) {
        let mandatoryParams = ['customer_id', 'recharge_number', 'operator', 'paytype', 'service', 'product_id'];
        for (let index in mandatoryParams) {
            let param = mandatoryParams[index];
            if (!_.get(record, param, null)) {
                return done(`Mandatory param ${param} missing`);
            }
            if (param == 'customer_id' || param == 'product_id') {
                try {
                    record.param = parseInt(param, 10);
                }
                catch (e) {
                    console.log("Error in parsing", param, e);
                }
            }
        }
        record.traceKey = `customerId:${_.get(record, 'customer_id')}_rechargeNumebr:${_.get(record, 'recharge_number')}_productId:${_.get(record, 'product_id')}`;
        return done(null);
    }

    preparePayload(record) {
        let self = this, 
            extraInfo = {};

        L.verbose(`preparePayload`, `Preparing payload for ${record.traceKey}`);

        extraInfo.eventState = "bill_gen";
        extraInfo.billSource = "archivalCronsExpiredUser";
        extraInfo.updated_data_source = "archivalCronsExpiredUser";
        extraInfo.created_source = "archivalCronsExpiredUser";
        extraInfo.isProductSource = "true";

        return {
            customerId: record.customer_id,
            rechargeNumber: record.recharge_number,
            operator: record.operator,
            paytype: record.paytype,
            service: record.service,
            productId: record.product_id,
            status: _.get(self.config, 'COMMON.bills_status.PENDING', 1),
            categoryId: _.get(self.config, ['CVR_DATA', record.product_id, 'category_id'], null),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            source: "archivalCronsExpiredUser",
            extra: JSON.stringify(extraInfo),
            dbEvent: "upsert",
            partialSmsFound: true
        }
    }

    pushRecordToNONPAYTM(done, payload) {
        let self = this;

        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_RECORD_KAFKA_INGESTER",'STATUS:ERROR',"TYPE:NON_PAYTM_RECORDS_DWH"]);
                self.L.critical('NON_PAYTM_RECORD_KAFKA_INGESTER :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return done(null);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_RECORD_KAFKA_INGESTER",'STATUS:SUCCESS',"TYPE:NON_PAYTM_RECORDS_DWH"]);
                self.L.log('NON_PAYTM_RECORD_KAFKA_INGESTER :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                return done(null);
            }
        })
    }

    configureKafka(done) {
        let self = this;

        ASYNC.waterfall([
            next => {
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('NON_PAYTM_RECORD_KAFKA_INGESTER :: configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }

            return done(null);
        });
    }

}

(function main() {
    if (require.main === module) {
        startup.init({

        }, function (err, options) {
            let script;
            try {
                script = new NonPaytmRecordKafkaIngester(options);
                script.start(
                    function (err) {
                        setTimeout(function () {
                            if (err) {
                                console.log("main::Error" + err);
                                process.exit(1);
                            } else {
                                console.log("main::completed");
                                process.exit(0);
                            }
                        }, 1000);
                    })
            } catch (err) {
                options.L.error(err)
                process.exit(1)
            }

        });
    }
})();

export default NonPaytmRecordKafkaIngester

// pushKafkaTestDataFileToS3(){
//     const data = `customer_id,recharge_number,operator,paytype,service,product_id
// 123456781,123456781,pvvnl,postpaid,electricity,123456781
// 123456782,123456782,pvvnl,postpaid,electricity,123456782
// `;
//     fs.writeFile("/var/log/digital-reminder/Smart_Fetch_Test$2024-08-30.csv", data, "utf-8", (err) => {
//     if (err) console.log(err);
//     else console.log("Data saved");
//     });
//     fs.readFile(`/var/log/digital-reminder/Smart_Fetch_Test$2024-08-30.csv`, (err, data) => {
//         if (err) throw err;
//         const params = {
//           Bucket: 'digital-reminder', // pass your bucket name
//           Key: 'digital-reminder/NON_PAYTM_DWH_KAFKA_INGESTER/Smart_Fetch_Test$2024-08-30.csv', // file will be saved as testBucket/contacts.csv
//           Body: data
//         };
//         this.s3.upload(params, (s3Err, data) => {
//           if (s3Err) throw s3Err
//           console.log(`File uploaded successfully at ${data.Location}`)
//         });
//        });
// }