import L from 'lgr'
import _ from 'lodash'
import Helper from '../lib/helper'
import startup from '../lib/startup'
import Publisher from '../services/publisher'
import LatencyProvider from '../services/latencyProvider'
import digitalUtility from 'digital-in-util'
import ASYNC from 'async'
import utility from '../lib'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import PUBLISHER_BILL_FETCH_PATTERN from '../lib/publisherBillFetchPattern'
import BILLS_SUBSCRIBER from '../services/billSubscriber'
import REQUEST from 'request'
import BillsLibrary from '../lib/bills'
import { resolve } from 'q'





let ffrKafkaPublisher = null;
let commonLib = null;
let reminderUtils = null;
let operatorCountMapping = {};
let processedRNs=[];


class uppcl_demerger {
    constructor(options){
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.activePidLib = options.activePidLib;
        this.config = options.config;
        this.tableName = 'bills_uppcl';
        this.operator='uttar pradesh power corporation ltd. (uppcl)';
        this.newProductIds=['**********','1234595279','1234595280'];
        this.dbBatchSize=1000;
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.truePidStatuses = [
            _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5),
            _.get(this.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6),
            _.get(this.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14)
        ];
        this.billsLib = new BillsLibrary(options);
    }
_calcNextBillFetchDate({ operator, amount, customNextBillFetchDate, evaluatedNbfd, billDate, billDueDate, lastDueDate, lastBillDate, isConnectionError,paymentDone, deducedStatus, noBill, isDateFmtValid, defaultNextBillDueDate, errorMessageCode, tableName, lastAmount, oldBillAmount, productId, oldProductId, serviceId, billDateBasedGateways, custInfoValues }) {
    let self = this,
        nextBillFetchDate = null,   //default value for next bill fetch date
        status = null,
        dateToBeUsed = billDate || billDueDate,
        nextBillFetchDateForRetry = self.billSubscriber.getNextBillFetchDateForRetry(operator, lastBillDate, lastDueDate, productId, oldProductId, 3, billDateBasedGateways),
        nextRetryFreq = self.billSubscriber.getNextRetryFreq(operator, productId, oldProductId, 1),
        nbfdStartOfDay = true;

    if (isConnectionError) {
        nextBillFetchDate = MOMENT().add(nextRetryFreq, 'days');
        status = _.get(self.config, 'COMMON.bills_status.CONNECTION_ERROR', 8)
    }
    else if(paymentDone){
        let billDelay = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS', operator, 'firstBillDelay'], 5),
            billDelayTimeUnit = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS', operator, 'BILL_DELAY_TIME_UNIT'], 'days');
        nextBillFetchDate =  billDelay < 0 ?  MOMENT().add(Math.abs(Number(billDelay)), 'months'): MOMENT().add(billDelay, billDelayTimeUnit);
        status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14);
        amount = 0;
    }
    else if (noBill || (!_.isNaN(errorMessageCode) && _.get(self.config, ['SUBSCRIBER_CONFIG', 'NO_BILL_ERROR_MESSAGE_CODES'], []).indexOf(errorMessageCode) >= 0)) { //if noBill is true, that means new bill is still not generated for the user
        let dateToBeUsedForNoBill = billDateBasedGateways.indexOf(operator) > -1 ? lastBillDate : lastDueDate;
        status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
        if(dateToBeUsedForNoBill){
            //This is the case where operators nbfd is 30 days, so we are adding a month instead of 30days to maintain the correct bill cycle
            dateToBeUsedForNoBill = self.billSubscriber.getFirstBillFetchInterval(operator) < 0 ? MOMENT(dateToBeUsedForNoBill).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(operator))), 'months') : MOMENT(dateToBeUsedForNoBill).add(self.billSubscriber.getFirstBillFetchInterval(operator), 'days');
        }
        else {
            dateToBeUsedForNoBill=null;
        }
        //dateToBeUsedForNoBill = dateToBeUsedForNoBill ? MOMENT(dateToBeUsedForNoBill).add(self.getFirstBillFetchInterval(operator), 'days') : null;

        let rescheduleTime = self.billSubscriber.eligibleForReschedule('NO_BILL', operator);

        if (dateToBeUsedForNoBill && dateToBeUsedForNoBill.diff(MOMENT(), 'days') > 0) {
            nextBillFetchDate = dateToBeUsedForNoBill;
        }
        else if (rescheduleTime) {
            nextBillFetchDate = rescheduleTime;
            nbfdStartOfDay = false;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
        } else {
            nextBillFetchDate = nextBillFetchDateForRetry;
        }
    }
    else if (_.get(custInfoValues, 'invalid', 0) == 1) {  // Record is invalid, so marking it as not in use. It will be archived soon.
        nextBillFetchDate = MOMENT('2080-01-01', 'YYYY-MM-DD'); // Just setting NBFD to future value to avoid status over riding and tracking purpose
        status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)
    }
    else if (!deducedStatus) { //if deducedStatus is false       
        nextBillFetchDate = nextBillFetchDateForRetry;
        status = _.get(self.config, 'COMMON.bills_status.VALIDATION_FAILED', 9)
    }
    else if (amount <= 0 && dateToBeUsed == null) { // Getting deduces status = true but not getting bill
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILLDATE_DUEDATE', `OPERATOR:${operator}`]);
        nextBillFetchDate = nextBillFetchDateForRetry;
        status = _.get(self.config, 'COMMON.bills_status.VALIDATION_FAILED', 9)
    }
    // In case billDate, dueDate is invalid,  nbfd= now() + NEXT_BILL_FETCH_DATES
    else if (_.get(self.operatorConfig, [operator, 'INVALID_BILLDATE_DUEDATE'], false)) {
        if (amount > 0) {
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(operator) < 0 ? MOMENT().add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(operator))), 'months') : MOMENT().add(self.billSubscriber.getFirstBillFetchInterval(operator), 'days');
            status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
        } else {
            let rescheduleTime = self.billSubscriber.eligibleForReschedule('NO_BILL', operator);
            if (rescheduleTime) {
                nextBillFetchDate = rescheduleTime;
                nbfdStartOfDay = false;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
            } else {
                nextBillFetchDate = nextBillFetchDateForRetry;
            }
            status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
        }
    }
    else if (!isDateFmtValid) {
        nextBillFetchDate = nextBillFetchDateForRetry;
        status = _.get(self.config, 'COMMON.bills_status.WRONG_DUE_DATE', 10)
    }
    //If bill fetch is successful & billDueDate is greater than 1 month then schedule nextBillFetchDate to new dueDate minus 1 month (default case)
    else if (self.billsLib.isEarlyBillFetch({billDueDate : billDueDate, lastDueDate : lastDueDate })) { 
        let earlyBillFetchResult = self.billsLib.getEarlyBillFetchResultParams({
            billDueDate : billDueDate
        });
        nextBillFetchDate = earlyBillFetchResult.nextBillFetchDate;
        status =  earlyBillFetchResult.status;
    }
    //First preference given to custom next bill fetch date
    else if (customNextBillFetchDate) {
        nextBillFetchDate = customNextBillFetchDate;
        status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
    }
    //If amount is negative OR due date is old then set next Bill fetch date according to NEXT_RETRY_FREQUENCY or 3 days (OLD BILL)
    //IF (negativeAmount & dueDate is Null -> payment done by user)
    //OR (if billDueDate is present & old billDueDate -> )
    //OR (if billDate is applicable AND if lastBillDate is same as billDate )
    else if (
        (billDueDate && billDueDate.diff(MOMENT(), 'days') < 0) ||
        (lastBillDate && billDate && lastBillDate.diff(billDate) == 0
            && oldBillAmount != null && amount != null && oldBillAmount == amount)) {
        let rescheduleTime = self.billSubscriber.eligibleForReschedule('OLD_BILL_FOUND', operator);
        if (dateToBeUsed) {
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(operator) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(operator))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(operator), 'days');
        }
        if(nextBillFetchDate &&  nextBillFetchDate.diff(MOMENT(), 'days') <= 0){
            if (rescheduleTime) {
                nextBillFetchDate = rescheduleTime;
                nbfdStartOfDay = false;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:OLD_BILL_FOUND_RESCHEDULE', `OPERATOR:${operator}`]);
            } else {
                nextBillFetchDate = nextBillFetchDateForRetry;
            }
        }
        status = _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)
    }
    // if nbfd from gateway or constant then after checking old bill condition.
    else if (evaluatedNbfd) {
        nextBillFetchDate = evaluatedNbfd;
        status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
    }
    //First priorty to be given to billDate and second to billDueDate.
    else if (dateToBeUsed) {
        //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
        nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(operator) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(operator))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(operator), 'days');
        status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
    }
    // If due date is not populated by operator the setting automatic 7 days later as Due date.
    else if (amount > 0 && billDueDate === null && lastDueDate && lastDueDate.diff(MOMENT()) < -1) {
        //If amount > 0, billDueDate is not found and pastDueDate has expired => dueDate = NOW + 7 (nextBillDueDate)
        //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
        nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(operator) < 0 ? MOMENT(defaultNextBillDueDate).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(operator))), 'months') :  MOMENT(defaultNextBillDueDate).add(self.billSubscriber.getFirstBillFetchInterval(operator), 'days');
        status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
    }
    else {
        let rescheduleTime = self.billSubscriber.eligibleForReschedule('NO_BILL', operator);
        if (rescheduleTime) {
            nextBillFetchDate = rescheduleTime;
            nbfdStartOfDay = false;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
        }
        else{
            nextBillFetchDate = nextBillFetchDateForRetry;
        }
        status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
    }


    //rescheduling based on operator config for each error code wherever applicable
    if(!(status==_.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) ||
    (status==_.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) || 
    (status==_.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)))){
        let billStatus,
        statusMap=_.get(self.config, 'COMMON.bills_status', null);
        for(const property in statusMap) {
            if(statusMap[property]==status){
                billStatus=property;
            }
        }
        if(billStatus){
            let rescheduleTime = self.billSubscriber.eligibleForReschedule(billStatus, operator);
            if (rescheduleTime) {
                nextBillFetchDate = rescheduleTime;
                nbfdStartOfDay = false;
                utility._sendMetricsToDD(1, [`REQUEST_TYPE:${billStatus}_RESCHEDULE`, `OPERATOR:${operator}`]);
            }
        }
    }

    //If somehow we are getting nextBillFetchDate in past time then that means either the bill hasn't been updated yet at operator side, So we should retry them next day, instead of retrying them on ( billDuteDate + N ) days, bcoz (billDueDate + N) can come out to be a already passed date, so we will stuck in loop in those scenarios
    if (nextBillFetchDate.diff(MOMENT(), 'days') < -1 ) {
        nextBillFetchDate = nextBillFetchDateForRetry;
        status = _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)
    }

    let operatorCount = _.get(operatorCountMapping, tableName, -1);
    operatorCountMapping[tableName] = (operatorCount + 1) % self.timeDiff;
    //setting the exact time for PRIMARY_CYCLE by deviding time in batches
    return {
        nextBillFetchDate: nbfdStartOfDay ? nextBillFetchDate.startOf('day') : nextBillFetchDate,
        status: status,
        amount: amount
    }
}

async truePIDFound(data,currentRecord, trueProductId){

    let self=this;
    return new Promise((resolve,reject) => { 

    if(_.get(data, 'API_ERROR', null)=='VALIDATION_API_REQUEST_FAILURE') 
    return resolve(false);
    
    /* Keeping existing and updated records in data */
    data.currentRecord = currentRecord;
    data.customerDataResponse = self.billSubscriber.stringCleanup(data.customerDataResponse ,/\[ object object\]/gi)

    let
    productId = trueProductId,
        /*name of the table in which the record resides*/
        operator = _.toLower(_.get(self.config, ['CVR_DATA', trueProductId, 'operator'] , null)),
        tableName = self.billSubscriber.getTableName(trueProductId, operator),

        /*'recharge_number' and 'productId' will be needed in where clause of the update table query*/
        rechargeNumber = _.get(data, 'userData.recharge_number', null),

        /*amount, billDueDate (if present) will be saved as it is in DB*/
        amount = utility.getFilteredAmount(_.get(data, 'customerDataResponse.currentBillAmount', '0')),
        parsedDueDateObj = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDueDate', null)),
        billDueDate = parsedDueDateObj.value,
        isDateFmtValid = parsedDueDateObj.isDateFmtValid,

        /*billDate, customNextBillFetchDate, lastDueDate, billDateBasedGateways will be used to calculate the next_bill_fetch_date of the record*/
        billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []),
        parsedBillDateObj = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDate', null)),
        billDateFromGateway = parsedBillDateObj.value,
        // billDateFromGateway = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDate', null)).value,
        billDate = billDateBasedGateways.indexOf(operator) > -1 ? billDateFromGateway : null,

        customNextBillFetchDate = null,
        nextBillFetchDate = null,
        gatewayNbfd = utility.getFilteredDate(_.get(data, 'customerDataResponse.nextBillFetchDate', null)).value,
        lastDueDate = _.get(currentRecord, 'due_date', null) === null ?
            MOMENT('0001-01-01') :
            MOMENT(_.get(currentRecord, 'due_date')),

        lastBillDate = _.get(currentRecord, 'bill_date', null) === null ?
            MOMENT('0001-01-01') :
            MOMENT(_.get(currentRecord, 'bill_date')),

        lastBillFetchDate = _.get(currentRecord, 'bill_fetch_date', null) === null ?
            null :
            MOMENT(_.get(currentRecord, 'bill_fetch_date')),

        lastAmount = _.get(currentRecord, 'amount', null),
        paymentDone=_.get(data,'customerDataResponse.paymentDone',false),
        isConnectionError = _.get(data, 'validationGwResponse.connectionError', false),
        deducedStatus = _.get(data, 'validationGwResponse.deducedStatus', true),
        noBill = _.get(data, 'validationGwResponse.noBill', false),

        errorMessageCode = _.get(data, 'validationGwResponse.errorMessageCode', null) ? Number(_.get(data, 'validationGwResponse.errorMessageCode', null)) : NaN,
        frontendErrorMessage = _.get(data, 'validationGwResponse.frontendErrorMessage', ''),
        extraDetails = _.get(data, 'extra', {}),
        custInfoValues = _.get(data, 'customerDataResponse', null),
        serviceId = parseInt(_.get(data, 'serviceId', 0)),
        source = _.get(data, 'source'),
        lastStatus = _.get(data, 'status', 0),
        updatedAmount = null,
        updatedBillDate = null,
        updatedDueDate = null,
        productInfoMap = _.get(this.config, "productInfoMap", []);


    try {
        if (_.get(data, 'source', null) != 'ffr' && errorMessageCode) {
            var keyComponents = [
                'en-IN',
                errorMessageCode,
                'VALIDATION',
                _.get(productInfoMap[productId], "verticalId", ''),
                _.get(productInfoMap[productId], "merchant_id", '')
            ],
                payload = {
                    'paytype_label': _.get(productInfoMap[productId], "paytype_label", ''),
                    'operator_label': _.get(productInfoMap[productId], "operator_label", ''),
                    'recharge_number': rechargeNumber
                },
                textMessage = LOCALISATION.getDefaultEnglishMessage(
                    self.localisation_client,
                    keyComponents,
                    true,
                    payload

                );
            if (textMessage) frontendErrorMessage = textMessage
        }
        if (typeof extraDetails == 'string') {
            extraDetails = JSON.parse(extraDetails);
        }
    } catch (ex) {
        L.error('billSubscriber::_dumpInSQL', 'Error in parsing extra details.', ex);
        extraDetails = {};
    }
    if (!extraDetails) {
        extraDetails = {};
    }
    L.log('billSubscriber::_dumpInSQL Bill fetched for'
        + ' Recharge Number: ' + rechargeNumber
        + ' amount: ' + amount
        + ' productId: ' + productId
        + ' isConnectionError: ' + isConnectionError
        + ' deducedStatus: ' + deducedStatus
        + ' noBill: ' + noBill
        + ' Bill Date: ' + billDateFromGateway
        + ' Due Date: ' + billDueDate
        + ' paymentDone: ' + paymentDone
        + ' lastBillDate: ' + (lastBillDate != null && lastBillDate.isValid() ? lastBillDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
        + ' lastDueDate: ' + (lastDueDate != null && lastDueDate.isValid() ? lastDueDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
        + ' lastBillFetchDate: ' + (lastBillFetchDate != null && lastBillFetchDate.isValid() ? lastBillFetchDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
        + ' lastAmount: ' + lastAmount
        + ' lastStatus:' + lastStatus);

    if (!self.billSubscriber._sanityChecks(tableName, operator, rechargeNumber, productId)) {
        L.error('_dumpInSQL:: ', '_sanityChecks failed')
        return resolve(false)
    }

    //If amount > 0, billDueDate is not found and pastDueDate has expired => dueDate = NOW + 7
    var defaultNextBillDueDate = MOMENT().add(7, 'days');
    if (!gatewayNbfd) {
        customNextBillFetchDate = self.billSubscriber._getCustomBillFetchDateAsPerOperator(serviceId, operator, billDueDate, rechargeNumber);
    }
    // constantnbfd covers nbfd from gateway and where nbfd is set to single day every month like 26
    let evaluatedNbfd = self.billSubscriber._getConstantNextBillFetchDate(gatewayNbfd, operator);
    // ---- calculate next bill fetch date & status----//
    let result = self._calcNextBillFetchDate({
        operator,
        amount,
        customNextBillFetchDate,
        evaluatedNbfd,
        billDate,
        billDueDate,
        lastDueDate,
        lastBillDate,
        isConnectionError,
        paymentDone,
        deducedStatus,
        noBill,
        isDateFmtValid,
        defaultNextBillDueDate,
        errorMessageCode,
        tableName,
        lastAmount,
        productId,
        oldProductId: data.oldcatalogProductID,
        serviceId,
        billDateBasedGateways,
        custInfoValues
    });    

    let billFetchDate = null;

    //Convert to required formats
    billDueDate = billDueDate && billDueDate.format('YYYY-MM-DD')
    billDate = billDate && billDate.format('YYYY-MM-DD')
    nextBillFetchDate = result.nextBillFetchDate && result.nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss')

    L.log('_dumpInSQL::calcNextBillFetchDate Recharge_number: ' + rechargeNumber + ' nextBillFetchDate: ' + nextBillFetchDate + ' status: ' + result.status);
    
    extraDetails.errorMessageCode = errorMessageCode;
    extraDetails.frontendErrorMessage = frontendErrorMessage;
    //Checking if we can get the updated amount from gateway.
    if (_.indexOf(self.truePidStatuses, result.status) >= 0) {
        return resolve(true)
        // extraDetails.displayValues = displayValues;
    }
    else return resolve(false);
    })
}

updateStatusInOldTable(data, cb){
    let self=this;
    let recharge_number = _.get(data, ['currentRecord','recharge_number'], null);

    if( !recharge_number) return cb("Recharge_number not found")

    let query = `update bills_uppcl set status=13 where recharge_number=${recharge_number}`;

    L.verbose(`Executing: ${query}`);
    self.dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}
checkIfEntryAlreadyExist(message, data, cb){
    let self=this;
    return new Promise((resolve,reject)=>{
    let recharge_number = _.get(data, 'recharge_number', null),
    truePID = _.get(message,'truePid', null),
    operator = _.toLower(_.get(self.config, ['CVR_DATA', truePID, 'operator'] , null)),
    tableName = self.billSubscriber.getTableName(truePID, operator),
    customer_id=_.get(data, 'customer_id', null);
    if(!customer_id || !recharge_number) return reject("Either cust_id or recharge_number not found")
    let query = `select * from ${tableName} where recharge_number=${recharge_number} and customer_id=${customer_id}`;

    L.verbose(`Executing: ${query}`);
    self.dbInstance.exec(function (err, data) {
        if (err) {
            return reject(err);
        }
        else if(data && data.length>0) return resolve(true);
        else return resolve(false);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
    })
}

async createEntryInNewTable(data, product_id){
    let self=this;
    return new Promise((resolve,reject)=>{
    let creation_data = {
        recharge_number : _.get(data,'recharge_number',null),
        customer_id :   _.get(data,'customer_id',null),
        amount : _.get(data,'amount',null),
        bill_date:_.get(data,'bill_date',null)==null? null: MOMENT(_.get(data,'bill_date',null)).format('YYYY-MM-DD HH:mm:ss'), 
        due_date: _.get(data,'due_date',null)==null? null: MOMENT(_.get(data,'due_date',null)).format('YYYY-MM-DD HH:mm:ss'),
        bill_fetch_date: _.get(data,'bill_fetch_date',null)==null? null: MOMENT(_.get(data,'bill_fetch_date',null)).format('YYYY-MM-DD HH:mm:ss'),
        gateway : _.get(data,'gateway',null),
        paytype : _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'paytype'] , null)),
        customer_mobile:_.get(data,'customer_mobile',null),
        customer_email:_.get(data,'customer_email',null),
        payment_channel:_.get(data,'payment_channel',null),
        retry_count:_.get(data,'retry_count',null),
        status:_.get(data,'status',null),
        reason:_.get(data,'reason',null),
        extra:_.get(data,'extra',null),
        published_date: _.get(data,'published_date',null) ==null ? MOMENT().format('YYYY-MM-DD HH:mm:ss'): MOMENT(_.get(data,'published_date',null)).format('YYYY-MM-DD HH:mm:ss'),
        user_data:_.get(data,'user_data',null),
        notification_status:_.get(data,'notification_status',null),
        payment_date: _.get(data,'payment_date',null)==null? null: MOMENT(_.get(data,'payment_date',null)).format('YYYY-MM-DD HH:mm:ss'),
        service_id:_.get(data,'service_id',null),
        customerOtherInfo:_.get(data,'customerOtherInfo',null),
        is_automatic:_.get(data,'is_automatic',null),
        operator : _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'operator'] , null)),
        circle: _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'circle'] , null)),
        service: _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'service'] , null)),
        product_id:  product_id,
        next_bill_fetch_date: MOMENT().add(1,'days').format('YYYY-MM-DD HH:mm:ss'),

    };
    let tableName = self.billSubscriber.getTableName(product_id, _.get(creation_data, 'operator', null)),
    query = `INSERT INTO ${tableName} (customer_id, recharge_number, product_id, operator,status ` 
    if(creation_data.amount!=null) query = query+`,amount`
    if(creation_data.bill_date!=null) query = query+`,bill_date`
    if(creation_data.bill_fetch_date!=null) query = query+`,bill_fetch_date`
    if(creation_data.next_bill_fetch_date!=null) query = query+`,next_bill_fetch_date`
    if(creation_data.due_date!=null) query = query+`,due_date`
    if(creation_data.customer_mobile!=null) query = query+`,customer_mobile`
    if(creation_data.customer_email!=null) query = query+`,customer_email`
    if(creation_data.payment_channel!=null) query = query+`,payment_channel`
    if(creation_data.reason!=null) query = query+`,reason`
    if(creation_data.extra!=null) query = query+`,extra`
    if(creation_data.published_date!=null) query = query+`,published_date`
    if(creation_data.user_data!=null) query = query+`,user_data`
    if(creation_data.payment_date!=null) query = query+`,payment_date`
    if(creation_data.customerOtherInfo!=null) query = query+`,customerOtherInfo`
    if(creation_data.is_automatic!=null) query = query+`,is_automatic`
    if(creation_data.service_id!=null) query = query+`,service_id`
    if(creation_data.notification_status!=null) query = query+`,notification_status`
    if(creation_data.retry_count!=null) query = query+`,retry_count`
    if(creation_data.circle!=null) query = query+`,circle`
    if(creation_data.paytype!=null) query = query+`,paytype`
    if(creation_data.service!=null) query = query+`,service`
    if(creation_data.gateway!=null) query = query+`,gateway`



    query = query+ `) VALUES ('${creation_data.customer_id}','${creation_data.recharge_number}','${creation_data.product_id}','${creation_data.operator}','${creation_data.status}'`
    if(creation_data.amount!=null) query = query+`,'${creation_data.amount}'`
    if(creation_data.bill_date!=null) query = query+`,'${creation_data.bill_date}'`
    if(creation_data.bill_fetch_date!=null) query = query+`,'${creation_data.bill_fetch_date}'`
    if(creation_data.next_bill_fetch_date!=null) query = query+`,'${creation_data.next_bill_fetch_date}'`
    if(creation_data.due_date!=null) query = query+`,'${creation_data.due_date}'`
    if(creation_data.customer_mobile!=null) query = query+`,'${creation_data.customer_mobile}'`
    if(creation_data.customer_email!=null) query = query+`,'${creation_data.customer_email}'`
    if(creation_data.payment_channel!=null) query = query+`,'${creation_data.payment_channel}'`
    if(creation_data.reason!=null) query = query+`,'${creation_data.reason}'`
    if(creation_data.extra!=null) query = query+`,'${creation_data.extra}'`
    if(creation_data.published_date!=null) query = query+`,'${creation_data.published_date}'`
    if(creation_data.user_data!=null) query = query+`,'${creation_data.user_data}'`
    if(creation_data.payment_date!=null) query = query+`,'${creation_data.payment_date}'`
    if(creation_data.customerOtherInfo!=null) query = query+`,'${creation_data.customerOtherInfo}'`
    if(creation_data.is_automatic!=null) query = query = query+`,'${creation_data.is_automatic}'`
    if(creation_data.service_id!=null) query = query = query+`,'${creation_data.service_id}'`
    if(creation_data.notification_status!=null) query = query = query+`,'${creation_data.notification_status}'`
    if(creation_data.retry_count!=null) query = query = query+`,'${creation_data.retry_count}'`
    if(creation_data.circle!=null) query = query = query+`,'${creation_data.circle}'`
    if(creation_data.paytype!=null) query = query = query+`,'${creation_data.paytype}'`
    if(creation_data.service!=null) query = query = query+`,'${creation_data.service}'`
    if(creation_data.gateway!=null) query = query = query+`,'${creation_data.gateway}'`
    query = query+")"

    L.verbose(`Executing: ${query}`);
    self.dbInstance.exec(function (err, data) {
        if (err) {
            return reject(err);
        }
        return resolve(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
    })
}

formatKafkaPayload(message,record){
    let self = this,
    product_id= _.get(message, 'truePid', null);
    return {
        recharge_number : _.get(message, ['currentRecord','recharge_number'],null),
        customer_id :   _.get(record, 'customer_id',null),
        paytype : _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'paytype'] , null)),
        new_operator : _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'operator'] , null)),
        new_circle: _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'circle'] , null)),
        new_service: _.toLower(_.get(self.config, ['CVR_DATA', product_id, 'service'] , null)),
        new_pid:  product_id,
        next_bill_fetch_date: MOMENT().add(1,'days').format('DD/MM/YYYY HH:MM'),
        is_automatic:_.get(message, ['currentRecord','is_automatic'],null),
        old_operator:_.get(message, ['currentRecord','operator'],null),
        old_circle:_.get(message, ['currentRecord','circle'],null),
        old_service:_.get(message, ['currentRecord','service'],null),
        old_pid:_.get(message, ['currentRecord','product_id'],null)
    }
}


async hitFfrValidationApiInLoop(done, currentRecord, message,source = null ) {
    let self = this,
    truePID = false;
    for(let i=0;i<this.newProductIds.length;i++){
        let
        apiOpts = {
            "uri": _.get(self.config, ['FFR', 'VALIDATION_URL'], null),
            "method": 'POST',
            "timeout": 60000,
            'json': {
                'cart_items': [
                    {
                        price: _.get(self.config, ['CVR_DATA',self.newProductIds[i], 'min_amount'], 10)>10? _.get(self.config, ['CVR_DATA',self.newProductIds[i], 'min_amount'], 10): 10,
                        product_id:this.newProductIds[i],
                        quantity: 1,
                        fulfillment_req: _.get(message, 'userData', {})
                    }
                ],
                'channel_id': "digital-reminder",
            }
        };
    self.L.info("hitFfrValidationApi", `apiOpts for recharge_number ${message.userData.recharge_number} and catalogProductID ${self.newProductIds[i]}`, JSON.stringify(apiOpts));
    await self.hitFfrValidationApi(apiOpts,message);
    truePID = await self.truePIDFound(message,currentRecord,self.newProductIds[i])
    if(truePID){
        self.L.log(`hitFfrValidationApiInLoop:: Found correct new PID-> ${self.newProductIds[i]} for recharge_number: ${_.get(currentRecord, 'recharge_number', null)}`)
        _.set(message, 'truePid', self.newProductIds[i]);
        break;
    }else{
        self.L.log(`hitFfrValidationApiInLoop:: PID-> ${self.newProductIds[i]} is incorrect for recharge_number: ${_.get(currentRecord, 'recharge_number', null)}`)
    }
}
return done(null,message)
}

delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

async hitFfrValidationApi(apiOpts,message){
    var self=this;
        return new Promise((resolve,reject) => {
        var latencyStart = new Date().getTime();        
        REQUEST(apiOpts, (error, response, body) => {
            utility._sendMetricsToDD(1,[`REQUEST_TYPE:UPPCL_DEMERGER`,`TYPE:FFR_VALIDATION_API`,`STATCODE:${_.get(response, 'statusCode', '5XX')}`])
            // publish status to datadog
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'UPPCL_DEMERGER',
                'URL': '/v1/recharge/validate',
                'OPERATOR': self.operator,
                'batch': 0,
                'source' : 'uppcl_demerger_script'
            });
    
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:' + 'UPPCL_DEMERGER',
                'STATCODE:' + _.get(response, 'statusCode', '5XX'),
                'OPERATOR:' + self.operator,
                'batch'+ 0,
                'source:' + 'uppcl_demerger_script'
            ]);
            L.log('hitFfrValidationApi', `FFR_VALIDATION_API_latency::recharge_number:${message.userData.recharge_number}_latency:${new Date().getTime() - latencyStart}milliseconds`);
    
            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                        self.L.error("hitFfrValidationApi", `Error parsing data received for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, e);
                    _.set(message, 'API_ERROR', 'VALIDATION_API_REQUEST_FAILURE');
                    return resolve(message);
                }
            }
            if (error || _.get(response, 'statusCode', null) != '200') {
                let errorStamp = error || "Invalid Statcode " + _.get(response, 'statusCode', null);
    
                    self.L.error("hitFfrValidationApi", `error encountered for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, errorStamp);
                _.set(message, 'API_ERROR', 'VALIDATION_API_REQUEST_FAILURE');
                return resolve(message);
            } else {
                self.L.info("hitFfrValidationApi", `Received response for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, JSON.stringify(body));
    
                _.set(message, 'API_ERROR', null);
                _.set(message, 'customerDataResponse', _.get(body, 'cart_items.0.customerDataResponse', null));
                _.set(message, 'validationGwResponse', _.get(body, 'cart_items.0.validationGwResponse', null));
    
                // If frontendErrorMessage is blank, then will pick error message from body.error in case of error response
                if (!_.get(message, 'validationGwResponse.frontendErrorMessage', null)) {
                    _.set(message, 'validationGwResponse.frontendErrorMessage', _.get(body, 'error', null))
                }
                _.set(message, 'source', 'ffr');
            }
            return resolve(message);

        })
        });
}

async publishToKafkaInloop(message, allRecords, cb){
    let self=this,
    error = null;

    for(let i=0; i <allRecords.length;i++){
        await self.publishToKafka(message, allRecords[i]).then(()=>{
        }).catch((err)=>{
            error=err
            return cb(err);
        })
    }
    if(!error)
    return cb(null);
}
async publishToKafka(message, record){
    let self = this;

    return new Promise((resolve,reject)=>{
        let kafkaPayload = self.formatKafkaPayload(message,record)

                self.ffrKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.UPPCL_DEMERGER_SYNC.TOPIC', ''),
                    messages: JSON.stringify(kafkaPayload)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPPCL_DEMERGER", 'STATUS:ERROR', "TYPE:KAFKA_EVENTS","operator:"+_.get(kafkaPayload,'new_operator',null)]);
                        self.L.critical('kafkaEvent::', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(kafkaPayload), error);
                        return reject(error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPPCL_DEMERGER", 'STATUS:PUBLISHED', "TYPE:KAFKA_EVENTS", "OPERATOR:" + _.get(kafkaPayload,'new_operator',null)]);
                        self.L.log('kafkaEvent::', 'Message published successfully in Kafka', ' on topic MIGRATION_RECORDS', JSON.stringify(kafkaPayload));
                        return resolve();
                    }
                }, [200, 800]);
    })
}

getAllRecords(message,cb){
    let self=this;
    let recharge_number = _.get(message, ['currentRecord','recharge_number'], null);

    if( !recharge_number) return cb("Recharge_number not found")

    let query = `select * from bills_uppcl where recharge_number=${recharge_number}`;

    L.verbose(`Executing: ${query}`);
    self.dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

/*
   Going recursively for this function instead with Async lib's method, because we need to have a provision of halting the process for just a small moment, in cases when the queue is full
*/
async _processRecords(currentRecord) {

    // Replacing product_id with active PID
    let self = this;
    return new Promise((resolve,reject) => {
        if(processedRNs.includes(_.get(currentRecord, 'recharge_number',null))) {
            self.L.log("_processRecords:: Recharge_number already processed ", _.get(currentRecord, 'recharge_number',null)) 
            return resolve();
        }
        if(_.get(currentRecord,'circle',null)=='rural'){
            self.L.log("_processRecords:: Recharge_number is in rural circle, hence skipping migration ", _.get(currentRecord, 'recharge_number',null)) 
            return resolve();
        }
        processedRNs.push(_.get(currentRecord, 'recharge_number',null));
    currentRecord.traceKey = `Id:${_.get(currentRecord, 'id', '')}_Op:${_.get(currentRecord, 'operator', '')}_RN:${_.get(currentRecord, 'recharge_number', '')}`;

    let message = {
        customerInfo: {
            customer_id: _.get(currentRecord, 'customer_id', ''),
            customer_mobile: _.get(currentRecord, 'customer_mobile', '')
        },
        userData: {
            recharge_number: _.get(currentRecord, 'recharge_number', ''),
            amount: 10,
        },
        productInfo: {
            operator: _.get(currentRecord, 'operator', ''),
            circle: _.get(currentRecord, 'circle', ''),
            service: _.get(currentRecord, 'service', ''),
            paytype: _.get(currentRecord, 'paytype', ''),
            validationtimeout: self.validationTimeOut
        },
        logs: '',
        catalogProductID: _.get(currentRecord, 'product_id', ''),
        customerDataResponse: {},
        etc: {},
        billFetchTime: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        lastDueDate: _.get(currentRecord, 'due_date', null),
        lastBillDate: _.get(currentRecord, 'bill_date', null),
        lastAmount: _.get(currentRecord, 'amount', null),
        lastBillFetchDate: _.get(currentRecord, 'bill_fetch_date', null),
        extra: _.get(currentRecord, 'extra', {}),
        source: 'digital-reminder',
        status: _.get(currentRecord, 'status', 0),
        serviceId: _.get(currentRecord, 'service_id', 0)
    },
        allRecords=[];
    // for additional user_info
    let userData = _.get(currentRecord, 'user_data', null);
    if (userData) {
        try {
            userData = JSON.parse(userData);
            message.userData = _.extend({}, userData, message.userData);
        } catch (err) {
            L.error("services::publisher::processRecord", "Error in JSON parsing" + err);
        }
    }
    /*New Code for publishing mechanism */
        ASYNC.waterfall([
            next => {
                L.log('_processRecords', `Publishing data to FFR for recharge_number ${message.userData.recharge_number}`);
                self.recordsPublished++;
                // Hit FFR Validation API
                let source = "publisher" ;
                self.hitFfrValidationApiInLoop(next, currentRecord, message,source);
            },
            (message, next) => {
                /* Temp fix to avoid validation block on FFR*/
                if(_.get(message, 'truePid', null)){
                    self.getAllRecords(message, function(err,data){
                        if(err){
                            utility._sendMetricsToDD(1,[`REQUEST_TYPE:UPPCL_DEMERGER`,`STATUS:DB_ERROR`,'TYPE:GET_ALL_RECORDS'])
                            self.L.error(`getAllRecords:: Error while collecting all getAllRecords for a RN ${err}`)
                            return next(`Error while collecting all getAllRecords for a RN ${err}`)
                        }else{
                            allRecords=data;
                            self.publishToKafkaInloop(message, allRecords, function(err,data){
                                if(err) return next(err);
                                else return next(null,message);
                            })
                        }
                    })
                }
                else{
                    return next('No pid seems valid')
                }
            },
            (message,next)=> {
                self.checkAndCreateNewEntry(message,allRecords,function(err,data){
                    if(err){
                        utility._sendMetricsToDD(1,[`REQUEST_TYPE:UPPCL_DEMERGER`,`STATUS:DB_ERROR`,'TYPE:checkAndCreateNewEntry'])
                        self.L.error(`checkIfEntryAlreadyExist:: Error while creating new entry ${err}`)
                        return next();
                    }
                    else{
                        self.updateStatusInOldTable(message, function(err,data){
                            utility._sendMetricsToDD(1,[`REQUEST_TYPE:UPPCL_DEMERGER`,`STATUS:DB_ERROR`,'TYPE:updateStatusInOldTable'])
                            if(err) self.L.error(`updateStatusInOldTable:: Error while marking record NOT IN USE ${err}`)
                            return next();
                        })
                    }
                })       
            },
        ],
            (err, message) => {                
                 if (err) {
                    L.error('_processRecords', 'error encountered', err);
                }
                return resolve();
            }
        );
    })
}

async checkAndCreateNewEntry(message,allRecords,cb){
    let self=this;
    let breakloop=false;
    if(allRecords.length==0) return cb();
            for(let i=0;i<allRecords.length;i++){
                let exists=false;
                await self.checkIfEntryAlreadyExist(message, allRecords[i]).then((data)=>{
                    self.L.log(`checkIfEntryAlreadyExist:: Entry already exists for recharge_number ${_.get(allRecords[i],'recharge_number',null)} and customer_id ${_.get(allRecords[i],'customer_id',null)} -> ${data}`)
                    if(data==true) exists=true;
                    else exists = false;
                }).catch((err)=>{
                    breakloop=true;
                    return cb(err)
                })
                if(breakloop) break;
                if(!exists){
                    await self.createEntryInNewTable(allRecords[i],_.get(message, 'truePid',null)).then((err,data)=>{
                    }).catch((err)=>{
                        breakloop=true;
                        return cb(err);
                    })
                }
                if(breakloop) break;
                if(i==allRecords.length-1) return cb();
            }    
}

/**
 * 
 * @param {*} done 
 * @param {*} records 
 */

async startProcessingRecords(callback, records) {
    let self = this;
    processedRNs=[];
        for(let i =0;i<records.length;i++){
            await self._processRecords(records[i]); 
        }
    self.delay(function(err){
        return callback(null);
    },1000)
}

getCompleteRecord(recharge_number, cb){
    let self=this;
    if(!recharge_number || recharge_number==undefined) return cb("Recharge number is missing")
    let query = `SELECT * FROM ${self.tableName} WHERE recharge_number='${recharge_number}' LIMIT 1`;
    L.verbose('fetchFreshRecords', query);
    self.dbInstance.exec(function (error, data) {
        if (error || !(data)) {
            L.critical('getCompleteRecord', 'error in fetching data from db ->  ', error);
            return cb(error);
        }
        else {
            return cb(null, data)
        }
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}
fetchFreshRecords(tableName, batchSize, params, done) {
    let self = this,
        query = `SELECT * FROM ${tableName} WHERE status not in (${params.statuses.join(',')}) 
              AND id > ? order by id asc LIMIT ?`;
    L.verbose('fetchFreshRecords', self.dbInstance.format(query, params));

    self.dbInstance.exec(function (error, data) {
        if (error || !(data)) {
            L.critical('fetchFreshRecords', 'error in fetching data from db ->  ', error);
            return done(error);
        }
        else {
            done(null, data)
        }
    }, 'DIGITAL_REMINDER_SLAVE', query, [
        params.offset,
        batchSize
    ]);
}

/*
       Function to process fresh records
    */
       _processFreshRecords(params, done) {
        let self = this,
        lastId = null;
        self.publishedInBatch = {}
        ASYNC.waterfall([
            //fetch fresh records from Database
            next => {
                self.fetchFreshRecords(self.tableName, self.dbBatchSize, params, next);
            },

            //Publish and update the records
            (records, next) => {
                if(records && _.isArray(records) && records.length > 0){
                    lastId = _.get(records[records.length-1], 'id', null);
                }
                L.log('_processFreshRecords', `Processing ${records.length} fresh records for ${self.operator}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPPCL_DEMERGER', 'STATUS:PROCESSING_RECORDS', `OPERATOR:${self.operator}`, `RECORDS_PUBLISHED_COUNT:${self.recordsPublished}` , `RECORDS_COUNT:${records.length}`]);

                self.startProcessingRecords(function () {
                    return next(null, records.length, lastId);
                }, records);

            }
        ],
            (err, count, lastId) => {
                if (err) {
                    return done(err)
                }
                done(null, count, lastId)
            }
        );

    }

runForAllRecord(opts, options,dbInstance,cb) {
    let self = this;
    let publisher = new Publisher({
        ...options,
        tableName: this.tableName,
        latencyProvider: new LatencyProvider(options),
        operator: this.operator
    });
        ASYNC.waterfall([
            //Step 1: Process fresh records (refer definition of FRESH_RECORD from documentation)
            next => {
                L.log('_execSteps::', this.operator, 'processing fresh records...')
                // In case of change in params, Please also update in models/aggregator.js query which is generating report using same params
                self.getCompleteRecord(opts.recharge_number, function(err,data){
                    if(err){
                        self.L.error("Error while fetching entry from table for this recharge_number :", opts.recharge_number);
                        return next("Error while fetching entry from table for this recharge_number :", opts.recharge_number);
                    }
                    else{
                        return next(null,data);
                    }
                })
            },
            (record, next)=>{
                self.startProcessingRecords(function(){
                    return next();
                },record)
            }
            //         totalFreshRecordsCount = 0; //Just for logging purpose
            //     self.recordsPublished = 0;
            //     self._processFreshRecords(params, function __keepFetchingUntilDone(err, recordsCountInLastBatch, lastId) {
            //         if (err) {
            //             L.error('_execSteps', 'Error processing fresh records', 'for NBFD', params.nextBillFetchDate)
            //             //No matter what happened, don't stop at error and proceed further
            //             next()
            //         }
            //         //If batch size is equal to records fetched that means there can be more records.
            //         else if (recordsCountInLastBatch >= self.dbBatchSize) {
            //             L.log('_execSteps._processFreshRecords::', self.operator, 'recordsCountInLastBatch', recordsCountInLastBatch);
            //             totalFreshRecordsCount += recordsCountInLastBatch
            //             if(lastId) _.set(params, 'offset', lastId);
            //             else _.set(params, 'offset', _.get(params,'offset',0)+self.dbBatchSize);
            //             self._processFreshRecords(params, __keepFetchingUntilDone)
            //         }
            //         else { //No records left, so proceeding to next step
            //             totalFreshRecordsCount += recordsCountInLastBatch
            //             L.log('_execSteps._processFreshRecords::', self.operator, 'total fresh records processed', totalFreshRecordsCount);
            //             if (totalFreshRecordsCount) {
            //                 utility._sendMetricsToDD(totalFreshRecordsCount, ['REQUEST_TYPE:UPPCL_DEMERGER', 'STATUS:FRESH_RECORDS', 'TABLE:' + self.tableName]);
            //             }
            //             next()
            //         }
            //     });
            // }
        ],
            err => {
                //Lets invoke the main callback to finish the cycle
                return cb(err)
            }
        );
}

configureKafka(done, options) {
    let self = this;
    ASYNC.waterfall([
        next => {
            /**
             * Kafka publisher to publish events to FFR and AUTOMATIC pipeline 
             */
            self.ffrKafkaPublisher = new options.INFRAUTILS.kafka.producer({
                "kafkaHost": options.config.KAFKA.TOPICS.UPPCL_DEMERGER_SYNC.HOSTS
            });
            self.ffrKafkaPublisher.initProducer('high', function (error) {
                return next(error)
            });
        }
    ], function (error) {
        if (error) {
            L.critical('configureKafka', 'Could not initialize Kafka', error);
        }
        return done(error);
    });
}
}

function execute(opts, cb) {
    L.setLevel('verbose');
    if ((!opts.recharge_number || !opts.recharge_number.length)) {
        return cb(new Error('please provide recharge_number'));
    }
    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }
        commonLib = new utility.commonLib(options);
        reminderUtils = new digitalUtility.ReminderUtils();
        let script = new uppcl_demerger(options);
        script.configureKafka(function (error) {
            if (error) {
                L.critical('Script :: execute', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                L.log('Script :: execute', 'Kafka Configured successfully !!');
                script.runForAllRecord(opts, options,options.dbInstance, function(error, result){
                    if(error){
                        L.critical('Script :: execute', 'Error occured', error);
                    }
                    return cb(error);
                })
            }
        }, options);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-r, --recharge_number <value>', 'recharge_number', Helper.list)
            .parse(process.argv);
        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();


/*
(Put same logic as publisher till hitting ffr validation api)
Step 1) Start kafka-publisher to publish events to FFR and Gateway team
Step 2) Get records in batches for status not in ('7','13'), batchsize=1000;
Step 3) Hit FFR validation api in loop using each product_ids of the new operators.
Step 4) BFR028 will confirm if the record has migrated out of the pid, some specific error codes or successfull billFetch will confirm new pid.
Step 5) On indentifying new pid, findout new table through pid. 
Step 6) Mark status=13 in older pid and create a new record in new table (Handling should be there for duplicate records)
*/