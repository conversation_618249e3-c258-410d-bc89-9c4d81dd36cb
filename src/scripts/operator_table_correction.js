import startup from '../lib/startup';
import utility from '../lib'
import MOMENT from 'moment';
import request from 'request'
import _ from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';


/*
 * <PERSON>ript for https://jira.mypaytm.com/browse/IN-47124
 * npm run build && VAULT=0 node dist/scripts/operator_table_correction.js
*/

class csv_uppcl {

    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.config = options.config;
        this.operators = ['pvvnl', 'dvvnl', 'mvvnl', 'puvvnl'];
        this.operator_mapping = {
            'pvvnl' : {
                'product_id' : 467044907,
                'name' : 'paschimanchal vidyut vitran nigam limited (pvvnl)'
            },
            'dvvnl' : {
                'product_id' : 467045545,
                'name' : 'dakshinanchal vidyut vitran nigam limited (dvvnl)'
            }, 
            'mvvnl' : {
                'product_id' : 467045320,
                'name' : 'madhyanchal vidyut vitran nigam limited (mvvnl)'
            }, 
            'puvvnl' : {
                'product_id' : 467045319,
                'name' : 'purvanchal vidyut vitaran limited (puvvnl)'
            }
        };
        this.totalRow = 0;
        this.inputFileName = 'src/scripts/files/input.csv';
        this.outputFileName = 'src/scripts/files/output.csv';
        this.headers = 'product_id,customer_id,recharge_number,operator,error';
        this.retryAllowed = ['PROCESSING_ERROR', 'NO_OPERATOR_FOUND'];
        this.writeStream = fs.createWriteStream(this.outputFileName);
        this.writeStream.write(`${this.headers}\n`);
    }
    
    async processRecord(cb, data) {
        let self = this, requestData = self.transformDataForAPI(data);
        ++self.totalRow;
        if (!self.isValidRequest(requestData)) {
            self.writeData(requestData, 'INVALID_CSV_DATA');
            return cb('INVALID_CSV_DATA');
        }
        try { 
            const responseData = await self.callFFRValidationAPI(requestData);
            const correctOperators = self.getCorrectOperatorsNames(responseData);
            if (correctOperators.length == 1) {
                const incorrectTable = 'bills_' + requestData.operator,
                    correctTable = 'bills_' + correctOperators[0];
                if (incorrectTable != correctTable) {
                    const dbData = await self.readDatabase(requestData, incorrectTable);
                    if (dbData && _.isArray(dbData) && dbData.length == 1) {
                        try {
                            await self.insertDatabase(dbData[0], correctTable, correctOperators[0], responseData);
                            await self.updateDatabase(requestData, incorrectTable);
                            self.L.log(`processRecord :: ${JSON.stringify(data)} processed succesfully !!`);
                            self.publishMetric('SUCCESS', 'DATA_UPDATED');
                        } catch (error) {
                            self.writeData(requestData, 'DB_WRITE_ERROR');
                            return cb('DB_WRITE_ERROR');
                        }
                    } else {
                        self.writeData(requestData, 'DB_READ_ERROR');
                        return cb('DB_READ_ERROR');
                    }
                } else {
                    self.L.log(`processRecord :: Data : ${JSON.stringify(data)} already in correct table !!`);
                    self.publishMetric('SUCCESS', 'CORRECT_DATA_ALREADY');
                } 
            } else if (correctOperators.length == 0) {
                let isCaseOf1033 = false;
                self.operators.forEach(operator => {
                    if (responseData[operator]['statusCode'] == '200' && responseData[operator]['errorMessageCode'] == 1033) {
                        isCaseOf1033 = true;
                    }
                });
                if (isCaseOf1033) {
                    try {
                        await self.updateDatabase(requestData, 'bills_' + requestData.operator);
                        self.L.log(`processRecord :: 1033 found for ${JSON.stringify(data)}`);
                        self.publishMetric('SUCCESS', '1033_FOUND');
                    } catch (error) {
                        self.writeData(requestData, 'DB_WRITE_ERROR');
                        return cb('DB_WRITE_ERROR');
                    }
                } else {
                    self.L.log(`processRecord :: No operator found for ${JSON.stringify(data)}`);
                    self.publishMetric('SUCCESS', 'NO_OPERATOR_FOUND');
                    self.writeData(requestData, 'NO_OPERATOR_FOUND');
                }
            } else {
                self.L.log(`processRecord :: Multi operator found for ${JSON.stringify(data)}`);
                self.publishMetric('SUCCESS', 'MULTI_OPERATOR_FOUND');
                self.writeData(requestData, 'MULTI_OPERATOR_FOUND');
            }
            return cb();
        } catch (error) {
            self.writeData(requestData, 'PROCESSING_ERROR');
            return cb(error);
        }    
    }

    writeData(requestData, error) {
        let self = this;
        self.writeStream.write(`${requestData.product_id},${requestData.customer_id},${requestData.recharge_number},${requestData.operator},${error}\n`, 'utf8');
    }

    publishMetric(status, type) {
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPPCL_CORRECTION', `STATUS:${status}`, `TYPE:${type}`]);
    }

    transformDataForAPI(data) {
        let params =
        {
            'recharge_number': _.get(data, 'recharge_number', null),
            'operator': _.get(data, 'operator', null),
            'product_id': _.get(data, 'product_id', null),
            'customer_id': _.get(data, 'customer_id', null),
        }
        return params;
    }

    isValidRequest(requestData) {
        let self = this;
        if(!requestData.recharge_number){
            self.L.log("Got recharge_number as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'RECHARGE_NUMBER_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.operator){
            self.L.log("Got operator as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'OPERATOR_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.product_id){
            self.L.log("Got product_id as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'PRODUCT_ID_NOT_FOUND_FROM_CSV');
            return false;
        }
        if(!requestData.customer_id){
            self.L.log("Got customer_id as null from CSV File");
            self.publishMetric('PARAMS MISSING', 'CUSTOMER_ID_NOT_FOUND_FROM_CSV');
            return false;
        }
        return true;
    }

    async callFFRValidationAPI(requestData) {
        let self = this, responseData = {};
        await Promise.all(self.operators.map(operator => {
            return new Promise((resolve, reject) => {
                self.validateCall(requestData, operator, (error, response, body) => {
                    if (error) {
                        self.L.log(`callFFRValidationAPI :: ${requestData.recharge_number} with ${operator} occurred error ${error}`);
                        self.publishMetric('ERROR', 'FFR_VALIDATION_API');
                        responseData[operator] = {'statusCode': '400'};
                    } else {
                        if (body && typeof body === 'string') {
                            try {
                                body = JSON.parse(body);
                            } catch (e) {
                                body = null;
                                self.L.error("FFRValidationAPI", `Error parsing data received for recharge_number ${requestData.recharge_number} with ${operator}`, e);
                            }
                        }
                        if (body != null) {
                            let errorMessageCode = _.get(body, 'cart_items.0.validationGwResponse.errorMessageCode', null);
                            responseData[operator] = {'statusCode': '200', 'errorMessageCode' : errorMessageCode};
                        } else {
                            responseData[operator] = {'statusCode': '400'};
                        }
                        self.L.log(`callFFRValidationAPI :: ${requestData.recharge_number} with ${operator} processed succesfully !!`);
                        self.publishMetric('SUCCESS', 'FFR_VALIDATION_API');
                    }
                    resolve();
                });
            });
        }));
        return responseData;
    }

    validateCall(requestData, operator, cb) {
        let self = this,
            apiOpts = {
            "uri"       : _.get(self.config, ['FFR', 'VALIDATION_URL'], null),
            "method"    : "POST",
            "timeout"   : 60000,
            'json' : {
                'cart_items' : [
                    {
                        'price'           : 1234,
                        'product_id'      : self.operator_mapping[operator]['product_id'],
                        'quantity'        : 1,
                        'fulfillment_req' : {
                            'recharge_number': requestData.recharge_number
                        }
                    }
                ],
                'customer_id': requestData.customer_id,
                'channel_id' : 'digital-reminder-uppcl',
            }
        };
    
        self.L.log(`Hitting FFR Api: ${JSON.stringify(apiOpts)}`);
        request(apiOpts, function (err, res, body) {
            self.L.log('RESPONSE BODY: ', JSON.stringify(body, null, 4));
            return cb(err, res, body);
        });
    }
    
    getCorrectOperatorsNames(apiResponseData) {
        let self = this, correctOperators = [];
        self.operators.forEach(operator => {
            if (apiResponseData[operator]['statusCode'] == '200' && (apiResponseData[operator]['errorMessageCode'] == 1030 || apiResponseData[operator]['errorMessageCode'] == null) ) {
                correctOperators.push(operator);
            }
        });
        return correctOperators;
    }

    async readDatabase(requestData, tableName) {
        let self = this,
            query = `SELECT * FROM ${tableName} WHERE product_id = ? and customer_id = ? and recharge_number = ?;`,
            param = [
                requestData.product_id,
                requestData.customer_id,
                requestData.recharge_number
            ];

        self.L.log("Query :: ", query, " :: params :: ", param);

        return await new Promise((resolve, reject) => {
            self.dbInstance.exec((err, records) => {
                if (err) {
                    self.L.critical('readDatabase :: ', query, err);
                    self.publishMetric('ERROR', err);
                    reject(err);
                }
                resolve(records);
            }, "DIGITAL_REMINDER_SLAVE", query, param);
        });
    }
    
    async insertDatabase(dbData, correctTable, operator, responseData) {
        let self = this;

        let query = `INSERT INTO ${correctTable} \ 
            (customer_id,recharge_number,product_id,operator,amount,bill_date,due_date,bill_fetch_date,next_bill_fetch_date,gateway, \
            paytype,service,circle,customer_mobile,customer_email,payment_channel,retry_count,status,reason,extra,published_date, \
            user_data,notification_status,payment_date,service_id,customerOtherInfo,is_automatic,old_bill_fetch_date) \
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE next_bill_fetch_date=VALUES(next_bill_fetch_date);`,
            param = [
                _.get(dbData, 'customer_id', null),
                _.get(dbData, 'recharge_number', null),
                self.operator_mapping[operator]['product_id'],
                self.operator_mapping[operator]['name'],
                _.get(dbData, 'amount', 0.00),
                _.get(dbData, 'bill_date', null),
                _.get(dbData, 'due_date', null),
                _.get(dbData, 'bill_fetch_date', null),
                MOMENT().add(1, 'day').format('YYYY-MM-DD'),
                _.get(dbData, 'gateway', null),
                _.get(dbData, 'paytype', null),
                _.get(dbData, 'service', null),
                _.get(dbData, 'circle', null),
                _.get(dbData, 'customer_mobile', null),
                _.get(dbData, 'customer_email', null),
                _.get(dbData, 'payment_channel', null),
                _.get(dbData, 'retry_count', '0'),
                _.get(dbData, 'status', '0'),
                _.get(dbData, 'reason', null),
                _.get(dbData, 'extra', null),
                _.get(dbData, 'published_date', null),
                _.get(dbData, 'user_data', null),
                _.get(dbData, 'notification_status', '1'),
                _.get(dbData, 'payment_date', null),
                _.get(dbData, 'service_id', '0'),
                _.get(dbData, 'customerOtherInfo', null),
                _.get(dbData, 'is_automatic', '0'),
                _.get(dbData, 'old_bill_fetch_date', null),
            ];

        self.L.log("Query :: ", query, " :: params :: ", param);

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('insertDatabase :: ', query, error);
                self.publishMetric('ERROR', error);            
            }
        }, 'DIGITAL_REMINDER_MASTER', query, param);
    };

    async updateDatabase(requestData, incorrectTable) {
        let self = this,
            query = `UPDATE ${incorrectTable} SET status = 13 WHERE product_id = ? and customer_id = ? and recharge_number = ?;`,
            param = [
                requestData.product_id,
                requestData.customer_id,
                requestData.recharge_number
            ];

        self.L.log("Query :: ", query, " :: params :: ", param);

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('updateDatabase :: ', query, error);
                self.publishMetric('ERROR', error);
            }
        }, 'DIGITAL_REMINDER_MASTER', query, param);
    };

    getCSV_DataFrom_Local(cb) {
        let self = this;
        try {
            const Stream = fs.createReadStream(self.inputFileName);
            const csvStream = parseStream(Stream, { headers: true });
            csvStream
                .on('data', (data) => {
                    let errorData = _.get(data, 'error', null);
                    if (errorData == null || self.retryAllowed.includes(errorData)) {
                        csvStream.pause();
                        self.processRecord((err) => {
                            if (err) {
                                self.publishMetric('ERROR', err);                                
                                self.L.error('getCSV_DataFrom_Local :: Error in processing data', err, JSON.stringify(data));
                            }
                            csvStream.resume();
                        }, data);
                    }
                })
                .on('end', rowCount => {
                    setTimeout(() => {
                        self.L.log(`getCSV_DataFrom_Local :: ${rowCount} Data rows processed`);
                        return cb();
                    }, 1000);
                })
                .on('error', error => {
                    setTimeout(() => {
                        self.L.error("processCSV data", "Error", error);
                        return cb(error);
                    }, 1000);
                });
        }
        catch (err) {
            return cb(err);
        }
    }

    async start(cb) {
        let self = this;
        await self.getCSV_DataFrom_Local(function (err) {
            if (err) {
                self.L.error('start::Error', err);
            } else {
                self.L.log('start::completed, totalRowCount ', self.totalRow);
            }
            self.writeStream.end();
            return cb(err);
        });
    }
}

(function main() {
    if (require.main === module) {
        startup.init({
        }, function (err, options) {
            let script = new csv_uppcl(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
        });
    }
})();
