import L from 'lgr'
import _, { lowerCase } from 'lodash'
import MOMENT from 'moment';

import ASYNC from 'async'
import startup from '../lib/startup'
import utility from '../lib'
import activePid from '../lib/activePid'


class remove_duplicate_billsCreditCard {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.client = options.cassandraDbClient;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.dbBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 1000 : 5
        this.dbUpdateBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 100 : 2
        this.id = 0;//_.get(options, 'startingID', 31752591)
        this.dbInstance = options.dbInstance
        this.activePidLib = options.activePidLib;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
    }

    start(cb, opts) {
        let self = this;

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }

        self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
        });
        this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
            self.L.critical('nonPaytmKafkaPublisher:: error in initialising Producer :: ', error);
            self.L.log("RECENTBILLS :: NON_PAYTM_RECORDS KAFKA PRODUCER STARTED....");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DUPLICATE_NON_RU_RECORD", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:NON_PAYTM']);
        });


        self.fetchRecords(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self._processRecordsInBatch(() => {
                    self.id = _.last(data)['id']

                    if (self.greyScaleEnv) {
                    }

                    self.L.log('fetchRecords', `processing next batch , Starting id  ${self.id}`);
                    setTimeout(() => {
                        self.fetchRecords(_doUntilNoMoreRecords);
                    }, 1000);
                }, data, 0);
            } else {
                self.L.log('fetchRecords', `No record found for Starting id  ${self.id}`);
                cb();
            }
        });

    }
    
    
    

    fetchRecords(done) {
        let self = this;
        const query = `SELECT id, recharge_number,customer_id,service,bank_name,due_date,extra from bills_creditcard where id > ${self.id} AND YEAR(updated_at) = 2024 
        AND due_date < CURDATE() order by id asc limit ${self.dbBatchSize} `
        self.L.log("fetchRecords", query, [self.dbBatchSize, self.id]);
        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetails::', query, error);
            }
            return done(error, res);
        }, 'DIGITAL_REMINDER_SLAVE', query, [self.dbBatchSize, self.id]);
    }

    _maskRechargeNumber(rechargeNumber) {
        if (!rechargeNumber || typeof rechargeNumber !== 'string') {
            return '';
        }
    
        const parts = rechargeNumber.split(' ');
    
        if (parts.length === 4) {
            parts[0] = 'XXXX';
            parts[1] = 'XXXX';
            return parts.join(' ');
        }

        console.log("created rechargeNumber", rechargeNumber);
    
        return rechargeNumber;
    }

    


    _processRecordsInBatch(done, records) {
        let
            self = this;

        ASYNC.eachLimit(records, 10, function (dataRow, cb) {
            
            let ruRechargeNumber= _.get(dataRow, 'recharge_number', '');
            let nonRuRechargeNumber = self._maskRechargeNumber(ruRechargeNumber);
            let bankName=_.get(dataRow, 'bank_name', '');

            let params = {
                rechargeNumber: nonRuRechargeNumber,
                customerId: _.get(dataRow, 'customer_id', ''),
                service: _.get(dataRow, 'service', ''),
                operator: _.toLower(bankName),
                dueDate: _.get(dataRow, 'due_date', ''),
                extra: _.get(dataRow, 'extra', '')
            }

            self.processRecordsforDeletion(params);
            
        }, function (err) {
            if (err) console.log(err)
            done(err)
        });
    }

    updateRecord(extra) {
        if (extra) {
            extra.recon_id = 'newReconId';
            extra.updated_source = 'sms';
        }
        return record;
    }

    async readBills(params){
        const self = this;
        const query = `SELECT * FROM bills_non_paytm WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator = ?`;
        const queryParams = [params.rechargeNumber, params.customerId, params.service, params.operator];
        var latencyStart = new Date().getTime();
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'readBills'});
                this.L.log(`nonPaytmBills::readBills fetched ${result.rows.length} for ${JSON.stringify(params)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'readBills'});
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:readBills`]);
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async processRecordsforDeletion(params) {
        let self = this;
        let existingRecord = await self.readBills(params);
        if(existingRecord.length == 1){
            console.log("existingRecord from non ru", existingRecord);
            console.log("params from ru", existingRecord);
            let dueDateTimeStampForNonRU = _.get(existingRecord[0], 'due_date', null);
            let amountForNonRU = _.get(existingRecord[0], 'amount', null);
            dueDateTimeStampForNonRU = await this.convertStringtoTimeStamp(dueDateTimeStampForNonRU);
            let dueDateTimeStampForRU = await this.convertStringtoTimeStamp(params.dueDate);
            console.log("dueDateTimeStampForNonRU", dueDateTimeStampForNonRU);
            console.log("dueDateTimeStampForRU", dueDateTimeStampForRU);
            console.log("amountForNonRU", amountForNonRU);


            if(dueDateTimeStampForNonRU > dueDateTimeStampForRU && dueDateTimeStampForNonRU > new Date() && amountForNonRU>0){
                
                let dbExtraFromRU = params.extra;
                dbExtraFromRU.updated_data_source = "sms";

                let dbExtraFromNonRU = JSON.parse(_.get(existingRecord[0], 'extra', '{}'));
                dbExtraFromRU.recon_id=dbExtraFromNonRU.recon_id;

                let params = {
                    id: params.id,
                    due_date: dueDateTimeStampForNonRU,
                    amount: amountForNonRU,
                    bill_date: _.get(existingRecord[0], 'bill_date', null),
                    status: _.get(existingRecord[0], 'status', null),
                    extra: JSON.stringify(dbExtraFromRU)
                };

                console.log("params for update", params);

                this.updateDb((error) => {
                    if (error) {
                        console.log('Error updating database:', error);
                    } else {
                        console.log('Database updated successfully');
                    }
                }, params);
            }

        //delete

        let nonPaytmKafkaPayload = {
            customerId: _.get(existingRecord[0], 'customer_id', null),
            service: _.get(existingRecord[0], 'service', null),
            paytype: _.get(existingRecord[0], 'paytype', null),
            productId: _.get(existingRecord[0], 'product_id', null),
            operator: _.get(existingRecord[0], 'operator', null),
            rechargeNumber: _.get(existingRecord[0], 'recharge_number', null),
            dbEvent: 'delete'
        }

        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
            messages: JSON.stringify(nonPaytmKafkaPayload)
        }], (error) => {
            if(error){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DUPLICATE_NON_RU_RECORD", 'STATUS:ERROR', "TYPE:NON_PAYTM_EVENTS"]);
                self.L.critical('nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(nonPaytmKafkaPayload), error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DUPLICATE_NON_RU_RECORD", 'STATUS:PUBLISHED', "TYPE:NON_PAYTM_EVENTS", "OPERATOR:" + recentBillsData.operator]);
                self.L.log('nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(nonPaytmKafkaPayload));
            }
        })
    }
        
    }

    async convertStringtoTimeStamp(dateString) {
        let date = new Date(dateString);
        let utcTime = date.getTime();
        let localDate = new Date(utcTime);
        localDate.setHours(0, 0, 0, 0);
        return (localDate.getTime());
    }

   

    updateDb(cb, params) {
        let self = this;

        let query = `UPDATE bills_creditcard SET due_date = ?, amount = ?, bill_date = ?, status = ?, extra = ? WHERE id = ?`;

        queryParams = [params.due_date, params.amount, params.bill_date, params.status, params.extra, params.id];
        
        console.log("Query, ", query, queryParams);

        self.dbInstance.exec(function (err) {
           
            if (err) {
               console.log("self.dbInstance.exec error ", err, queryParams);
            }
            return cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --path <value>', 'path', String)
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try {
                let script = new remove_duplicate_billsCreditCard({ ...options, startingID: commander.startingID });
                script.start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 1000);
                }, commander);
            } catch (err) {
                console.log(err)
            }
        });
    }
})();

// NODE_ENV=production node dist/scripts/airtel_csv_loader.js --clean_up 1 --path '/path/to/file/airtel.csv' -v