import ASYNC from 'async'
import _ from 'lodash'
import startup from '../lib/startup'
import utility from '../lib'
import BILLS from '../models/bills'
import RemindableUsersLibrary from '../lib/remindableUser'

let skip = 0;
let batchSize = 100;

function executeRecursive(options, callback) {
    options.L.log('executeRecursive:: ', `reading fresh batch from plan_validity table`);
    readDataFromPlanValidity(options, batchSize, skip, function (err, data) {
        if (err) {
            return callback(err);
        } 
        if (!data) {
            return callback(null, "Processed all records from plan_validity table");
        }

        processBatch(options, data, function(err) {
            if (err) {
                options.L.error('processBatch:: ', `error occurred in processing the batch`, err);
            } 
            options.L.log('processBatch:: ', `processing done for the batch`);
            skip = skip + batchSize;
            setTimeout(executeRecursive, 500, options, callback);
            // executeRecursive(options, callback);
        })
    })
}

function readDataFromPlanValidity(options, batchSize, skipRows = 0, done) {
    let query = `SELECT * FROM plan_validity ORDER BY id asc LIMIT ?,?`,
        dbInstance = options.dbInstance;
    
    let latencyStart = new Date().getTime();
    dbInstance.exec(function (error, data) {
        utility._sendLatencyToDD(latencyStart, {'REQUEST_TYPE':'DB_QUERY', 'TYPE':'READ_DATA_FROM_PV_FOR_SCRIPT'});
        if (error) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY', 'TYPE:READ_DATA_FROM_PV_FOR_SCRIPT', 'STATUS:ERROR']);
            options.L.error('readDataFromPlanValidity::', `error occurred `, error);
            return done(error);
        }
        if (data.length === 0) {
            options.L.log('readDataFromPlanValidity:: ', `Finished reading all data from plan_validity table`);
            return done(null, null);
        }
        options.L.log(`Batch of size ${batchSize} fetched from plan_validity, skipping ${skipRows} rows`);
        done(null, data);
    }, 'RECHARGE_ANALYTICS_SLAVE', query, [
        skipRows,
        batchSize
    ]);
}

function processBatch(options, records, done) {
    ASYNC.each(records,
        (record, cb) => {
            fetchAndUpdateCustomerNameForRecord(options, record, cb);
        },
        err => {
        if (err) {
            console.log(`processRecords:: error occurred ${err}`);
            done(err);
        } 
        done();
    });
}

function fetchAndUpdateCustomerNameForRecord(options, record, done) {
    /**
     *  1. check if customer_name and rn_customer_name exists
     *  2. if yes, we don't need to do anything, process next record, callback()
     *  3. hit oauth api to fetch name
     *  4. update the plan validity table
     */
    let extra, customerName, rnCustomerName,
    rechargeNumber = _.get(record, 'recharge_number', null);
    utility._sendMetricsToDD(1, ['REQUEST_TYPE:FETCH_AND_UPDATE_CUST_NAMES_PV_SCRIPT', 'STATUS:PROCESSING_RECORDS']);
    try {
        extra = JSON.parse(_.get(record, 'extra', null)) ? JSON.parse(_.get(record, 'extra', null)) : {},
        customerName = _.get(extra, 'customer_name', null),
        rnCustomerName = _.get(extra, 'rn_customer_name', null);   
    } catch (e) {
        options.L.error('fetchAndUpdateCustomerNameForRecord:: ', `error occurred while parsing record for rech number ${rechargeNumber}`, e);
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:FETCH_AND_UPDATE_CUST_NAMES_PV_SCRIPT', 'STATUS:PARSING_ERROR']);
        return done();
    }
    
    if (customerName && rnCustomerName) {
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:FETCH_AND_UPDATE_CUST_NAMES_PV_SCRIPT', 'STATUS:CUST_NAMES_ALREADY_PRESENT']);
        options.L.log('fetchAndUpdateCustomerNameForRecord:: ', `both cust name and rn cust name present, skipping record for rech number ${rechargeNumber}`);
        return done();
    }

    let customerData = {
        customer_id : _.get(record, 'customer_id', null)
    },
    rnCustomerData = {
        customer_id: _.get(record, 'rn_customer_id', null)
    }
    
    ASYNC.waterfall([
        (callback) => {
            if (!customerName) {
                options.L.log('fetchAndUpdateCustomerNameForRecord:: ', `fetching cust name for cust id ${_.get(record, 'customer_id', null)}, rech number ${rechargeNumber}`);
                options.remindableUsersLibrary._getUserDetails(callback, customerData);
            } else {
                callback(null, false);
            }
        }, 
        (fetchedCustomerName, callback) => {
            if (fetchedCustomerName) {
                _.set(extra, 'customer_name', _.get(customerData, 'customer_name', null));
            }
            if (!rnCustomerName) {
                options.L.log('fetchAndUpdateCustomerNameForRecord:: ', `fetching rn cust name for cust id ${_.get(record, 'rn_customer_id', null)}, rech number ${rechargeNumber}`);
                options.remindableUsersLibrary._getUserDetails(callback, rnCustomerData);
            } else {
                callback(null, false);
            }
        }, 
        (fetchedRnCustomerName, callback) => {
            if (fetchedRnCustomerName) {
                _.set(extra, 'rn_customer_name', _.get(rnCustomerData, 'customer_name', null));
            }
            let query = `UPDATE plan_validity SET extra = ? WHERE id = ?`,
            queryParams = [
                JSON.stringify(extra), 
                _.get(record, 'id', null)
            ],
            dbInstance = options.dbInstance;
            options.L.log('fetchAnUpdateCustomerNameForRecord:: ', `executing query ${dbInstance.format(query, queryParams)} for rech number ${rechargeNumber}`);
            let latencyStart = new Date().getTime();
            dbInstance.exec(function (err, data) {
                utility._sendLatencyToDD(latencyStart, {'REQUEST_TYPE':'DB_QUERY', 'TYPE':'UPDATE_CUST_NAMES_PV_SCRIPT'});
                if (err) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY', 'TYPE:UPDATE_CUST_NAMES_PV_SCRIPT', 'STATUS:ERROR']);
                    options.L.error('fetchAndUpdateCustomerNameForRecord:: ', `error executing update query for rech number ${rechargeNumber}`, err);
                    callback(err);
                }
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY', 'TYPE:UPDATE_CUST_NAMES_PV_SCRIPT', 'STATUS:SUCCESS']);
                options.L.log('fetchAndUpdateCustomerNameForRecord:: ', `successfully updated plan_validity table for rech number ${rechargeNumber}`);
                callback();
            }, 'RECHARGE_ANALYTICS', query, queryParams);
            
        }
    ], (err, result) => {
        if (err) {
            options.L.error('fetchAndUpdateCustomerNameForRecord::', `operation failed for rech number ${rechargeNumber}`);
        }
        done();
    });


}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: false
        }
    }, (err, options) => {
        if (err) {
            return;
        }
        options.remindableUsersLibrary = new RemindableUsersLibrary(options);
        options.billsModel = new BILLS(options);
        executeRecursive(options, (err, results) => {
            if (err) {
                options.L.error('updateCustNamesInPlanValidity:: ', `Script execution failed`, err);
                process.exit(1);
            }
            options.L.log('updateCustNamesInPlanValidity:: ', `Script executed successfully!!`, results);
            process.exit(0);
        });
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();