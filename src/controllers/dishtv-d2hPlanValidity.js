"use strict";
import utility from '../lib';
import MOMENT from 'moment';
import _ from "lodash";
import OAuth from '../lib/oauth';

class planValidityCtrlr{
    constructor(options, parentController){
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.pc = parentController;
        this.dbInstance = options.dbInstance;
        this.rechargeConfig = options.rechargeConfig;
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
    }

    async transaction(req, res){
        try{
            let record = _.get(req, "body", {});
            await this.executeStrategy(record);
            res.status(200).send({"MESSAGE": "SUCCESS"});
            return;
        }
        catch (err) {
            this.L.error('planValidityCtrlr: transaction:: ', err);
            res.status(200).send({"MESSAGE": "UNKNOWN_ERROR"});
            return;
        }
    }

    async executeStrategy (record) {
        let self = this;

        if (['dishtv', 'd2h (formerly videocon d2h)'].includes(record.operator) && record.paytype == 'prepaid') {
            utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','STATUS:D2H_STRATEGY']);
            await self.executeD2HStrategy(record);
            return;
        }

        await self.defaultStrategy(record);
        return;
    }

    async defaultStrategy(record, message) {
        utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','STATUS:RECORD_NOT_PROCESSED']);
        this.L.log('defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        return;
    }

    async publishInKafka(payload) {
        let topic = this.config.KAFKA.SERVICES.PLAN_VALIDITY_SYNC_DB.TOPIC;
        try {
            await this.pc.planValidityPublisher.publishData([{
                topic: topic,
                messages: JSON.stringify(payload)
            }], null, [200, 800]);

            this.L.log('publishInKafka:: Message published successfully in Kafka', ` on topic ${topic}`, JSON.stringify(payload));
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_RMQ_CONSUMER', 'TOPIC:' + topic, 'STATUS:PUBLISHED', `OPERATOR:${payload.productInfo_operator}`]);
            return;
        } catch (error) {
            this.L.critical('publishInKafka:: Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_RMQ_CONSUMER', 'TOPIC:' + topic, 'STATUS:PUBLISHED_FAILED', `OPERATOR:${payload.productInfo_operator}`]);
            return;
        }
    }

    async executeD2HStrategy(record) {
        let self = this;
        try {
            if(!record) {
                return
            }
            // if (_.get(record, 'mobileNumber', '') === '') { // legacy code flow -> When operator didn't provide MobileNo 
            //     await self.custDetailsBasedOnSubscriberIdFlow(record); // convert logic for lookup into plan_validity table instead of dth_customer_info table
            // } else {
                await self.custDetailsBasedOnMobileNumberFlow(record);
            // }
        }
        catch (err) {
            this.L.error(`d2hStrategy:: Error for record :${JSON.stringify(record)}, ${err}`);
            return;
        }

    }

    /**
     * 
     *  @param {*} record :{
     *   mobileNumber : <> optional,
     *   userFlag: <> optional,
     *   service: <> mandatory,
     *   operator: <> mandatory,
     *   rechargeNumber: <> mandatory
     * }
     * @returns 
     */
    
    async custDetailsBasedOnMobileNumberFlow(record){
        let self = this;
        try {
            const product_id = _.get(record, 'productId', ''); // productId value will be populated in coming future
            if(!product_id) {
                this.L.log('d2hStrategy::executeStrategy:: ', 'skipping record as product ID not found', JSON.stringify(record));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:PRODUCT_ID_NOT_FOUND']);
                return;
            }

            let customerDetails = await self.getCustDetails(record);
            if (!customerDetails || !_.isArray(customerDetails) || customerDetails.length < 1) {
                this.L.log('d2hStrategy:: custDetailsBasedOnMobileNumberFlow:: ', 'customer details not found. ', JSON.stringify(record));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:CUSTOMER_DETAILS_NOT_FOUND']);
                return;
            } else {
                this.L.log(`d2hStrategy::custDetailsBasedOnMobileNumberFlow:: fetched customer details for ${record.rechargeNumber} : ${JSON.stringify(customerDetails[0])}`);
            }
            let kafkaPayload = this.getKafkaPayload(customerDetails[0], record, product_id);
            await this.publishInKafka(kafkaPayload);
            return;
        } catch (err) {
            this.L.error(`d2hStrategy:: Error for record :${JSON.stringify(record)}, ${err}`);
            return;
        }

    }

    /**
     * 
     *  @param {*} record :{
     *   mobileNumber : <> optional,
     *   userFlag: <> optional,
     *   service: <> mandatory,
     *   operator: <> mandatory,
     *   rechargeNumber: <> mandatory
     * }
     * @returns : {
     *  "customerId" : <>,
     *  "customerMobile" : <>,
     *  "rechargeNumber" : <>   
     * }
     */
    async getCustDetails(record){
        let self = this;
        try {
            let mobileNumber = _.get(record, 'mobileNumber', ''),     // optional
                userFlag  = _.get(record, 'userFlag', ''),            // optional
                service = _.get(record, 'service', ''),               // mandatory
                operator = _.get(record, 'operator', ''),             // mandatory
                rechargeNumber = _.get(record, 'rechargeNumber', ''), // mandatory
                customerDetails = [];

            customerDetails = await self.fetchCustomerDetails({
                tableName: "plan_validity",
                rechargeNumber: rechargeNumber,
                mobileNumber: mobileNumber,
                operator: operator,
                service: service
            });

            if (customerDetails && _.isArray(customerDetails) && !(customerDetails.length < 1)) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_FOUND', 'TYPE:CUST_DETAILS_FOUND_FROM_PLAN_VALIDITY_TABLE']);
                self.L.log(`d2hStrategy::getCustDetails:: fetched customer details from table plan_validity for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}_customerDetails:${JSON.stringify(customerDetails[0])}`);
                return customerDetails;
            }
            self.L.log(`d2hStrategy::getCustDetails:: customer details not found from table plan_validity for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);

            customerDetails = await self.fetchCustomerDetails({
                tableName: "dth_customer_info",
                rechargeNumber: rechargeNumber,
                mobileNumber: mobileNumber,
                operator: operator,
                service: service
            });

            if (customerDetails && _.isArray(customerDetails) && !(customerDetails.length < 1)) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_FOUND', 'TYPE:CUST_DETAILS_FOUND_FROM_DTH_CUSTOMER_INFO_TABLE']);
                self.L.log(`d2hStrategy::getCustDetails:: fetched customer details from table dth_customer_info for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}_customerDetails:${JSON.stringify(customerDetails[0])}`);
                return customerDetails;
            }
            self.L.log(`d2hStrategy::getCustDetails:: customer details not found from table dth_customer_info for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);

            if (mobileNumber === '') {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:MOBILE_NUMBER_NOT_FOUND']);
                self.L.log(`d2hStrategy::getCustDetails:: customer details not found for rechargeNumber:${rechargeNumber} mobileNumber didn't exists`);
                return customerDetails;
            }

            if (_.has(self.config, ['COMMON', 'D2H_GET_USER_CUSTID_COMPATIABLE_FLAG', userFlag])) {
                let queryString = `?fetch_strategy=USERID,BASIC&phone=${mobileNumber}`;
                self.L.log(`d2hStrategy::getCustDetails:: from OAuth.fetchCustomerDetail API for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);
                let response = await self.OAuth.fetchCustomerDetail(queryString);       
                // dummy sample response -> {"basicInfo":{"countryCode":"91","displayName":"Abhas","email":"<EMAIL>","firstName":"ABHAS BHATNAGAR","lastName":"","phone":"7503251652"},"userId":105439218}
                // transform customerDetails -> {customerId: <> , customerMobile: <>, rechargeNumber: <>}
                if (_.get(response, ['userId'], '') === '') {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:CUST_ID_NOT_FOUND_FROM_OATH_API']);
                    self.L.log(`d2hStrategy::getCustDetails:: customer details not found from OAuth API for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);
                    return customerDetails; // OAuth API didn't contains customer details
                }
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_FOUND', 'TYPE:CUST_DETAILS_FOUND_FROM_OATH_API']);
                self.L.log(`d2hStrategy::getCustDetails:: customer details found from OAuth API for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}_customerId:${_.get(response, ['userId'], '')}`);

                let customerDetailsObj = {
                    "customerId" : _.get(response, ['userId'], ''),
                    "customerMobile" : mobileNumber,
                    "rechargeNumber" : rechargeNumber
                }
                customerDetails = [customerDetailsObj];

                try {
                    await self.insertRecordsInDthTable(customerDetailsObj, record); // If insert DB query gets failed then will we have to proceed further    
                    self.L.log(`d2hStrategy::getCustDetails::insertRecordsInDthTable Customer details inserted into dth_customer_info table for rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);
                } catch (err) {
                    self.L.error(`d2hStrategy::getCustDetails::insertRecordsInDthTable Error while inserting record rechargeNumber:${rechargeNumber}_mobileNumber:${mobileNumber}`);
                    return customerDetails;
                }   
            }
            return customerDetails;
        }
        catch (error) {
            self.L.error(`d2hStrategy::getCustDetails Error for record :${JSON.stringify(record)}, ${error}`);
            return null;
        }

    }

    /**
     * 
     * @param {*} customerDetailsObj : {
     *  "customerId" : <> mandatory,
     *  "customerMobile" : <> mandatory,
     *  "rechargeNumber" : <> mandatory
     * }
     * @param {*} record 
     * @returns 
     */
    async insertRecordsInDthTable(customerDetailsObj, record){
        try {
            let self = this;
            const extra = JSON.stringify({"source" : "operator_flow" });
            const query = `INSERT INTO dth_customer_info \
            (recharge_number, customer_id, product_id, operator, amount, paytype, service, customer_mobile, extra) \
            values (?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE customer_id=VALUES(customer_id), product_id=VALUES(product_id), \
            customer_mobile=VALUES(customer_mobile), amount=VALUES(amount), extra=VALUES(extra)`;

            // case 1: User on-boarded from Operator data: Record will be converted to paytm user once recharge transaction is done
            //          (if user is on-boarded from operator data (non-paytm user) we will make it paytm user (if recharge event is received))
            //          converting extra.source="operator_data" -> extra.source="payment_flow" 
            // case 2: User already a paytm user once recharge transaction is done (it will remain paytm user)

            const params = [
                record.rechargeNumber,
                customerDetailsObj.customerId,
                record.productId,
                record.operator,
                record.amount,
                record.paytype,
                record.service,
                customerDetailsObj.customerMobile,
                extra
            ];

            return new Promise((resolve, reject) => {
                self.dbInstance.exec((error, response) => {
                    if(error) {
                        this.L.critical(`insertRecordsInDthTable:: error = ${error} for query ${query}`);
                        return reject(error);
                    }
                    return resolve(response);
                }, 'OPERATOR_SYNC', query, params);
            })


        } catch (error) {
            this.L.error("insertRecordsInDthTable:: error ", error, " for record", record);
        }
    }
    
    /**
    async custDetailsBasedOnSubscriberIdFlow(record){
        let self = this;
        try {
            if(!record) {
                return
            }
            let customerDetails = await self.fetchCustomerDetails({
                tableName: "dth_customer_info",
                rechargeNumber: record.rechargeNumber,
                operator: record.operator,
                service: record.service
            });
            if (!customerDetails || !_.isArray(customerDetails) || customerDetails.length < 1) {
                this.L.log('d2hStrategy:: custDetailsBasedOnSubscriberIdFlow:: ', 'customer details not found. ', JSON.stringify(record));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND']);
                return;
            } else {
                this.L.log(`d2hStrategy::executeStrategy:: fetched customer details for ${record.rechargeNumber} : ${JSON.stringify(customerDetails[0])}`);
            }

            const product_id = _.get(customerDetails[0], 'product_id', '');
            if(!product_id) {
                this.L.log('d2hStrategy::custDetailsBasedOnSubscriberIdFlow:: ', 'skipping record as product ID not found', JSON.stringify(record));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:D2H_STRATEGY', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND']);
                return;
            }

            let kafkaPayload = this.getKafkaPayload(customerDetails[0], record, product_id);
            await this.parent.publishInKafka(kafkaPayload);
            return;
        }
        catch (err) {
            this.L.error(`d2hStrategy:: Error for record :${JSON.stringify(record)}, ${err}`);
            return;
        }
    }
     */
     


    async fetchCustomerDetails(options) {
        let self = this,
            tableName = options.tableName,
            rechargeNumber = options.rechargeNumber,
            mobileNumber = options.mobileNumber,
            operator = options.operator,
            service = options.service;

        let recharge_number_str = '';
        if (rechargeNumber !== '') {
            recharge_number_str += `'${rechargeNumber}'`;
        }
        if (mobileNumber !== '') {
            if (recharge_number_str !== '') {
                recharge_number_str += ", "
            }
            recharge_number_str += `'${mobileNumber}'`;
        }

        if(recharge_number_str === '') {
            return null;
        }

        let tableDatabaseMapping = {
            "dth_customer_info" : "OPERATOR_SYNC_SLAVE",
            "plan_validity" : "RECHARGE_ANALYTICS_SLAVE"
        },
            tableCustomerMobileMapping = {
                "dth_customer_info" : "customer_mobile",
                "plan_validity" : "cust_mobile"
            },
        tableCustomerEmailMapping = {
            "plan_validity": "cust_email"
        }
        let databaseName = tableDatabaseMapping[tableName];
        let customerMobile = tableCustomerMobileMapping[tableName];
        let customerEmail = tableCustomerEmailMapping[tableName];

        return new Promise((resolve, reject) => {
            let query;
            if(tableName=='plan_validity'){
                query = `SELECT customer_id as customerId, recharge_number as rechargeNumber, ${customerMobile} as customerMobile, ${customerEmail} as customerEmail FROM ${tableName} WHERE ((recharge_number in ( ${recharge_number_str} )  AND operator = '${operator}' AND service = '${service}') OR (cust_rech_meta in ( ${recharge_number_str} ) AND operator = '${operator}' AND service = '${service}' )) ORDER BY updated_at DESC;`;
            }
            else{
                query = `SELECT customer_id as customerId, recharge_number as rechargeNumber, ${customerMobile} as customerMobile FROM ${tableName} WHERE ((recharge_number in ( ${recharge_number_str} )  AND operator = '${operator}' AND service = '${service}') OR (cust_rech_meta in ( ${recharge_number_str} ) AND operator = '${operator}' AND service = '${service}' )) ORDER BY updated_at DESC;`;
            }
            
            self.dbInstance.exec(function (err, data) {
                if (err || !(data)) {
                    self.L.critical('d2hStrategy:: fetchCustomerDetails::', 'error occurred while getting data from DB: ', err);
                    reject(err);
                }
                resolve(data);

            }, databaseName, query, []);
        });
    }

    getKafkaPayload(customerDetails, record, product_id) {
        let self = this;
        const customerId = _.get(customerDetails, 'customerId', '');
        const customerMobile = _.get(customerDetails, 'customerMobile', '');
        const customerEmail = _.get(customerDetails, 'customerEmail', '');
        const rechargeNumber = _.get(customerDetails, 'rechargeNumber', '');
        const activePid = self.activePidLib.getActivePID(product_id);
        const payloadToBePublishedInKafka = {
            metaData: {},
            dataFromDthPublisher: true,
            validityExpiryDate: MOMENT(record.validityExpiryDate).format('YYYY-MM-DD HH:mm:ss'),
            windowTime: MOMENT().format('YYYY-MM-DD 00:00:00'),
            userData_recharge_number: rechargeNumber,
            catalogProductID: activePid,
            userData_amount: record.amount,
            productInfo_operator: record.operator,
            productInfo_circle: null, 
            productInfo_service: record.service,
            productInfo_paytype: record.paytype,
            customerInfo_customer_id: customerId,
            customerInfo_customer_phone: customerMobile,
            customerInfo_customer_email: customerEmail,
            inStatusMap_transactionStatus: "SUCCESS",
            originalPid: product_id,
            triedPids: [],
        };
        return payloadToBePublishedInKafka;
    }
}
module.exports = planValidityCtrlr;