import VALIDATOR from 'validator'
import MOMENT from 'moment'
import _ from 'lodash'
import utility from '../lib'
import <PERSON>NCDECPUTIL from '../lib/EncryptionDecryptioinHelper'

let L = null;

class PlanValidity {
    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.encDecpUtil = new ENCDECPUTIL(options);
        this.config = options.config;
        this.dbInstance = options.dbInstance;
    }

    /**
     * Function used to collect plan validity data
     * @param {*} done 
     * @param {*} id get record based on id>
     * @param {*} startTime validity expiry start time
     * @param {*} endTime validity expiry end time
     * @param {*} batchSize process data in batches
     * @param {*} clause optional
     */
    getRecordsToNotify(done, id, startTime, endTime, batchSize, clause = {}, dayValue) {
        let self = this;
        let query = `select * from plan_validity WHERE id > ?`;
        let queryParams = [id];
        if (clause.operator) {
            query += ` AND operator in (?)`;
            queryParams.push(clause.operator);
        }
        if (clause.operator_exclude) {
            query += ` AND operator not in (?)`;
            queryParams.push(clause.operator_exclude);
        }
        if (_.get(clause, ['PVREMINDER_DAYVALUES', dayValue, 'where'])) {
            let whereClause = _.get(clause, ['PVREMINDER_DAYVALUES', dayValue, 'where']);
            console.log(whereClause)
            query += whereClause.bind(self)();
        }
        
        if(clause.dthService) {
            query += ` AND service != 'mobile' AND validity_expiry_date >= ? and validity_expiry_date < ? order by id limit ?`;
        } else {
            query += ` AND service != 'dth' AND validity_expiry_date >= ? and validity_expiry_date < ? order by id limit ?`;
        }
            
        queryParams.push(startTime);
        queryParams.push(endTime);
        queryParams.push(batchSize);

        L.verbose('planValidity::getRecordsToNotify', query, JSON.stringify(queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getRecordsToNotify', 'SOURCE': 'PLAN_VALIDITY'
            });
            if (error || !(data)) {
                L.critical('getRecordsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
    }

        /**
     * Function used to collect plan validity data
     * @param {*} done 
     * @param {*} records an array containing IDs of the records we want to fetch
     */
    getRecordsToNotifyByID(done, records) {
        let self = this;
        let query = `select * from plan_validity WHERE id in (?)`;
        let queryParams = [records];
        

        L.verbose('planValidity::getRecordsToNotify',  self.dbInstance.format(query , queryParams));
        let latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getRecordsToNotifyByID', 'SOURCE': 'PLAN_VALIDITY'
            });
            if (error || !(data)) {
                L.critical('getRecordsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
    }
    

      /**
     * Function used to get all valid IDs from DB which satisfy the condition 
     * @param {*} done 
     * @param {*} startTime validity expiry start time
     * @param {*} endTime validity expiry end time
     * @param {*} clause optional
     */
      getAllElgibleIDs(done, startTime, endTime, clause = {}, dayValue) {
        let self = this;
        let query = `select id from plan_validity WHERE `;
        let queryParams = [];
        if (clause.operator) {
            query += `operator in (?)`;
            queryParams.push(clause.operator);
        }
        if (clause.operator_exclude) {
            query += ` AND operator not in (?)`;
            queryParams.push(clause.operator_exclude);
        }
        if (_.get(clause, ['PVREMINDER_DAYVALUES', dayValue, 'where'])) {
            let whereClause = _.get(clause, ['PVREMINDER_DAYVALUES', dayValue, 'where']);
            query += whereClause.bind(self)();
        }
        
        if(clause.dthService) {
            query += ` AND service = 'dth' AND validity_expiry_date >= ? and validity_expiry_date < ?`;
        } else {
            query += ` AND service in ('mobile' ,'datacard') AND validity_expiry_date >= ? and validity_expiry_date < ? `;
        }
            
        queryParams.push(startTime);
        queryParams.push(endTime);

        L.verbose('planValidity::getAllElgibleIDs',  self.dbInstance.format(query , queryParams));
        let latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getAllElgibleIDs', 'SOURCE': 'PLAN_VALIDITY'
            });
            if (error || !(data)) {
                L.critical('getAllElgibleIDs', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
    }

    /**
     * 
     * @param {*} done return  response 
     * @param {*} id table id
     * @param {*} mobile user monile number to be updated
     * @param {*} email user email to be updated
     * @param {*} rn_customer_id recharge number paytm customer id to be updated
     */
    updateUserData(done, id, mobile, email, rn_customer_id) {
        let self = this,
            query = `update plan_validity set cust_mobile=?, cust_email=?,rn_customer_id=? WHERE id=?`;
        L.log('updating user data for the id: ' + id);
        L.log("Mobile:" + mobile + "  email_id :" + email + " rn_customer_id :" + rn_customer_id);

        self.dbInstance.exec(function (error, data) {
            if (error || !(data)) {
                L.critical('updateUserData', 'error in updating user data: ', error);
            }
            return done();
        }, 'RECHARGE_ANALYTICS', query, [
            mobile,
            email,
            rn_customer_id,
            id
        ]);
    }

    deletePlan(params, cb) {
        let self = this,
            query = 'DELETE FROM plan_validity \
                     WHERE \
                     recharge_number = ? AND amount = ?',
            queryParams = [_.get(params, 'recharge_number', 0), _.get(params, 'amount', 0)];
        self.dbInstance.exec(function (err, result) {
            if (err) {
                L.critical("deleteFromPlanValidity", "Error occurred while deleting record ", err);
                cb(err);
            } else {
                L.log("deleteFromPlanValidity", "Deleted successfully");
                cb();
            }
        }, 'RECHARGE_ANALYTICS', query, queryParams);
    }

    getValidity(done, rechargeNumbers) {
        let self = this,
            query = 'SELECT recharge_number,operator,service,validity_expiry_date FROM plan_validity WHERE recharge_number in (?)',
            queryParams = [rechargeNumbers];

        L.verbose('getValidity',self.dbInstance.format(query,queryParams)); 
        L.log('getValidity','Fetching validity from plan_validity...');

        self.dbInstance.exec(function (err, result) {
            if(err && err.code && err.code == 'ER_LOCK_DEADLOCK'){
                // Retrying query again in case of deadlock
                L.error('getValidity','Deadlock detected...for',rechargeNumbers);
                self.dbInstance.exec(function (err, result) {
                    if (err) {
                        L.critical("getValidity-retry", "Error occurred while fetching record ", err);
                        return done(err);
                    } else {
                        L.log("getValidity-retry", "Data fetched...");
                        return done(null,result);
                    }
                }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);            
            }
            else if (err) {
                L.error("getValidity", "Error occurred while fetching record ", err);
                return done(err);
            } else {
                L.log("getValidity", "Data fetched...");
                return done(null,result);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
    }


    // async deletePlanByBucket(params) {
    //     let self = this,
    //         service = _.get(this.config,['CVR_DATA',data.productID,'service'],'').toLowerCase(),
    //         paytype = _.get(self.config, ['CVR_DATA', data.productID, 'paytype'], '').toLowerCase();
        
    //     let encryptedParams = _.cloneDeep(params);
    //     try {
    //         encryptedParams.rechargeNumber = self.encDecpUtil.encryptData(encryptedParams.rechargeNumber);
    //         let response = await self.deletePlanByBucketDefault(encryptedParams);
    //         if(response.affectedRows && response.affectedRows > 0) {
    //             return response;
    //         }
    //         return await self.deletePlanByBucketDefault(params);
    //     } catch(err) {
    //         return err;
    //     }
    // }
    /**
     * 
     * @param {*} params contains required fields for deleting from plan_validity
     */
    deletePlanByBucket(params) {
        const self = this,
            query = 'DELETE FROM plan_validity WHERE recharge_number = ? AND operator = ? AND customer_id = ? AND plan_bucket = ? AND service = ?',
            queryParams = [
                params.rechargeNumber,
                params.operator,
                params.customerID,
                params.planBucket,
                params.service
            ];
        L.log('deletePlanByBucket','query',self.dbInstance.format(query,queryParams));
        return new Promise((resolve, reject) => {
            if(!params.planBucket) {
                return resolve("Invalid planBucket");
            }
            else
            {
                self.dbInstance.exec((error, data) => {
                    if (error) {
                        L.critical("deleteFromPlanValidity", "Error occurred while deleting record ", error);
                        reject(error);
                    }
                    L.log('deletePlanByBucket :: response from db:',JSON.stringify(data));
                    resolve(data);
                }, 'RECHARGE_ANALYTICS', query, queryParams);
            }
        });
    }

    getValidityBykey(done, param) {
        let self = this,
            query = 'SELECT * FROM plan_validity WHERE recharge_number = ? and operator = ? and plan_bucket = ?',
            queryParams = [param.rechargeNumber,param.operator,param.planBucket];

        L.verbose('getValidityById',self.dbInstance.format(query,queryParams)); 
        L.log('getValidityById','Fetching validity from plan_validity...');

        self.dbInstance.exec(function (err, result) {
            if(err && err.code && err.code == 'ER_LOCK_DEADLOCK'){
                // Retrying query again in case of deadlock
                L.error('getValidityById','Deadlock detected...for', queryParams);
                self.dbInstance.exec(function (err, result) {
                    if (err) {
                        L.critical("getValidityById-retry", "Error occurred while fetching record ", err);
                        return done(err);
                    } else {
                        L.log("getValidityById-retry", "Data fetched...");
                        return done(null,result);
                    }
                }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);            
            }
            else if (err) {
                L.error("getValidityById", "Error occurred while fetching record ", err);
                return done(err);
            } else {
                L.log("getValidityById", "Data fetched...");
                return done(null,result);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
    }
}

export default PlanValidity
