import VALIDATOR from 'validator'
import MOMENT    from 'moment'
import _         from 'lodash'
import utility from '../lib'

let L = null;

/*
   Old Bill reminder service for distrbuting the request to the service files.

 */
class OldBills {
   
    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
    }

    /*
	*	Function will get data on the basis of customer_id,
	*	product_id and recharge_number
    */
   	getBill(cb, operator, customerId, productId, rechargeNumber ) {

      let self = this,
          query = `
                  SELECT
                    id,
                    cust_id as customerId,
                    recharge_number as rechargeNumber,
                    product_id as productId,
                    operator,
                    amount,
                    due_date as dueDate,
                    bill_fetch_date as billFetchDate,
                    nextBillFetchDate,
                    currentGw as gateway,
                    paytype,
                    service,
                    circle,
                    cust_mobile as customerMobile,
                    cust_email as customerEmail,
                    payment_channel as paymentChannel,
                    created_at as createdAt
                  FROM
                    bills
                  WHERE
                    cust_id = ?
                  AND
                    product_id = ?
                  AND
                    recharge_number = ?
                    `;
      var latencyStart = new Date().getTime();
   		self.dbInstance.exec(function (err, data){
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBill'});
            if(err || !(data)) {
              utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBill`]);
              L.error('getBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err,data);

        }, 'RECHARGE_ANALYTICS', query, [
            customerId,
            productId,
            rechargeNumber
        ]);
        	
    }

    /*
  * Function will get data on the basis of customer_id,
  * product_id and recharge numbers
    */
    getMultipleBill(cb, customerIds, productIds, rechargeNumbers) {

      let self = this,
          query = `
                  SELECT
                    id,
                    cust_id as customerId,
                    recharge_number as rechargeNumber,
                    product_id as productId,
                    operator,
                    amount,
                    due_date as dueDate,
                    bill_fetch_date as billFetchDate,
                    nextBillFetchDate,
                    currentGw as gateway,
                    paytype,
                    service,
                    circle,
                    cust_mobile as customerMobile,
                    cust_email as customerEmail,
                    payment_channel as paymentChannel,
                    created_at as createdAt
                  FROM
                    bills
                  WHERE
                    cust_id in (?)
                  AND
                    product_id in (?)
                  AND
                    recharge_number in (?)
                    `;
      var latencyStart = new Date().getTime();
      self.dbInstance.exec(function (err, data){
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getMultipleBill'});
            if(err || !(data)) {
              utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getMultipleBill`]);
              L.error('getMultipleBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err,data);

        }, 'RECHARGE_ANALYTICS', query, [
            customerIds,
            productIds,
            rechargeNumbers
        ]);

    }

   	/*
	*	Function will create data in Bills Tables on the basis of customer_id,
	*	product_id and recharge_number as Key
    */
   	createBill(cb, params) {

   		let self=this,
          query = `INSERT INTO 
                    bills 
                    (
                      cust_id,
                      recharge_number,
                      product_id,
                      operator,
                      currentGw,
                      nextBillFetchDate,
                      service,
                      paytype,
                      circle,
                      cust_mobile,
                      cust_email,
                      payment_channel,
                      amount
                    )
                 VALUES 
                    ? 
                 ON DUPLICATE KEY UPDATE 
                    amount=(amount + VALUES(amount)),
                    cust_email=VALUES(cust_email),
                    cust_mobile=VALUES(cust_mobile),
                    operator=VALUES(operator),
                    currentGw=VALUES(currentGw),
                    service=VALUES(service),
                    paytype=VALUES(paytype),
                    circle=VALUES(circle),
                    payment_channel=VALUES(payment_channel)`;
      var latencyStart = new Date().getTime();
      self.dbInstance.exec(function (err, data){
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'createBill'});
            if(err || !(data)) {
              utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createBill`]);
              L.error('getBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err,data);

        }, 'RECHARGE_ANALYTICS', query, [[[
          params.customerId,
          params.rechargeNumber,
          params.productId,
          params.operator,
          params.gateway,
          params.nextBillFetchDate,
          params.service,
          params.paytype,
          params.circle, 
          params.customerMobile, 
          params.customerEmail, 
          params.paymentChannel,
          _.get(params,'amount',0)]]
          ]
      );

   	}
    
    
    /*
       Function to update bills table in recharge_analytics DB
     */
    updateBill(cb, params) {

      let self  = this,
          query =  `UPDATE 
                        bills 
                    SET 
                        nextBillFetchDate     = ?,
                        bill_fetch_date       = ?,
                        bill_fetch_start_time = ?,
                        amount                = ?, 
                        due_date              = ?
                    WHERE 
                        recharge_number       = ? 
                    AND 
                        product_id            = ?`;

      //Update the record with appropriate values
      var latencyStart = new Date().getTime();
      self.dbInstance.exec(function (err, data){
           utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateBill'});
           if(err || !(data)) {
              utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBill`]);
              L.error('old updateBill::', 'error occurred while dumping data in DB: ', err);
           }
           else {
              //Useful logging
              L.log(`Old Bills updated:: [recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, amount: ${params.amount}, billDueDate: ${params.billDueDate}, nextBillFetchDate: ${params.nextBillFetchDate}]`)
           }
           cb(err,data);

       }, 'RECHARGE_ANALYTICS', query, [
           params.nextBillFetchDate,
           params.billFetchDate,
           params.billFetchStartTime, 
           params.amount,  
           params.billDueDate, 
           params.rechargeNumber,
           params.productId
       ]); 
    }
}

export default OldBills