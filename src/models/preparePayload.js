import MOMENT from 'moment';
import _ from 'lodash'

class preparePayload {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
    }

    preparePayloadForNonPaytmBillsConsumer(payload, source) {
        let self = this;
        let finalPayload = {
            customerId: _.get(payload, 'customerId', null),
            rechargeNumber: _.get(payload, 'rechargeNumber', null),
            productId: _.get(payload, 'productId', null),
            operator: _.get(payload, 'operator', null),
            service: _.get(payload, 'service', null),
            amount: _.get(payload, 'amount', _.get(payload, 'currentBillAmount', null)),
            dueDate: MOMENT(payload.billDueDate).isValid() ? MOMENT(payload.billDueDate).format('YYYY-MM-DD HH:mm:ss') : null,
            billDate: _.get(payload, 'billDate', MOMENT().format('YYYY-MM-DD HH:mm:ss')),
            billFetchDate: _.get(payload, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss')),
            paytype: _.get(payload, 'paytype', null),
            circle: _.get(payload, 'circle', null),
            customer_mobile: _.get(payload, 'customer_mobile', _.get(payload, 'customerMobile', null)),
            customer_email: _.get(payload, 'customer_email', _.get(payload, 'customerEmail', null)),
            status: _.get(self.config, ['COMMON', 'bills_status', 'BILL_FETCHED'], 4),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            customerOtherInfo: _.get(payload, ['billsData', 'customerOtherInfo'], '{}'),
            extra: _.get(payload, ['billsData', 'extra'], '{}'),
            recordFoundOfSameCustId: _.get(payload, 'recordFoundOfSameCustId', null),
            source: 'UPMS',
            dbEvent: "upsert",
            isDuplicateCANumberOperator: _.get(payload, 'isDuplicateCANumberOperator', false),
            alternateRechargeNumber: _.get(payload, 'alternateRechargeNumber', null),
            alternateCAExistsInDB: _.get(payload, 'alternateCAExistsInDB', false),
            reason: _.get(payload, 'reason', null)
        };

        return finalPayload;
    }
}

export default preparePayload