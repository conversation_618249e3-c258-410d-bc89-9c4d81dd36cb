import ENCDECPUTIL from '../lib/EncryptionDecryptioinHelper'
import MOMENT from 'moment'
import _ from 'lodash'
import utility from '../lib'
import ASYNC from 'async'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'
import recentBillLibrary from '../lib/recentBills';
import Logger from '../lib/logger'

let L = null;

class Bills {
    constructor(options) {
        L = options.L;
        this.encDecpUtil = new ENCDECPUTIL(options);
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.nonruClient = options.nonRuCassandraDbClient;
        this.EncryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());
        this.oldBillFetchDueDateAllowedService = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], ['electricity']);
        this.isEncryptionEnabled = _.get(this.config, ['DYNAMIC_CONFIG', 'ENCRYPTION_CONFIG', 'VALIDATION_SYNC', 'ENABLED'], false);
        this.uatEnabledCustomers = _.get(this.config, ['DYNAMIC_CONFIG', 'ENCRYPTION_CONFIG', 'VALIDATION_SYNC', 'UAT_ENABLED_CUSTOMERS'], []);
        this.cc_service = 'financial services';
        this.keysToEncryptForUserData = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 'recharge_number_6']);
        this.keysToEncryptForCustomerOtherInfo = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey']);
        this.keysToEncryptForCustomerExtra = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey']);
        this.demergerOperatorsListBaja = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'DEMERGER_OPERATOR', "Baja Finance Loan"], null);


        this.initializeVariable();
        
        this.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.logger = new Logger(options);
        this.commonLib = new utility.commonLib(options);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable() {
        this.L.verbose("Reinitializing variables")
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], ['electricity']);
        this.oldBillFetchDueDateAllowedService = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.keysToEncryptForUserData = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 'recharge_number_6']);
        this.keysToEncryptForCustomerOtherInfo = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'current_outstanding_amount', 'currentOutstandingAmount']);
        this.keysToEncryptForCustomerExtra = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount', 'lastDueDt', 'lastAmount']);
        this.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
    }

    getBill(cb, tableName, operator, customerId, productId, rechargeNumber) {
        let self = this,
            service = _.get(self.config, ['CVR_DATA', productId, 'service'], '').toLowerCase(),
            payType = _.get(self.config, ['CVR_DATA', productId, 'paytype'], '').toLowerCase();
        
        if(self.encDecpUtil.isWhitelistedForCC(service, payType, parseInt(customerId))) {
            let encryptedRechargeNumber = rechargeNumber;
            if(typeof encryptedRechargeNumber != 'string') encryptedRechargeNumber = self.encDecpUtil.parseToString(encryptedRechargeNumber);
            encryptedRechargeNumber = self.encDecpUtil.encryptData(encryptedRechargeNumber);
            self.getBillDefault((error, data) => {
                if(error) {
                    return cb(error, data);
                } else if(data && data.length == 0) {
                    return self.getBillDefault(cb, tableName, operator, customerId, productId, rechargeNumber);
                }
                data = self.encDecpUtil.parseDbResponse(data, customerId);
                return cb(error, data);
            }, tableName, operator, customerId, productId, encryptedRechargeNumber);
        } else {
            self.getBillDefault(cb, tableName, operator, customerId, productId, rechargeNumber);
        }
    }

    getBillDefault(cb, tableName, operator, customerId, productId, rechargeNumber) {
        let
            self = this,
            service = _.get(self.config, ['CVR_DATA', productId, 'service'], ''),
            operatorFromConfigs = _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator]));

        let query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`,
            queryParams = [
                customerId,
                _.toLower(operator),
                _.toLower(service),
                rechargeNumber
            ];

        L.log('getBill','query',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBill`]);
                L.critical('getBill::', 'error occurred while getting data from DB: ', err);
            } else if (data && data.length > 0) {
                _.set(data, '0.gateway', operatorFromConfigs || _.get(data, '0.gateway', null));
            }
            L.log('getBill :: response from DB', data);
            cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getCCBill(cb, tableName, operator, customerId, productId, referenceId) {
        let self = this,
            service = _.get(self.config, ['CVR_DATA', productId, 'service'], '').toLowerCase(),
            payType = _.get(self.config, ['CVR_DATA', productId, 'paytype'], '').toLowerCase();
        
        if(self.encDecpUtil.isWhitelistedForCC(service, payType, parseInt(customerId))) {
            let encryptedRefId = referenceId;
            if(typeof encryptedRefId != 'string') encryptedRefId = self.encDecpUtil.parseToString(encryptedRefId);
            encryptedRefId = self.encDecpUtil.encryptData(encryptedRefId);
            self.getCCBillDefault((error, data) => {
                if(error) {
                    return cb(error, data);
                } else if(data && data.length == 0) {
                    return self.getCCBillDefault(cb, tableName, operator, customerId, productId, referenceId);
                }
                data = self.encDecpUtil.parseDbResponse(data, customerId);
                return cb(error, data);
            }, tableName, operator, customerId, productId, encryptedRefId);
        } else {
            self.getCCBillDefault(cb, tableName, operator, customerId, productId, referenceId);
        }
    }

    getCCBillDefault(cb, tableName, operator, customerId, productId, referenceId) {
        let
            self = this,
            service = _.get(self.config, ['CVR_DATA', productId, 'service'], ''),
            operatorFromConfigs = _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator]));

        let query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND reference_id = ?`,
            queryParams = [
                customerId,
                _.toLower(operator),
                _.toLower(service),
                referenceId
            ];

        //as request is already logged in controller, so not query here again
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getCCBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getCCBill`]);
                L.critical('getCCBill::', 'error occurred while getting data from DB: ', err);
            } else if (data && data.length > 0) {
                _.set(data, '0.gateway', operatorFromConfigs || _.get(data, '0.gateway', null));
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getBillByCustomer(cb, tableName, customerId) {
        let
            self = this;
        let query = `SELECT * FROM ${tableName} WHERE customer_id = ?`,
            queryParams = [
                customerId
            ];

        L.verbose('getBillByCustomer','query',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillByCustomer'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillByCustomer`]);
                return cb(err, null);
            }else{
                try{
                    //self.L.log('getBillByCustomerWithEncryptedData: database row as it is', 'data', data);
                    let decryptedData = self.EncryptionDecryptioinHelper.parseDbResponse(data, customerId);
                    //self.L.log('getBillByCustomerWithEncryptedData: decrypted data', 'data', decryptedData);
                    return cb(err, decryptedData);
                }catch(e){
                    self.L.error(`Error in parsing data for customer_id: ${customerId} : `, e);
                    return cb(e, null);
                }
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }


    updateCCBillByCustomerId(cb, tableName, params){
        let self=this; //decrypted
        self.setReconIdAndReferenceID(params);
        let queryWithEncryptedParams = false

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(_.get(params, 'service', ''), _.get(params, 'paytype', ''), _.get(params, 'customer_id', null))){
            if(_.get(params, 'is_encrypted', 0) == 1){
                let paramsAtRootLevel = ['recharge_number','reference_id','customer_mobile', 'customer_email']
                let encrypted_params = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams({
                        ...params,
                        customerId:params.customer_id
                    }, paramsAtRootLevel, self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
                if(_.get(params, 'isPartial', false)){
                    _.set(encrypted_params, 'enc_amount', null);
                }
                queryWithEncryptedParams = true;
                return self.updateExistingCCBillByCustomerId(cb, tableName, encrypted_params, queryWithEncryptedParams);
            }else{
                return self.createAndDeleteCCBill(cb, tableName, params);
            }
        }else{
            return this.updateExistingCCBillByCustomerId(cb, tableName, params, queryWithEncryptedParams);
        }
    }
    createAndDeleteCCBill(cb, tableName, params, returnNewlyCreatedRecord = false){
        let self=this,
            newlyInsertedRecord = null;

        ASYNC.waterfall([
            next=>{
                let paramsAtRootLevel = ['recharge_number','reference_id','customer_mobile', 'customer_email']
                let encrypted_params = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams({
                        ...params,
                        customerId:params.customer_id
                    }, paramsAtRootLevel, self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
                self.createCCBillForCustomerIdWithEncDecHandling(next, tableName, encrypted_params, true, returnNewlyCreatedRecord);
            },
            (insertResp, next) => {
                if(returnNewlyCreatedRecord == true) {
                    newlyInsertedRecord = insertResp;
                } else {
                    next = insertResp;
                }
                self.deleteDuplicateCards(next, tableName, params);
            }
        ],
        function(err, result){
            if(err){
                return cb(err, null);
            }else{
                if(returnNewlyCreatedRecord == true) return cb(null, newlyInsertedRecord);
                return cb(null, result);
            }
        });
    }

    setReconIdAndReferenceID(params){
        let self=this;
        let recon_id = utility.generateReconID(_.get(params,'recharge_number',''),params.bank_name , params.amount , params.due_date , params.bill_date)
        params.extra = utility.setReconIdInString(params.extra , recon_id);
        if(params.par_id && params.par_id !== ''){
            params.reference_id = params.par_id;
        }
    }

    updateExistingCCBillByCustomerId(cb, tableName, params, queryWithEncryptedParams){
        let
            self = this,
            encryptedQueryClause = `enc_amount = ?, enc_due_date = ?`,
            decryptedQueryClause = `amount = ?, due_date = ? `,
            nextBillFetchDateClaue = `next_bill_fetch_date=?`;

            let query = `UPDATE ${tableName} SET bill_date = ?, status = ?, bill_fetch_date = ?, customerOtherInfo = ?, extra = ?, is_encrypted = ?, ${queryWithEncryptedParams ? encryptedQueryClause : decryptedQueryClause} ${params.next_bill_fetch_date ? `, ${nextBillFetchDateClaue}` : ''} WHERE id = ?`,
            queryParams = [
                params.bill_date,
                params.status,
                params.bill_fetch_date,
                params.customerOtherInfo,
                params.extra,
                _.get(params, 'is_encrypted', 0),
                queryWithEncryptedParams ? params.enc_amount : params.amount,
                queryWithEncryptedParams ? params.enc_due_date : params.due_date,
                params.next_bill_fetch_date,
                params.id
            ];

            // query = `UPDATE ${tableName} SET amount = ?,bill_date = ?,due_date = ?,status = ?,bill_fetch_date = ?,customerOtherInfo = ?,extra = ? WHERE id = ?`,
            // queryParams = [
            //     params.amount,
            //     params.bill_date,
            //     params.due_date,
            //     params.status,
            //     params.bill_fetch_date,
            //     params.customerOtherInfo,
            //     params.extra,
            //     params.id
            // ];
            // if(params.next_bill_fetch_date && MOMENT(params.next_bill_fetch_date,"YYYY-MM-DD HH:mm:ss").isValid()){
            //     query = `UPDATE ${tableName} SET amount = ?,bill_date = ?,due_date = ?,status = ?,bill_fetch_date = ?,customerOtherInfo = ?,extra = ?,next_bill_fetch_date=? WHERE id = ?`,
            //     queryParams = [
            //         params.amount,
            //         params.bill_date,
            //         params.due_date,
            //         params.status,
            //         params.bill_fetch_date,
            //         params.customerOtherInfo,
            //         params.extra,
            //         params.next_bill_fetch_date,
            //         params.id
            //     ];
            // }

        L.log('updateCCBillByCustomerId', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateCCBillByCustomerId'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateCCBillByCustomerId`]);
                L.critical('updateCCBillByCustomerId::', 'error occurred : ', err);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    updateCCBBPSBillById(cb, tableName, params) {
                
        let recon_id = utility.generateReconID(_.get(params,'recharge_number',''),params.bank_name , params.amount , params.dueDate , params.billDate)

        params.extra = utility.setReconIdInString(params.extra , recon_id)

        let recordToInsert = _.cloneDeep(params);

        if(this.EncryptionDecryptioinHelper.isWhitelistedForCC("financial services","credit card", params.customer_id)) {
            recordToInsert.customerMobile = params.customerMobile ? this.EncryptionDecryptioinHelper.encryptData(params.customerMobile) : null;
            recordToInsert.enc_amount = this.EncryptionDecryptioinHelper.encryptData(_.toString(_.get(params, 'amount', 0)));
            recordToInsert.enc_due_date = this.EncryptionDecryptioinHelper.encryptData(_.get(params, 'dueDate', null)); 
            recordToInsert.amount = null;
            recordToInsert.dueDate = null;
            recordToInsert.recharge_number = this.EncryptionDecryptioinHelper.encryptData( _.get(params, 'recharge_number',null));
            _.set(recordToInsert, 'user_data', this.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'user_data', {}), this.keysToEncryptForUserData)); 
            _.set(recordToInsert, 'customerOtherInfo', this.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'customerOtherInfo', {}), this.keysToEncryptForCustomerOtherInfo));
            _.set(recordToInsert, 'extra', this.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'extra', {}), this.keysToEncryptForCustomerExtra));
            recordToInsert.is_encrypted = 1;
        }   
        
        let
            self = this,
            query = `UPDATE ${tableName} SET amount = ?,due_date = ?,user_data = ?,next_bill_fetch_date = ?,customer_mobile = ?, circle = ? ,customerOtherInfo = ? ,extra = ? ,status = ?, bill_date = ?, bill_fetch_date = ?, enc_amount = ?, enc_due_date = ?, is_encrypted = ? WHERE id = ?`,
            queryParams = [
                recordToInsert.amount,
                recordToInsert.dueDate,
                recordToInsert.user_data,
                recordToInsert.nextBillFetchDate,
                recordToInsert.customerMobile,
                'bbps',
                recordToInsert.customerOtherInfo,
                recordToInsert.extra,
                recordToInsert.status,
                recordToInsert.billDate,
                recordToInsert.billFetchDate,
                recordToInsert.enc_amount,
                recordToInsert.enc_due_date,
                _.get(recordToInsert, 'is_encrypted', 0),
                recordToInsert.id
                
            ];

        L.log('updateCCBBPSBillById', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateCCBBPSBillById'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateCCBBPSBillById`]);
                L.critical('updateCCBBPSBillById::', 'error occurred : ', err);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    updateConsentById(tableName, params){
        const self = this;
        let query, queryParams;
        if (params.nextBillFetchDate){
            query = `UPDATE ${tableName} SET  consent_valid_till = ?, next_bill_fetch_date = ? WHERE id = ?`;
            queryParams = [params.consentValidTill, params.nextBillFetchDate, params.id ];
        }else{
            query = `UPDATE ${tableName} SET consent_valid_till = ? WHERE id = ?`;
            queryParams = [params.consentValidTill, params.id ];
        }
        L.log('updateConsentById', 'query', self.dbInstance.format(query, queryParams));
        let latencyStart = new Date().getTime();
        return new Promise((resolve, reject) => {
            self.dbInstance.exec(function(err, data){
                utility._sendLatencyToDD(latencyStart, {'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateConsentById'});
                if (err){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateCCconsentById`]);
                    L.critical('updateCCBBPSBillById::', 'error occurred : ', err);
                    return reject(err);
                }
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:SUCCESS', `TYPE:updateCCconsentById`]);
                return resolve();
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        });
    }

    deleteDuplicateCards(cb, tableName, params){
        let
            self = this,
            query = `DELETE FROM ${tableName} WHERE id = ?`,
            queryParams = [
                params.id
            ];

            if(_.get(params, 'id', null)){
                L.log('deleteDuplicateCards', 'query', self.dbInstance.format(query, queryParams));
                var latencyStart = new Date().getTime();
                self.dbInstance.exec(function (err, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'deleteDuplicateCards'});
                    if(err){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:deleteDuplicateCards`]);
                        L.critical('deleteDuplicateCards::', 'error occurred : ', err);
                    }
                    return cb(err, data);
                }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
            }
            else return cb('Not received any valid unique id to delete record')
    }

    updateCCBillPaidByCustomerId(cb, tableName, params){
        let self=this; //decrypted
        self.setReconIdAndReferenceID(params);
        let queryWithEncryptedParams = false

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(_.get(params, 'service', ''), _.get(params, 'paytype', ''), _.get(params, 'customer_id', null))){
            if(_.get(params, 'is_encrypted', 0) == 1){
                let paramsAtRootLevel = ['recharge_number','reference_id','customer_mobile', 'customer_email']
                let encrypted_params = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams({
                        ...params,
                        customerId:params.customer_id
                    }, paramsAtRootLevel, self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
                queryWithEncryptedParams = true;
                return self.updateCCBillPaidByCustomerIdWithEncDecHandling(cb, tableName, encrypted_params, queryWithEncryptedParams);
            }else{
                return self.createAndDeleteCCBill(cb, tableName, params);
            }
        }else{
            return this.updateCCBillPaidByCustomerIdWithEncDecHandling(cb, tableName, params, queryWithEncryptedParams);
        }
    }


    updateCCBillPaidByCustomerIdWithEncDecHandling(cb, tableName, params, queryWithEncryptedParams){
        let
            self = this,

            query = `UPDATE ${tableName} SET ${queryWithEncryptedParams? 'enc_amount':'amount'}= ?,status = ?,customerOtherInfo = ?,payment_date = ?, extra = ? WHERE id = ?`,
            queryParams = [
                queryWithEncryptedParams? params.enc_amount : params.amount,
                params.status,
                params.customerOtherInfo,
                params.payment_date,
                params.extra,
                params.id
            ];

        L.log('updateCCBillPaidByCustomerId', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateCCBillPaidByCustomerId'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateCCBillPaidByCustomerId`]);
                L.critical('updateCCBillPaidByCustomerId::', 'error occurred : ', err);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    async getNotificationPrepaidRecords(cb, tableName, key, value) {
        let self = this;
        let query = `SELECT customer_id, recharge_number, product_id, operator, notification_status  from ${tableName} where ${key} = '${value}'`;
        var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (err, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getNotificationPrepaidRecords'});
                if (err || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getNotificationPrepaidRecords`]);
                    self.L.critical('bills::getNotificationPrepaidRecords::', 'error occurred while getting data from DB: ', err);
                }
                cb(err, data);
            }, 'RECHARGE_ANALYTICS_SLAVE', query, []);
    }

    getNotificationCCRecords(cb, tableName, key, value) {
        let self = this;
        let query = `SELECT customer_id, recharge_number, product_id, reference_id, operator, notification_status  from ${tableName} where ${key} = '${value}'`;
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getNotificationCCRecords'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getNotificationCCRecords`]);
                L.critical('bills::getNotificationCCRecords::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, []);
    }

    getNotificationRecords(cb, tableName, key, value) {
        let self = this;
        let query = `SELECT customer_id, recharge_number, product_id, operator, notification_status  from ${tableName} where ${key} = '${value}'`;
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getNotificationRecords'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getNotificationRecords`]);
                L.critical('bills::getNotificationRecords::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, []);
    }

    getBillsRecord(cb, tableName, operator, service, rechargeNumber, customerId, referenceId, paytype) {
        let self = this;
        if(service!=null) service = service.toLowerCase();
        if(paytype!=null) paytype = paytype.toLowerCase();
        if(self.encDecpUtil.isWhitelistedForCC(service, paytype, parseInt(customerId))) {
            let encryptedreferenceId = referenceId;
            if(typeof encryptedreferenceId != 'string') encryptedreferenceId = self.encDecpUtil.parseToString(encryptedreferenceId);
            encryptedreferenceId = self.encDecpUtil.encryptData(encryptedreferenceId);
            self.getBillsRecordDefault((error, data) => {
                if(error) {
                    return cb(error, data);
                } else if(data && data.length == 0) {
                    return self.getBillsRecordDefault(cb, tableName, operator, service, rechargeNumber, customerId, referenceId);
                }
                data = self.encDecpUtil.parseDbResponse(data, customerId);
                return cb(error, data);
            }, tableName, operator, service, rechargeNumber, customerId, encryptedreferenceId);
        } else {
            self.getBillsRecordDefault(cb, tableName, operator, service, rechargeNumber, customerId, referenceId);
        }
    }

    handlePrepaidRecordAPI(query, queryParams, prepaidQuery, prepaidQueryParams, params, prepaidTableName, done) {
        let self = this;
        var latencyStart = new Date().getTime();
        L.log(`handlePrepaidRecordAPI, query - ${query} queryParams - ${queryParams} \n \n prepaidQuery - ${prepaidQuery} prepaidQueryParams - ${prepaidQueryParams}`);
        ASYNC.parallel([
            (callback) => {
                self.dbInstance.exec(function (err, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsRecord' });
                    if (err || !(data)) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsRecord`]);
                        L.critical('getBillsRecord::', 'error occurred while getting data from DB: ', err);
                    }
                    return callback(err, data);
                }, 'DIGITAL_REMINDER_SLAVE', prepaidQuery, prepaidQueryParams);
            },
            (callback) => {
                self.dbInstance.exec(function (err, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsRecord' });
                    if (err || !(data)) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsRecord`]);
                        L.critical('getBillsRecord::', 'error occurred while getting data from DB: ', err);
                    }
                    callback(err, data);
                }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
            }
        ], (err, results) => {
            if (results.length > 1) {
                let prepaidResults = results[0];
                if (prepaidResults && prepaidResults.length > 0) {
                    L.log(`handlePrepaidRecordAPI, prepaidResults.length - ${prepaidResults.length}`);
                    return done(err, prepaidResults);
                }
                else
                    return done(err, results[1]);
            }
            else {
                return done(err, results[0]);
            }
        });
    }

    getBillsRecordDefault(cb, tableName, operator, service, rechargeNumber, customerId, referenceId) {
        if (tableName == null || tableName == undefined || tableName == '') {
            return cb(new Error('Table name is missing'));
        }
        let
            self = this,
            query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`,
            queryParams = [
                customerId,
                _.toLower(operator),
                _.toLower(service),
                rechargeNumber
            ];
        if (service == "financial services" && referenceId) {
            query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND reference_id = ?`;
            queryParams = [
                customerId,
                referenceId
            ];
        }

        var latencyStart = new Date().getTime();
        if (self.lowBalancePrepaidElectricityAllowedOperators.includes(operator)) {
            let prepaidTableName = tableName + '_prepaid';
            let prepaidQuery = `SELECT * FROM ${prepaidTableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`;
            self.handlePrepaidRecordAPI(query, queryParams, prepaidQuery, queryParams, null, prepaidTableName, (err, results) => {
                return cb(err, results);
            })
        }
        else {
            self.dbInstance.exec(function (err, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsRecord' });
                if (err || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsRecord`]);
                    L.critical('getBillsRecord::', 'error occurred while getting data from DB: ', err);
                }
                cb(err, data);

            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        }
    }

    getBillsOfSameRech(cb, tableName, params) {
        let
            self = this,
            operator = _.get(params, 'operator'),
            service = _.get(params, 'service'),
            rechargeNumber = _.get(params, 'rechargeNumber') + '',
            query = ` SELECT * FROM ${tableName} WHERE  operator = ? AND service = ? AND recharge_number = ?`,
            queryParams = [
                _.toLower(operator),
                _.toLower(service),
                rechargeNumber
            ];
        let ignorePrepaidFlow = _.get(params, 'isPrepaid', 0) == 1 ? (_.get(params, 'is_automatic', 0) == 0 ? true : false) : false;
        

        if (params.isDuplicateCANumberOperator && params.alternateRechargeNumber) {
            [query, queryParams] = self.getQueryAndParamsForDuplicateCANumberOperator(tableName, params);
        }

        if(params.isDemergerCase) {
            query += ` AND customer_id = ?`;
            queryParams.push(_.get(params, 'customerId', null));
        }

        if (params.isCreditCardOperator) {
            if (params.referenceId) {
                query += ` AND reference_id = ?`;
                queryParams.push(params.referenceId);
            } else if (params.customerId && !ignorePrepaidFlow) {
                query += ` AND customer_id = ?`;
                queryParams.push(_.get(params, 'customerId', null));
            } else {
                return cb(new Error(`Can't get the records for CC bills.`), null);
            }
        }

        L.log('getBillsOfSameRech', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();

        if (self.lowBalancePrepaidElectricityAllowedOperators.includes(operator)) {
            let prepaidTableName = tableName.endsWith('_prepaid') ? tableName : tableName + '_prepaid';
            let prepaidQuery = `SELECT * FROM ${prepaidTableName} WHERE  operator = ? AND service = ? AND recharge_number = ?`;
            self.handlePrepaidRecordAPI(query, queryParams, prepaidQuery, queryParams, params, prepaidTableName, (err, results) => {
                return cb(err, results);
            })
        } else {
            self.dbInstance.exec(function (err, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsOfSameRech' });
                if (err || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsOfSameRech`]);
                    L.critical('getBillsOfSameRech::', 'error occurred while getting data from DB: ', err);
                }
                return cb(err, data);

            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        }
    }

    getQueryAndParamsForDuplicateCANumberOperator(tableName, params){
        let query = ` SELECT * FROM ${tableName} WHERE  operator = ? AND service = ? AND (recharge_number = ? OR recharge_number = ?)`,
        queryParams = [
            _.toLower(_.get(params, 'operator')),
            _.toLower(_.get(params, 'service')),
            _.get(params, 'rechargeNumber') + '',
            _.get(params, 'alternateRechargeNumber') + ''
        ];
        return [query, queryParams];
    }

     getBillByCustomerRechargeNumber(cb, tableName, params) {
        let
            self = this,
            operator = _.get(params, 'operator'),
            rechargeNumber = _.get(params, 'rechargeNumber') + '',
            customer_id = _.get(params, 'customerId'),
            query = ` SELECT * FROM ${tableName} WHERE  operator = ? AND recharge_number = ? AND customer_id = ?`,
            queryParams = [
                _.toLower(operator),
                rechargeNumber,
                customer_id
            ];

        L.verbose('getBillsOfSameRech', 'query', self.dbInstance.format(query, queryParams));
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.critical('getBillsOfSameRech::', 'error occurred while getting data from DB: ', err);
            }
            L.log("data from db {}",data);
            return cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }


    async getBillByRechNumberServiceOperator(tableName, params) {
        const self = this;
        const operator = _.get(params, 'operator');
        const service = _.get(params, 'service');
        const rechargeNumber = _.get(params, 'rechargeNumber');
        if (!operator || !service || !rechargeNumber) {
            self.L.critical(`getBillByRechNumberServiceOperator:: missing required parameters, operator - ${operator}, service - ${service}, rechargeNumber - ${rechargeNumber}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:DB_QUERY",
                'STATUS:ERROR',
                `TYPE:PARAMETER_MISSING`
            ]);
            return Promise.reject(new Error(errorMsg));
        }
        const query = `SELECT * FROM ${tableName} WHERE recharge_number = ? AND service = ? AND operator = ?`;
        const queryParams = [
            rechargeNumber,
            _.toLower(service),
            _.toLower(operator)
        ];

        const latencyStart = new Date().getTime();
        self.L.log(`getBillByRechNumberServiceOperator :: Fetching data for ${rechargeNumber}, queryParams: ${queryParams}`);

        return new Promise((resolve, reject) => {
            self.dbInstance.exec((err, data) => {
                if (err || !data) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:DB_QUERY",
                        'STATUS:ERROR',
                        `TYPE:FETCH_RECORD`
                    ]);
                    self.L.critical('getBillByRechNumberServiceOperator::', 'Error occurred while fetching data from DB:', err);
                    return reject(err);
                } else {
                    utility._sendLatencyToDD(latencyStart, {
                        'REQUEST_TYPE': 'DB_QUERY',
                        'TYPE': 'FETCH_RECORD',
                        'STATUS': 'SUCCESS'
                    });
                    self.L.log(`getBillByRechNumberServiceOperator:: Successfully fetched ${data.length} records.`);
                    return resolve(data);
                }
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        });
    }

    async getBillByCustIdRechNumberServiceOperator(tableName, params) {
        const self = this;
        const customerId = _.get(params, 'customerId');
        const operator = _.get(params, 'operator');
        const service = _.get(params, 'service');
        const rechargeNumber = _.get(params, 'rechargeNumber');
        if (!operator || !service || !rechargeNumber) {
            self.L.critical(`getBillByRechNumberServiceOperator:: missing required parameters, customerId - ${customerId}, operator - ${operator}, service - ${service}, rechargeNumber - ${rechargeNumber}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:DB_QUERY",
                'STATUS:ERROR',
                `TYPE:PARAMETER_MISSING`
            ]);
            return Promise.reject(new Error(errorMsg));
        }
        const query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND recharge_number = ? AND service = ? AND operator = ?`;
        const queryParams = [
            customerId,
            rechargeNumber,
            _.toLower(service),
            _.toLower(operator)
        ];

        const latencyStart = new Date().getTime();
        self.L.log(`getBillByRechNumberServiceOperator :: Fetching data for customerId - ${customerId}, ${rechargeNumber}, queryParams: ${queryParams}`);

        return new Promise((resolve, reject) => {
            self.dbInstance.exec((err, data) => {
                if (err || !data) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:DB_QUERY",
                        'STATUS:ERROR',
                        `TYPE:FETCH_RECORD`
                    ]);
                    self.L.critical('getBillByRechNumberServiceOperator::', 'Error occurred while fetching data from DB:', err);
                    return reject(err);
                } else {
                    utility._sendLatencyToDD(latencyStart, {
                        'REQUEST_TYPE': 'DB_QUERY',
                        'TYPE': 'FETCH_RECORD',
                        'STATUS': 'SUCCESS'
                    });
                    self.L.log(`getBillByRechNumberServiceOperator:: Successfully fetched ${data.length} records.`);
                    return resolve(data);
                }
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        });
    }

    getBillsOfSameCustId(cb, tableName, params){
        let
            self = this,
            operator = _.get(params, 'operator'),
            customerId = _.get(params, 'customerId'),
            query = ` SELECT * FROM ${tableName} WHERE  customer_id = ? AND operator = ?`,
            queryParams = [
                customerId,
                _.toLower(operator)
            ];

        self.L.verbose('getBillsOfSameRech', 'query', self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsOfSameCustId'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsOfSameCustId`]);
                L.critical('getBillsOfSameCustId::', 'error occurred while getting data from DB: ', err);
            }
            return cb(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }


    createBillForPaytmPostpaid(cb, tableName, params) {        
        let
            self = this,
            operator = params.operator,
            service_id = 0,
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            extra = _.get(params, 'extra', null);
        params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
        let is_automatic = null;
        if(_.get(params, 'is_automatic', null) !== null) {
            is_automatic = _.get(params, 'is_automatic', null)
        } else if (_.get(params, 'is_automatic_diffCustId', 0) == 0 || _.get(params, 'is_automatic_diffCustId', null) == null) {
            is_automatic = 0;
        } else if(_.get(params, 'is_automatic_diffCustId', 0) > 2) {
            is_automatic = 4;
        } else {
            is_automatic = 2;
        }
        _.set(params, 'is_automatic', is_automatic);
        let recon_id = utility.generateReconID(params.rechargeNumber,params.operator , params.amount ,  _.get(params, 'dueDate', null) ,  _.get(params, 'billDate', null))
        
        extra = utility.setReconIdInString(extra , recon_id)

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date,bill_fetch_date, bill_date,
                service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,extra,is_automatic,customerOtherInfo)                
                VALUES ?
                ON DUPLICATE KEY UPDATE
                amount = VALUES(amount),
                customer_email=VALUES(customer_email),
                customer_mobile=VALUES(customer_mobile),
                bill_fetch_date=VALUES(bill_fetch_date),
                operator=VALUES(operator),
                bill_date =VALUES(bill_date),
                service=VALUES(service),
                paytype=VALUES(paytype),
                circle=VALUES(circle),
                service_id = VALUES(service_id),
                payment_channel=VALUES(payment_channel),
                notification_status=VALUES(notification_status),
                retry_count=VALUES(retry_count),
                reason=VALUES(reason),
                extra=VALUES(extra),
                status=VALUES(status)
                ${(params.billGen && params.billGen==true) ? '' : ', payment_date = VALUES(payment_date)' }
                ${(params.gateway && !_.isEmpty(params.gateway)) ? ', gateway = VALUES(gateway)' : ''}
                ${(params.user_data && !_.isEmpty(params.user_data)) ? ', user_data = VALUES(user_data)' : ''}
                ,due_date = VALUES(due_date),
                next_bill_fetch_date=VALUES(next_bill_fetch_date)
                ${(params.is_automatic) ? ', is_automatic = "' + params.is_automatic + '"' : ''}
                ,customerOtherInfo = VALUES(customerOtherInfo)`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                params.billFetchDate,
                _.get(params, 'billDate', null),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                params.paymentChannel,
                params.amount,
                params.retryCount,
                (params.amount > 0)? 4:11,
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                _.get(params, 'dueDate', null),
                extra,
                params.is_automatic,
                params.customerOtherInfo
            ]]];
        L.log('createBill::executionQuery::',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createBill`]);
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }


    async getMultipleBill(cb, tableName, operator, customerIds, productIds, rechargeNumbers) {
        let self = this,
            encryptedRechargeNumbers = [],
            whiteListedCustomerIdsForCcEncryption = [],
            whiteListedProductIdsForCcEncryption = [];
        for(let i = 0; i < productIds.length; i++) {
            let service = _.get(self.config, ['CVR_DATA', productIds[i], 'service'], '').toLowerCase();
            let payType = _.get(self.config, ['CVR_DATA', productIds[i], 'paytype'], '').toLowerCase();
            for(let j=0; j < customerIds.length; j++) {
                if(self.encDecpUtil.isWhitelistedForCC(service, payType, parseInt(customerIds[j]))) {
                    whiteListedProductIdsForCcEncryption.push(productIds[i]);
                    whiteListedCustomerIdsForCcEncryption.push(customerIds[j]);
                }
            }
        }

        for(let i = 0; i < rechargeNumbers.length; i++) {
            let encryptedRechargeNumber = rechargeNumbers[i];
            if(typeof encryptedRechargeNumber != 'string') encryptedRechargeNumber = self.encDecpUtil.parseToString(encryptedRechargeNumber);
            encryptedRechargeNumber = self.encDecpUtil.encryptData(encryptedRechargeNumber);
            encryptedRechargeNumbers.push(encryptedRechargeNumber);
        }

        let response = [];
        if(whiteListedCustomerIdsForCcEncryption.length > 0) {

            self.getMultipleBillDefault((error, encData) => {
                if(encData.length > 0) {
                    for(let i = 0; i < encData.length; i++) {
                        let keysToUpdate = {
                            "rechargeNumber": "recharge_number",
                            "dueDate": "due_date",
                            "customerMobile": "customer_mobile",
                            "customerEmail": "customer_email"
                        };
                        let tempData = self.mapDBResponseToActualResponse(encData[i], keysToUpdate);
                        tempData = self.encDecpUtil.parseDbResponse([tempData], tempData.customerId);
                        keysToUpdate = {
                            "recharge_number": "rechargeNumber",
                            "due_date": "dueDate",
                            "customer_mobile": "customerMobile",
                            "customer_email": "customerEmail"
                        };
                        tempData = self.mapDBResponseToActualResponse(tempData[0], keysToUpdate);
                        delete tempData['is_encrypted'];
                        response.push(tempData);
                    }
                }
                let tables = _.get(self.config,['DYNAMIC_CONFIG','CCBP','ENCRYPTION_CONFIG','TABLES_CONTAINING_ENCRYPTION_DATA'],['bills_creditcard']),
                    excludeNotEncryptedCcNumbers = false;
                if(tables.includes(tableName)) excludeNotEncryptedCcNumbers = true;
                self.getMultipleBillDefault((error, data) => {
                    if(error) {
                        if(response.length == 0) {
                            return cb(error, response);
                        } else {
                            return cb(null, response);
                        }
                    }
                    for(let i = 0; i < data.length; i++) {
                        let tempData = self.mapDBResponseToActualResponse(data[i]);
                        tempData = self.mapActualResponseToDBResponse(self.encDecpUtil.parseDbResponse([tempData], tempData.customerId));
                        response.push(tempData[0]);
                    }
                    return cb(null, response);
                }, tableName, operator, customerIds, productIds, rechargeNumbers, false, excludeNotEncryptedCcNumbers);
            }, tableName, operator, whiteListedCustomerIdsForCcEncryption, whiteListedProductIdsForCcEncryption, encryptedRechargeNumbers, true);
        
        } else {
            self.getMultipleBillDefault(cb, tableName, operator, customerIds, productIds, rechargeNumbers);
        }

    }

    mapDBResponseToActualResponse(data, keysToUpdate) {
        let keys = Object.keys(data);
        for(let index = 0; index < keys.length; index++) {
            let key = keys[index];
            if(keysToUpdate[key]) {
                data[keysToUpdate[key]] = data[key];
                delete data[key]; 
            }
        }
        return data;
    }

    getMultipleBillDefault(cb, tableName, operator, customerIds, productIds, rechargeNumbers, fetchIsEncryptedColumn = false,excludeNotEncryptedCcNumbers = false) {
        let self = this,
            operatorFromConfigs = _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])),
            service = _.get(self.config, ['CVR_DATA', _.get(productIds, '0'), 'service'], '');

        //L.verbose('bills::getMultipleBill', 'Gateway from config ' + operatorFromConfigs);

        let query1 = ` SELECT id, customer_id as customerId, recharge_number as rechargeNumber, product_id as productId, operator, amount,
                due_date as dueDate, bill_fetch_date as billFetchDate, next_bill_fetch_date as nextBillFetchDate,
                ${operatorFromConfigs ? '\'' + operatorFromConfigs + '\'' : 'gateway'} as gateway,
                paytype, service, circle, customer_mobile as customerMobile, customer_email as customerEmail, payment_channel as paymentChannel,
                retry_count as retryCount, status, reason, created_at as createdAt, updated_at as updatedAt, customerOtherInfo as customerOtherInfo ,extra as extra `,
            query2 = `FROM ${tableName} WHERE customer_id in (?) AND operator = ? AND service = ? AND recharge_number in (?)`,
            queryParams = [
                customerIds,
                _.toLower(operator),
                _.toLower(service),
                rechargeNumbers
            ];
        let query;
        if(fetchIsEncryptedColumn == true) {
            query = query1 + `, is_encrypted ` + query2;
        } else {
            query = query1 + query2;
        }
        
        if(excludeNotEncryptedCcNumbers == true) {
            query = query + ` AND is_encrypted != 1`;
        } 

        //L.verbose('getMultipleBill','query',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getMultipleBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getMultipleBill`]);
                L.critical('getMultipleBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    createBill(cb, tableName, params, fromRecents = false, fromSource) {
        let
            self = this,
            operator = params.operator,
            extra = _.get(params, 'extra', null),
            customer_type = _.get(params, 'customer_type', null),
            bankName = _.get(params, 'bankName', null);

            params.service_id = 0,
            params.notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));
            params.fromRecents = fromRecents;
            params.fromSource = fromSource;
        
        let tempExtra = extra ? JSON.parse(extra) : {customer_type: customer_type };

        let amount=_.get(params , 'amount',0)
        if(_.get(params,'amountWithinDeviation', false) == true){
            self.L.log(`amountWithinDeviation is true for ${params.rechargeNumber}`)
            amount = 0
        }
        let recon_id = utility.generateReconID(_.get(params , 'rechargeNumber',_.get(params,'recharge_number','')), _.toLower(params.service) == "financial services" ? bankName : operator ,amount ,  _.get(params, 'dueDate', null) , _.get(params, 'billDate', null))

        _.set(tempExtra,'recon_id',recon_id)
        if(fromRecents){
            if(self.includedOperator.indexOf(_.toLower(operator)) > -1){
                _.set(tempExtra , 'source_subtype_2','FULL_BILL')
            }else{
                _.set(tempExtra , 'source_subtype_2',null);
            }
        }else{
            _.set(tempExtra , 'source_subtype_2','PARTIAL_BILL')
        }
        _.set(tempExtra , 'user_type',"RU")
        if (!tempExtra.created_source) tempExtra.created_source = fromRecents ? 'transaction' : _.get(params, 'source', null) == "api" ? "api" : 'validationSync';
        tempExtra.updated_source = fromRecents ? 'transaction' : _.get(params, 'source', null) == "api" ? "api" : 'validationSync';
        tempExtra.updated_data_source = fromRecents ? 'transaction' : _.get(params, 'source', null) == "api" ? "api" : 'validationSync';
        extra = JSON.stringify(tempExtra);

        // if(!extra) {
        //     extra = {};
        //     extra.customer_type = _.get(params, 'customerInfo_customer_type', null)
        //     extra = JSON.stringify(extra)
        // } else {
        //     let temp = JSON.parse(extra);
        //     temp.customer_type = customer_type ? customer_type : temp.customer_type;
        //     extra = JSON.stringify(temp);
        // }
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
        let is_automatic = null;
        if(_.get(params, 'is_automatic', null) !== null) {
            is_automatic = _.get(params, 'is_automatic', null)
        } else if (_.get(params, 'is_automatic_diffCustId', 0) == 0 || _.get(params, 'is_automatic_diffCustId', null) == null) {
            is_automatic = 0;
        } else if(_.get(params, 'is_automatic_diffCustId', 0) > 2) {
            is_automatic = 4;
        } else {
            is_automatic = 2;
        }
        _.set(params, 'is_automatic', is_automatic);
        if(_.toLower(params.service) == 'financial services' && typeof(params.customerOtherInfo) === 'string') {
            let customerOtherInfoNew;
            try {
                customerOtherInfoNew = JSON.parse(params.customerOtherInfo);
            } catch(error) {
                if(error) {
                    L.critical('createBill::', 'error occurred while parsing customerOtherInfo: ', error);
                }
            }

            if (_.get(customerOtherInfoNew, 'current_outstanding_amount', null)) {
                let currentOutstandingAmount = _.get(customerOtherInfoNew, 'current_outstanding_amount', 0) - Math.abs(params.amount);
                if (currentOutstandingAmount < 0) currentOutstandingAmount = 0;
                customerOtherInfoNew.current_outstanding_amount = currentOutstandingAmount;
                _.set(params, 'customerOtherInfo', JSON.stringify(customerOtherInfoNew));
            }
        }

        var daysToBeAddedWhenNBFDIsLessThanCurrentDay = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', 'NBFD_LESS_THAN_CURRENT_DAY'], 7);
        if(_.toLower(params.service) == 'financial services' && MOMENT(params.nextBillFetchDate) < MOMENT()) {
            params.nextBillFetchDate = MOMENT().add(daysToBeAddedWhenNBFDIsLessThanCurrentDay, 'days').format('YYYY-MM-DD HH:mm:ss');
        }

        return self.formatParamsAndexecuteCreateBillQuery(cb, tableName, params);
    }

    formatParamsAndexecuteCreateBillQuery(cb, tableName, params) {
        let self = this;
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(_.get(params, 'service', ''),_.get(params, 'paytype', ''), _.get(params, 'customerId', null))){
            self.formatCCBillParamsAndExecute(cb, tableName, params);
        }else{
            return self.executeOlderCreateBillQuery(cb, tableName, params);
        }
    }

    executeOlderCreateBillQuery(cb, tableName, params) {
        let
            self = this,
            operator = params.operator,
            service_id = 0,
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            referenceId = _.get(params, 'referenceId', null),
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            tokenisedCreditCard = _.get(params, 'tokenisedCreditCard', null),
            parId = _.get(params, 'parId', null),
            tin = _.get(params, 'tin', null),
            bankName = _.get(params, 'bankName', null),
            cardnetwork = _.get(params, 'cardNetwork', null),
            extra = _.get(params, 'extra', null),
            deletedOldFormatMCN = _.get(params, 'deletedOldFormatMCN', true),
            customer_type = _.get(params, 'customer_type', null),
            mandatoryUpdateNBFD = _.get(self.config,['RECENT_BILL_CONFIG', 'OPERATORS', params.operator,'mandatoryUpdateNBFD'], false),
            fromRecents = _.get(params, 'fromRecents', false),
            fromSource = _.get(params, 'fromSource', null);

        let isValidPrepaidElectricityRecord = (this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params));
        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id${isCreditCardOperator ? ',reference_id' : ''}${tokenisedCreditCard ? ',par_id' : ''}${tokenisedCreditCard ? ',tin' : ''},operator,gateway,next_bill_fetch_date ${fromRecents ? '' : ', bill_fetch_date'} ${( (fromRecents || fromSource === 'emiDueService') || (fromSource === 'billCylinderService')) ? ', bill_date' : ''},
                service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,extra ${(fromRecents && params.is_automatic) ? ',is_automatic' : ''}${isCreditCardOperator ? ',bank_name' : ''}${isCreditCardOperator ? ',card_network' : ''}
                ${((fromSource === 'billCylinderService') || fromSource==='billDthService' || (fromSource === 'emiDueService') || (deletedOldFormatMCN === true)) ? ', customerOtherInfo' : '' })                 
                VALUES ?
                ON DUPLICATE KEY UPDATE
                ${(isValidPrepaidElectricityRecord) ? 'amount ='+ null+',' : (params.amount > 0) ? 'amount = VALUES(amount),' : 'amount=(CASE WHEN (service="dth") THEN VALUES(amount) WHEN (status=' + notInUsedStatus + ') THEN VALUES(amount) WHEN ('+_.get(params,'amountWithinDeviation', false)+'=true) THEN 0 ELSE (IFNULL(amount,0) + VALUES(amount)) END),'}
                customer_email=VALUES(customer_email),
                customer_mobile=VALUES(customer_mobile),
                ${fromRecents ? '' : 'bill_fetch_date=VALUES(bill_fetch_date),'}
                operator=VALUES(operator),
                ${(fromRecents || (fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') ) && _.get(params, 'billDate', null) ? 'bill_date =VALUES(bill_date),' : ''}
                service=VALUES(service),
                paytype=VALUES(paytype),
                circle=VALUES(circle),
                service_id = VALUES(service_id),
                payment_channel=VALUES(payment_channel),
                notification_status=VALUES(notification_status),
                retry_count=VALUES(retry_count),
                reason=VALUES(reason),
                extra=VALUES(extra),
                ${(service_id == 4)
                    ? 'status=VALUES(status),next_bill_fetch_date=VALUES(next_bill_fetch_date)'
                    : `status= (CASE
                                    WHEN ${isValidPrepaidElectricityRecord} THEN ${_.get(params, 'status', 11)}
                                    WHEN amount<=0 THEN VALUES(status)
                                    ELSE status END)`}
                ${(params.paymentDate && !_.isEmpty(params.paymentDate)) ? ', payment_date = VALUES(payment_date)' : ''}
                ${(params.gateway && !_.isEmpty(params.gateway)) ? ', gateway = VALUES(gateway)' : ''}
                ${(params.user_data && !_.isEmpty(params.user_data)) ? ', user_data = VALUES(user_data)' : ''}
                ${((params.dueDate && !_.isEmpty(params.dueDate)) || params.status == 0) ? ', due_date = VALUES(due_date)' : ''}
                ${(!mandatoryUpdateNBFD && fromRecents && service_id != 4)
                    ? `, next_bill_fetch_date = (CASE
                        WHEN ${isValidPrepaidElectricityRecord}
                        THEN '${params.nextBillFetchDate}'
                        WHEN amount <= 0 and next_bill_fetch_date < now()
                        THEN '${params.nextBillFetchDate}'
                        ELSE next_bill_fetch_date
                    END)`
                    : ''}
                ${( mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ', next_bill_fetch_date="' + params.nextBillFetchDate + '"' : ''}
                ${(fromRecents && params.is_automatic) ? ', is_automatic = "' + params.is_automatic + '"' : ''}
                ${((fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') || (params.customerOtherInfoToBeUpdated)) ? ', customerOtherInfo = VALUES(customerOtherInfo)' : ''}
                ${((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1)) ? ', old_bill_fetch_date= ' + null : ''

            }`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                ...(isCreditCardOperator ? [referenceId] : []),
                ...(tokenisedCreditCard ? [parId] : []),
                ...(tokenisedCreditCard ? [tin] : []),
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                ...(fromRecents ? [] : [MOMENT().format('YYYY-MM-DD HH:mm:ss')]),
                ...((fromRecents || fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') ? [_.get(params, 'billDate', null)] : []),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                params.paymentChannel,
                isValidPrepaidElectricityRecord ? null : params.amount,
                params.retryCount,
                isValidPrepaidElectricityRecord ? 11 : params.status,
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                _.get(params, 'dueDate', null),
                extra,
                ...((fromRecents && params.is_automatic) ? [params.is_automatic] : []),
                ...(isCreditCardOperator ? [bankName] : []),
                ...(isCreditCardOperator ? [cardnetwork] : []),
                ...((fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') || (deletedOldFormatMCN === true)?  [params.customerOtherInfo] : []),
            ]]];
        L.log('createBill::executionQuery::',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createBill`]);
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    copyAdditionalParamsFromHistoricalRecord(params, historicalRecord) {
        let self = this;
        try{
            historicalRecord = _.get(historicalRecord, '0', {});
            _.set(historicalRecord, 'due_date',MOMENT(_.get(historicalRecord, 'due_date', null)).isValid()? MOMENT.utc(_.get(historicalRecord, 'due_date', null)).format('YYYY-MM-DD HH:mm:ss') : null);
            let keysToCopy = ['bill_date', 'bill_fetch_date', 'due_date', 'next_bill_fetch_date', 'reason', 'retry_count', 
                                'notification_status', 'service_id', 'is_automatic', 'customer_other_info', 'extra', 'circle',
                                'customer_email', 'customer_mobile', 'published_date', 'data_source', 'remind_later_date', 
                                'customer_bucket','consent_valid_till'];
            
            //every key in params is in camel case and in historical record it is in snake case
            _.forEach(keysToCopy, function(key){
                if(key == 'extra' || key == 'customer_other_info' || key == 'user_data'){
                    if(typeof params[_.camelCase(key)] == 'string'){
                        params[_.camelCase(key)] = JSON.parse(params[_.camelCase(key)]);
                    }
                    let historicalRecordKey = _.get(historicalRecord, key, {});
                    if(typeof historicalRecordKey == 'string'){
                        historicalRecordKey = JSON.parse(historicalRecordKey);
                    }

                    let paramsKey = _.get(params, _.camelCase(key), {});

                    let keysToCopyFromHistoricalRecord = _.keys(historicalRecordKey);
                    keysToCopyFromHistoricalRecord.forEach(historicalRecordKey => {
                        if(_.get(paramsKey, historicalRecordKey, null) == null){
                            paramsKey[historicalRecordKey] = historicalRecordKey;
                        }
                    });
                    params[_.camelCase(key)] = JSON.stringify(paramsKey);

                }
                else if(_.get(historicalRecord, key, null) != null){
                    _.set(params, _.camelCase(key), _.get(historicalRecord, key, null));
                }
            });
        }catch(e){
            self.L.error('copyAdditionalParamsFromHistoricalRecord::', 'error occurred : ', e);
        }
    }

    formatCCBillParamsAndExecute(cb, tableName, params) {
        let self = this;
        let historcalRecords = _.get(params, 'transactionHistory', []);
        let queryWithEncryptedParams = false;
        let encrypted_params = {};
        //filter historical record for the same customer_id, recharge_number, reference_id
        let historicalRecord = _.filter(historcalRecords, function (record) {
            return record.customer_id == params.customerId && record.recharge_number == params.rechargeNumber && record.reference_id == params.referenceId;
        });        
        let is_encrypted = _.get(historicalRecord, '0.is_encrypted', 0);

        //this.logger.log("formatCCBillParamsAndExecute existing params : ", params, _.toLower(params.service));
        if(!is_encrypted && _.isArray(historicalRecord) && historicalRecord.length > 0){
            //if historical record is not encrypted then need to create new entry and migrate all columns from historical record
            self.copyAdditionalParamsFromHistoricalRecord(params, historicalRecord);
        }

        params = self.additionalHandlingForRecentBills(params, historicalRecord);
        //this.logger.log("formatCCBillParamsAndExecute additionalHandlingForRecentBills params : ", params, _.toLower(params.service));
        let paramsToEncryptAtRootLevel = ['rechargeNumber', 'customerMobile', 'customerEmail', 'referenceId'];
        _.set(params, 'customer_id', _.get(params, 'customerId', null));
        _.set(params, 'due_date', _.get(params, 'dueDate', null));
        encrypted_params = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams(params, paramsToEncryptAtRootLevel, self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
        _.set(encrypted_params, 'amount', null);
        _.set(encrypted_params, 'due_date', null);
        queryWithEncryptedParams = true;

        ASYNC.waterfall([
            next=>{
                self.executeCreateBillQuery(next, tableName, queryWithEncryptedParams? encrypted_params:params, queryWithEncryptedParams);
            },
            (dbResponse,next)=>{
                if(!is_encrypted && (_.isArray(historicalRecord) && historicalRecord.length > 0)){
                    let paramsForDeletingRecord = {
                        id: _.get(historicalRecord, '0.id', null)
                    }
                    self.deleteDuplicateCards(next, tableName, paramsForDeletingRecord);
                }else{
                    next(null, null);
                }
            }
        ],
        function(err, result){
            if(err){
                return cb(err, null);
            }else{
                return cb(null, result);
            }
        });
    }
    
    /**
     * This function is only called for CC cases
     * @param {*} params 
     * @param {*} historicalRecord 
     * @returns 
     */
    additionalHandlingForRecentBills(params, historicalRecord) {
        let self = this;
        try{
            let amountFromHistoricalRecord = _.get(historicalRecord, '0.amount', 0);
            let amount = _.get(params, 'amount', 0);
           
            if(params.amount > 0){
                params.amount = amount;
            }else{
                if(_.get(params, 'service', '')=='dth'){
                    params.amount = amount;
                }
                if(_.get(params, 'status', null) == _.get(params, 'notInUsedStatus', 13)){
                    params.amount = amount;
                }
                if(_.get(params, 'amountWithinDeviation', false) == true){
                    params.amount = 0;
                }else{
                    params.amount = amountFromHistoricalRecord + amount;
                }
            }

            if(_.get(params, 'service_id', 0) == 4){
                params.status = params.status;
            }else{
           
                if(params.amount <= 0){
                    params.status = params.status;
                }else{
                    params.status = _.get(historicalRecord, '0.status', 0);
                }
            }
            return params;
        }catch(e){
            self.L.error('additionalHandlingForRecentBills::', 'error occurred : ', e);
            return params;
        }
        //${(params.amount > 0) ? 'amount = VALUES(amount),' : 'amount=(CASE WHEN (service="dth") THEN VALUES(amount) WHEN (status=' + notInUsedStatus + ') THEN VALUES(amount) WHEN ('+_.get(params,'amountWithinDeviation', false)+'=true) THEN 0 ELSE (IFNULL(amount,0) + VALUES(amount)) END),'}
        //amount=(CASE WHEN (service="dth") THEN VALUES(amount) WHEN (status=13) THEN VALUES(amount) WHEN (false=true) THEN 0 ELSE (IFNULL(amount,0) + VALUES(amount)) END),
    }

    executeCreateBillQuery(cb, tableName, params, queryWithEncryptedParams=false) {
        let self = this;
        let encryptedClause = `enc_amount, enc_due_date, amount`;
        let decryptedClause = `amount, due_date`;
        let dueDateKey = queryWithEncryptedParams ? 'enc_due_date' : 'due_date';
        let duedateAdditionCondition = (params[dueDateKey] && !_.isEmpty(params[dueDateKey])) || params.status == 0
        let isCreditCardOperator = _.get(params, 'isCreditCardOperator', null);
        let fromRecents = _.get(params, 'fromRecents', false);
        let fromSource = _.get(params, 'source', null);
        let notInUsedStatus = _.get(params, 'notInUsedStatus', 13);
        let service_id = _.get(params, 'service_id', 0);
        let referenceId = _.get(params, 'referenceId', null);
        let tokenisedCreditCard = _.get(params, 'tokenisedCreditCard', null);
        let parId = _.get(params, 'parId', null);
        let tin = _.get(params, 'tin', null);
        let mandatoryUpdateNBFD = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS', params.operator, 'mandatoryUpdateNBFD'], false);
        let deletedOldFormatMCN = _.get(params, 'deletedOldFormatMCN', true);
        let extra = _.get(params, 'extra', null);
        let bankName = _.get(params, 'bankName', null);
        let cardnetwork = _.get(params, 'cardNetwork', null);

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id${isCreditCardOperator ? ',reference_id' : ''}${tokenisedCreditCard ? ',par_id' : ''}${tokenisedCreditCard ? ',tin' : ''},operator,gateway,next_bill_fetch_date, bill_fetch_date , bill_date,
                service,paytype,circle,customer_mobile,customer_email,payment_channel, ${queryWithEncryptedParams? encryptedClause : decryptedClause}, retry_count,status,reason,user_data, notification_status, payment_date, service_id,extra ,is_automatic${isCreditCardOperator ? ',bank_name' : ''}${isCreditCardOperator ? ',card_network' : ''}
                ,customerOtherInfo, is_encrypted, published_date, data_source, remind_later_date, customer_bucket, consent_valid_till)
                VALUES ?
                ON DUPLICATE KEY UPDATE
                ${queryWithEncryptedParams?'':(params.amount > 0) ? 'amount = VALUES(amount),' : 'amount=(CASE WHEN (service="dth") THEN VALUES(amount) WHEN (status=' + notInUsedStatus + ') THEN VALUES(amount) WHEN ('+_.get(params,'amountWithinDeviation', false)+'=true) THEN 0 ELSE (IFNULL(amount,0) + VALUES(amount)) END),'}
                ${queryWithEncryptedParams? 'enc_amount = VALUES(enc_amount),':''}
                ${duedateAdditionCondition? `${dueDateKey} = VALUES(${dueDateKey}),`:''}
                customer_email=VALUES(customer_email),
                customer_mobile=VALUES(customer_mobile),
                ${fromRecents ? '' : 'bill_fetch_date=VALUES(bill_fetch_date),'}
                operator=VALUES(operator),
                ${(fromRecents || (fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') ) && _.get(params, 'billDate', null) ? 'bill_date =VALUES(bill_date),' : ''}
                service=VALUES(service),
                paytype=VALUES(paytype),
                circle=VALUES(circle),
                service_id = VALUES(service_id),
                payment_channel=VALUES(payment_channel),
                notification_status=VALUES(notification_status),
                retry_count=VALUES(retry_count),
                reason=VALUES(reason),
                extra=VALUES(extra),
                ${(service_id == 4) ? 'status=VALUES(status),next_bill_fetch_date=VALUES(next_bill_fetch_date)' : queryWithEncryptedParams?  'status=VALUES(status) ' : 'status= (CASE WHEN amount<=0 THEN VALUES(status) ELSE status END)'}  
                ${(params.paymentDate && !_.isEmpty(params.paymentDate)) ? ', payment_date = VALUES(payment_date)' : ''}
                ${(params.gateway && !_.isEmpty(params.gateway)) ? ', gateway = VALUES(gateway)' : ''}
                ${(params.user_data && !_.isEmpty(params.user_data)) ? ', user_data = VALUES(user_data)' : ''}
                ${(!mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ', next_bill_fetch_date= (CASE WHEN amount <= 0 and next_bill_fetch_date < now() THEN "' + params.nextBillFetchDate + '" ELSE next_bill_fetch_date END )' : ''}
                ${( mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ', next_bill_fetch_date="' + params.nextBillFetchDate + '"' : ''}
                ${(fromRecents && params.is_automatic) ? ', is_automatic = "' + params.is_automatic + '"' : ''}
                ${((fromSource === 'emiDueService') || (fromSource === 'billCylinderService') || (fromSource==='billDthService') || (params.customerOtherInfoToBeUpdated)) ? ', customerOtherInfo = VALUES(customerOtherInfo)' : ''}
                ${((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1)) ? ', old_bill_fetch_date= ' + null : ''

            }`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                ...(isCreditCardOperator ? [referenceId] : []),
                ...(tokenisedCreditCard ? [parId] : []),
                ...(tokenisedCreditCard ? [tin] : []),
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                _.get(params, 'billFetchDate', null),
                _.get(params, 'billDate', null),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                params.paymentChannel,
                ...(queryWithEncryptedParams? [params.enc_amount, params.enc_due_date, null] : [params.amount, params.dueDate]),
                params.retryCount,
                params.status,
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                extra,
                _.get(params, 'is_automatic', 0),
                ...(isCreditCardOperator ? [bankName] : []),
                ...(isCreditCardOperator ? [cardnetwork] : []),
                _.get(params, 'customerOtherInfo', null),
                _.get(params, 'is_encrypted', 0),
                _.get(params, 'publishedDate', null),
                _.get(params, 'dataSource', null),
                _.get(params, 'remindLaterDate', null),
                _.get(params, 'customerBucket', null),
                _.get(params, 'consentValidTill', null),
            ]]];
        L.log(self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createBill`]);
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    /**
     * 
     * @param {*} cb 
     * @param {*} tableName 
     * @param {*} params 
     */
    createCCBillForCustomerId(cb, tableName, params) {
        let self=this;
        let queryWithEncryptedParams = false
        self.setReconIdAndReferenceID(params);

        let encrypted_params = {};
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(_.get(params, 'service', ''), _.get(params, 'paytype', ''), _.get(params, 'customer_id', null))){
            let paramsAtRootLevel = ['recharge_number','reference_id','customer_mobile', 'customer_email']
            encrypted_params = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams({
                    ...params,
                    customerId:params.customer_id
                }, paramsAtRootLevel, self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
            queryWithEncryptedParams = true;
        }
        return this.createCCBillForCustomerIdWithEncDecHandling(cb, tableName, queryWithEncryptedParams?encrypted_params:params, queryWithEncryptedParams);
    }
    createCCBillForCustomerIdWithEncDecHandling(cb, tableName, params, enableEncryptedQuery, returnNewlyCreatedRecord = false) {
        
        let
            self = this,
            operator = params.operator,
            service_id = 0,
            tokenisedCreditCard = false,
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            referenceId = _.get(params, 'reference_id', null);
        params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));
        
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        if(params.par_id && params.par_id!='') tokenisedCreditCard = true;
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.product_id, 'service'], '');
        
        let encryptedClause = `,enc_due_date, enc_amount, amount`;
        let decryptedClause = `,due_date, amount`;

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id${tokenisedCreditCard ? ',tin' : ''}${tokenisedCreditCard ? ',par_id' : ''},reference_id, operator,gateway,next_bill_fetch_date , bill_fetch_date, bill_date ,
                service,paytype,circle,customer_mobile,customer_email,payment_channel${enableEncryptedQuery? encryptedClause:decryptedClause},retry_count,status,reason,user_data, notification_status, payment_date, service_id,extra ,is_automatic
                , customerOtherInfo, bank_name, card_network, published_date, data_source, remind_later_date, customer_bucket, is_encrypted, consent_valid_till)
                VALUES ?
                ON DUPLICATE KEY UPDATE
                ${_.get(params, 'is_encrypted', false)==true? `enc_due_date=VALUES(enc_due_date), enc_amount=VALUES(enc_amount),`: `due_date=VALUES(due_date), amount=VALUES(amount),`}  
                customer_email=VALUES(customer_email),
                customer_mobile=VALUES(customer_mobile),
                bill_fetch_date=VALUES(bill_fetch_date),
                operator=VALUES(operator),
                ${_.get(params, 'bill_date', null) ? 'bill_date =VALUES(bill_date),' : ''}
                service=VALUES(service),
                paytype=VALUES(paytype),
                circle=VALUES(circle),
                service_id = VALUES(service_id),
                payment_channel=VALUES(payment_channel),
                notification_status=VALUES(notification_status),
                retry_count=VALUES(retry_count),
                reason=VALUES(reason),
                extra=VALUES(extra),
                status=VALUES(status)  
                ${(params.payment_date && !_.isEmpty(params.payment_date)) ? ', payment_date = VALUES(payment_date)' : ''}
                ${(params.gateway && !_.isEmpty(params.gateway)) ? ', gateway = VALUES(gateway)' : ''}
                ${(params.user_data && !_.isEmpty(params.user_data)) ? ', user_data = VALUES(user_data)' : ''}
                ${((params.due_date && !_.isEmpty(params.due_date)) || params.status == 0) ? ', due_date = VALUES(due_date)' : ''}
                , next_bill_fetch_date=VALUES(next_bill_fetch_date)
                , is_automatic = VALUES(is_automatic)
                , customerOtherInfo = VALUES(customerOtherInfo)
                , is_encrypted = VALUES(is_encrypted)`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                (params.product_id + ''),
                ...(tokenisedCreditCard ? [params.tin] : []),
                ...(tokenisedCreditCard ? [params.par_id] : []),
                referenceId,
                params.operator,
                params.gateway,
                params.next_bill_fetch_date,
                params.bill_fetch_date,
                params.bill_date,
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customer_mobile,
                params.customer_email,
                params.payment_channel,
                enableEncryptedQuery? params.enc_due_date: params.due_date,
                enableEncryptedQuery? params.enc_amount: params.amount,
                ...(enableEncryptedQuery? [null]:[]),
                params.retry_count,
                params.status,
                params.reason,
                params.user_data,
                _.get(params, 'notification_status', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'payment_date', null),
                service_id,
                _.get(params, 'extra', null),
                0,
                params.customerOtherInfo,
                _.get(params, 'bank_name', null),
                _.get(params, 'card_network', null),
                _.get(params, 'published_date', null),
                _.get(params, 'data_source', null),
                _.get(params, 'remind_later_date', null),
                _.get(params, 'customer_bucket', null),
                _.get(params, 'is_encrypted', 0),
                _.get(params, 'consent_valid_till', null)
            ]]];

        L.verbose('createBill::executionQuery::',JSON.stringify(query),"_queryParams::",JSON.stringify(queryParams));

        var latencyStart = new Date().getTime();
        // return cb(null);
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createCCBillForCustomerId'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createCCBillForCustomerId`]);
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            if(returnNewlyCreatedRecord == true) return cb(err, data);
            cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    createSmsParsingLoanEmiBill(cb, tableName, params) {
        let
            self = this,
            operator = params.operator,
            service_id = 0;
        params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));
        let is_automatic = params.is_automatic || 0;
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        let recon_id = utility.generateReconID(_.get(params,'rechargeNumber',''), operator,  params.amount , _.get(params, 'commonDueDate', null), _.get(params, 'billDate', null))

        _.set(params,'extra' , utility.setReconIdInString( _.get(params,'extra',null) , recon_id))
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date , bill_fetch_date, bill_date,
                service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,customerOtherInfo,extra ,is_automatic)                
                VALUES ?`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                _.get(params, 'billFetchDate', null),
                _.get(params, 'billDate', null),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                _.get(params,'paymentChannel',null),
                _.get(params,'commonAmount',0),
                params.retryCount,
                _.get(params,'commonStatus',4),
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                _.get(params, 'commonDueDate', null),
                _.get(params, 'customerOtherInfo',null),
                _.get(params, 'extra', null),
                is_automatic,
            ]]];
        
        self.L.log('createSmsParsingLoanEmiBill::',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createSmsParsingLoanEmiBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createSmsParsingLoanEmiBill`]);
                L.critical('createBill::', 'error occurred while inserting data in DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }
    
    createBillOfValidationSync(cb, tableName, params) {
        let
            self = this,
            operator = params.operator,
            service_id = 0,
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13);

        params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));

        let is_automatic = null;
        if(_.get(params, 'is_automatic', null) !== null) {
            is_automatic = _.get(params, 'is_automatic', null)
        } else if (_.get(params, 'is_automatic_diffCustId', 0) == 0 || _.get(params, 'is_automatic_diffCustId', null) == null) {
            is_automatic = 0;
        } else if(_.get(params, 'is_automatic_diffCustId', 0) > 2) {
            is_automatic = 4;
        } else {
            is_automatic = 2;
        }
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        let recon_id = utility.generateReconID( params.rechargeNumber , params.operator , _.get(params,'commonAmount',0) ,  _.get(params, 'commonDueDate', null) , _.get(params, 'billDate', null))

        _.set(params,'extra' , utility.setReconIdInString( _.get(params,'extra',null) , recon_id))
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
        if(params.customerOtherInfo == 'null') {
            params.customerOtherInfo = null;
        }

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date , bill_fetch_date, bill_date,
                service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,customerOtherInfo,extra ,is_automatic
            ${_.get(params, 'service', null) == 'electricity' ? ',old_bill_fetch_date' : ''})                
                VALUES ?`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                _.get(params, 'billFetchDate', null),
                _.get(params, 'billDate', null),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                _.get(params,'paymentChannel',null),
                _.get(params,'commonAmount',0),
                params.retryCount,
                _.get(params,'commonStatus',0),
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                _.get(params, 'commonDueDate', null),
                _.get(params, 'customerOtherInfo',null),
                _.get(params, 'extra', null),
                is_automatic,
                ...(_.get(params, 'service', null) == 'electricity' ? [_.get(params, 'oldBillFetchDate', null)] : [])
            ]]];
        
        L.log('createBillOfValidationSync::',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createBillOfValidationSync'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createBillOfValidationSync`]);
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }



    createCCBillOfValidationSync(cb, tableName, params) {
        let
            self = this,
            service_id = 0;

        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }

        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');
        let recon_id = utility.generateReconID( params.rechargeNumber , params.bankName , _.get(params,'commonAmount',0) ,  null , _.get(params, 'billDate', null))
       
        _.set(params,'extra' , utility.setReconIdInString( _.get(params,'extra',null) , recon_id))

        let recordExtra = _.get(params, 'extra', null);
        if(typeof recordExtra == 'string'){
            try{
            recordExtra = JSON.parse(recordExtra);
            }catch(err){
                if (err) {
                    L.critical('createBill::', 'error occurred while inserting data in DB: ', err);
                } 
            }
        }
        let cardVariant =  _.get(params, 'issuingBankCardVariant', '');
        let skin = _.get(params, 'skin_url', '');
        let source = _.get(params, 'skin_source', '');
        if(cardVariant){
            _.set(recordExtra, 'issuingBankCardVariant', cardVariant);
        }
        if(skin){
            _.set(recordExtra, 'skin_url', skin);
        }
        if(source){
            _.set(recordExtra, 'skin_source', source);
        }

        let recordToInsert = _.cloneDeep(params);
        _.set(recordToInsert, 'extra', recordExtra);
        
        if(this.EncryptionDecryptioinHelper.isWhitelistedForCC("financial services", "credit card", params.customerId)) {
            recordToInsert.rechargeNumber = this.EncryptionDecryptioinHelper.encryptData(params.rechargeNumber);
            recordToInsert.enc_due_date = this.EncryptionDecryptioinHelper.encryptData(_.get(params, 'dueDate', null));
            recordToInsert.dueDate = null;
            recordToInsert.customerMobile = params.customerMobile ? this.EncryptionDecryptioinHelper.encryptData(params.customerMobile) : null;
            recordToInsert.customerEmail = params.customerEmail ? this.EncryptionDecryptioinHelper.encryptData(params.customerEmail) : null;
            recordToInsert.enc_amount = this.EncryptionDecryptioinHelper.encryptData(_.toString(_.get(params,'commonAmount',0)));
            recordToInsert.commonAmount = null;
            recordToInsert.referenceId = this.EncryptionDecryptioinHelper.encryptData(_.get(params, 'referenceId', null));
            _.set(recordToInsert, 'user_data', this.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'user_data', {}), this.keysToEncryptForUserData)); 
            _.set(recordToInsert, 'customerOtherInfo', this.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'customerOtherInfo', {}), this.keysToEncryptForCustomerOtherInfo));
            _.set(recordToInsert, 'extra', this.EncryptionDecryptioinHelper.encryptJson(recordExtra, this.keysToEncryptForCustomerExtra));
            recordToInsert.is_encrypted = 1;
        }

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway, bill_fetch_date, bill_date, due_date, next_bill_fetch_date,
                service,paytype,circle,customer_mobile,customer_email,amount,retry_count,status,reason,user_data, notification_status, service_id, reference_id, customerOtherInfo, extra, par_id, tin, bank_name, card_network, enc_due_date, enc_amount, is_encrypted, consent_valid_till)
                VALUES ?`,
            queryParams = [[[
                recordToInsert.customerId,
                recordToInsert.rechargeNumber,
                recordToInsert.productId,
                recordToInsert.operator,
                recordToInsert.gateway,
                _.get(recordToInsert, 'billFetchDate', null),
                _.get(recordToInsert, 'billDate', null),
                _.get(recordToInsert, 'dueDate', null),
                _.get(recordToInsert, 'nextBillFetchDate', null),
                _.toLower(recordToInsert.service),
                recordToInsert.paytype,
                recordToInsert.circle,
                recordToInsert.customerMobile,
                recordToInsert.customerEmail,
                _.get(recordToInsert,'commonAmount',0),
                recordToInsert.retryCount,
                _.get(recordToInsert,'commonStatus',0),
                recordToInsert.reason,
                recordToInsert.user_data,
                _.get(recordToInsert, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                service_id,
                _.get(recordToInsert, 'referenceId', null),
                _.get(recordToInsert, 'customerOtherInfo',null),
                JSON.stringify(_.get(recordToInsert, 'extra', null)),
                _.get(recordToInsert, 'parId', null),
                _.get(recordToInsert, 'tin', null),
                _.get(recordToInsert, 'bankName', null),
                _.get(recordToInsert, 'cardNetwork', null),
                _.get(recordToInsert, 'enc_due_date', null),
                _.get(recordToInsert, 'enc_amount', null),
                _.get(recordToInsert, 'is_encrypted', 0),
                _.get(recordToInsert, 'consent_valid_till', null)
            ]]];
        
        L.log('createCCBillOfValidationSync::',self.dbInstance.format(query,queryParams));

        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                L.critical('createBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    createSmsParsingPostpaidBill(cb, tableName, params) {
        let
            self = this,
            operator = params.operator,
            service_id = 0;

        params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])))

        let is_automatic = params.is_automatic || 0;

        if (params.service_id) {
            service_id = params.service_id;
        }

        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date , bill_fetch_date, bill_date,
                service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,customerOtherInfo,extra ,is_automatic)                
                VALUES ?`,
            queryParams = [[[
                _.get(params, 'customerId', null),
                params.rechargeNumber,
                params.productId,
                params.operator,
                params.gateway,
                params.nextBillFetchDate,
                _.get(params, 'billFetchDate', null),
                _.get(params, 'billDate', null),
                _.toLower(params.service),
                params.paytype,
                params.circle,
                _.get(params, 'customerMobile', null),
                _.get(params, 'customerEmail', null),
                _.get(params,'paymentChannel',null),
                _.get(params,'commonAmount',0),
                params.retryCount,
                _.get(params,'commonStatus',4),
                params.reason,
                params.user_data,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                _.get(params, 'paymentDate', null),
                service_id,
                _.get(params, 'commonDueDate', null),
                _.get(params, 'customerOtherInfo',null),
                _.get(params, 'extra', null),
                is_automatic,
            ]]];
        
        self.L.log('createSmsParsingPostpaidBill::',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createSmsParsingPostpaidBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createSmsParsingPostpaidBill`]);
                L.critical('createBill::', 'error occurred while inserting data in DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }


    //recentBills
    updateBillsForSameRechargeNum(cb, tableName, params) {
        let self = this,
            billCylinderService = _.get(params, 'billCylinderService', false) || _.get(params, 'billDthService', false),
            mandatoryUpdateNBFD = _.get(self.config,['RECENT_BILL_CONFIG', 'OPERATORS', params.operator,'mandatoryUpdateNBFD'], false);

        if(_.get(params, 'is_automatic', null)== 1 || _.get(params, 'is_automatic', null)== 5){
            _.set(params, 'is_automatic', 2);
        } else if (_.get(params, 'is_automatic', null) == 3) {
            _.set(params, 'is_automatic', 4);
        } else{
            _.set(params, 'is_automatic', null);
        }

        let amount=_.get(params , 'amount',0)
        if(_.get(params,'amountWithinDeviation', false) == true){
            amount = 0
        }
        let recon_id = utility.generateReconID(_.get(params , 'rechargeNumber',''),_.toLower(params.service) == "financial services" ?  params.bankName : params.operator ,amount ,  _.get(params, 'dueDate', null) , _.get(params, 'billDate', null))

        let isValidPrepaidElectricityRecord = this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params);

        let query = `UPDATE ${tableName}
                    SET
                        ${(isValidPrepaidElectricityRecord)
                            ? 'amount ='+ null + ','
                            : billCylinderService
                                ? 'amount ='+ _.get(params, 'amount', '0.00') + ','
                                : 'amount = (CASE WHEN (' + _.get(params, 'amountWithinDeviation', false) + '=true) THEN 0 ELSE amount + ' + _.get(params, 'amount', '0.00') + ' END),'}
                        operator = ?,
                        gateway = ?,
                        service = ?,
                        paytype =  ?,
                        circle = ?,
                        retry_count = ?,
                        reason = ?,
                        status = (CASE WHEN amount>0 THEN status ELSE `+ params.status + ` END)
                        ${(!mandatoryUpdateNBFD && params.nextBillFetchDate && !_.isEmpty(params.nextBillFetchDate)) ? ` ,next_bill_fetch_date =  (CASE WHEN ${isValidPrepaidElectricityRecord} THEN '${params.nextBillFetchDate}' WHEN next_bill_fetch_date <now() THEN '${params.nextBillFetchDate}' ELSE next_bill_fetch_date END)` : ''}
                        ${( mandatoryUpdateNBFD && params.nextBillFetchDate && !_.isEmpty(params.nextBillFetchDate)) ? ' ,next_bill_fetch_date = "' + params.nextBillFetchDate + '"' : ''}
                        ${(params.is_automatic) ? ', is_automatic="' + params.is_automatic + '"' : ''}
                        ${((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1)) ? ', old_bill_fetch_date=' + null + '' : ''}
                        ${_.get(params, 'billDate', null) ? ',bill_date ="' + params.billDate + '"' : ''}
                        ${(isValidPrepaidElectricityRecord) ? ',due_date ='+null : (_.get(params, 'dueDate', null) ? ',due_date ="' + params.dueDate + '"' : '')}, extra = JSON_SET( JSON_REMOVE(COALESCE(extra, '{}'), '$.partialBillState'),'$.recon_id','${recon_id}','$.user_type','RU','$.updated_data_source','transaction', '$.source_subtype_2',null)`
                         

        let dataArr = [
            _.get(params, 'operator', null),
            _.get(params, 'gateway', null),
            _.get(params, 'service', null),
            _.get(params, 'paytype', null),
            _.get(params, 'circle', null),
            _.get(params, 'retryCount', 0),
            _.get(params, 'reason', null),
        ];

        if (_.get(params, 'isCreditCardOperator', false)) {
            query = self.modifyQueryForCreditCardOperator(query, dataArr, params);
        } else {
            query = self.modifyQueryForNonCreditCardOperator(query, dataArr, params);
        }

        L.log("update same recharge_number",self.dbInstance.format(query,dataArr));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, response) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillsForSameRechargeNum'});
            if (err || !(response)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillsForSameRechargeNum`]);
                L.critical('updateBillsForSameRechargeNum :: error occurred while updating bills for recharge number = ', _.get(params, 'rechargeNumber', ''), 'and pId = ', _.get(params, 'productId', ''), err);
            }
            cb(err, response);
        }, 'DIGITAL_REMINDER_MASTER', query, dataArr);
    }

    modifyQueryForCreditCardOperator(query, dataArr, params) {
        let self = this;
        let notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            operator = _.get(params, 'operator', null),
            rechargeNumber = _.get(params, 'rechargeNumber', null);

        let whereClause = ` WHERE recharge_number = ? AND customer_id != ? AND reference_id = ? AND status NOT in (?)`;
        dataArr.push(rechargeNumber);
        dataArr.push(params.customerId);
        dataArr.push(params.referenceId);
        dataArr.push([notInUseStatus, disabledStatus]);

        return query + whereClause;
    }

    modifyQueryForNonCreditCardOperator(query, dataArr, params) {
        let self = this;
        let notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
        disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
        operator = _.get(params, 'operator', null),
        rechargeNumber = _.get(params, 'rechargeNumber', null),
        service = _.get(params, 'service', null);
        let whereClause = ''

        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(rechargeNumber, operator);

        if(isOperatorPrefixEnabled){
            whereClause += `WHERE ((recharge_number = ? AND customer_id = ?) OR (recharge_number in (?,?) AND customer_id != ?))`;
            dataArr.push(alternateRechargeNumber);
            dataArr.push(params.customerId);
            dataArr.push(rechargeNumber);
            dataArr.push(alternateRechargeNumber);
            dataArr.push(params.customerId);
        }else{
            whereClause += `WHERE recharge_number = ? AND customer_id != ?`;
            dataArr.push(rechargeNumber);
            dataArr.push(params.customerId);
        }

        whereClause += ` AND operator = ?  AND service = ? AND status NOT in (?)`;
        dataArr.push(operator);
        dataArr.push(service);
        dataArr.push([notInUseStatus, disabledStatus]);

        return query + whereClause;
    }

    updateBillForSameRNandCID(cb, tableName, params) {
        let self=this;
        let encrypted_params = {};
        let isEncryptedParams = false;
        self.setReconIdInExtra(params);
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(_.get(params, 'service', ''),_.get(params, 'paytype', ''), _.get(params, 'customerId', null))){
            encrypted_params = _.cloneDeep(params);
            _.set(encrypted_params, 'enc_amount', self.EncryptionDecryptioinHelper.encryptData(_.toString(_.get(params, 'commonAmount', 0))));
            _.set(encrypted_params, 'enc_due_date', self.EncryptionDecryptioinHelper.encryptData(_.get(params, 'commonDueDate', null)));
            _.set(encrypted_params, 'user_data', self.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'user_data', {}),self.keysToEncryptForUserData));
            _.set(encrypted_params, 'customerOtherInfo', self.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'customerOtherInfo', {}),self.keysToEncryptForCustomerOtherInfo));
            _.set(encrypted_params, 'extra', self.EncryptionDecryptioinHelper.encryptJson(_.get(params, 'extra', {}),self.keysToEncryptForCustomerExtra));
            isEncryptedParams =true;
        }

        return self.updateBillForSameRNandCIDExisting(cb, tableName, isEncryptedParams?encrypted_params:params, isEncryptedParams);
    }

    setReconIdInExtra(params){
        let self=this;
        let recon_id = utility.generateReconID(_.get(params,'rechargeNumber',null),_.toLower(params.service) == "financial services" ?  _.get(params , 'bankName' , _.get(params , 'bank_name' , null)): params.operator ,_.get(params, 'commonAmount', null) , _.get(params, 'commonDueDate', null),params.billDate)
            let extra = utility.setReconIdInString(params.extra , recon_id)
            try{
                if(typeof extra == 'string'){
                    extra = JSON.parse(extra)
                    _.set(extra , 'source_subtype_2' , 'FULL_BILL')
                    extra = JSON.stringify(extra);
                }else{
                    _.set(extra , 'source_subtype_2' , 'FULL_BILL')
                }
            }catch(e){
                self.L.log('error in parsing extra in updateBillForSameRNandCID',e)
            }
        _.set(params, 'extra', extra);
        return;
    }

    updateBillForSameRNandCIDExisting(cb, tableName, params, isEncryptedParams=false) {
        //amount, due_date , status
        let self = this,
        // extra = JSON.stringify(_.get(params, 'extra', {})),
            lastPaymentDate = _.get(params, 'lastPaymentDate', null),
            lastBillDate = _.get(params, 'lastBillDate', null),
            userData = JSON.stringify(_.get(params, 'user_data', {})),
            customerOtherInfo = JSON.stringify(_.get(params, 'customerOtherInfo', {})),
            due_date = _.get(params, 'commonDueDate', null),
            enc_due_date = _.get(params, 'enc_due_date', null),
            status = _.get(params, 'commonStatus', null),
            notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            skipUpdateOnStatus = [
                notInUseStatus, disabledStatus
            ],
            amount = _.get(params, 'commonAmount', null),
            enc_amount = _.get(params, 'enc_amount', null),
            remindLaterDate = _.get(params,'remind_later_date',null),
            is_automatic = _.get(params, 'is_automatic', null),
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            id = _.get(params, 'id', null),
            billFetchDate = _.get(params, 'billFetchDate', null),
            whereClause = '',
            extra = JSON.stringify(_.get(params, 'extra', null)),
            updateQuery = `UPDATE ${tableName} SET ${isEncryptedParams ? 'amount=NULL,' : ''}`;

            if(customerOtherInfo == '"null"') {
                customerOtherInfo = null;
            }

        if(status === _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16)) {
            updateQuery += 'status=' + status + ',';
        } else if (((_.get(params, 'updateForOldBill', null) == true) || (lastPaymentDate < lastBillDate && status == 5)) && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += `${isEncryptedParams ? 'enc_amount="' + enc_amount + '",' : 'amount=' + amount + ','}`;
            updateQuery += 'status=' + status + ',';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
        } else if (status == 5 && lastPaymentDate && lastBillDate && (lastPaymentDate >= lastBillDate) && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'status=' + status + ',';
        } else {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += `${isEncryptedParams ? 'enc_amount="' + enc_amount + '",' : 'amount=' + amount + ','}`;
            updateQuery += 'status=' + status + ',';
            if(is_automatic != null){
                updateQuery += 'is_automatic=' + is_automatic + ',';
            }
            let dueDateQuery = due_date ? 'due_date = "' + due_date + '",' : 'due_date = ' + due_date + ',';
            updateQuery += `${isEncryptedParams ? 'enc_due_date = "' + enc_due_date + '",' : dueDateQuery}`;
            !_.isNull(params.billDate) ? updateQuery += 'bill_date = "' + params.billDate + '",' : '';
            !_.isNull(billFetchDate) ? updateQuery += 'bill_fetch_date = "' + billFetchDate + '",' : '';
            !_.isEmpty(params.user_data) ? updateQuery += 'user_data =' + userData + ',' : '';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) == null) ? updateQuery += 'old_bill_fetch_date = ' + null + ',' : '';
        }

        if(_.get(params,'resetRemindLaterDate',false)){
            L.log('resetRemindLaterDate:', 'resetting remind_later_date to null');
            updateQuery += 'remind_later_date= ' + null + ',';
        }

        updateQuery += ` retry_count = ? , next_bill_fetch_date = ? , reason = ? `;

        try {
            if (typeof extra == 'string') {
                extra = JSON.parse(extra)
            }
            if (typeof extra == 'string') {
                extra = JSON.parse(extra)
            }
        } catch (e) {
            self.L.log('error in parsing extra in updateBillForSameRNandCID', e)
        }
        if (_.get(extra, 'BBPSBillFetch', true) == false && isCreditCardOperator) {
            extra = JSON.stringify(extra)
            updateQuery = `UPDATE ${tableName} SET `;
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + `'${extra}'` + ', ' : '';
            updateQuery += 'next_bill_fetch_date = ' + null + ' ';
        }

        if (isCreditCardOperator) {
            whereClause = `WHERE id = ${id}`;
        } else {
            whereClause = `WHERE recharge_number = '${params.rechargeNumber}' AND customer_id=${params.customerId} AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`;
        }

        updateQuery += whereClause;
        let updateQueryArr = [
            params.retryCount,
            params.nextBillFetchDate,
            params.reason
        ];

        L.log('updateBillForSameRNandCID:', updateQuery, updateQueryArr);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillForSameRNandCID'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillForSameRNandCID`]);
                L.critical(' updateBillForSameRNandCID::', 'error occurred while dumping data in DB: ', updateQuery, err);
            }
            else {
                L.log(` updateBillForSameRNandCID:: Updated with values`,  `table_name: ${tableName}`, `recharge_number: ${params.rechargeNumber}`, `product_id: ${params.productId}, operator: ${params.operator}, amount: ${enc_amount}, billDueDate: ${enc_due_date}, nextBillFetchDate: ${params.nextBillFetchDate}, status: ${status}, NBFD: ${params.nextBillFetchDate}, errorMsgCode: ${params.errorMsgCode}, bill_date: ${params.billDate}, source: ${params.source}`);
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_MASTER', updateQuery, updateQueryArr)


    }

    updateBillForSameRNandDiffCID(cb, tableName, params) {
        //amount, due_date , status
        let self = this,
            // extra = JSON.stringify(_.get(params, 'extra', {})),
            userData = JSON.stringify(_.get(params, 'user_data', {})),
            lastPaymentDate = _.get(params, 'lastPaymentDate', null),
            lastBillFetchDate = _.get(params, 'lastBillFetchDate', null),
            customerOtherInfo = JSON.stringify(_.get(params, 'customerOtherInfo', {})),
            due_date = _.get(params, 'commonDueDate', null),
            status = _.get(params, 'commonStatus', null),
            notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            skipUpdateOnStatus = [
                notInUseStatus, disabledStatus
            ],
            amount = _.get(params, 'commonAmount', null),
            remindLaterDate = _.get(params,'remind_later_date',null),
            // is_automatic = _.get(params, 'is_automatic', null)!== null ? _.get(params, 'is_automatic', null) : _.get(params, 'is_automatic_diffCustId', null)== 0 ? 0: 2,
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            id = _.get(params, 'id', null),
            billFetchDate = _.get(params, 'billFetchDate', null),
            whereClause = '',
            updateQuery = `UPDATE ${tableName} SET `;

            let recon_id = utility.generateReconID(_.get(params,'rechargeNumber',null),_.toLower(params.service) == "financial services" ?  _.get(params , 'bankName' , _.get(params , 'bank_name' , null)): params.operator ,amount , due_date,params.billDate)
            let extra = utility.setReconIdInString(params.extra , recon_id)
          
            try{
                if(typeof extra == 'string'){
                    extra = JSON.parse(extra)
                    _.set(extra , 'source_subtype_2' , 'FULL_BILL')
                    extra = JSON.stringify(extra);
                }else{
                    _.set(extra , 'source_subtype_2' , 'FULL_BILL')
                }
            }catch(e){
                self.L.log('error in parsing extra in updateBillForSameRNandDiffCID',e)
            }
            extra = JSON.stringify(extra)
            let is_automatic=null;
            if(_.get(params, 'is_automatic', null)!==null && (_.get(params, 'is_automatic', null)==1 || _.get(params, 'is_automatic', null)==5 || _.get(params, 'is_automatic', null)==8)){
                    is_automatic=2;
            } else if (_.get(params, 'is_automatic', null) !== null && _.get(params, 'is_automatic', null) == 3) {
                is_automatic = 4;
            }
        
        if(self.includedOperator.includes(params.operator)){
            return cb(null);
        }
        if(status === _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16)) {
            updateQuery += 'status=' + status + ',';
        } else if (((_.get(params, 'updateForOldBill', null) == true) || (lastPaymentDate < lastBillFetchDate && status == 5)) && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += 'amount=' + amount + ',';
            updateQuery += 'status=' + status + ',';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
        } else if (status == 5 && lastPaymentDate && lastBillFetchDate && lastPaymentDate >= lastBillFetchDate && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'status=' + status + ',';
        } else {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += 'amount=' + amount + ',';
            updateQuery += 'status=' + status + ',';
            if(is_automatic != null){
                updateQuery += 'is_automatic=' + is_automatic + ',';
            }
            updateQuery += due_date ? 'due_date = "' + due_date + '",' : 'due_date = ' + due_date + ',' ;
            !_.isNull(params.billDate) ? updateQuery += 'bill_date = "' + params.billDate + '",' : '';
            !_.isNull(billFetchDate) ? updateQuery += 'bill_fetch_date = "' + billFetchDate + '",' : '';
            !_.isEmpty(params.user_data) ? updateQuery += 'user_data =' + userData + ',' : '';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) == null) ? updateQuery += 'old_bill_fetch_date = ' + null + ',' : '';
        }

        L.log("reset remind later date :",_.get(params,'resetRemindLaterDate',false));
        if(_.get(params,'resetRemindLaterDate',false)){
            L.log('resetRemindLaterDate:', 'resetting remind_later_date to null');
            updateQuery += 'remind_later_date = ' + null + ',';
        }

        updateQuery += ` retry_count = ? , next_bill_fetch_date = ? , reason = ? `;  
    
        if (isCreditCardOperator) {
            whereClause = `WHERE id = ${id}`;
        } else {
            whereClause = `WHERE recharge_number = '${params.rechargeNumber}' AND customer_id!=${params.customerId} AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`;
        }
        
        updateQuery += whereClause;
        let updateQueryArr = [
            params.retryCount,
            params.nextBillFetchDate,
            params.reason
        ];

        L.log('updateBillForSameRNandDiffCID:', updateQuery, updateQueryArr);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillForSameRNandDiffCID'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillForSameRNandDiffCID`]);
                L.critical(' updateBillForSameRNandDiffCID::', 'error occurred while dumping data in DB: ', updateQuery, err);
            }
            else {
                L.log(` updateBillForSameRNandDiffCID:: Updated with values',  '[table_name: ${tableName}, recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, operator: ${params.operator}, amount: ${amount}, billDueDate: ${due_date}, nextBillFetchDate: ${params.nextBillFetchDate}, user_data: ${params.user_data}, status: ${status}, NBFD: ${params.nextBillFetchDate}, errorMsgCode: ${params.errorMsgCode}, bill_date: ${params.billDate}, source: ${params.source}]`)
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_MASTER', updateQuery, updateQueryArr)


    }

    updateBillForSameRechargeNum(cb, tableName, params) {
        //amount, due_date , status
        let self = this,

            //extra = JSON.stringify(_.get(params, 'extra', {})),
            userData = JSON.stringify(_.get(params, 'user_data', {})),
            customerOtherInfo = JSON.stringify(_.get(params, 'customerOtherInfo', {})),
            due_date = _.get(params, 'commonDueDate', null),
            status = _.get(params, 'commonStatus', null),
            notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            skipUpdateOnStatus = [
                notInUseStatus, disabledStatus
            ],
            amount = _.get(params, 'commonAmount', null),
            is_automatic = _.get(params, 'is_automatic', null),
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            id = _.get(params, 'id', null),
            billFetchDate = _.get(params, 'billFetchDate', null),
            whereClause = '',
            updateQuery = `UPDATE ${tableName} SET `;

        if(status === _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16)) {
            updateQuery += 'status=' + status + ',';
        } else {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += 'amount=' + amount + ',';
            updateQuery += 'status=' + status + ',';
            if(is_automatic != null){
                updateQuery += 'is_automatic=' + is_automatic + ',';
            }
            updateQuery += due_date ? 'due_date = "' + due_date + '",' : 'due_date = ' + due_date + ',' ;
            !_.isNull(params.billDate) ? updateQuery += 'bill_date = "' + params.billDate + '",' : '';
            !_.isNull(billFetchDate) ? updateQuery += 'bill_fetch_date = "' + billFetchDate + '",' : '';
            !_.isEmpty(params.user_data) ? updateQuery += 'user_data =' + userData + ',' : '';
            //!_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
        }
        updateQuery += ` retry_count = ? , next_bill_fetch_date = ? , reason = ? `;    
        
        if (isCreditCardOperator) {
            whereClause = `WHERE id = ${id}`;
        }
        else if(self.includedOperator.includes(params.operator)){
            whereClause = `WHERE customer_id = ${params.customerId} AND recharge_number = '${params.rechargeNumber}' AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`
        }
        else {
            whereClause = `WHERE recharge_number = '${params.rechargeNumber}' AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`;
        }

        updateQuery += whereClause;
        let updateQueryArr = [
            params.retryCount,
            params.nextBillFetchDate,
            params.reason
        ];

        L.log('updateBillForSameRechargeNum:', updateQuery, updateQueryArr);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillForSameRechargeNum'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillForSameRechargeNum`]);
                L.critical(' updateBillForSameRechargeNum::', 'error occurred while dumping data in DB: ', updateQuery, err);
            }
            else {
                L.log(` updateBillForSameRechargeNum:: Updated with values',  '[table_name: ${tableName}, recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, operator: ${params.operator}, amount: ${amount}, billDueDate: ${due_date}, nextBillFetchDate: ${params.nextBillFetchDate}, user_data: ${params.user_data}, status: ${status}, NBFD: ${params.nextBillFetchDate}, errorMsgCode: ${params.errorMsgCode}, bill_date: ${params.billDate}, source: ${params.source}]`)
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_MASTER', updateQuery, updateQueryArr)
    }

    updateBillSource(cb, tableName, updated_source, updated_data_source, customerId, rechargeNumber) {
          let self = this,
              updateQuery = `UPDATE ${tableName} SET extra =  JSON_SET(COALESCE(extra,'{}' ) ,'$.updated_source', '${updated_source}','$.updated_data_source','${updated_data_source}') WHERE customer_id = ${customerId} AND recharge_number = '${rechargeNumber}'`;
         self.dbInstance.exec(function (err, data) {
            cb();
        }, 'DIGITAL_REMINDER_MASTER', updateQuery);
    }

    resetIsAutomatic(cb, tableName, rechargeNumber, automaticStatus, optionalParams={}) {
        let self = this;
        self.L.log('resetIsAutomatic::', "tableName:" + tableName + ", rechargeNumber:" + rechargeNumber + ", automaticStatus: " + automaticStatus);

        let updateQuery = `UPDATE ${tableName} SET is_automatic = ? WHERE recharge_number = '${rechargeNumber}' AND is_automatic = ?;`;
        if (!_.isEmpty(optionalParams) && optionalParams.isDemergerCase && optionalParams.customerId) {
            updateQuery += ` AND customer_id = ${optionalParams.customerId}`;
        }
        if (automaticStatus.indexOf(3) != -1) {
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (err, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'resetIsAutomatic3' });
                if (err) {
                    cb(err);
                } else {
                    if (automaticStatus.indexOf(4) != -1) {
                        latencyStart = new Date().getTime();
                        self.dbInstance.exec(function (err, data) {
                            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'resetIsAutomatic4' });
                            cb(err);
                        }, 'DIGITAL_REMINDER_MASTER', updateQuery, [2, 4]);
                    }
                }
            }, 'DIGITAL_REMINDER_MASTER', updateQuery, [1, 3]);
        }
    }

    updateBill(cb, tableName, params) {
        let self = this,
            disabled_status = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            userData = !_.isEmpty(params.user_data) ? JSON.stringify(params.user_data, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace("\'", " ");
                else
                    return value
            }) : '',
            extra = JSON.stringify(_.get(params, 'extra', {})),
            customerOtherInfo = JSON.stringify(_.get(params, 'customerOtherInfo', {})),
            due_date = params.dbDueDate,
            status = params.dbStatus,
            updateQuery = `UPDATE ${tableName} SET `;
        
        let recon_id = utility.generateReconID(params.rechargeNumber , _.toLower(params.service) == "financial services" ?  _.get(params , 'bankName' , _.get(params , 'bank_name' , null)): params.operator , params.amount , due_date , params.billDate)
        extra = utility.setReconIdInString(extra , recon_id)
        extra = JSON.stringify(extra)

        if (!_.isNull(params.billDueDate) && due_date != params.billDueDate) {
            (params.status && status != disabled_status) ? updateQuery += 'status = ' + params.status + ',' : '';
            !_.isNull(params.amount) && !_.isNaN(params.amount) ? updateQuery += 'amount =' + params.amount + ',' : "";
            updateQuery += 'due_date = "' + params.billDueDate + '",';
        }
        if (!_.isNull(params.billDueDate) && due_date == params.billDueDate && status != _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11)) {
            (params.status) ? updateQuery += 'status = ' + params.status + ',' : '';
            !_.isNull(params.amount) && !_.isNaN(params.amount) ? updateQuery += 'amount =' + params.amount + ',' : "";
        }
        if (_.isNull(params.billDueDate) && (params.status == 6 || status != _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11))) {
            (params.status) ? updateQuery += 'status = ' + params.status + ',' : '';
            updateQuery += 'amount =' + params.amount + ',';
        }

        updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';

        !_.isNull(params.billDate) ? updateQuery += 'bill_date = "' + params.billDate + '",' : '';
        !_.isEmpty(params.user_data) ? updateQuery += 'user_data =' + userData + ',' : '';
        !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
        updateQuery += ' retry_count = ?, next_bill_fetch_date = ?, bill_fetch_date = ?, reason = ? WHERE recharge_number = ? AND operator = ? AND service = ? AND customer_id = ?';
        //updateQuery += ' retry_count = ?, next_bill_fetch_date = ?, bill_fetch_date = ?, reason = ? WHERE recharge_number = ? AND product_id = ? AND customer_id = ?';

        let updateQueryArr = [
            params.retryCount,
            params.nextBillFetchDate,
            params.billFetchDate,
            params.reason,
            params.rechargeNumber,
            //params.productId,
            params.operator,
            params.service,
            params.customerId
        ];
        L.log('updateBill:', updateQuery, updateQueryArr);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBill`]);
                L.critical('new updateBill::', 'error occurred while dumping data in DB: ', updateQuery, err);
            }
            else {
                L.log(`updateBill:: Updated with values',  '[table_name: ${tableName}, recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, operator: ${params.operator}, amount: ${params.amount}, billDueDate: ${params.billDueDate}, nextBillFetchDate: ${params.nextBillFetchDate}, user_data: ${params.user_data}, status: ${params.status}, NBFD: ${params.nextBillFetchDate}, errorMsgCode: ${params.errorMsgCode}, bill_date: ${params.billDate}, source: ${params.source}]`)
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_MASTER', updateQuery, updateQueryArr)

    }

    updateBillForSameRechargeNumPostpaid(cb, tableName, params) {
        //amount, due_date , status
        let self = this,

            extra = JSON.stringify(_.get(params, 'extra', {})),
            userData = JSON.stringify(_.get(params, 'user_data', {})),
            customerOtherInfo = JSON.stringify(_.get(params, 'customerOtherInfo', {})),
            due_date = _.get(params, 'commonDueDate', null),
            status = _.get(params, 'commonStatus', null),
            notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            skipUpdateOnStatus = [
                notInUseStatus, disabledStatus
            ],
            amount = _.get(params, 'commonAmount', null),
            is_automatic = _.get(params, 'is_automatic', null),
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            id = _.get(params, 'id', null),
            billFetchDate = _.get(params, 'billFetchDate', null),
            whereClause = '',
            updateQuery = `UPDATE ${tableName} SET `;

        let recon_id = utility.generateReconID(params.rechargeNumber, _.toLower(params.service) == "financial services" ? _.get(params, 'bankName', _.get(params, 'bank_name', null)) : params.operator, params.commonAmount, due_date, params.billDate)
        extra = utility.setReconIdInString(extra , recon_id)
        extra = JSON.stringify(extra)

        if(status === _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16)) {
            updateQuery += 'status=' + status + ',';
        } else if ((_.get(params, 'updateForOldBillAmountDiff', null) == true) && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += 'amount=' + amount + ',';
            updateQuery += 'status=' + status + ',';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
        } else if ((_.get(params, 'updateForOldBillAmountSame', null) == true) && self.oldBillFetchDueDateAllowedService.includes(_.toLower(params.service))) {
            updateQuery += 'status=' + status + ',';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
        } else {
            updateQuery += 'customerOtherInfo =' + customerOtherInfo + ',';
            updateQuery += 'amount=' + amount + ',';
            updateQuery += 'status=' + status + ',';
            if(is_automatic != null){
                updateQuery += 'is_automatic=' + is_automatic + ',';
            }
            updateQuery += due_date ? 'due_date = "' + due_date + '",' : 'due_date = ' + due_date + ',' ;
            !_.isNull(params.billDate) ? updateQuery += 'bill_date = "' + params.billDate + '",' : '';
            !_.isNull(billFetchDate) ? updateQuery += 'bill_fetch_date = "' + billFetchDate + '",' : '';
            !_.isEmpty(params.user_data) ? updateQuery += 'user_data =' + userData + ',' : '';
            !_.isEmpty(extra) ? updateQuery += 'extra = ' + extra + ',' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) != null) ? updateQuery += 'old_bill_fetch_date = "' + params.oldBillFetchDate + '",' : '';
            ((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1) && _.get(params, 'oldBillFetchDate', null) == null) ? updateQuery += 'old_bill_fetch_date = ' + null + ',' : '';
        }

        L.log("reset remind later date :",_.get(params,'resetRemindLaterDate',false));
        if(_.get(params,'resetRemindLaterDate',false)){
            L.log('resetRemindLaterDate:', 'resetting remind_later_date to null');
            updateQuery += 'remind_later_date = ' + null + ',';
        }

        updateQuery += ` retry_count = ? , next_bill_fetch_date = ? , reason = ? `;    
        
        if (isCreditCardOperator) {
            whereClause = `WHERE id = ${id}`;
        }
        else if(self.includedOperator.includes(params.operator) || params.isDemergerCase || (_.get(params, 'isPrepaid', 0) == 1 && _.get(params, 'is_automatic', 0) == 0)){
            whereClause = `WHERE customer_id = ${params.customerId} AND recharge_number = '${params.rechargeNumber}' AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`
        }
        else {
            whereClause = `WHERE recharge_number = '${params.rechargeNumber}' AND operator = '${params.operator}' AND service= '${params.service}' AND status not in (${skipUpdateOnStatus})`;
        }

        updateQuery += whereClause;
        let updateQueryArr = [
            params.retryCount,
            params.nextBillFetchDate,
            params.reason
        ];

        L.log('updateBillForSameRechargeNumPostpaid:', updateQuery, updateQueryArr);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillForSameRechargeNumPostpaid'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillForSameRechargeNumPostpaid`]);
                L.critical(' updateBillForSameRechargeNumPostpaid::', 'error occurred while dumping data in DB: ', updateQuery, err);
            }
            else {
                L.log(` updateBillForSameRechargeNumPostpaid:: Updated with values',  '[table_name: ${tableName}, recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, operator: ${params.operator}, amount: ${amount}, billDueDate: ${due_date}, nextBillFetchDate: ${params.nextBillFetchDate}, user_data: ${params.user_data}, status: ${status}, NBFD: ${params.nextBillFetchDate}, errorMsgCode: ${params.errorMsgCode}, bill_date: ${params.billDate}, source: ${params.source}]`)
            }
            cb(err, data);

        }, 'DIGITAL_REMINDER_MASTER', updateQuery, updateQueryArr)


    }

    updateBillsNBFD(cb, tableName, record) {
        let self = this;
        let notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)
        let disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7)
        let skipUpdateOnStatus = [
            notInUseStatus, disabledStatus
        ]
        let nextBillFetchDate = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        let updateQuery = `UPDATE ${tableName} SET next_bill_fetch_date=? WHERE recharge_number=? AND operator=? AND service=? AND status not in (${skipUpdateOnStatus})`;
        let queryParams = [
            nextBillFetchDate,
            _.get(record, 'rechargeNumber', null),
            _.get(record, 'operator', null),
            _.get(record, 'service', null)
        ]

        if(record.isDemergerCase) {
            updateQuery += ` AND customer_id = ?`;
            queryParams.push(_.get(record, 'customerId', null));
        }

        let latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateBillsNBFD'});
            if (err || !data) {
                self.L.error('updateBillsNBFD::', `error occurred while updating NBFD for ${record.debugKey}`, err);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:DB_QUERY`, 
                    `STATUS:ERROR`, 
                    `TYPE:UPDATE_BILLS_NBFD`,
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`,
                    `SERVICE:${_.get(record, 'category', 'UNKNOWN')}`
                ]);
                return cb(err);
            } 
            self.L.log('updateBillsNBFD::', `successfully updated NBFD for record ${record.debugKey}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:DB_QUERY`, 
                `STATUS:SUCCESS`, 
                `TYPE:UPDATE_BILLS_NBFD`,
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`,
                `SERVICE:${_.get(record, 'category', 'UNKNOWN')}` 
            ]);
            cb(err);
        }, 'DIGITAL_REMINDER_MASTER', updateQuery, queryParams);
    }

    updateBills(cb, tableName, params, fromRecents = false, fromSource) {
        let
            self = this,
            operator = params.operator,
            service_id = 0,
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            referenceId = _.get(params, 'referenceId', null),
            isCreditCardOperator = _.get(params, 'isCreditCardOperator', null),
            extra = _.get(params, 'extra', null),
            customer_type = _.get(params, 'customer_type', null),
            mandatoryUpdateNBFD = _.get(self.config,['RECENT_BILL_CONFIG', 'OPERATORS', params.operator,'mandatoryUpdateNBFD'], false);
            params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));

        let amount=_.get(params , 'amount',0)
        if(_.get(params,'amountWithinDeviation', false) == true){
            amount = 0
        }
        
        let recon_id = utility.generateReconID(_.get(params , 'rechargeNumber',_.get(params,'recharge_number','')),  _.toLower(params.service) == "financial services" ?  _.get(params , 'bankName' , _.get(params , 'bank_name' , null)): params.operator , amount ,  _.get(params, 'dueDate', null) , _.get(params, 'billDate', null) )
       
        let tempExtra = extra ? JSON.parse(extra) : {customer_type: customer_type };
        tempExtra.recon_id = recon_id
        tempExtra.user_type = "RU"
        //tempExtra.updated_source = fromRecents ? 'transaction' : 'validationSync';
        extra = JSON.stringify(tempExtra);

       
        //     extra = {};
        //     extra.customer_type = _.get(params, 'customerInfo_customer_type', null)
        //     extra = JSON.stringify(extra)
        // } else {
        //     let temp = JSON.parse(extra);
        //     temp.customer_type = customer_type ? customer_type : temp.customer_type;
        //     extra = JSON.stringify(temp);
        // }
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }
        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');

        let is_automatic = null;
        if(_.get(params, 'is_automatic', null) !== null) {
            is_automatic = _.get(params, 'is_automatic', null)
        } else if (_.get(params, 'is_automatic_diffCustId', 0) == 0 || _.get(params, 'is_automatic_diffCustId', null) == null) {
            is_automatic = 0;
        } else if(_.get(params, 'is_automatic_diffCustId', 0) > 2) {
            is_automatic = 4;
        } else {
            is_automatic = 2;
        }
        _.set(params, 'is_automatic', is_automatic);
        
        let query = `UPDATE ${tableName} SET \
                ${(params.amount > 0) ? 'amount = '+params.amount+',' : 'amount=(CASE WHEN (status=' + notInUsedStatus + ') THEN '+params.amount+' WHEN ('+_.get(params,'amountWithinDeviation', false)+'=true) THEN 0 ELSE (IFNULL(amount,0) + '+params.amount+') END),'}
                customer_email=?,
                customer_mobile=?,
                ${fromRecents ? '' : "bill_fetch_date='"+MOMENT().format('YYYY-MM-DD HH:mm:ss')+"',"}
                operator=?,
                ${(fromRecents || (fromSource === 'emiDueService') ) && _.get(params, 'billDate', null) ? "bill_date ='"+_.get(params, 'billDate', null)+"'," : ''}
                service=?,
                paytype=?,
                circle=?,
                service_id = ?,
                payment_channel=?,
                notification_status=?,
                retry_count=?,
                reason=?,
                extra=?,
                ${(service_id == 4) ? 'status='+params.status+' ,next_bill_fetch_date='+params.nextBillFetchDate+'' : 'status= (CASE WHEN amount<=0 THEN '+params.status+' ELSE status END)'}  
                ${(params.paymentDate && !_.isEmpty(params.paymentDate)) ? ", payment_date = '"+_.get(params, 'paymentDate', null)+"'" : ''}
                ${(params.gateway && !_.isEmpty(params.gateway)) ? ", gateway = '"+params.gateway+"'" : ""}
                ${(params.user_data && !_.isEmpty(params.user_data)) ? ", user_data = '"+params.user_data+"'" : ""}
                ${((params.dueDate && !_.isEmpty(params.dueDate)) || params.status == 0) ? ', due_date = '+_.get(params, 'dueDate', null)+'' : ''}
                ${(!mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ", next_bill_fetch_date= (CASE WHEN amount <= 0 and next_bill_fetch_date < now() THEN '" + params.nextBillFetchDate + "' ELSE next_bill_fetch_date END )" : ""}
                ${( mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ", next_bill_fetch_date='" + params.nextBillFetchDate + "'" : ""}
                ${(fromRecents && params.is_automatic) ? ', is_automatic = "' + params.is_automatic + '"' : ''}
                ${( (fromSource === 'emiDueService') || (params.customerOtherInfoToBeUpdated) ) ?  ', customerOtherInfo = '+params.customerOtherInfo+'' : '' }`,
            queryParams = [
                params.customerEmail,
                params.customerMobile,
                params.operator,
                _.toLower(params.service),
                params.paytype,
                params.circle,
                service_id,
                params.paymentChannel,
                _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
                params.retryCount,
                params.reason,
                extra
                // params.productId,
            ];
            

            if(isCreditCardOperator) {
                query += ` WHERE recharge_number = ? AND customer_id = ? AND reference_id = ?`;
                queryParams.push(params.rechargeNumber);
                queryParams.push(params.customerId);
                queryParams.push(referenceId);
            } else {
                query += ` WHERE recharge_number = ? AND operator = ? and service = ? and customer_id = ?`;
                queryParams.push(params.rechargeNumber);
                queryParams.push(params.operator);
                queryParams.push(params.service);
                queryParams.push(params.customerId);
            }

        L.verbose('updateBills',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBills'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBills`]);
                L.critical('updateBills::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    updateReasonAsUserInitDeletion(params, tableName, cb){
        let self = this;

        let query = `UPDATE ${tableName} SET
            status = ?,
            notification_status = ?,
            reason = ? WHERE customer_id = ? AND`
        let queryParam = [params.status, params.notificationStatus, params.reason, params.customer_id];


        [query, queryParam] = self.commonLib.formatQueryAndParams(query, queryParam, params.recharge_number, params.operator);

        query += ` AND service = ? AND operator = ?`;
        queryParam.push(params.service);
        queryParam.push(params.operator);

        const latencyStart = new Date().getTime();
        ASYNC.parallel([
            (cbparallel) => {
                if (self.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator)) {
                    let prepaidTableName = tableName + '_prepaid';
                    let prepaidQuery = `UPDATE ${prepaidTableName} SET 
                        status = ?, 
                        notification_status = ?, 
                        reason = ? WHERE customer_id = ? AND recharge_number = ? AND service = ? AND operator = ?`;
                    self.L.log('getRecordForDeletetion::', self.dbInstance.format(prepaidQuery, queryParam));
                    self.dbInstance.exec(function (error, data) {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                        if (error || !(data)) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                            L.critical('getRecordForDeletetion', 'error : ', error);
                            return cbparallel(error);
                        }
                        else {
                            if (data && data.length > 0) {
                                params.tableName = prepaidTableName;
                            }
                            return cbparallel(null, data);
                        }
                    }, 'DIGITAL_REMINDER_MASTER', prepaidQuery, queryParam);
                }
                else {
                    return cbparallel(null, []);
                }
            },
            (cbparallel) => {
                self.L.log('getRecordForDeletetion::', self.dbInstance.format(query, queryParam));
                self.dbInstance.exec(function (error, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                    if (error || !(data)) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                        L.critical('getRecordForDeletetion', 'error : ', error);
                        return cbparallel(error);
                    }
                    return cbparallel(null, data);
                }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
            }
        ], (err, results) => {
            if (err)
                return cb(err);
            else {
                if (results.length > 1) {
                    let prepaidData = results[0];
                    if (prepaidData && prepaidData.length > 0) {
                        return cb(null, prepaidData);
                    }
                    else {
                        return cb(null, results[1]);
                    }
                }
                else {
                    return cb(null, results[0]);
                }
            }
        });

    }


    updateRecentBill(cb, tableName, params, fromRecents = false) {
        let
            self = this,
            operator = params.operator,
            disabled_status = _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
            notInUsedStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13),
            paymentDoneStatus = _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11),
            prepaidProductId = _.get(self.config, 'COMMON.PREPAID_PRODUCT_ID', []),
            extra = _.get(params, 'extra', null),
            customer_type = _.get(params, 'customer_type', null),
            referenceId = fromRecents ? _.get(params, 'referenceId', null) : null,
            isCreditCardOperator = fromRecents ? _.get(params, 'isCreditCardOperator', null) : null,
            tokenisedCreditCard = fromRecents ? _.get(params, 'tokenisedCreditCard', null) : null,
            parId = fromRecents ? _.get(params, 'parId', null) : null,
            tin = fromRecents ? _.get(params, 'tin', null) : null,
            service_id = 0,
            mandatoryUpdateNBFD = _.get(self.config,['RECENT_BILL_CONFIG', 'OPERATORS', params.operator,'mandatoryUpdateNBFD'], false);

        if (prepaidProductId.indexOf(params.productId) > -1) params.gateway = _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', params.product_id], null);
        else params.gateway = _.get(params, 'gateway', _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])));

        let recon_id = utility.generateReconID(_.get(params , 'rechargeNumber',_.get(params,'recharge_number','')),  _.toLower(params.service) == "financial services" ?  _.get(params , 'bankName' , _.get(params , 'bank_name' , null)): params.operator , params.amount ,  _.get(params, 'dueDate', null) , _.get(params, 'billDate', null) )
        if(!extra) {
            extra = {};
            extra.customer_type = customer_type;
            extra.recon_id = recon_id
            extra.user_type = "RU"
        } else {
            let temp = JSON.parse(extra);
            temp.customer_type = customer_type ? customer_type : temp.customer_type;
            temp.recon_id = recon_id
            temp.user_type = "RU"
            extra = JSON.stringify(temp);
        }
        
        //if params.service_id is not undefined 
        if (params.service_id) {
            service_id = params.service_id;
        }

        var daysToBeAddedWhenNBFDIsLessThanCurrentDay = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', 'NBFD_LESS_THAN_CURRENT_DAY'], 7);
        if(_.toLower(params.service) == 'financial services' && MOMENT(params.nextBillFetchDate) < MOMENT()) {
            params.nextBillFetchDate = MOMENT().add(daysToBeAddedWhenNBFDIsLessThanCurrentDay, 'days').format('YYYY-MM-DD HH:mm:ss');
        }

        let query = `UPDATE ${tableName} SET \
	                 amount=(IFNULL(amount,0) + ?),
                     customer_email=?,
                     customer_mobile=?,
                     notification_status = ?,
                     operator=?, 
                     service=?,
                     paytype=?,
                     circle=?, 
                     payment_channel=?, 
                     payment_date = ?, 
                     user_data = ?,
                     retry_count = ?,
                     reason = ?,
                     extra = ?
                     ${params.status ? ',status = \
                                 CASE \
                                     WHEN status = ' + disabled_status + ' OR (amount>0 AND status!=' + notInUsedStatus + ') THEN status \
                                    ELSE ' + params.status
                + ' END ' : ''}
                    ${params.dueDate ? ',due_date = "' + params.dueDate + '"' : ''}
                    ${((self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(params.service)) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(params.operator)) > -1)) ? ',old_bill_fetch_date = ' + null + '' : ''}
                     ,amount = (CASE WHEN(status= ${paymentDoneStatus} ) THEN 0 ELSE amount END)
                     ${(!mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ', next_bill_fetch_date= (CASE WHEN amount <= 0 THEN "' + params.nextBillFetchDate + '" ELSE next_bill_fetch_date END)' : ''} 
                     ${( mandatoryUpdateNBFD && fromRecents && service_id != 4) ? ', next_bill_fetch_date= "' + params.nextBillFetchDate + '"' : ''} 
                     ${(fromRecents && _.get(params, 'billDate', null)) ? ',bill_date ="' + params.billDate + '"' : ''}`,
            queryParams = [
                params.amount,
                params.customerEmail,
                params.customerMobile,
                params.notificationStatus,
                params.operator,
                params.service,
                params.paytype,
                params.circle,
                params.paymentChannel,
                params.paymentDate,
                params.user_data,
                params.retryCount,
                params.reason,
                params.extra
            ];
            if(this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params) && _.get(params, 'status', null) == 11){
                query+=`,amount = ${_.get(params, 'amount', null)}`;
                query+=`,due_date = null`;
            }

        if(isCreditCardOperator) {
            if (_.get(params,'customerOtherInfo.currentBillAmount',0) || _.get(params, 'customerOtherInfo.current_outstanding_amount', 0)) {

            if (_.get(params,'customerOtherInfo.currentBillAmount',0)) {
                let currentBillAmount   =   utility.getFilteredAmount(_.get(params,'customerOtherInfo.currentBillAmount',0)) +  params.amount ,
                currentMinBillAmount    =   utility.getFilteredAmount(_.get(params,'customerOtherInfo.currentMinBillAmount',0)) + params.amount ;
                    if (currentBillAmount<0) currentBillAmount = 0
                    if (currentMinBillAmount<0) currentMinBillAmount = 0
                    _.set(params,'customerOtherInfo.currentBillAmount',currentBillAmount)
                    _.set(params,'customerOtherInfo.currentMinBillAmount',currentMinBillAmount)
                }

                if(_.get(params, 'customerOtherInfo.current_outstanding_amount',null)) {
                let currentOutstandingAmount =  utility.getFilteredAmount(_.get(params,'customerOtherInfo.current_outstanding_amount',0)) - Math.abs(params.amount);
                if(currentOutstandingAmount<0) currentOutstandingAmount = 0
                _.set(params,'customerOtherInfo.current_outstanding_amount',currentOutstandingAmount)
                }

                query += ` ,customerOtherInfo = ? WHERE recharge_number = ? AND customer_id = ? AND reference_id = ?`;
                queryParams.push(JSON.stringify(_.get(params, 'customerOtherInfo', {})));
            }
            else {
                query += ` WHERE recharge_number = ? AND customer_id = ? AND reference_id = ?`;
            }
            queryParams.push(params.rechargeNumber);
            queryParams.push(params.customerId);
            queryParams.push(referenceId);

            if(tokenisedCreditCard){
                query += `AND par_id = ? AND tin = ?`
                queryParams.push(parId)
                queryParams.push(tin)
            }
        } else {
            query += ` WHERE recharge_number = ? AND operator = ? and service = ? and customer_id = ?`;
            queryParams.push(params.rechargeNumber);
            queryParams.push(operator);
            queryParams.push(params.service);
            queryParams.push(params.customerId);
        }

        L.log('updateRecentBill::',self.dbInstance.format(query,queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateRecentBill'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateRecentBill`]);
                L.critical('updateRecentBill::', 'error occurred while getting data from DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    mergeParams(currentRecord, params, isEncryptionEnabled = false) {
        let self = this;
        let mergedParams = _.cloneDeep(currentRecord);
        let queryParamKeysAndValues = self.getQueryParamsForUpdateBillByPublisher(params, isEncryptionEnabled);
        //there are two arrays present in queryParamKeysAndValues, we need to pick keys from first array and values from second array
        let keys = queryParamKeysAndValues.queryParamKeys;
        let values = queryParamKeysAndValues.queryParamValues;
        for (let i = 0; i < keys.length; i++) {
            _.set(mergedParams, keys[i], values[i]);
        }
        return mergedParams;
    }


    updateBillsByCCPublisherWithEncDecHandling(cb, tableName, currentRecord, params) {
        let self = this;
        _.set(params, 'service', _.get(currentRecord, 'service', null));
        let mergedParams = params;
        //self.L.log('updateBillsByCCPublisherWithEncDecHandling::', 'tableName:', tableName, 'currentRecord:', JSON.stringify(currentRecord), 'mergedParams:', JSON.stringify(mergedParams));
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(currentRecord.service, currentRecord.paytype, currentRecord.customer_id)){
            _.set(mergedParams, 'enc_amount', self.EncryptionDecryptioinHelper.encryptData(_.toString(params.amount)));
            mergedParams = self.mergeParams(currentRecord, params, true);
            if(_.get(currentRecord, 'is_encrypted', 0) == 1){
                return self.updateBillByPublisher(cb, tableName, mergedParams, true);
            }else{
                return self.createAndDeleteCCBillsByPublisher(cb, tableName, mergedParams);
            }
        }else{
            return self.updateBillByPublisher(cb, tableName, params);
        }
    }

    createAndDeleteCCBillsByPublisher(cb, tableName, params) {
        let self = this;
        ASYNC.waterfall([
            next=>{
                _.set(params, 'due_date', MOMENT(_.get(params, 'due_date', null)).isValid() ? MOMENT.utc(_.get(params, 'due_date', null)).format('YYYY-MM-DD HH:mm:ss') : null);
                let encryptedParam = self.EncryptionDecryptioinHelper.getEncryptedParamsFromGenericParams(params, ['recharge_number', 'reference_id', 'customer_email', 'customer_mobile'], self.keysToEncryptForCustomerExtra, self.keysToEncryptForCustomerOtherInfo, self.keysToEncryptForUserData);
                self.L.log('createAndDeleteCCBillsByPublisher::', 'encryptedParam:', encryptedParam);
                self.createCCBillByPublisher(next, tableName, encryptedParam, true);
            },
            (insertId, next)=>{
                let paramsForDeleteRecord = {
                    id: params.id,
                }
                self.deleteDuplicateCards((err)=>{
                    if(err){
                        L.error('createAndDeleteCCBillsByPublisher::', 'error occurred while deleting duplicate records: ', err);
                    }
                    next(err, insertId);
                }, tableName, paramsForDeleteRecord);
            }
        ], function (err, insertId) {
            if (err || !(insertId)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createAndDeleteCCBillsByPublisher`]);
                L.critical('createAndDeleteCCBillsByPublisher::', 'error occurred while dumping data in DB: ', err);
            }
            else {
                L.log('createAndDeleteCCBillsByPublisher::', 'Bills created and deleted via Publisher:: [recharge_number: ' + self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) + ', product_id: ' + params.product_id + ', operator: ' + params.operator + ', nextBillFetchDate: ' + params.next_bill_fetch_date + ', gateway: ' + params.gateway + ', customerId: ' + params.customer_id + ']')
            }
            cb(err, {"newRecordCreated":true, "insertId":insertId});
        })
    }
    /**
     * 
     * @param {*} cb 
     * @param {*} tableName 
     * @param {*} params 
     */
    createCCBillByPublisher(cb, tableName, params) {
        let self=this;
        let insertQuery = `INSERT INTO ${tableName} (
                customer_id, recharge_number, product_id, reference_id, operator, amount,
                bill_date, due_date, bill_fetch_date, next_bill_fetch_date, gateway, paytype, 
                service, circle, customer_mobile, customer_email, payment_channel, retry_count, 
                status, reason, extra, published_date, user_data, notification_status, 
                payment_date, service_id, customerOtherInfo, is_automatic, par_id, tin, 
                bank_name, card_network, data_source, remind_later_date, customer_bucket,
                enc_due_date, enc_amount, is_encrypted, consent_valid_till)
            VALUES (
                ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);
            SELECT LAST_INSERT_ID() AS id;`;
        let insertQueryParams = [
            _.get(params, 'customer_id', null),
            _.get(params, 'recharge_number', null),
            _.get(params, 'product_id', null),
            _.get(params, 'reference_id', null),
            _.get(params, 'operator', null),
            null,
            _.get(params, 'bill_date', null),
            null,
            _.get(params, 'bill_fetch_date', null),
            _.get(params, 'next_bill_fetch_date', null),
            _.get(params, 'gateway', null),
            _.get(params, 'paytype', null),
            _.get(params, 'service', null),
            _.get(params, 'circle', null),
            _.get(params, 'customer_mobile', null),
            _.get(params, 'customer_email', null),
            _.get(params, 'payment_channel', null),
            _.get(params, 'retry_count', null),
            _.get(params, 'status', null),
            _.get(params, 'reason', null),
            _.get(params, 'extra', null),
            _.get(params, 'published_date', null),
            _.get(params, 'user_data', null),
            _.get(params, 'notification_status', null),
            _.get(params, 'payment_date', null),
            _.get(params, 'service_id', null),
            _.get(params, 'customerOtherInfo', null),
            _.get(params, 'is_automatic', null),
            _.get(params, 'par_id', null),
            _.get(params, 'tin', null),
            _.get(params, 'bank_name', null),
            _.get(params, 'card_network', null),
            _.get(params, 'data_source', null),
            _.get(params, 'remind_later_date', null),
            _.get(params, 'customer_bucket', null),
            _.get(params, 'enc_due_date', null),
            _.get(params, 'enc_amount', null),
            _.get(params, 'is_encrypted', null),
            _.get(params, 'consent_valid_till', null)
        ]
        L.verbose('createCCBillByPublisher',self.dbInstance.format(insertQuery,insertQueryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'createCCBillByPublisher'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createCCBillByPublisher`]);
                L.critical('createCCBillByPublisher::', 'error occurred while dumping data in DB: ', err);
                cb(err);
            }else{
                self.L.log('createCCBillByPublisher:: result', data); 
                let insertedId = data[1][0].id;
                cb(null, insertedId);
            }
        }, 'DIGITAL_REMINDER_MASTER', insertQuery, insertQueryParams);

    }

    getQueryParamsForUpdateBillByPublisher(params, queryWithEncryptedParams = false) {
        return {
            "queryParamKeys": [
                "status",
                "retry_count",
                "next_bill_fetch_date",
                "published_date",
                queryWithEncryptedParams ? 'enc_amount' : 'amount',
                "reason",
                "gateway",
            ],
            "queryParamValues": [
                params.status,
                _.get(params, 'retry_count', _.get(params, 'retryCount', 0)),
                _.get(params, 'next_bill_fetch_date', _.get(params, 'nextBillFetchDate', null)),
                _.get(params, 'published_date', _.get(params, 'publishedDate', null)),
                queryWithEncryptedParams ? params.enc_amount : params.amount,
                params.reason,
                params.gateway,
            ]
        }
    }

    updateBillByPublisher(cb, tableName, params, queryWithEncryptedParams = false) {
        let self = this;
            let queryParamKeysAndValues = self.getQueryParamsForUpdateBillByPublisher(params, queryWithEncryptedParams);
            //queryParamKeysAndValues is an object which has two arrays queryParamKeys and queryParamValues, we need to pick keys from first array and values from second array
            let query = `UPDATE ${tableName} SET `;
            let queryParams = [];
            for (let i = 0; i < queryParamKeysAndValues.queryParamKeys.length; i++) {
                query += `${queryParamKeysAndValues.queryParamKeys[i]} = ?, `;
                queryParams.push(queryParamKeysAndValues.queryParamValues[i]);
            }
            query = query.slice(0, -2);
            query += ` WHERE id = ?`;
            queryParams.push(params.id);

            self.L.log('updateBillByPublisher query:: ',self.dbInstance.format(query, queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillByPublisher'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillByPublisher`]);
                L.critical('new updateBill::', 'error occurred while dumping data in DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    fetchFreshRecords(tableName, batchSize, skipRows = 0, params, done) {
        let self = this;
        let customerBucket = _.get(params, 'customerBucket', null);
        let query = `SELECT * FROM ${tableName} WHERE status not in (${params.statuses.join(',')}) AND next_bill_fetch_date > ? AND next_bill_fetch_date < ? AND operator = ? `;
        let queryParams = [
            params.nextBillFetchDateFrom,
            params.nextBillFetchDateTo,
            params.operator
        ];

        if(customerBucket && customerBucket != 'noBucket'){
            query += `AND customer_bucket = ?`;
            queryParams.push(customerBucket);
        }

        query += ` order by next_bill_fetch_date asc LIMIT ?,?`;
        queryParams.push(skipRows);
        queryParams.push(batchSize);

        L.log('fetchFreshRecords', self.dbInstance.format(query, queryParams));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_FETCH_QUERY", `BUCKET:${customerBucket}`, `OPERATOR:${params.operator}`, `SERVICE:${params.service}`]);

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchFreshRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchFreshRecords`]);
                L.critical('fetchFreshRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    /*fetchFreshRecordsforCC(tableName, batchSize, skipRows = 0, params, done) {
        let self = this;

        
        let disabledSources = _.get(self.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_PUBLISH'],{})

        let disabledList = []

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                disabledList.push(source)
            }
        })


        let sourceCheckClause = disabledList.length > 0 ? `AND (data_source IS NULL OR data_source NOT IN ("${disabledList}"))` : '';

        let query = `SELECT * FROM ${tableName} WHERE status not in (${params.statuses.join(',')}) 
                   ${sourceCheckClause} AND operator = ? LIMIT ?,?`;

        
        L.verbose('fetchFreshRecords', self.dbInstance.format(query, [
            params.operator,
            skipRows,
            batchSize
        ]));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchFreshRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchFreshRecords`]);
                L.critical('fetchFreshRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, [
            params.operator,
            skipRows,
            batchSize
        ]);
    }*/

    formatRecondCount(count) {
        const thresholds = [
            { limit: 1, label: '0' },
            { limit: 51, label: '1_50' },
            { limit: 101, label: '51_100' },
            { limit: 201, label: '101_200' },
            { limit: 301, label: '201_300' },
            { limit: 401, label: '301_400' },
            { limit: 501, label: '401_500' },
            { limit: 601, label: '501_600' },
        ];

        for (let i = 0; i < thresholds.length; i++) {
            if (count < thresholds[i].limit) {
                return thresholds[i].label;
            }
        }
        return '601_ABOVE';
    }

    fetchFreshRecordsforCC(tableName, batchSize, skipRows = 0, params, done) {
        let self = this;

        
        let disabledSources = _.get(self.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_PUBLISH'],{});

        let disabledList = []

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                disabledList.push(source)
            }
        })


        let sourceCheckClause = disabledList.length > 0 ? `AND (data_source IS NULL OR data_source NOT IN ("${disabledList}"))` : '';
        let consentValidTillClause = `AND consent_valid_till >= now()`;

        let customerBucket = _.get(params, 'customerBucket', null);
        let customerBucketClause = '';
        if(customerBucket && customerBucket != 'noBucket'){
            customerBucketClause = `AND customer_bucket = '${customerBucket}'`;
        }

        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_FETCH_QUERY", `BUCKET:${customerBucket}`, `OPERATOR:${params.operator}`, `SERVICE:${params.service}`]);


        let query = `SELECT * FROM ${tableName} force index (next_bill_fetch_date) WHERE status not in (${params.statuses.join(',')})
                   ${sourceCheckClause} AND next_bill_fetch_date > ? AND next_bill_fetch_date < ? AND operator = ? ${customerBucketClause} and id > ? ${consentValidTillClause} order by id asc LIMIT ?`;
        
        let queryParams = [
            params.nextBillFetchDateFrom,
            params.nextBillFetchDateTo,
            params.operator,
            skipRows,
            batchSize
        ];

        if(['neft_citibank','cc_citibank','cc_axis bank','neft_axis bank','visa_axisbank','neft_axisbank','mastercard_rblbank','cc_rbl bank','visa_rbl','neft_rbl bank','cc_yes bank','visa_yes','neft_yes bank','neft_yes','mastercard_yesbank','cc_dbs bank','visa_bom','cc_bank of maharashtra','cc_indian overseas bank','visa_iob','cc_onecard indian bank','cc_onecard south indian bank','visa_hsbc','neft_hsbc bank','cc_hsbc bank','cc_dhanlaxmi bank'].includes(params.operator)) {
            query = `SELECT * FROM ${tableName} force index (next_bill_fetch_date) WHERE status not in (${params.statuses.join(',')})
                   ${sourceCheckClause} AND (next_bill_fetch_date IS NULL OR next_bill_fetch_date < ?) AND operator = ? ${customerBucketClause} and id > ? ${consentValidTillClause} order by id asc LIMIT ?`;

            queryParams = [
                params.nextBillFetchDateTo,
                params.operator,
                skipRows,
                batchSize
            ];
        }
        
        L.log('fetchFreshRecords', self.dbInstance.format(query, queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchFreshRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchFreshRecords`]);
                L.critical('fetchFreshRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                L.log('fetchFreshRecords:: total records selected', params.operator, data.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:SUCCESS', `TYPE:fetchFreshRecords`, `OPERATOR:${params.operator}`, `VALUE:${self.formatRecondCount(data.length)}`])
                done(null, data);
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

/*    fetchFreshRecordsforCCPublisher(tableName, batchSize, lastId = 0, params, done) {
        let self = this;

        let query = `SELECT * FROM ${tableName} WHERE status not in (7,13) AND operator = ? AND id > ? order by id LIMIT ?`;

        
        L.verbose('fetchFreshRecords', self.dbInstance.format(query, [
            params.operator,
            lastId,
            batchSize
        ]));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchFreshRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchFreshRecords`]);
                L.critical('fetchFreshRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, [
            params.operator,
            lastId,
            batchSize
        ]);
    }*/

    fetchDeadRecords(tableName, batchSize, params, done) {
        let self = this,
            query = `SELECT * FROM ${tableName} WHERE status = ? AND next_bill_fetch_date > ? AND next_bill_fetch_date < ? LIMIT ?`;
        /**  query may be operator specific */
        L.verbose('fetchDeadRecords', query, params);

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchDeadRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchDeadRecords`]);
                L.critical('fetchDeadRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, [
            params.status,
            params.nextBillFetchDateFrom,
            params.nextBillFetchDateTo,
            batchSize
        ]);
    }

    getBillsToNotifyForPP(done, tableName, batchSize, dueDate, offsetId,lastId,currentDate) {
        let self = this;
            let query = `SELECT * FROM ${tableName} WHERE due_date = ? and id> ? order by id limit ?`,
            queryParams = [
                dueDate,
                lastId,
                batchSize,
            ];

        console.log("printing the dueDate for getBillsToNotifyForPP :: ",dueDate);

        L.verbose('getBillsToNotifyForPP', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsToNotifyForPP'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotifyForPP`]);
                L.critical('getBillsToNotifyForPP', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getBillsToNotifyEndOfDay(done, tableName, batchSize, dueDate, offsetId, lastId, currentDate, fetchEncryptedDueDate = false) {
        let self = this;

        let dueDateColumn = "due_date",
            dbDueDate = dueDate;
           
        if(fetchEncryptedDueDate == true) {
            dueDateColumn = "enc_due_date";
            dbDueDate = self.EncryptionDecryptioinHelper.encryptData(MOMENT(dueDate).format('YYYY-MM-DD HH:mm:ss'));
        } 

        let query = `SELECT * FROM ${tableName} WHERE ${dueDateColumn} = ? and id > ? order by id limit ?`,
            queryParams = [
                dbDueDate,
                lastId,
                batchSize
            ];

        console.log("printing the dueDate for getBillsToNotify :: ", dbDueDate); //this will be encrypted for ccbp

        L.verbose('getBillsToNotify', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsToNotifyEndOfDay' });
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotifyEndOfDay`]);
                L.critical('getBillsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                if(fetchEncryptedDueDate == true) {
                    return done(null, self.EncryptionDecryptioinHelper.parseDbResponse(data, -1));
                } else {
                    return done(null, data)
                }
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getBillsToNotifyForOldBillDuePublisher(done, tableName, batchSize, dueDate, offsetId, lastId, currentDate) {
        let self = this;
        let query = `SELECT * FROM ${tableName} WHERE old_bill_fetch_date = ? and id > ? order by id limit ?`,
            queryParams = [
                dueDate,
                lastId,
                batchSize
            ];

        console.log("printing the dueDate for getBillsToNotifyForOldBillDuePublisher :: ", dueDate);

        L.verbose('getBillsToNotify', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsToNotifyForOldBillDuePublisher' });
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotifyForOldBillDuePublisher`]);
                L.critical('getBillsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    /*
        Used to get the records whose bills is due
    */
    getBillsToNotify(done, tableName, batchSize, dueDate, offsetId, lastId, currentDate, fetchEncryptedDueDate = false) {
        let self = this;

        let dueDateColumn = "due_date",
            dbDueDate = dueDate;
           
        if(fetchEncryptedDueDate == true) {
            dueDateColumn = "enc_due_date";
            dbDueDate = self.EncryptionDecryptioinHelper.encryptData(MOMENT(dueDate).format('YYYY-MM-DD HH:mm:ss'));
        }    

        let query = `SELECT * FROM ${tableName} WHERE ${dueDateColumn} = ? and id > ? order by id limit ?`,
            queryParams = [
                dbDueDate,
                lastId,
                batchSize
            ];

        console.log("printing the dueDate for getBillsToNotify :: ",dbDueDate); //this will be encrypted for ccbp

        L.verbose('getBillsToNotify', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getBillsToNotify' });
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotify`]);
                L.critical('getBillsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                if(fetchEncryptedDueDate == true) {
                    return done(null, self.EncryptionDecryptioinHelper.parseDbResponse(data, -1));
                } else {
                    return done(null, data)
                }
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getBillsToNotifyBillGen(done, tableName, batchSize, billGenDate, offsetId, currentDate) {
        let self = this;
        let billGenFromDate = billGenDate;
        let billGenToDate = MOMENT(billGenDate).add(1, 'day').format('YYYY-MM-DD');
        let tablesWithEncryptedData = _.get(self.config,['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG','TABLES_WITH_ENCRYPTED_RECORDS'],['bills_creditcard']),
            IS_CCBP_ENC_ENABLED = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'IS_CCBP_ENC_ENABLED'], false);
        //we will fetch based on the bill_fetch_date
        let query = `SELECT * FROM ${tableName} WHERE bill_fetch_date >= ? and bill_fetch_date < ? limit ? offset ?`,
            queryParams = [
                billGenFromDate,
                billGenToDate,
                batchSize,
                offsetId
            ];
        console.log("printing the BillGenDate :: ",billGenDate);
        L.verbose('getBillsToNotifyBillGen', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsToNotifyBillGen'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotifyBillGen`]);
                L.critical('getBillsToNotifyBillGen', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                if(tablesWithEncryptedData.includes(tableName) && IS_CCBP_ENC_ENABLED == true){
                    return done(null, self.EncryptionDecryptioinHelper.parseDbResponse(data, -1));
                } else {
                    return done(null, data)
                }
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    /*
        Used to get the records where a new bill is fetched today and is due after 3 days,
        or is due tomorrow.
    */
    getBillsWhereBillFetchDateBetween(done, tableName, batchSize, date1, date2, offsetId) {

        let self = this,
            dueDate1 = MOMENT().add(3, 'days').format('YYYY-MM-DD'),
            dueDate2 = MOMENT().add(1, 'days').format('YYYY-MM-DD'),

            query = `SELECT *
                        FROM ${tableName} force INDEX (bill_fetch_date)
                     WHERE 
                            bill_fetch_date >= ?
                        AND bill_fetch_date <= ?
                        AND (
                            (due_date >  ? AND status = ?)
                            OR
                            (due_date = ?) )
                        AND amount > ?
                        AND notification_status != ? AND service_id != ? limit ? offset ?`,
            params = [
                date1,
                date2,
                dueDate1,
                _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
                dueDate2,
                _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0),
                _.get(self.config, 'COMMON.notification_status.DISABLED', 0),
                _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4),
                batchSize,
                offsetId
            ];

        L.verbose('getBillsWhereBillFetchDateBetween', 'query - ', query, params);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsWhereBillFetchDateBetween'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsWhereBillFetchDateBetween`]);
                L.critical('getBillsWhereBillFetchDateBetween', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, params);
    }

    getRecord(done, condition, param, tableName, db = 'DIGITAL_REMINDER_SLAVE') {
        let self = this;
        let query = `SELECT ${param} FROM ${tableName} WHERE ${condition}`;

        L.verbose('getRecord', 'query - ', query);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getRecord'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecord`]);
                L.critical('getRecord', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, db, query, []);
    }

    updateUserData(done, tableName, id, mobile, email) {
        let self = this,
            query = `UPDATE ${tableName} SET customer_mobile=?, customer_email=? WHERE id=?`;
        L.log('updating user data for the id: ' + id);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateUserData'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateUserData`]);
                L.critical('updateUserData', 'error in updating user data: ', error);
            }
            return done();
        }, 'DIGITAL_REMINDER_MASTER', query, [
            mobile,
            email,
            id
        ]);
    }
    updateAutomaticStatusForSameCustId(done, tableName, is_automatic, operator, productId, rechargeNumber, customerId, rechargeCardId) {
    let
        self = this,
        service = _.get(self.config, ['CVR_DATA', productId, 'service'], ''),
        operatorFromConfigs = _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])),
        query=null,
        queryParams = [
            is_automatic,
            _.toLower(operator),
            _.toLower(service),
            rechargeNumber
        ];

    if(is_automatic==0){
        query = `UPDATE ${tableName} SET is_automatic=?  WHERE  operator = ? AND service = ? AND recharge_number = ?`;
    }
    else if(is_automatic==1 || is_automatic==5 || is_automatic==8){
        query = `UPDATE ${tableName} SET is_automatic=?  WHERE  operator = ? AND service = ? AND recharge_number = ? AND customer_id=${customerId}`;
    }
    if(rechargeCardId!=null){
        query += '\
        AND reference_id = ?',
        queryParams.push(rechargeCardId);
    }
    var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateAutomaticStatusForSameCustId'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateAutomaticStatusForSameCustId`]);
                L.critical('update is_automatic', 'error in updating user data: ', error);
            }
            return done();
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }
    updateAutomaticStatusForDiffCustId(done, tableName, is_automatic, operator, productId, rechargeNumber, customerId, rechargeCardId) {
        let
            self = this,
            service = _.get(self.config, ['CVR_DATA', productId, 'service'], ''),
            operatorFromConfigs = _.get(self.config, ['RULEENGINE_CONFIG', operator], _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', operator])),
            query= `UPDATE ${tableName} SET is_automatic=2  WHERE  operator = ? AND service = ? AND recharge_number = ? AND customer_id!=${customerId}`,
            
            queryParams = [
                _.toLower(operator),
                _.toLower(service),
                rechargeNumber
            ];
        
            if(rechargeCardId!=null){
                query += '\
                AND reference_id = ?',
                queryParams.push(rechargeCardId);
            }
            
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateAutomaticStatusForDiffCustId'});
                if (error || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateAutomaticStatusForDiffCustId`]);
                    L.critical('update is_automatic', 'error in updating user data: ', error);
                }
                return done();
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        }
   
    getRecordForDeletetion(params, cb) {
        let self = this,
            service = _.get(params,"service",'').toLowerCase(),
            paytype = _.get(params,"paytype",'').toLowerCase();

        if(self.encDecpUtil.isWhitelistedForCC(service,paytype, parseInt(params.customer_id))) {
            let encryptedRechargeNumber = params.recharge_number;
            if(typeof encryptedRechargeNumber != 'string') encryptedRechargeNumber = self.encDecpUtil.parseToString(encryptedRechargeNumber);
            encryptedRechargeNumber = self.encDecpUtil.encryptData(encryptedRechargeNumber);
            let encryptedParams = _.cloneDeep(params);
            encryptedParams.recharge_number = encryptedRechargeNumber;
            self.getRecordForDeletetionDefault(encryptedParams, (error, data) => {
                if(error) {
                    return cb(error,data);
                } else if(data && data.length == 0) {
                    return self.getRecordForDeletetionDefault(params, cb);
                }

                let decryptedData = self.encDecpUtil.parseDbResponse(data, parseInt(params.customer_id));
                return cb(null, data);
            });
        } else {
            return self.getRecordForDeletetionDefault(params, cb);
        }

    }
        /*
      Fetch Records filtered on recharge_number, customer_id, operator
    */
      getRecordForDeletetionDefault(params, cb) {
        let self = this;
        let query = `SELECT * FROM ${params.tableName} WHERE`;
        let queryParam = [];
        let includedOperator = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_POSTPAIDFLOW', 'COMMON', 'INCLUDE_OPERATOR'],[]);

        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(params.recharge_number, params.operator);

        if(isOperatorPrefixEnabled){
            query += ` recharge_number in (${JSON.stringify(params.recharge_number)}, ${JSON.stringify(alternateRechargeNumber)})`;
        }
        else{
            query += ` recharge_number = ?`;
            queryParam.push(params.recharge_number);
        }

        query += ` AND customer_id = ?`;
        queryParam.push(params.customer_id);
    
        if (params.isCreditCardOperator) {
            query += ` AND service = ? AND ((bank_name = ? AND card_network = ?) OR (operator = ?))`;
            queryParam.push(params.service, params.bankName, params.cardNetwork, params.operator);
        } else {
            query += ` AND operator = ? AND service = ?`;
            queryParam.push(params.operator, params.service);
        }


        let latencyStart = new Date().getTime();

        if (params.paytype == "prepaid" && params.operator == "airtel") {
            self.L.log('getRecordForDeletetion::', self.dbInstance.format(query, queryParam));
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                if (error || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                    L.critical('getRecordForDeletetion', 'error : ', error);
                    return cb(error);
                }
                return cb(null, data);
            }, 'DIGITAL_REMINDER_SLAVE', query, queryParam);
        }
        else if (params.paytype == "prepaid" && includedOperator.indexOf(params.operator) < 0) {
            let read_query = `SELECT * from ${params.tableName} where recharge_number = '${params.recharge_number}' AND customer_id = ${params.customer_id} AND operator = '${params.operator}' AND service = '${params.service}'`;
            self.L.log('getRecordForDeletetion::', self.dbInstance.format(read_query, []));
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                if (error || !(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                    L.critical('getRecordForDeletetion', 'error : ', error);
                    return cb(error);
                }
                return cb(null, data);
            }, 'RECHARGE_ANALYTICS_SLAVE', read_query, []);
        }
        else {
            ASYNC.parallel([
                (cbparallel) => {
                    if (self.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator)) {
                        let prepaidTableName = params.tableName + '_prepaid';
                        let prepaidQuery = `SELECT * FROM ${prepaidTableName} WHERE recharge_number = ? AND customer_id = ? AND operator = ? AND service = ?`;
                        self.L.log('getRecordForDeletetion::', self.dbInstance.format(prepaidQuery, queryParam));
                        self.dbInstance.exec(function (error, data) {
                            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                            if (error || !(data)) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                                L.critical('getRecordForDeletetion', 'error : ', error);
                                return cbparallel(error);
                            }
                            else {
                                if (data && data.length > 0) {
                                    console.log("🚀 ~ data:", data.length)
                                    params.tableName = prepaidTableName;
                                }
                                return cbparallel(null, data);
                            }
                        }, 'DIGITAL_REMINDER_SLAVE', prepaidQuery, queryParam);
                    }
                    else {
                        return cbparallel(null, []);
                    }
                },
                (cbparallel) => {
                    self.logger.log('getRecordForDeletetion::', self.dbInstance.format(query, queryParam), _.get(params, 'service', null));
                    self.dbInstance.exec(function (error, data) {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'GETRECORDFORDELETION' });
                        if (error || !(data)) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForDeletetion`]);
                            L.critical('getRecordForDeletetion', 'error : ', error);
                            return cbparallel(error);
                        }
                        return cbparallel(null, data);
                    }, 'DIGITAL_REMINDER_SLAVE', query, queryParam);
                }
            ], (err, results) => {
                if (err)
                    return cb(err);
                else {
                    let prepaidData = results[0];
                    if (prepaidData && prepaidData.length > 0) {
                        return cb(null, prepaidData);
                    }
                    else {
                        return cb(null, results[1]);
                    }
                }
            });
        }
    }
    
    updateNotificationStatus(cb, tableName, params) {
        let self = this,
            query = `UPDATE ${tableName} SET ${params.stopBillFetch ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : ''} notification_status=? WHERE `,
            queryParam = [params.notificationStatus];

        if (_.get(params, 'X-USER-ID', '')) {
            if (_.get(params, 'rechargeNumber', '')) {
                query = query + `customer_id = ? and recharge_number = ?`;
                queryParam.push(_.get(params, 'X-USER-ID', ''), _.get(params, 'rechargeNumber', ''));
                L.log("updateNotificationStatus", "updating notification status for customer id =", _.get(params, 'X-USER-ID', ''), " rechargeNumber =", params.rechargeNumber, " operator =", params.operator);
            }
            else {
                let error = 'cannot update notification status as recharge number is missing';
                L.error("updateNotificationStatus", error);
                return cb(error, null);
            }
        }
        else {
            if (_.isEmpty(_.get(params, 'rechargeNumber', ''))) {
                query = query + `customer_id = ?`;
                queryParam.push(params.customerId);
            } else if (_.isEmpty(_.get(params, 'customerId', ''))) {
                query = query + `recharge_number = ?`;
                queryParam.push(params.rechargeNumber);
            } else {
                query = query + `customer_id = ? and recharge_number = ?`;
                queryParam.push(params.customerId, params.rechargeNumber);
            }
            L.log("updateNotificationStatus", "updating notification status for customer id =", params.customerId, " rechargeNumber =", params.rechargeNumber, " operator =", params.operator);
        }
        
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateNotificationStatus'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateNotificationStatus`]);
                L.critical("updateNotificationStatus", "error in updating notification status", error);
            }        
            return cb(error, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
    }

    /*
        Disable bill by updating status as 7
    */
    updateBillStatus(cb, tableName, params) {
        let self = this,
            query = `Update ${tableName} SET product_id = ?, status = ? , amount=0,due_date=null,customerOtherInfo=null,extra=null WHERE recharge_number = ? and customer_id = ? and operator = ? AND service = ?`,
            queryParam = [],
            status = _.get(params, "status", null) ? params.status : _.get(this.config, 'COMMON.bills_status.DISABLED', 7);

        queryParam.push(params.productId);
        queryParam.push(status);

        queryParam.push(params.rechargeNumber);
        queryParam.push(params.customerId);
        queryParam.push(params.operator);
        queryParam.push(_.get(self.config, ['CVR_DATA', params.productId, 'service'], ''));

        L.log("updateBillStatus", "disable bill status for customer id=", params.customerId, " rechargeNumber=", params.rechargeNumber, " productId=", params.productId, " operator=", params.operator);
        //L.verbose('updateBillStatus',self.dbInstance.format(query,queryParam));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillStatus'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillStatus`]);
                L.critical("updateBillStatus", "error in udating bill status", error);
            }
            return cb(error, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
    }

    updateAmount(cb, tableName, params) {
        let self = this,
            query = `UPDATE ${tableName}
                     SET
	                amount=? ,
		        bill_fetch_date=? ,
		        status=?
                     WHERE
                        recharge_number = ?`,
            queryParam = [_.get(params, 'amount', 0),
            MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            _.get(params, 'status', 11),
            _.get(params, 'recharge_number')];
        L.log("updateAmount", "updating amount for rechargeNumber =", params.recharge_number, " operator =", params.operator);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateAmount'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateAmount`]);
                L.critical("updateAmount", "error in updating amount", error);
            }
            return cb(error, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
    }

    updateDueDateInReminder(cb, tableName, params) {
        let self = this,
            query = `UPDATE ${tableName} SET  due_date = ? WHERE `,
            queryParam = [params.dueDate];

        if (params.operator && params.service) {

            query = query + `customer_id = ? and recharge_number = ? and operator = ? and service = ?`;
            queryParam.push(params.customerId, params.rechargeNumber, params.operator, params.service);

            L.log("updateDueDateInReminder", `updating due date ${params.dueDate} for customer id =${params.customerId} rechargeNumber=${params.rechargeNumber} pid=${params.productId} operator=${params.operator} service=${params.service}`);

            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateDueDateInReminder'});
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateDueDateInReminder`]);
                    L.critical("updateDueDateInReminder", "error in updating due date", error);
                }
                return cb(error, data);
            }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
        } else {
            L.error("updateDueDateInReminder", `unable to update due date for customer id =${params.customerId} rechargeNumber=${params.rechargeNumber} pid=${params.productId} operator=${params.operator} service=${params.service}`);

            return cb("unable to update due date");
        }

    }

    insertRecordInArchive(cb, tableName, params) {
        if(params.paytype == "prepaid"){
            return cb();
        }

        let self = this,
            query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date, bill_fetch_date,
            service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,due_date,bill_date,extra,published_date,customerOtherInfo,is_automatic,enc_amount, enc_due_date, is_encrypted)                
            VALUES ?
            ON DUPLICATE KEY UPDATE
            amount = VALUES(amount),
            customer_email=VALUES(customer_email),
            customer_mobile=VALUES(customer_mobile),
            bill_fetch_date=VALUES(bill_fetch_date),
            product_id=VALUES(product_id),
            operator=VALUES(operator),
            service=VALUES(service),
            paytype=VALUES(paytype),
            circle=VALUES(circle),
            service_id = VALUES(service_id),
            payment_channel=VALUES(payment_channel),
            notification_status=VALUES(notification_status),
            retry_count=VALUES(retry_count),
            reason=VALUES(reason),
            extra=VALUES(extra),
            status=VALUES(status),
            payment_date = VALUES(payment_date),
            gateway = VALUES(gateway),
            user_data = VALUES(user_data),
            bill_date = VALUES(bill_date),
            published_date = VALUES(published_date),
            customerOtherInfo = VALUES(customerOtherInfo),
            next_bill_fetch_date = VALUES(next_bill_fetch_date),
            due_date = VALUES(due_date),
            is_automatic = VALUES(is_automatic),
            enc_amount = VALUES(enc_amount),
            enc_due_date = VALUES(enc_due_date),
            is_encrypted = VALUES(is_encrypted)`;

        /** Some records have 00 due date which is now not allowed to insert */
        if(params.due_date == "0000-00-00 00:00:00"){
            params.due_date = null;
        }
        
            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertRecordInArchive'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertRecordInArchive`]);
                L.critical("insertRecordInArchive", "error in inserting/updating data", error);
            }
            return cb(error);
        }, 'DIGITAL_REMINDER_MASTER', query, [[[
            params.customer_id,
            params.recharge_number,
            params.product_id,
            params.operator,
            params.gateway,
            params.next_bill_fetch_date,
            params.bill_fetch_date,
            params.service,
            params.paytype,
            params.circle,
            params.customer_mobile,
            params.customer_email,
            params.payment_channel,
            params.amount,
            params.retry_count,
            params.status,
            params.reason,
            params.user_data,
            params.notification_status,
            params.payment_date,
            params.service_id,
            params.due_date,
            params.bill_date,
            params.extra,
            params.published_date,
            params.customerOtherInfo,
            params.is_automatic,
            _.get(params,'enc_amount',null),
            _.get(params,'enc_due_date',null),
            _.get(params,'is_encrypted',null)
        ]]]);
    }

    removeRecord(cb, tableName, params) {
        let self = this,
            query = `DELETE FROM ${tableName} WHERE id = ?`;
        
        let latencyStart = new Date().getTime();
        if(tableName == "plan_validity"){
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'REMOVERECORD'}); 
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:removeRecord`]);
                    L.critical("removeRecord", "error while removing data for id:", params.id, error);
                }
                return cb(error);
            }, 'RECHARGE_ANALYTICS', query, [
                params.id
            ]);
        }
        else{
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'REMOVERECORD'}); 
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:removeRecord`]);
                    L.critical("removeRecord", "error while removing data for id:", params.id, error);
                }
                return cb(error);
            }, 'DIGITAL_REMINDER_MASTER', query, [
                params.id
            ]);
        }
    }

    async deleteAllMatchingMCNs(row) {
        const self = this;
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', row.operator], null);

        if(!tableName){
            tableName = "bills_creditcard";
        }
        const query = `DELETE FROM ${tableName} WHERE recharge_number = '${row.recharge_number}' AND customer_id = '${row.customer_id}' AND reference_id = '${row.reference_id}'`;
        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            self.dbInstance.exec((error, data) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'deleteAllMatchingMCNs'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:deleteAllMatchingMCNs`, `OPERATOR:${row.operator}`]);
                L.critical("deleteAllMatchingMCNs", "error while removing data for :", row.recharge_number, row.customer_id, row.operator, error);
                // reject(error)
            }
            console.log("all deletions done!!")
            resolve(data);
        }, 'DIGITAL_REMINDER_MASTER', query,);
        })
    }

    async deleteCreditCardMCN(row) {
        const self = this;
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', row.operator], null);

        if(!tableName){
            tableName = "bills_creditcard";
        }
        const query = `DELETE FROM ${tableName} WHERE recharge_number = '${row.recharge_number}' AND customer_id = '${row.customer_id}' AND reference_id = '${row.reference_id}'`;
        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            self.dbInstance.exec((error, data) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'deleteCreditCardMCN'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:deleteCreditCardMCN`,`OPERATOR:${row.operator}`]);
                L.critical("deleteCreditCardMCN", "error while removing data for :", row.recharge_number, row.customer_id, row.operator, error);
                // reject(error)
            }
            resolve(data);
        }, 'DIGITAL_REMINDER_MASTER', query,);
        })
    }

    fetchNotInUseRecords(cb, tableName, batchSize, fromId) {
        let
            self = this,
            query = `SELECT * FROM ${tableName} WHERE status = 13 and is_automatic = 0 and  id > ${fromId} ORDER BY id limit ${batchSize}`;
            if(tableName.startsWith('bills_airtelprepaid')){
                query = `SELECT * FROM ${tableName} WHERE status in (131,132,134,135,136,137,140) and notification_status != 0 and  id > ${fromId} ORDER BY id limit ${batchSize}`;
            }
            
            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchNotInUseRecords'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchNotInUseRecords`, `SOURCE:ARCHIVAL_CRONS`]);
                L.critical('fetchNotInUseRecords', 'Error - ', err);
            }
            // Filter the records for `reason = 'userInitDeletion'` where `notification_status = 0`
            let lengthOfFetchRecords =0;
            let lastRecordId =null;
            if(data && _.isArray(data)){
                lengthOfFetchRecords = data.length;
                if (data.length > 0){
                    lastRecordId = data[lengthOfFetchRecords - 1].id;
                }
            }
            const filteredData = data.filter(record => record.notification_status != 0 || (record.notification_status == 0 && record.reason === 'userInitDeletion'));
            cb(null, filteredData, lengthOfFetchRecords, lastRecordId);
        }, 'DIGITAL_REMINDER_SLAVE', query);
    }

    fetchAirtelRecords(tableName, params, query, done) {
        let self = this;

        L.verbose('fetchAirtelRecords::', query, "params: ",params);

        let queryStartTime = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(queryStartTime, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchAirtelRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchAirtelRecords`]);
                L.critical('fetchAirtelRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                let queryEndTime = new Date().getTime();
                let duration = (queryEndTime - queryStartTime)/1000;

                self.L.log('fetchAirtelRecords',`Query took - ${duration} seconds`);
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, [
            params.nextBillFetchDateFrom,
            params.nextBillFetchDateTo,
            params.batchSize

        ]);
    }

    v2fetchAirtelRecords(tableName, params, query, done) {
        let self = this;

        L.verbose('fetchAirtelRecords::', query, "params: ",params);

        let queryStartTime = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(queryStartTime, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'fetchAirtelRecords'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:fetchAirtelRecords`]);
                L.critical('fetchAirtelRecords', 'error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                let queryEndTime = new Date().getTime();
                let duration = (queryEndTime - queryStartTime)/1000;

                self.L.log('fetchAirtelRecords',`Query took - ${duration} seconds`);
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, params);
    }

    updateAirtelRecord(cb, tableName, params, dry_run, viaConsumer=false) {
        let self = this,
            query = `UPDATE ${tableName} SET status = ?, retry_count = ?, next_bill_fetch_date = ?, published_date = ?, extra = ?,reason = ?,customerOtherInfo = ?,due_date=?, ${viaConsumer? 'payment_date = ?,': ''} bill_fetch_date=? WHERE id = ?`,
            queryParams = [
                params.status,
                params.retryCount,
                params.nextBillFetchDate,
                params.publishedDate,
                params.extra,
                params.reason,
                params.customerOtherInfo,
                params.billDueDate,
                ...(viaConsumer?[params.paymentDate]:[]),
                params.billFetchDate,
                params.id
            ];

        L.log('updateAirtelRecord', `Updating record for ${params.traceKey}`, self.dbInstance.format(query, queryParams));

        if (dry_run) return cb();

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateAirtelRecord'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateAirtelRecord`]);
                L.critical('updateAirtelRecord', 'error occurred while dumping data in DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    insertCSVRecord(cb, tableName, params) {
        let
            self = this;

        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,next_bill_fetch_date,
                service,paytype,customer_mobile,customer_email,retry_count,status, notification_status, service_id)                
                VALUES ?`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                params.product_id,
                params.operator,
                params.next_bill_fetch_date,
                _.toLower(params.service),
                params.paytype,
                params.customer_mobile,
                params.customer_email,
                params.retry_count,
                params.status,
                _.get(params, 'notification_status', 0),
                _.get(params, 'service_id', 0)
            ]]];

        L.verbose('insertCSVRecord-insert', self.dbInstance.format(query, queryParams));

        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertCSVRecord'});

            if (err && err.code && err.code == 'ER_DUP_ENTRY') {
                // Looks like record already exists...updating it...
                query = `UPDATE ${tableName} SET next_bill_fetch_date='${params.next_bill_fetch_date}',due_date=NULL,bill_fetch_date=NULL,customer_email='${params.customer_email}',customer_mobile='${params.customer_mobile}',bill_fetch_date=NULL,published_date=NULL,customerOtherInfo=NULL,retry_count=${params.retry_count},status=${params.status},reason=NULL,extra=NULL WHERE recharge_number = '${params.recharge_number}' and customer_id = ${params.customer_id}`;

                L.verbose('insertCSVRecord-Update', query);
                var latencyStart2 = new Date().getTime();
                self.dbInstance.exec(function (err, data) {
                    utility._sendLatencyToDD(latencyStart2, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertCSVRecord'});
                    if (err) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertCSVRecord`]);
                        L.critical('insertCSVRecord-Update', `error occurred while updating data from DB: for ${params.traceKey}`, err);
                    }
                    return cb(err, data);
                }, 'DIGITAL_REMINDER_MASTER', query);
            }
            else if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertCSVRecord`]);
                L.critical('insertCSVRecord-insert', `error occurred while inserting data from DB: for ${params.traceKey}`, err);
                return cb(err, data);
            } else {
                return cb(err, data);
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    insertRecordInArchiveAirtelPrepaid(cb, tableName, params) {
        let self = this,
            query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,gateway,next_bill_fetch_date, bill_fetch_date,
            service,paytype,circle,customer_mobile,customer_email,amount,retry_count,status,reason, notification_status, payment_date, service_id,due_date,extra,published_date,customerOtherInfo)                
            VALUES ?
            ON DUPLICATE KEY UPDATE
            amount = VALUES(amount),
            customer_email=VALUES(customer_email),
            customer_mobile=VALUES(customer_mobile),
            bill_fetch_date=VALUES(bill_fetch_date),
            product_id=VALUES(product_id),
            operator=VALUES(operator),
            service=VALUES(service),
            paytype=VALUES(paytype),
            circle=VALUES(circle),
            service_id = VALUES(service_id),
            notification_status=VALUES(notification_status),
            retry_count=VALUES(retry_count),
            reason=VALUES(reason),
            extra=VALUES(extra),
            status=VALUES(status),
            payment_date = VALUES(payment_date),
            gateway = VALUES(gateway),
            published_date = VALUES(published_date),
            customerOtherInfo = VALUES(customerOtherInfo),
            next_bill_fetch_date = VALUES(next_bill_fetch_date),
            due_date = VALUES(due_date)`;

            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertRecordInArchiveAirtelPrepaid'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertRecordInArchiveAirtelPrepaid`]);
                L.critical("insertRecordInArchiveAirtelPrepaid", "error in inserting/updating data", error);
            }
            return cb(error);
        }, 'DIGITAL_REMINDER_MASTER', query, [[[
            params.customer_id,
            params.recharge_number,
            params.product_id,
            params.operator,
            params.gateway,
            params.next_bill_fetch_date,
            params.bill_fetch_date,
            params.service,
            params.paytype,
            params.circle,
            params.customer_mobile,
            params.customer_email,
            params.amount,
            params.retry_count,
            params.status,
            params.reason,
            params.notification_status,
            params.payment_date,
            params.service_id,
            params.due_date,
            params.extra,
            params.published_date,
            params.customerOtherInfo
        ]]]);
    }

    findActiveRecords(done, tableName, limit) {
        let
            self = this,
            query = `SELECT group_concat(id) as ids from ${tableName} WHERE status != 13 limit ${limit}`;

            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'findActiveRecords'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:findActiveRecords`]);
                L.critical('findActiveRecords::', `error occurred while getting data from DB: for ${params.traceKey}`, err);
            }
            return done(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query);
    }

    markRecordsInactive(done, tableName, ids) {
        let
            self = this,
            query = `UPDATE ${tableName} set status = 13 WHERE id in (${ids})`;

            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'markRecordsInactive'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:markRecordsInactive`]);
                L.critical('markRecordsInactive::', `error occurred while updating data for ids: ${ids}`, err);
            }
            return done(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query);
    }

    insertHistoricalRecords(cb, params) {
        let self = this,
            query = `INSERT INTO bills_history (customer_id, recharge_number, product_id, operator, amount,
                due_date, bill_date, bill_fetch_date, next_bill_fetch_date, gateway, paytype, service, circle,
                customer_mobile, customer_email, retry_count, reason, extra, published_date, user_data,
                notification_status, payment_date, service_id, customerOtherInfo, is_automatic, status, payment_channel,
                reference_id, is_encrypted, enc_amount, enc_due_date)
                VALUES ?`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                params.product_id,
                params.operator,
                params.amount,
                params.due_date,
                params.bill_date,
                params.bill_fetch_date,
                params.next_bill_fetch_date,
                params.gateway,
                params.paytype,
                params.service,
                params.circle,
                params.customer_mobile,
                params.customer_email,
                params.retry_count,
                params.reason,
                params.extra,
                params.published_date,
                params.user_data,
                params.notification_status,
                params.payment_date,
                params.service_id,
                params.customerOtherInfo,
                params.is_automatic,
                params.status,
                params.payment_channel,
                params.reference_id,
                _.get(params, 'is_encrypted', null),
                _.get(params, 'enc_amount', null),
                _.get(params, 'enc_due_date', null)
            ]]];

            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertHistoricalRecords'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertHistoricalRecords`]);
                L.critical("insertHistoricalRecords :: bills", "error in inserting data", error);
            }
            return cb(error);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }



    insertPrepaidHistoricRecords(cb, params) {
        let self = this,
            query = `INSERT INTO plan_validity_history ( customer_id, recharge_number,  product_id, service, operator, circle, 
                amount, validity_expiry_date, order_date, latest_recharge_date, plan_bucket, category_name, created_at,
                updated_at, cust_mobile, cust_email, rn_customer_id, order_ids, status, extra, notification_status, 
                cust_rech_meta)
                VALUES ?`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                params.product_id,
                params.service,
                params.operator,
                params.circle,
                params.amount,
                params.validity_expiry_date,
                params.order_date,
                params.latest_recharge_date,
                params.plan_bucket,
                params.category_name,
                params.created_at,
                params.updated_at,
                params.customer_mobile,
                params.cust_email,
                params.rn_customer_id,
                params.order_ids,
                params.status,
                params.extra,
                params.notification_status,
                params.cust_rech_meta
            ]]];

            var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'insertPrepaidHistoricRecords'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertPrepaidHistoricRecords`]);
                L.critical("inserPrepaidtHistoricRecords :: Plans", "error in inserting data", error);
            }
            return cb(error);
        }, 'RECHARGE_ANALYTICS' , query, queryParams);
    }


    /**
     * 
     * @param {*} tableName which needs to be updated
     * @param {*} data object containing the required field which needs to be updated along with the fields on which the query will be running
     */
    async markAsPaidForCC(tableName, data, idArray, dbRecords, isEncrypted, notCcEncryptionWhiteListed = false) {
        let self = this;

        if(isEncrypted == true || notCcEncryptionWhiteListed == true) {
            return self.markAsPaidForCCDefault(tableName, data, idArray, isEncrypted);
        } else {
            if(!dbRecords || dbRecords.length == 0) {
                return;
            }
            let finalResponse = {};
            for(let i=0; i<dbRecords.length; i++) {
                try {
                    _.set(dbRecords[i], 'amount', 0);
                    if(_.get(dbRecords[i], 'due_date', null) != null) {
                        let parsedDueDate = MOMENT(_.get(dbRecords[i], 'due_date', null), MOMENT.ISO_8601).utc().format('YYYY-MM-DD HH:mm:ss');
                        _.set(dbRecords[i], 'due_date', parsedDueDate);
                    }
                    let encryptedRecord = self.encDecpUtil.getEncryptedParamsFromGenericParams(dbRecords[i],['recharge_number','reference_id','customer_mobile','customer_email', 'amount', 'due_date']);
                    _.set(encryptedRecord, 'status', 15);
                    _.set(encryptedRecord, 'bank_name', data.bankName);
                    _.set(encryptedRecord, 'card_network', data.cardNetwork);
                    
                    _.set(encryptedRecord, 'amount', null);
                    _.set(encryptedRecord, 'due_date', null);
                    let {insertQuery, insertQueryParams} = self.getInsertQueryAndParams(encryptedRecord, tableName);
                    // let {deleteQuery, deleteQueryParams} = self.getDeleteQueryAndParams(dbRecords[0], tableName);
                    
                    let insertResp = await self.updateBillsTable(insertQuery, insertQueryParams);
                    await self.promisifiedDeleteDuplicateCards(tableName, encryptedRecord);
                    // let finalResponse = {};
                    let keys = Object.keys(insertResp);
                    for(let index=0; index<keys.length; index++) {
                        if(keys[index] == 'message') continue;
                        finalResponse[keys[index]] = _.get(finalResponse, keys[index], 0) + insertResp[keys[index]];    
                    }
                    // finalResponse.push(insertResp);
                } catch(err) {
                    return err;
                }
            }
            return finalResponse;
        } 
    }

    promisifiedDeleteDuplicateCards(tableName, params) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.deleteDuplicateCards((error, data) => {
                if(error) return reject(error);
                return resolve(data);
            }, tableName, params);    
        })
    }

    getDeleteQueryAndParams(params, tableName) {
        let deleteQuery = `DELETE FROM ${tableName} WHERE id = ?`;
        let deleteQueryParams = [
            params.id
        ];
        return {deleteQuery, deleteQueryParams};
    }

    getInsertQueryAndParams(params, tableName) {
        let data = _.cloneDeep(params);
        delete data['id'];
        _.set(data, 'is_encrypted', 1);
        _.set(data, 'updated_at', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        let keys = Object.keys(data);
        let values = Object.values(data);

        let insertQuery = `INSERT INTO ${tableName} (${keys.join(',')}) VALUES (${keys.map(() => '?').join(',')})`;
        return {insertQuery, insertQueryParams: values};
    }

    /**
     * 
     * @param {*} tableName which needs to be updated
     * @param {*} data object containing the required field which needs to be updated along with the fields on which the query will be running
     */
     markAsPaidForCCDefault(tableName, data, idArray, isEncrypted = false) {
        try {
            let self = this,
            placeholders = idArray.map(() => '?').join(','),
                query = `UPDATE ${tableName} SET ${isEncrypted == true ? 'enc_amount = ?' : "amount = ?"}, status = ?, bank_name = ?, card_network = ? WHERE id IN (${placeholders})`,
                queryParams = [
                    (isEncrypted == true? self.encDecpUtil.encryptData(self.encDecpUtil.parseToString(_.get(data, 'amount', 0))) : _.get(data, 'amount', 0)),
                    _.get(data, 'status', 15),
                    _.get(data,'bankName',''),
                    _.get(data,'cardNetwork',''),
                    ...idArray
                ];
            
            L.log("markAsPaid", "Marking as Paid for rechargeNumber =", self.encDecpUtil.encryptData(data.rechargeNumber), "operator =", data.operator, "customerID =", data.customerID);
            
            L.log('markAsPaid','query',self.dbInstance.format(query,queryParams));
            return new Promise((resolve, reject) => {
                self.dbInstance.exec((error, data) => {
                    if (error) {
                        L.error("markAsPaid", "error in marking as paid", error);
                        reject(error);
                    }
                    L.log('markAsPaid :: response from DB', data);
                    resolve(data);
                }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
            });
        } catch (error) {
            L.error("markAsPaid", "Error", error, 'data:', JSON.stringify(data), 'table:', tableName);
            throw(error);
        }
    }

    markAsPaid(tableName, data) {
        try {
            let self = this,
                query = `UPDATE ${tableName} SET amount = ?, status = ? WHERE`,
                queryParams = [
                    _.get(data, 'amount', 0),
                    _.get(data, 'status', 15),
                ];

            let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(data.rechargeNumber, data.operator);
            if (isOperatorPrefixEnabled) {
                query += ' recharge_number in (?,?)';
                queryParams.push(alternateRechargeNumber);
                queryParams.push(data.rechargeNumber);
            }else{
                query += ' recharge_number = ?';
                queryParams.push(data.rechargeNumber);
            }
            query += ' AND customer_id = ? AND service = ?';
            queryParams.push(data.customerID);
            queryParams.push(data.service);

            if (data.paytype !== 'prepaid') {
                query += ' AND is_automatic != ?';
                queryParams.push(1);
            }
            if (data.paytype === 'credit card') {
                query += ' AND reference_id = ?';
                queryParams.push(data.referenceID);
            } else {
                query += ' AND operator = ?';
                queryParams.push(data.operator);
            }
            L.log("markAsPaid", "Marking as Paid for rechargeNumber =", data.rechargeNumber, "operator =", data.operator, "customerID =", data.customerID);
            
            L.verbose('markAsPaid','query',self.dbInstance.format(query,queryParams));
            return new Promise((resolve, reject) => {
                var latencyStart = new Date().getTime();
                self.dbInstance.exec((error, data) => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'markAsPaid'});
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:markAsPaid`]);
                        L.error("markAsPaid", "error in marking as paid", error);
                        reject(error);
                    }
                    resolve(data);
                }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
            });
        } catch (error) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:markAsPaid`]);
            L.error("markAsPaid", "Error", error, 'data:', JSON.stringify(data), 'table:', tableName);
            throw(error);
        }
    }

    async getRecordByRechargeNumberCustIdFromBillsTable(params){
        let self = this;
        console.log(`getRecordByRechargeNumberCustIdFromBillsTable:: inside this function`);
        return new Promise((resolve,reject) => {
            
            let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', _.toLower(params.operator)], null);
            if(!tableName) return reject("No table found");

            let query = `SELECT * FROM ${tableName} WHERE recharge_number = ? and customer_id = ?`;
            let queryParams = [ params.rechargeNumber, +params.customerID ];

            self.L.verbose(`getRecordByRechargeNumberCustIdFromBillsTable`,`query:${self.dbInstance.format(query, queryParams)}`)
            
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getRecordByRechargeNumberCustIdFromBillsTable'});
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordByRechargeNumberCustIdFromBillsTable`]);
                    self.L.critical("getRecordByRechargeNumberCustIdFromBillsTable", `Error executing query:${query} for params:${queryParams}:error->${error}`);
                    return reject(error)
                }
                return resolve(data);
            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        });
    }
    ingestRealtimePayloads(params,table,cb){
        let self=this,
        query = `INSERT INTO ${table} (customer_id, recharge_number, operator, service, paytype, product_id, amount, dataConsumed, source_kafka_topic, ruOnboarded, due_date, classifier_id, classifier_name, template_body,status,error_message,source,sender_id,payload) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,
        queryParam= [
            _.get(params,'customer_id',null),
            _.get(params,'recharge_number',null),
            _.get(params,'operator',null),
            _.get(params,'service',null),
            _.get(params,'paytype',null),
            _.get(params,'product_id',null),
            _.get(params,'amount',null),
            _.get(params,'dataConsumed',null),
            _.get(params,'source_kafka_topic',null),
            _.get(params,'ruOnboarded',null),
            _.get(params,'due_date',null),
            _.get(params,'classifier_id',null),
            _.get(params,'classifier_name',null),
            _.get(params,'template_body',null),
            _.get(params,'status',null),
            _.get(params,'error_message',null),
            _.get(params,'source',null),
            _.get(params,'sender_id',null),
            JSON.stringify(_.get(params,'payload',''))
        ];
        L.log('ingestCCPayloads::', 'query', self.dbInstance.format(query, queryParam));
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:ingestRealtimePayloads`]);
                L.critical('ingestCCPayloads::', 'error occurred while inserting data in DB: ', err);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
    }

    insertAnalyticsRecordInDB(params, table){
        let self = this,
        rtsp_id = _.get(params, "rtspId", null),
        ref_id = _.get(params, "refId", null),
        sms_rcvd_time = _.get(params, "smsRcvdTime", null),
        sms_processed_start_time = _.get(params, "smsParsingEntryTime", null),
        sms_processed_end_time = _.get(params, "smsParsingExitTime", null),
        status = _.get(params, "status", null),
        failure_reason = _.get(params, 'failureReason', null),
        notification_publish_time = _.get(params, 'notificationPublishTime', null),
        notification_creation_time = _.get(params, 'notificationCreationTime', null),
        category = _.get(params, "category", null),
        data_source = _.get(params, "dataSource", null),
        customer_id = _.get(params, "customerId", null),

        query = `INSERT INTO ${table} (ref_id, rtsp_id, sms_rcvd_time, sms_processed_start_time, sms_processed_end_time, status, failure_reason, notification_publish_time, notification_creation_time, category, data_source, customer_id) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)` ,
        queryParam=[
            ref_id,
            rtsp_id, 
            sms_rcvd_time, 
            sms_processed_start_time, 
            sms_processed_end_time, 
            status, 
            failure_reason, 
            notification_publish_time, 
            notification_creation_time, 
            category,
            data_source,
            customer_id
        ];
        return new Promise((resolve, reject) =>{            
            self.dbInstance.exec(function (err, data) {
                if (err || !(data)) {
                    L.critical('insertAnalyticsRecordInDB::', 'error occurred while inserting data in DB: ', err);
                }
                if(err){
                    return reject(err);
                }
                return resolve(data);
            }, 'DIGITAL_REMINDER_MASTER', query, queryParam);
        });
    }

    getRecordForRecentsConsumer(cb, params) {
        let self = this;  
        let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', _.toLower(params.operator)], null);
        let notInUseStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13);
        let disabledStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7);
        let skipUpdateOnStatus = [
                notInUseStatus, disabledStatus
            ]
        
        if(!tableName) 
            return cb(`getRecordForRecentsConsumer:: No table found for operator ${params.operator}`);
        let query = `SELECT * FROM ${tableName} WHERE recharge_number = ? and STATUS NOT IN (${skipUpdateOnStatus})`;
        let queryParams = [ params.rechargeNumber ];

        if(params.paytype === 'credit card'){
            if(params.tokenisedCreditCard) {
                if( params.panUniqueReference == null){
                    // add metric
                    return cb('PAR ID not found for credit card')
                }
                query += ` and par_id = ?`
                queryParams.push(params.panUniqueReference)
            } else {
                if( params.referenceId == null){
                    // add metric
                    return cb('reference ID not found for credit card')
                }
                query = ` SELECT * FROM ${tableName} WHERE customer_id = ? and STATUS NOT IN (${skipUpdateOnStatus}) and reference_id = ?`
                queryParams = [params.customerID, params.referenceId];
            }
        }

        self.L.verbose(`getRecordForRecentsConsumer`,`query:${self.dbInstance.format(query, queryParams)}`)
        
        var latencyStart = new Date().getTime();
        self.dbInstance.exec( (error, data) => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getRecordForRecentsConsumer'});
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRecordForRecentsConsumer`]);
                self.L.critical("getRecordForRecentsConsumer", `Error executing query:${query} for params:${queryParams}:error->${error}`);
            }
            return cb(error, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    async updateBillsTable(query,queryParams){
        let self = this;
        return new Promise((resolve,reject) => {
            self.L.verbose(`updateBillsTable`,`query:${self.dbInstance.format(query, queryParams)}`)
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateBillsTable'});
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBillsTable`]);
                    self.L.critical("updateBillsTable", `Error executing query:${query} for params:${queryParams}:error->${error}`);
                    return reject(error)
                }
                return resolve(data);
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        });
    }

    async updatePlanValidityTable(query,queryParams){
        let self = this;
        return new Promise((resolve,reject) => {
            self.L.verbose(`updatePlanValidityTable`,`query:${self.dbInstance.format(query, queryParams)}`)
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updatePlanValidityTable'});
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updatePlanValidityTable`]);
                    self.L.critical("updatePlanValidityTable", `Error executing query:${query} for params:${queryParams}:error->${error}`);
                    return reject(error)
                }
                return resolve(data);
            }, 'RECHARGE_ANALYTICS', query, queryParams);
        });
    }

    getBillData(cb, query, queryParams) {
        let self = this;
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillData'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillData`]);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getPlanValidityData(cb, query, queryParams) {
        let self = this;
        L.verbose('getPlanValidityData','query : ',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getPlanValidityData'});
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getPlanValidityData`]);
            }
            return cb(err, data);
        }, 'RECHARGE_ANALYTICS', query, queryParams);
    }

      /*
        Used to get the records whose bills is due
    */
    getAirtelPrepaidBillsToNotify(done, tableName, batchSize, dueDate, offsetId) {
        let self = this;
            let query = `SELECT * FROM ${tableName} WHERE status = 133 and due_date = ? limit ? offset ?`,
            queryParams = [
                dueDate,
                batchSize,
                offsetId
            ];

        console.log("printing the dueDate for getBillsToNotify :: ",dueDate);

        L.verbose('getBillsToNotify', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsToNotify'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsToNotify`]);
                L.critical('getBillsToNotify', 'error in fetching data from the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

     updateAirtelBills(done, tableName, id) {
        let self = this;
            let query = `UPDATE ${tableName} set status = 140 WHERE id = ? `,
            queryParams = [id];

        console.log("printing the id for updateAirtelBills :: ",id);

        L.verbose('updateAirtelBills', query, queryParams);
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (error, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'updateAirtelBills'});
            if (error || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateAirtelBills`]);
                L.critical('updateAirtelBills', 'error in updating data in the db: ', error);
                return done(error);
            }
            else {
                return done(null, data)
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    prepaidToPostpaidInsertion(cb, tableName, records) {
        if (!records || records.length === 0) {
            return cb(null);
        }
    
        let columns = Object.keys(records[0]).filter(col => col !== 'id');
        let placeholders = records.map(() => `(${columns.map(() => '?').join(', ')})`).join(', ');
        let queryParams = records.reduce((acc, record) => acc.concat(columns.map(col => record[col])), []);
    
        let updateClause = columns.map(col => `${col} = VALUES(${col})`).join(', ');
    
        let query = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES ${placeholders} ON DUPLICATE KEY UPDATE ${updateClause}`;
    
        let latencyStart = new Date().getTime();
        this.dbInstance.exec((err, result) => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'prepaidToPostpaidInsertion'});
            if (err) {
                this.L.error('prepaidToPostpaidInsertion :: Error inserting multiple records:', err);
                return cb(err);
            }
            this.L.log('prepaidToPostpaidInsertion :: Inserted multiple records successfully. affectedRows:', result.affectedRows);
            return cb(null, result);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    deleteRecordsByIds(cb, tableName, ids, rechargeNumber) {
        if (!ids || ids.length === 0) {
            return cb(null);
        }
    
        let placeholders = ids.map(() => '?').join(', ');
        let query = `DELETE FROM ${tableName} WHERE id IN (${placeholders}) AND recharge_number = ?`;
    
        let latencyStart = new Date().getTime();
        this.dbInstance.exec((err, result) => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'deleteRecordsByIds'});
            if (err) {
                this.L.error('deleteRecordsByIds :: Error deleting records by IDs:', err);
                return cb(err);
            }
            this.L.log('deleteRecordsByIds :: Deleted records successfully. affectedRows:', result.affectedRows);
            return cb(null);
        }, 'DIGITAL_REMINDER_MASTER', query, [...ids, rechargeNumber]);
    }

    getBillsOfCustIdServiceOperator(cb, tableName, params){
        let
            self = this,
            customerId = _.get(params, 'customerId'),
            service = _.get(params, 'service'),
            operator = _.get(params, 'operator'),
            limit = _.get(params, 'limit', 50),
            query = ` SELECT * FROM ${tableName} WHERE customer_id = ? AND service = ? AND operator = ? LIMIT ?`,
            queryParams = [
                customerId,
                service,
                _.toLower(operator),
                limit
            ];
        self.L.verbose('getBillsOfCustIdServiceOperator', 'query', self.dbInstance.format(query, queryParams));
        let latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getBillsOfCustIdServiceOperator'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getBillsOfCustIdServiceOperator`]);
                L.critical('getBillsOfCustIdServiceOperator::', 'error occurred while getting data from DB: ', err);
            }
            return cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }
}

export default Bills
