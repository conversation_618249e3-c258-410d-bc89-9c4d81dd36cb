/*
  jshint 
    esversion: 8
 */

'use strict';
import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _ from 'lodash';
import BILL_REMINDER_NOTIFICATION from '../../services/billReminderNotification';
import NOTIFICATION from '../../lib/notification';
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: notification-create service test suite", function () {
    let billReminderNotificationObj,options,
        record = {
            amount : 877,
            product_id : 194,
            recharge_number : "RN",
            user_data : "{\"recharge_number_2\":\"RN2\"}",
            paytype : "Loan Payment",
        },
        payLoad = {
            operator : 'vodafone idea',
            short_operator_name : 'vi',
            recharge_number : "RN",
            amount : 700.98,
            service : "Credit Card",
            category_id : 21
        },
        notificationRecord = {
            template_id : 7126
        }, notificationConfig;

    before(function (done) {
        console.log("reached at line 46")
        STARTUP_MOCK.init(function(error, mock_options){
            console.log("reached at line 49")
            options = mock_options;
            notificationConfig = mock_options.config.NOTIFICATION;
            console.log("reached at line 52-1")
            billReminderNotificationObj = new BILL_REMINDER_NOTIFICATION(options);
            console.log("reached at line 52")
            billReminderNotificationObj.operatorsSendingNotificationToRegisteredUser = _.get(notificationConfig, 'registeredUserNotificationOperator', null);
            billReminderNotificationObj.blackListOperators = _.get(notificationConfig, 'BlackListOperator', null);
            billReminderNotificationObj.blackListCustomers = _.get(mock_options.config, ['DYNAMIC_CONFIG','NOTIFICATION_EXCEPTIONS','BLOCK_CUSTOMER_NOTIFICATIONS','CUSTOMER_ID'],null);
            billReminderNotificationObj.cvrData = _.get(mock_options.config, 'CVR_DATA', {});
            console.log("reached at line 55")

            //billReminderNotificationObj.start();
            done();
        });
    });

    it("configureKafka | Configuration of Kafka publisher and subscriber", (done) => {
        billReminderNotificationObj.configureKafka(function(error){
            expect(error).to.be.equal(null);
            done();
        });
    });

    it("initialize variable",()=>{
        
        billReminderNotificationObj.initializeVariable()
        expect(billReminderNotificationObj.disabledSourcesList[0]).to.be.equal('PG')
    })

    it("validateDataToProcessForNotification | non parsable kafka payload", (done) => {
        let kafkaPayload = { value : ""};
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal("unable to get valid data from kafka payload");
            done();
        },kafkaPayload);
    });

    it("validateDataToProcessForNotification | data_source is disabled ", (done) => {
       
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"neft_sbi","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true,'data_source':"PG"}})
        };
        billReminderNotificationObj.disabledSourcesList=["PG"]
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal("record data source is PG and is disabled");
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | blacklisted operator", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"icici prudential life insurance","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`Blacklisted operator icici prudential life insurance`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | blacklisted customer_id", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":1212,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`Blacklisted customer_id 1212`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | blacklisted customer_id", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":1240772863,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.not.be.equal(`Blacklisted customer_id 1240772863`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | Subscription enabled RN", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":1,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`Subscription exists`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | Not In Use Record (status=13)", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":13,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`Not In use record`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | Invalid notificationType", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"INVALID","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`Notification type not supported/passed`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | BILLGEN Record | amount < allowed amount for notification", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":0,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`amount <= defined amount 0`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | BILLGEN Record | notification status = disabled", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":0,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`notification status:0 disabled`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | BILLGEN Record | Prepaid service Id => no bill gen notifications for this service id", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":4,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(`prepaid service id 4`);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | dueDate_currDate_diff = 1", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":MOMENT().add(1,'days').format("YYYY-MM-DD") + "T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(null);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | dueDate_currDate_diff = 3", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":MOMENT().add(3,'days').format("YYYY-MM-DD") + "T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(null);
            done();
        },kafkaPayload);
    });
    it("validateDataToProcessForNotification | dueDate_currDate_diff = 10 | Valid due date margin", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":MOMENT().add(10,'days').format("YYYY-MM-DD") + "T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(null);
            done();
        },kafkaPayload);
    });

    it("validateDataToProcessForNotification | tata power delhi distribution limited | dueDate = null", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"tata power delhi distribution limited","amount":300,"dueDate": null,"billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error,record){
            expect(error).to.be.equal(null);
            expect(record.due_date).to.be.equal(null);
            expect(record.dueDate).to.be.equal('Invalid date');
            done();
        },kafkaPayload);
    });

    it("validateDataToProcessForNotification | DUEDATE record", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"DUEDATE","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":30,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":4,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal(null);
            done();
        },kafkaPayload);
    });

    it("validateDataToProcessForNotification | DUEDATE record", (done) => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"DUEDATE","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":30,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":0,"service_id":4,"billGen":true}})
        };
        billReminderNotificationObj.validateDataToProcessForNotification(function(error){
            expect(error).to.be.equal('notification status:0 disabled');
            done();
        },kafkaPayload);
    });

    it("getDeepLinkUrl | valid record", () => {
        expect(billReminderNotificationObj.getDeepLinkUrl(record,notificationRecord, payLoad)).to.be.equal("paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?product_id=194$price=877$recharge_number=RN$recharge_number_2=RN2$utm_source=billGenReminder$utm_medium=push$utm_campaign=credit%20card_vi_7126");
    });
    it("getDeepLinkUrl | valid record | cat id to landling not present | service = mobile", () => {
        let _payLoad = { operator : 'vodafone idea',short_operator_name : 'vi',recharge_number : "RN",amount : 700.98,service : "Mobile",category_id : 88998899};
        expect(billReminderNotificationObj.getDeepLinkUrl(record,notificationRecord, _payLoad)).to.be.equal("paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/88998899?$product_id=194$price=877$recharge_number=RN$recharge_number_2=RN2$utm_source=billGenReminder$utm_medium=push$utm_campaign=mobile_vi_7126");
    });
    it("getDeepLinkUrl | valid record | cat id to landling not present | service = datacard", () => {
        let _payLoad = { operator : 'vodafone idea',short_operator_name : 'vi',recharge_number : "RN",amount : 700.98,service : "datacard",category_id : 88998899};
        expect(billReminderNotificationObj.getDeepLinkUrl(record,notificationRecord, _payLoad)).to.be.equal("paytmmp://datacard_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/88998899?product_id=194$price=877$recharge_number=RN$recharge_number_2=RN2$utm_source=billGenReminder$utm_medium=push$utm_campaign=datacard_vi_7126");
    });
    it("getDeepLinkUrl | valid record | cat id to landling not present | service = dth", () => {
        let _payLoad = { operator : 'vodafone idea',short_operator_name : 'vi',recharge_number : "RN",amount : 700.98,service : "DTH",category_id : 88998899};
        expect(billReminderNotificationObj.getDeepLinkUrl(record,notificationRecord, _payLoad)).to.be.equal("paytmmp://dth?url=https://catalog.paytm.com/v1/mobile/getproductlist/88998899?product_id=194$price=877$recharge_number=RN$recharge_number_2=RN2$utm_source=billGenReminder$utm_medium=push$utm_campaign=dth_vi_7126");
    });
    it("getDeepLinkUrl | valid record | cat id to landling not present | any other service", () => {
        let _payLoad = { operator : 'vodafone idea',short_operator_name : 'vi',recharge_number : "RN",amount : 700.98,service : "Rest Service",category_id : 88998899};
        expect(billReminderNotificationObj.getDeepLinkUrl(record,notificationRecord, _payLoad)).to.be.equal("paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/88998899?product_id=194$price=877$recharge_number=RN$recharge_number_2=RN2$utm_source=billGenReminder$utm_medium=push$utm_campaign=rest%20service_vi_7126");
    });

    it("getQueryParams | valid record | default delimeter (&)", () => {
        let data = { operator : 'airtel', amount : 877, product_id : 194, recharge_number : "RN" };
        expect(billReminderNotificationObj.getQueryParams(data)).to.be.equal("product_id=194&price=877&recharge_number=RN");
    });
    it("getQueryParams | valid record | delimeter $", () => {
        let data = { operator : 'airtel', amount : 877, product_id : 194, recharge_number : "RN" };
        expect(billReminderNotificationObj.getQueryParams(data,"$")).to.be.equal("product_id=194$price=877$recharge_number=RN");
    });
    it("getQueryParams | valid record | default delimeter (&) | CC operator", () => {
        let data = { operator : 'paytmfirstcc', amount : 877, product_id : 194, recharge_number : "RN", bank_name : "sbi", card_network : "visa" };
        expect(billReminderNotificationObj.getQueryParams(data)).to.be.equal("recharge_number=RN&bank_name=sbi&card_network=visa");
    });

    it("getQueryParams | valid record | delimeter $ | ignoreAmountInDeeplink is true", () => {
        let data = { operator : 'airtel', amount : 877, product_id : 194, recharge_number : "RN", ignoreAmountInDeeplink: true };
        expect(billReminderNotificationObj.getQueryParams(data,"$")).to.be.equal("product_id=194$price=$recharge_number=RN");
    });

    it('getQueryParams | valid record | delimeter $ | append expandBrowsePlan for "prepaid category" ', () => {
        let data = { operator : 'airtel', amount : 877, product_id : 194, recharge_number : "RN" ,category_id: 17 };
        expect(billReminderNotificationObj.getQueryParams(data,"$")).to.be.equal("product_id=194$recharge_number=RN$expandBrowsePlan=true");
    });

    it("getExtraRechargeNum | valid record | default delimeter (&)", () => {
        expect(billReminderNotificationObj.getExtraRechargeNum(record)).to.be.equal("&recharge_number_2=RN2");
    });
    it("getExtraRechargeNum | valid record | $ delimeter", () => {
        expect(billReminderNotificationObj.getExtraRechargeNum(record,"$")).to.be.equal("$recharge_number_2=RN2");
    });
    it("getExtraRechargeNum | Non parsable user_date", () => {
        expect(billReminderNotificationObj.getExtraRechargeNum({"user_data" : "Invalid json"})).to.be.equal("");
    });

    it("convertKafkaPayloadToRecord | null payload", () => {
        let kafkaPayload = { 
            value : ""
        };
        expect(billReminderNotificationObj.convertKafkaPayloadToRecord(kafkaPayload)).to.be.equal(null);
    });
    it("convertKafkaPayloadToRecord | valid payload", () => {
        let kafkaPayload = { 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"bses yamuna","amount":17853.88,"dueDate":"2020-05-05T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        };
        let result = billReminderNotificationObj.convertKafkaPayloadToRecord(kafkaPayload);

        expect(result.id).to.be.equal(22),
        expect(result.customer_id).to.be.equal(12125505),
        expect(result.recharge_number).to.be.equal('darsh'),
        expect(result.old_product_id).to.be.equal(205364130),
        expect(result.product_id).to.be.equal(205364130),
        expect(result.operator).to.be.equal('bses yamuna'),
        expect(result.amount).to.be.equal(17853.88),
        expect(result.due_date).to.be.equal('2020-05-05T00:00:00.000Z'),
        expect(result.bill_fetch_date).to.be.equal('2020-04-29T15:37:07.000Z'),
        expect(result.next_bill_fetch_date).to.be.equal('2021-03-02T00:00:00.000Z'),
        expect(result.gateway).to.be.equal('bses'),
        expect(result.paytype).to.be.equal('postpaid'),
        expect(result.service).to.be.equal('mobile'),
        expect(result.circle).to.be.equal(''),
        expect(result.customer_mobile).to.be.equal('7887878787'),
        expect(result.customer_email).to.be.equal('<EMAIL>'),
        expect(result.status).to.be.equal(4),
        expect(result.user_data).to.be.equal('{}'),
        expect(result.notification_status).to.be.equal(1),
        expect(result.payment_date).to.be.equal('2020-03-26T17:42:16.000Z'),
        expect(result.service_id).to.be.equal(0),
        expect(result.is_automatic).to.be.equal(0),
        expect(result.source).to.be.equal('reminderBillFetch'),
        expect(result.notificationType).to.be.equal('BILLGEN'),
        expect(result.templates).to.be.equal(null),
        expect(result.debugKey).to.be.equal('id-22,customerId-12125505,rechargeNumber-darsh,operator-bses yamuna')
    });

    it("getParamsForUrl | Valid Record", () => {
        let result = billReminderNotificationObj.getParamsForUrl(record, notificationRecord, payLoad); 
        expect(Object.keys(result).length).to.be.equal(14); // To ack when payload increases or decreases
        expect(result.product_id).to.be.equal(194);
        expect(result.recharge_number).to.be.equal('RN');
        expect(result.user_data).to.be.equal('{\"recharge_number_2\":\"RN2\"}');
        expect(result.amount).to.be.equal(700.98);
        expect(result.product_service).to.be.equal('credit card');
        expect(JSON.stringify(result.notificationRecord)).to.be.equal(JSON.stringify({"template_id":7126}));
        expect(result.paytype).to.be.equal('Loan Payment');
        expect(result.category_id).to.be.equal(21);
        expect(result.short_operator_name).to.be.equal('vi');
        expect(result.operator).to.be.equal('vodafone idea');
    });
    it("getParamsForUrl | Passing all null values to verify null checks to get Values", () => {
        let record = { product_id : null,user_data : null,paytype : null},
            payLoad = {operator : null,recharge_number : null,amount : null,service : null,category_id : null},
            notificationRecord = {};
        let result = billReminderNotificationObj.getParamsForUrl(record, notificationRecord, payLoad); 
        expect(result.product_id).to.be.equal(null);
        expect(result.recharge_number).to.be.equal(null);
        expect(result.user_data).to.be.equal(null);
        expect(result.amount).to.be.equal(null);
        expect(result.product_service).to.be.equal(null);
        expect(JSON.stringify(result.notificationRecord)).to.be.equal(JSON.stringify({}));
        expect(result.paytype).to.be.equal(null);
        expect(result.category_id).to.be.equal(null);
        expect(result.short_operator_name).to.be.equal(null);
        expect(result.operator).to.be.equal(null);
    });

    // it('createUrl | ', () => { 
    //     let paramsForUrl = billReminderNotificationObj.getParamsForUrl(record, notificationRecord, payLoad); 
    //     let [err, url] = billReminderNotificationObj.createUrl(paramsForUrl);
    // })

    
    describe("# getTemplates", function () {
        let record, notificationType, payload, templateIdByService, defaultTemplateId,tableName;

        before(function () {
            record = {
                operator: 'vodafone',
                recharge_number: "9988778899",
                customer_mobile: "9988778899",
                amount: 200
            };

            notificationType = "BILLGEN";
            templateIdByService = notificationConfig.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;

            payload = { 
                service: "mobile", 
            };
            tableName = "bills_vodafone";

        })

        afterEach(function () {
            record = {
                operator: 'vodafone',
                recharge_number: "9988778899",
                customer_mobile: "9988778899",
                service: "mobile",
                amount: 200
            };

            notificationType = "BILLGEN";

            templateIdByService = notificationConfig.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;

            payload = { 
                service: "mobile", 
            };
            tableName = "bills_vodafone";
        });

        it("if operator is vodafone and customer is registered at paytm and notificationType is BILLGEN", function () {
            
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');
            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);
            
            expect(record.ignoreAmountInDeeplink).to.be.undefined;

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            record.bill_date = MOMENT().subtract(5,'day').startOf('day').format('YYYY-MM-DD');
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT);

            // testing for Partial Bills - when both amount and due date are not present
            record.due_date = null;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT_NODUEDATE);

            // testing for Partial Bills - when due date is not present
            record.amount = 200;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NODUEDATE);           
        });


        it("if operator is 'vodafone idea' and customer is registered at paytm and notificationType is BILLGEN", function () {
            record.operator = "vodafone idea";
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');

            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);
            
            expect(record.ignoreAmountInDeeplink).to.be.undefined;

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            record.bill_date = MOMENT().subtract(5,'day').startOf('day').format('YYYY-MM-DD');
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT);

            // testing for Partial Bills - when both amount and due date are not present
            record.due_date = null;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT_NODUEDATE);

            // testing for Partial Bills - when due date is not present
            record.amount = 200;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NODUEDATE);
        });

        it("if operator is not in (vodafone, vodeafone idea) -> customer has registered and notificationType is BILLGEN", function () {
            record.operator = "airtel";
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');
            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);

            expect(record.ignoreAmountInDeeplink).to.be.undefined;

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            record.bill_date = MOMENT().subtract(5,'day').startOf('day').format('YYYY-MM-DD');
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT);

            // testing for Partial Bills - when both amount and due date are not present
            record.due_date = null;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT_NODUEDATE);

            // testing for Partial Bills - when due date is not present
            record.amount = 200;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NODUEDATE);

            record.operator = "jio";
            record.bill_date = null;
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);
            
            expect(record.ignoreAmountInDeeplink).to.be.undefined;

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            record.bill_date = MOMENT().subtract(5,'day').startOf('day').format('YYYY-MM-DD');
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT);

            // testing for Partial Bills - when both amount and due date are not present
            record.due_date = null;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT_NODUEDATE);

            // testing for Partial Bills - when due date is not present
            record.amount = 200;
            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_SMS_NODUEDATE);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_PUSH_NODUEDATE);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_CHAT_NODUEDATE);
            expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_GENERIC_EMAIL_NODUEDATE);
        });

        it("if operator is not in (vodafone, vodeafone idea) -> customer has not registered and notificationType is BILLGEN", function () {
            record.operator = "airtel";
            record.customer_mobile = "1234567890";
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');

            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);
            
            expect(record.ignoreAmountInDeeplink).to.be.undefined;

            record.operator = "jio";
            record.customer_mobile = "1234567890";

            templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_SMS);
            expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_PUSH);
            expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_BILLGEN_CHAT);

            expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_BILLGEN_EMAIL);
            
            expect(record.ignoreAmountInDeeplink).to.be.undefined;
        });

        // it("if operator is vodafone and customer is registered at paytm and notificationType is DUEDATE", function () {
        //     notificationType = "DUEDATE";

        //     let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
            
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;

        //     // testing for Partial Bills - when amount is not present
        //     record.amount = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS_NOAMOUNT);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH_NOAMOUNT);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT_NOAMOUNT);
        //     expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_EMAIL_NOAMOUNT);
        // });

        // it("if operator is 'vodafone idea' and customer is registered at paytm and notificationType is DUEDATE", function () {
        //     record.operator = "vodafone idea";
        //     notificationType = "DUEDATE";

        //     let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
            
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;

        //     // testing for Partial Bills - when amount is not present
        //     record.amount = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS_NOAMOUNT);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH_NOAMOUNT);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT_NOAMOUNT);
        //     expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_EMAIL_NOAMOUNT);
        // });

         
        // it("if operator is not in (vodafone, vodeafone idea) -> customer has registered and notificationType is DUEDATE", function () {
        //     record.operator = "airtel";
        //     notificationType = "DUEDATE";
        //     tableName = "bills_airtel"

        //     let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
        
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;

        //     // testing for Partial Bills - when amount is not present
        //     record.amount = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS_NOAMOUNT);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH_NOAMOUNT);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT_NOAMOUNT);
        //     expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_EMAIL_NOAMOUNT);
        
        //     record.operator = "jio";
        //     record.amount = 200;
        //     tableName = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
            
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;

        //     // testing for Partial Bills - when amount is not present
        //     record.amount = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS_NOAMOUNT);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH_NOAMOUNT);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT_NOAMOUNT);
        //     expect(templates.EMAIL).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_EMAIL_NOAMOUNT);
        // });

        // it("if operator is not in (vodafone, vodeafone idea) -> customer has not registered and notificationType is DUEDATE", function () {
        //     record.operator = "airtel";
        //     record.customer_mobile = "1234567890";
        //     notificationType = "DUEDATE";
        //     tableName = "bills_airtel";

        //     let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
            
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;
        
        //     record.operator = "jio";
        //     tableName = null;
        //     templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
        //     expect(templates.SMS).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_SMS);
        //     expect(templates.PUSH).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_PUSH);
        //     expect(templates.CHAT).to.be.equal(templateIdByService.BR_MOBILE_DUEDATE_CHAT);
        
        //     expect(templates.EMAIL).to.be.equal(defaultTemplateId.BR_DUEDATE_EMAIL);
            
        //     expect(record.ignoreAmountInDeeplink).to.be.undefined;
        // });

        it("if customer has not registerd at paytm for vodafone,'vodafone idea' operators and notificationType is BILLGEN", function () {
            record.customer_mobile = "1234567890";
            record.due_date = MOMENT().add(15,'day').startOf('day').format('YYYY-MM-DD');

            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);

            expect(templates.SMS).to.be.equal(7127);
            expect(templates.PUSH).to.be.equal(7126);
            expect(templates.CHAT).to.be.equal(7573);
            expect(templates.EMAIL).to.be.equal(4597);

            expect(record.recharge_number).to.be.not.equal(record.customer_mobile);
        })

        // it("if customer has not registerd at paytm for vodafone,'vodafone idea' operators and notificationType is DUEDATE", function () {
        //     record.customer_mobile = "1234567890";
        //     notificationType = "DUEDATE";

        //     let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);
            
        //     expect(templates.SMS).to.be.equal(7098);
        //     expect(templates.PUSH).to.be.equal(7099);
        //     expect(templates.CHAT).to.be.equal(7580);
        //     expect(templates.EMAIL).to.be.equal(4602);


        //     expect(record.recharge_number).to.be.not.equal(record.customer_mobile);
        // })

        it("if customer has not registerd at paytm for vodafone,'vodafone idea' operators and notificationType is not DUEDATE and BILLGEN", function () {
            record.customer_mobile = "1234567890";
            notificationType = "PREPAID";

            let templates = billReminderNotificationObj.getTemplates(record, notificationType, payload, tableName);

            expect(templates.SMS).to.be.equal(6335);
            expect(templates.PUSH).to.be.equal(6336);
            expect(templates.CHAT).to.be.equal(null);
            expect(templates.EMAIL).to.be.equal(null);

            expect(record.recharge_number).to.be.not.equal(record.customer_mobile);
        })

        it("should create a notification of type BR_FINANCIAL SERVICES_7_BILLGEN_PUSH_CIR if data_source is CIR", function () {

            let new_record = { id: 2183832,
                customer_id: *********,
                recharge_number: 'XXXX XXXX XXXX 0000',
                old_product_id: 176581875,
                product_id: 176581875,
                operator: 'neft_sbi',
                amount: 171029.26,
                bill_date: '2023-10-25T00:00:00.000Z',
                dataConsumed: null,
                due_date: '2023-11-18T00:00:00.000Z',
                bill_fetch_date: MOMENT().subtract(7,'days'),
                next_bill_fetch_date: '2023-11-22T00:00:00.000Z',
                gateway: 'creditcardneft',
                paytype: 'credit card',
                service: 'financial services',
                circle: '',
                customer_mobile: '9945243142',
                customer_email: '<EMAIL>',
                status: 4,
                user_data: '{"recharge_number_2":"2837287287328378","recharge_number_3":"23812982982938","recharge_number_5":"1235666"}',
                notification_status: 1,
                payment_date: '2022-07-26T22:50:05.000Z',
                service_id: 0,
                customerOtherInfo: '{"customerId":273882378,"subscriberNumber":"4726 42XX XXXX 0088","subscriberName":null,"subscriberEmailId":null,"subscriberDOB":null,"subscriberAltNumber":null,"subscriberAddress":null,"subscriberGender":null,"subscriberCity":null,"minReloadAmount":null,"currentBillAmount":171029.26,"billDueDate":"Nov 14, 2023","billDate":"Oct 25, 2023","complianceRespCd":"","responseCode":"000","complianceReason":"","refId":"68W6UW3J8MI48JTO29AMUEPHT4P33141515","msgId":"9BJQHLMP5VV0ANDUBPN29EFYCSO33141515","customerName":"Gagan","billNumber":"","billPeriod":"","additionalFees":{}}',
                is_automatic: 0,
                source: 'BillGenPublisherRealTime',
                notificationType: 'BILLGEN',
                templates: null,
                skipNotification: null,
                time_interval: null,
                extra: '{"customer_type":1,"last_paid_amount":4000,"errorMessageCode":null,"frontendErrorMessage":null,"lastSuccessBFD":null,"billFetchDate":"2023-11-10 16:57:43","lastDueDt":"0001-01-01 00:00:00","lastBillDt":"0001-01-01 00:00:00","lastAmount":171029.26}',
                bank_name: null,
                card_network: null,
                timestamps: 
                 { billFetchReminder_acknowledgeTime: *************,
                   billFetchReminder_onBoardTime: null },
                debugKey: 'id-2183832,customerId-*********,rechargeNumber-XXXX XXXX XXXX 0000,operator-neft_sbi',
                refId: null,
                rtspId: null,
                data_source: 'CIR' }

            let templates = billReminderNotificationObj.getTemplates(new_record, notificationType, {...payload,service:'financial services'}, "bills_creditcard");
        
            expect(templates.PUSH).to.be.equal(templateIdByService["BR_FINANCIAL SERVICES_7_BILLGEN_PUSH_CIR"]);
            

        });

        it("should create a notification of type BR_FINANCIAL SERVICES_7_BILLGEN_PUSH if data_source is empty", function () {

            let new_record = { id: 2183832,
                customer_id: *********,
                recharge_number: 'XXXX XXXX XXXX 0000',
                old_product_id: 176581875,
                product_id: 176581875,
                operator: 'neft_sbi',
                amount: 171029.26,
                bill_date: '2023-10-25T00:00:00.000Z',
                dataConsumed: null,
                due_date: '2023-11-18T00:00:00.000Z',
                bill_fetch_date: MOMENT().subtract(7,'days'),
                next_bill_fetch_date: '2023-11-22T00:00:00.000Z',
                gateway: 'creditcardneft',
                paytype: 'credit card',
                service: 'financial services',
                circle: '',
                customer_mobile: '9945243142',
                customer_email: '<EMAIL>',
                status: 4,
                user_data: '{"recharge_number_2":"2837287287328378","recharge_number_3":"23812982982938","recharge_number_5":"1235666"}',
                notification_status: 1,
                payment_date: '2022-07-26T22:50:05.000Z',
                service_id: 0,
                customerOtherInfo: '{"customerId":273882378,"subscriberNumber":"4726 42XX XXXX 0088","subscriberName":null,"subscriberEmailId":null,"subscriberDOB":null,"subscriberAltNumber":null,"subscriberAddress":null,"subscriberGender":null,"subscriberCity":null,"minReloadAmount":null,"currentBillAmount":171029.26,"billDueDate":"Nov 14, 2023","billDate":"Oct 25, 2023","complianceRespCd":"","responseCode":"000","complianceReason":"","refId":"68W6UW3J8MI48JTO29AMUEPHT4P33141515","msgId":"9BJQHLMP5VV0ANDUBPN29EFYCSO33141515","customerName":"Gagan","billNumber":"","billPeriod":"","additionalFees":{}}',
                is_automatic: 0,
                source: 'BillGenPublisherRealTime',
                notificationType: 'BILLGEN',
                templates: null,
                skipNotification: null,
                time_interval: null,
                extra: '{"customer_type":1,"last_paid_amount":4000,"errorMessageCode":null,"frontendErrorMessage":null,"lastSuccessBFD":null,"billFetchDate":"2023-11-10 16:57:43","lastDueDt":"0001-01-01 00:00:00","lastBillDt":"0001-01-01 00:00:00","lastAmount":171029.26}',
                bank_name: null,
                card_network: null,
                timestamps: 
                 { billFetchReminder_acknowledgeTime: *************,
                   billFetchReminder_onBoardTime: null },
                debugKey: 'id-2183832,customerId-*********,rechargeNumber-XXXX XXXX XXXX 0000,operator-neft_sbi',
                refId: null,
                rtspId: null,
                data_source: '' }
            // let new_record = JSON.parse(JSON.stringify(record))
            // new_record.operator = "neft_sbi";

            let templates = billReminderNotificationObj.getTemplates(new_record, notificationType, {...payload,service:'financial services'}, "bills_creditcard");
        
            expect(templates.PUSH).to.be.equal(templateIdByService["BR_FINANCIAL SERVICES_7_BILLGEN_PUSH"]);
            

        });

    });
    
    describe("# getTemplateId", function () {
        let record, notificationType, type, payload, templateIdByService, defaultTemplateId;

        it("when data comes in templates key of record in case of airtel publisher", function () {
            record = {
                templates: {
                    SMS: 1234,
                    EMAIL: 2345,
                    PUSH: 3456,
                    CHAT: 3212,
                }
            }

            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload)).to.be.equal(record.templates[type]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload)).to.be.equal(record.templates[type]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload)).to.be.equal(record.templates[type]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload)).to.be.equal(record.templates[type]);
        })

        it("template id is configured in the operator_template_mapping config file | notificationType is DUEDATE", function () {
            record = {
                operator: "tata power delhi distribution limited",
                amount: 1200
            };
            notificationType= "DUEDATE";
            payload = {
                service : 'Electricity'
            }
            let operatorTemplateMappingConfig = options.config.OPERATOR_TEMPLATE_MAPPING;
            let templateIds = operatorTemplateMappingConfig[record.operator];

            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_tatapower";

            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            let noAmount = "_NOAMOUNT";
            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
        })  

        it("template id is configured in the operator_template_mapping config file | notificationType is BILLGEN", function () {
            record = {
                operator: "tata power delhi distribution limited",
                amount: 1200
            };
            notificationType= "BILLGEN";
            payload = {
                service : 'Electricity'
            }
            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_tatapower";
            
            let operatorTemplateMappingConfig = options.config.OPERATOR_TEMPLATE_MAPPING;
            let templateIds = operatorTemplateMappingConfig[record.operator];

            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}`]);

            //testing for Partial Bills - when amount is not present
            record.amount = null;
            record.bill_date = MOMENT( MOMENT().subtract(15,'day').format("YYYY-MM-DD 00:00:00"));
            let noAmount = "_NOAMOUNT";
            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}`]);

            // testing for Partial Bills - when amount and due date both are not present
            dueDate = null;
            let noDueDate = "_NODUEDATE";
            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}${noDueDate}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}${noDueDate}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}${noDueDate}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noAmount}${noDueDate}`]);

            // testing for Partial Bills - when due date is not present
            record.amount = 1200;
            type = "SMS";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noDueDate}`]);
            
            type = "PUSH";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noDueDate}`]);
            
            type = "CHAT";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noDueDate}`]);
            
            type = "EMAIL";
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIds[`${notificationType}_${type}${noDueDate}`]);
        })

        it("when template id is configured on basis of service  | notificationType is BILLGEN", function () {
            record = {
                operator: "vodafone",
                amount: 200
            };
            notificationType= "BILLGEN";
            payload = {
                service: "mobile"
            }
            templateIdByService = options.config.NOTIFICATION.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;

            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_vodafone";
            
            type = "SMS";
            let key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key]|| defaultTemplateId[`BR_${notificationType}_${type}`]));

            // testing for Partial Bills - when amount is not present
            record.bill_date = MOMENT( MOMENT().subtract(15,'day').format("YYYY-MM-DD 00:00:00"));
            record.amount = null;
            let noAmount = "_NOAMOUNT";

            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);

            // testing for Partial Bills - when both amount and due date are not present
            dueDate = null;
            let noDueDate = "_NODUEDATE";
            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noAmount}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);

            // testing for Partial Bills - when due date is not present
            record.amount = 200;
            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}${noDueDate}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
        })

        it("when template id is configured on basis of service  | notificationType is BILLGEN and data source is present", function () {
            let new_record = { id: 2183832,
                customer_id: *********,
                recharge_number: 'XXXX XXXX XXXX 0000',
                old_product_id: 176581875,
                product_id: 176581875,
                operator: 'neft_sbi',
                amount: 171029.26,
                bill_date: '2023-10-25T00:00:00.000Z',
                dataConsumed: null,
                due_date: '2023-11-18T00:00:00.000Z',
                bill_fetch_date: MOMENT().subtract(7,'days'),
                next_bill_fetch_date: '2023-11-22T00:00:00.000Z',
                gateway: 'creditcardneft',
                paytype: 'credit card',
                service: 'financial services',
                circle: '',
                customer_mobile: '9945243142',
                customer_email: '<EMAIL>',
                status: 4,
                user_data: '{"recharge_number_2":"2837287287328378","recharge_number_3":"23812982982938","recharge_number_5":"1235666"}',
                notification_status: 1,
                payment_date: '2022-07-26T22:50:05.000Z',
                service_id: 0,
                customerOtherInfo: '{"customerId":273882378,"subscriberNumber":"4726 42XX XXXX 0088","subscriberName":null,"subscriberEmailId":null,"subscriberDOB":null,"subscriberAltNumber":null,"subscriberAddress":null,"subscriberGender":null,"subscriberCity":null,"minReloadAmount":null,"currentBillAmount":171029.26,"billDueDate":"Nov 14, 2023","billDate":"Oct 25, 2023","complianceRespCd":"","responseCode":"000","complianceReason":"","refId":"68W6UW3J8MI48JTO29AMUEPHT4P33141515","msgId":"9BJQHLMP5VV0ANDUBPN29EFYCSO33141515","customerName":"Gagan","billNumber":"","billPeriod":"","additionalFees":{}}',
                is_automatic: 0,
                source: 'BillGenPublisherRealTime',
                notificationType: 'BILLGEN',
                templates: null,
                skipNotification: null,
                time_interval: null,
                extra: '{"customer_type":1,"last_paid_amount":4000,"errorMessageCode":null,"frontendErrorMessage":null,"lastSuccessBFD":null,"billFetchDate":"2023-11-10 16:57:43","lastDueDt":"0001-01-01 00:00:00","lastBillDt":"0001-01-01 00:00:00","lastAmount":171029.26}',
                bank_name: null,
                card_network: null,
                timestamps: 
                 { billFetchReminder_acknowledgeTime: *************,
                   billFetchReminder_onBoardTime: null },
                debugKey: 'id-2183832,customerId-*********,rechargeNumber-XXXX XXXX XXXX 0000,operator-neft_sbi',
                refId: null,
                rtspId: null,
                data_source: 'CIR' }
            notificationType= "BILLGEN";
            payload = {
                service: "mobile"
            }
            templateIdByService = options.config.NOTIFICATION.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;

            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_creditcard";
            
            type = "SMS";
            let key = `BR_FINANCIAL SERVICES_7_BILLGEN_SMS_CIR`;
            expect(billReminderNotificationObj.getTemplateId(type, new_record, notificationType, {...payload,service:"financial services"}, dueDate, tableName)).to.be.equal(templateIdByService[key]);

            delete new_record['amount']
            key = `BR_FINANCIAL SERVICES_7_BILLGEN_SMS_NOAMOUNT_CIR`;
            templateIdByService[key]=123
            expect(billReminderNotificationObj.getTemplateId(type, new_record, notificationType, {...payload,service:"financial services"}, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            
        })

        it("when template id is configured on basis of service | notificationType is DUEDATE ", function () {
            record = {
                operator: "vodafone", 
                amount: 200
            };
            notificationType= "DUEDATE";
            payload = {
                service: "mobile"
            }
            
            templateIdByService = options.config.NOTIFICATION.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;
            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_vodafone";
        

            type = "SMS";
            let key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key] || defaultTemplateId[`BR_${notificationType}_${type}`]));

            // testing for Partial Bills - when no amount is present 
            record.amount = null;
            let noAmount = "_NOAMOUNT";
            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}${noAmount}`;
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key] || defaultTemplateId[`BR_${notificationType}_${type}${noAmount}`]));
        })

        it("when template id is configured on basis of service | notificationType is DUEDATE | notification before duedate", function () {
            record = {
                operator: "neft_hdfcbank",
                amount: 2000
            };
            notificationType= "DUEDATE";
            payload = {
                service: "financial services"
            }
            
            templateIdByService = options.config.NOTIFICATION.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;
            let dueDate = MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_creditcard";
            let dayValue = "-1_";
            
            type = "SMS";
            let key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key]));

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            let noAmount = "_NOAMOUNT";
            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key]));
        });

        it("when template id is configured on basis of service | notificationType is DUEDATE | notification after duedate  ", function () {
            record = {
                operator: "neft_hdfcbank",
                amount: 2000
            };
            notificationType= "DUEDATE";
            payload = {
                service: "financial services"
            }
            
            templateIdByService = options.config.NOTIFICATION.TEMPLATE_ID_BY_SERVICE;
            defaultTemplateId = notificationConfig.templateid;
            let dueDate = MOMENT( MOMENT().add(7,'day').format("YYYY-MM-DD 00:00:00"));
            let tableName = "bills_creditcard";
            let dayValue = "7_";
            
            type = "SMS";
            let key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key]));

            // testing for Partial Bills - when amount is not present
            record.amount = null;
            let noAmount = "_NOAMOUNT";

            type = "SMS";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "PUSH";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "CHAT";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal(templateIdByService[key]);
            
            type = "EMAIL";
            key = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}${noAmount}`
            expect(billReminderNotificationObj.getTemplateId(type, record, notificationType, payload, dueDate, tableName)).to.be.equal((templateIdByService[key]));
        })
    })
    

    it("_utmByTemplateId | Valid Template", () => {
        expect(billReminderNotificationObj._utmByTemplateId(4597,"vodafone idea")).to.be.equal("&utm_source=billGenReminder&utm_medium=email&utm_campaign=default_vodafone%20idea_4597");
    });
    it("_utmByTemplateId | Valid Template with $ operator", () => {
        expect(billReminderNotificationObj._utmByTemplateId(4597,"vodafone idea","$")).to.be.equal("$utm_source=billGenReminder$utm_medium=email$utm_campaign=default_vodafone%20idea_4597");
    });
    it("_utmByTemplateId | template notFound in TEMPLATE_UTM config", () => {
        expect(billReminderNotificationObj._utmByTemplateId(9999,"vodafone idea")).to.be.equal("&utm_source=billReminderNotFound&utm_medium=null&utm_campaign=default_vodafone%20idea_9999");
    });

    it("getUtmParam | Blank input", () => {
        expect(billReminderNotificationObj.getUtmParam(null)).to.be.equal("");
    });
    it("getUtmParam | null in string", () => {
        expect(billReminderNotificationObj.getUtmParam("null")).to.be.equal("null");
    });
    it("getUtmParam | undefined", () => {
        expect(billReminderNotificationObj.getUtmParam(undefined)).to.be.equal("");
    });
    it("getUtmParam | only 4 spaces", () => {
        expect(billReminderNotificationObj.getUtmParam("      ")).to.be.equal("%20%20%20%20%20%20");
    });
    it("getUtmParam | wefbuf lisuhfiu -> valid input", () => {
        expect(billReminderNotificationObj.getUtmParam("wefbuf lisuhfiu")).to.be.equal("wefbuf%20lisuhfiu");
    });
    it("getUtmParam | 1234 lisuhfiu -> valid input", () => {
        expect(billReminderNotificationObj.getUtmParam("1234 lisuhfiu")).to.be.equal("1234%20lisuhfiu");
    });

    it("getLast4digitsOfCC | Valid CC number", () => {
        expect(billReminderNotificationObj.getLast4digitsOfCC("42xx xxxx 7678")).to.be.equal("7678");
    });
    it("getLast4digitsOfCC | null CC number", () => {
        expect(billReminderNotificationObj.getLast4digitsOfCC(null)).to.be.equal(null);
    });
    it("getLast4digitsOfCC | Not passing CC number", () => {
        expect(billReminderNotificationObj.getLast4digitsOfCC()).to.be.equal("");
    });

    it("getMinDueAmount | Non parsable customerOtherInfo", () => {
        let record = {customerOtherInfo: {currentMinBillAmount : 5500}};
        expect(billReminderNotificationObj.getMinDueAmount(record)).to.be.equal(null);
    });
    it("getMinDueAmount | Valid customerOtherInfo", () => {
        let record = {customerOtherInfo: JSON.stringify({currentMinBillAmount : 5500})};
        expect(billReminderNotificationObj.getMinDueAmount(record)).to.be.equal(5500);
    });
    it("getMinDueAmount | Valid customerOtherInfo but currentMinBillAmount not defined", () => {
        let record = {customerOtherInfo: JSON.stringify({currentMinBillAmount2 : 5500})};
        expect(billReminderNotificationObj.getMinDueAmount(record)).to.be.equal(null);
    });  

    ///////////////////////////////////
    //    Just for code coverage    //
    //////////////////////////////////
    it("execSteps | Just for Coverage check", () => {
        let records = [{ 
            value : JSON.stringify({"source":"reminderBillFetch","notificationType":"BILLGEN","data":{"id":22,"is_automatic":0,"customerId":12125505,"rechargeNumber":"darsh","productId":205364130,"operator":"airtel","amount":300,"dueDate":MOMENT().add(1,'days').format("YYYY-MM-DD") + "T00:00:00.000Z","billFetchDate":"2020-04-29T15:37:07.000Z","nextBillFetchDate":"2021-03-02T00:00:00.000Z","gateway":"bses","paytype":"postpaid","service":"mobile","circle":"","customerMobile":"7887878787","customerEmail":"<EMAIL>","paymentChannel":"","retryCount":0,"status":4,"reason":"","userData":"{}","createdAt":"2020-03-26T17:01:02.000Z","updatedAt":"2020-04-29T15:37:07.000Z","billDate":null,"paymentDate":"2020-03-26T17:42:16.000Z","notification_status":1,"service_id":0,"billGen":true}})
        }];
        billReminderNotificationObj.execSteps(records);
    });

    it('sendNotification | record = null | notificationRecord = null | data = null', () => {
        let record = null, notificationRecord = null, data = null, billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
    });

    it('sendNotification | notificationRecord.type = SMS | disableSmsForUser error case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"SMS","recipients":100340,"notificationType":"DUEDATE","template_id":7127},
            data = {"amount":699,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"","category_id":21,"service":"Mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Airtel","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
            
        let disableSmsForUser_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'disableSmsForUser').callsFake(function fakeFn(record, callback){
            return callback("disableSmsForUser:: Invalid data received", false);
        }, null, null);   
        
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({
            "product_id": *********,
            "recharge_number": "**********",
            "user_data": "",
            "amount": 1253.26,
            "product_service": "Mobile",
            "notificationRecord": notificationRecord,
            "paytype": "postpaid",
            "category_id": 17,
            "short_operator_name": "Airtel",
            "operator": "airtel",
            "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
        });

        let url_stub = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns([null, "https://paytm.com/recharge?recharge_number=**********&product_id=*********&price=1253.26&utm_source=billGenReminder&utm_medium=sms&utm_campaign=Airtel&category_id=17"]);        
        let createTinyUrl_stub = sinon.stub(billReminderNotificationObjNew, 'createTinyUrl').callsFake(function fakeFn(url_stub, callback){
            return callback("Error while creating tinyURL", null);
        }, null, null); 

        let smsNotificationData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary , 'getSmsNotiData').returns({
            "templateName": "Bill Generation - Mobile/Landline SMS",
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [
                    "9510064716"
                ]
            },
            "dynamicParams": {
                "amount": 1253.26,
                "recharge_number": "8446557999",
                "operator": "Airtel",
                "operator_label": "Airtel",
                "brand": "Airtel",
                "thumbnail": "https://assetscdn1.paytm.com/images/catalog/operators/1635871053960.png",
                "category_id": 21,
                "service": "Mobile",
                "time_interval": null,
                "due_date": "6th Jan 2022",
                "short_operator_name": "Airtel",
                "unsubscribe_url": "https://paytm.me/OKVb-Q8",
                "sms_short_link": "https://paytm.me/1IHZ-uk"
            }
        });
        let sendProcessedNotification_stub = sinon.stub(billReminderNotificationObjNew , 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback();
        }); 

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);

            expect(disableSmsForUser_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(0);
            expect(url_stub).to.have.callCount(0);
            expect(createTinyUrl_stub).to.have.callCount(0);
            expect(smsNotificationData_stub).to.have.callCount(0);
            expect(sendProcessedNotification_stub).to.have.callCount(0);
            

            disableSmsForUser_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            createTinyUrl_stub.restore();
            smsNotificationData_stub.restore();
            sendProcessedNotification_stub.restore();
        });
    
    });

    it('sendNotification | notificationRecord.type = SMS | disableSmsForUser true case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"SMS","recipients":100340,"notificationType":"DUEDATE","template_id":7127},
            data = {"amount":699,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"","category_id":21,"service":"Mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Airtel","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
            
        let disableSmsForUser_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'disableSmsForUser').callsFake(function fakeFn(record, callback){
            return callback(null, true);
        }, null, null);   
        
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({
            "product_id": *********,
            "recharge_number": "**********",
            "user_data": "",
            "amount": 1253.26,
            "product_service": "Mobile",
            "notificationRecord": notificationRecord,
            "paytype": "postpaid",
            "category_id": 17,
            "short_operator_name": "Airtel",
            "operator": "airtel",
            "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
        });

        let url_stub = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns([null, "https://paytm.com/recharge?recharge_number=**********&product_id=*********&price=1253.26&utm_source=billGenReminder&utm_medium=sms&utm_campaign=Airtel&category_id=17"]);        
        let createTinyUrl_stub = sinon.stub(billReminderNotificationObjNew, 'createTinyUrl').callsFake(function fakeFn(url_stub, callback){
            return callback("Error while creating tinyURL", null);
        }, null, null); 

        let smsNotificationData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary , 'getSmsNotiData').returns({
            "templateName": "Bill Generation - Mobile/Landline SMS",
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [
                    "9510064716"
                ]
            },
            "dynamicParams": {
                "amount": 1253.26,
                "recharge_number": "8446557999",
                "operator": "Airtel",
                "operator_label": "Airtel",
                "brand": "Airtel",
                "thumbnail": "https://assetscdn1.paytm.com/images/catalog/operators/1635871053960.png",
                "category_id": 21,
                "service": "Mobile",
                "time_interval": null,
                "due_date": "6th Jan 2022",
                "short_operator_name": "Airtel",
                "unsubscribe_url": "https://paytm.me/OKVb-Q8",
                "sms_short_link": "https://paytm.me/1IHZ-uk"
            }
        });
        let sendProcessedNotification_stub = sinon.stub(billReminderNotificationObjNew , 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback();
        }); 

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);

            expect(disableSmsForUser_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(0);
            expect(url_stub).to.have.callCount(0);
            expect(createTinyUrl_stub).to.have.callCount(0);
            expect(smsNotificationData_stub).to.have.callCount(0);
            expect(sendProcessedNotification_stub).to.have.callCount(0);
            

            disableSmsForUser_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            createTinyUrl_stub.restore();
            smsNotificationData_stub.restore();
            sendProcessedNotification_stub.restore();
        });
    
    });

    
    it('sendNotification | notificationRecord.type = SMS | createTinyUrl error case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"SMS","recipients":100340,"notificationType":"DUEDATE","template_id":7127},
            data = {"amount":699,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"","category_id":21,"service":"Mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Airtel","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
            
        let disableSmsForUser_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'disableSmsForUser').callsFake(function fakeFn(record, callback){
            return callback(null, false);
        }, null, null);   
        
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({
            "product_id": *********,
            "recharge_number": "**********",
            "user_data": "",
            "amount": 1253.26,
            "product_service": "Mobile",
            "notificationRecord": notificationRecord,
            "paytype": "postpaid",
            "category_id": 17,
            "short_operator_name": "Airtel",
            "operator": "airtel",
            "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
        });

        let url_stub = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns([null, "https://paytm.com/recharge?recharge_number=**********&product_id=*********&price=1253.26&utm_source=billGenReminder&utm_medium=sms&utm_campaign=Airtel&category_id=17"]);        
        let createTinyUrl_stub = sinon.stub(billReminderNotificationObjNew, 'createTinyUrl').callsFake(function fakeFn(url_stub, callback){
            return callback("Error while creating tinyURL", null);
        }, null, null); 

        let smsNotificationData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary , 'getSmsNotiData').returns({
            "templateName": "Bill Generation - Mobile/Landline SMS",
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [
                    "9510064716"
                ]
            },
            "dynamicParams": {
                "amount": 1253.26,
                "recharge_number": "8446557999",
                "operator": "Airtel",
                "operator_label": "Airtel",
                "brand": "Airtel",
                "thumbnail": "https://assetscdn1.paytm.com/images/catalog/operators/1635871053960.png",
                "category_id": 21,
                "service": "Mobile",
                "time_interval": null,
                "due_date": "6th Jan 2022",
                "short_operator_name": "Airtel",
                "unsubscribe_url": "https://paytm.me/OKVb-Q8",
                "sms_short_link": "https://paytm.me/1IHZ-uk"
            }
        });
        let sendProcessedNotification_stub = sinon.stub(billReminderNotificationObjNew , 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback();
        }); 

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);

            expect(disableSmsForUser_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(createTinyUrl_stub).to.have.callCount(1);
            expect(smsNotificationData_stub).to.have.callCount(0);
            expect(sendProcessedNotification_stub).to.have.callCount(0);
            

            disableSmsForUser_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            createTinyUrl_stub.restore();
            smsNotificationData_stub.restore();
            sendProcessedNotification_stub.restore();
        });
    
    });

    it('sendNotification | notificationRecord.type = SMS | createUrl error case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"SMS","recipients":100340,"notificationType":"DUEDATE","template_id":7127},
            data = {"amount":699,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"","category_id":21,"service":"Mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Airtel","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
            
        let disableSmsForUser_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'disableSmsForUser').callsFake(function fakeFn(record, callback){
            return callback(null, false);
        }, null, null);   
        
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({
            "product_id": *********,
            "recharge_number": "**********",
            "user_data": "",
            "amount": 1253.26,
            "product_service": "Mobile",
            "notificationRecord": notificationRecord,
            "paytype": "postpaid",
            "category_id": 17,
            "short_operator_name": "Airtel",
            "operator": "airtel",
            "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns(["Error while creating URL", null]);        
        let createTinyUrl_stub = sinon.stub(billReminderNotificationObjNew, 'createTinyUrl').callsFake(function fakeFn(url_stub, callback){
            return callback(null, "https://paytm.me/1IHZ-uk");
        }, null, null); 

        let smsNotificationData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary , 'getSmsNotiData').returns({
            "templateName": "Bill Generation - Mobile/Landline SMS",
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [
                    "9510064716"
                ]
            },
            "dynamicParams": {
                "amount": 1253.26,
                "recharge_number": "8446557999",
                "operator": "Airtel",
                "operator_label": "Airtel",
                "brand": "Airtel",
                "thumbnail": "https://assetscdn1.paytm.com/images/catalog/operators/1635871053960.png",
                "category_id": 21,
                "service": "Mobile",
                "time_interval": null,
                "due_date": "6th Jan 2022",
                "short_operator_name": "Airtel",
                "unsubscribe_url": "https://paytm.me/OKVb-Q8",
                "sms_short_link": "https://paytm.me/1IHZ-uk"
            }
        });
        let sendProcessedNotification_stub = sinon.stub(billReminderNotificationObjNew , 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback();
        }); 

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);

            expect(disableSmsForUser_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(createTinyUrl_stub).to.have.callCount(0);
            expect(smsNotificationData_stub).to.have.callCount(0);
            expect(sendProcessedNotification_stub).to.have.callCount(0);
            
            disableSmsForUser_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            createTinyUrl_stub.restore();
            smsNotificationData_stub.restore();
            sendProcessedNotification_stub.restore();
        }); 
    });
    

    it('sendNotification | notificationRecord.type = SMS | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"SMS","recipients":100340,"notificationType":"DUEDATE","template_id":7127},
            data = {"amount":699,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"","category_id":21,"service":"Mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Airtel","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
            
        let disableSmsForUser_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'disableSmsForUser').callsFake(function fakeFn(record, callback){
            return callback(null, false);
        }, null, null);   
        
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({
            "product_id": *********,
            "recharge_number": "**********",
            "user_data": "",
            "amount": 1253.26,
            "product_service": "Mobile",
            "notificationRecord": notificationRecord,
            "paytype": "postpaid",
            "category_id": 17,
            "short_operator_name": "Airtel",
            "operator": "airtel",
            "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns([null, "https://paytm.com/recharge?recharge_number=**********&product_id=*********&price=1253.26&utm_source=billGenReminder&utm_medium=sms&utm_campaign=Airtel&category_id=17"]);        
        let createTinyUrl_stub = sinon.stub(billReminderNotificationObjNew, 'createTinyUrl').callsFake(function fakeFn(url_stub, callback){
            return callback(null, "https://paytm.me/1IHZ-uk");
        }, null, null); 

        let smsNotificationData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary , 'getSmsNotiData').returns({
            "templateName": "Bill Generation - Mobile/Landline SMS",
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [
                    "9510064716"
                ]
            },
            "dynamicParams": {
                "amount": 1253.26,
                "recharge_number": "8446557999",
                "operator": "Airtel",
                "operator_label": "Airtel",
                "brand": "Airtel",
                "thumbnail": "https://assetscdn1.paytm.com/images/catalog/operators/1635871053960.png",
                "category_id": 21,
                "service": "Mobile",
                "time_interval": null,
                "due_date": "6th Jan 2022",
                "short_operator_name": "Airtel",
                "unsubscribe_url": "https://paytm.me/OKVb-Q8",
                "sms_short_link": "https://paytm.me/1IHZ-uk"
            }
        });

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);

            expect(disableSmsForUser_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(createTinyUrl_stub).to.have.callCount(1);
            expect(smsNotificationData_stub).to.have.callCount(1);

            disableSmsForUser_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            createTinyUrl_stub.restore();
            smsNotificationData_stub.restore();
        });
    });
    
    it('sendNotification | notificationRecord.type = PUSH | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"PUSH","recipients":100340,"notificationType":"DUEDATE","template_id":4753},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        
        let utm_stub = sinon.stub(billReminderNotificationObjNew, '_utmByTemplateId').returns("$utm_source=billGenReminder$utm_medium=push$utm_campaign=airtel");
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForChatAndPush').returns({
            operator : "airtel",
            amount : 700,
            product_id :*********,
            recharge_number :"**********",
            category_id : 17
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'getQueryParams').returns("product_id=194$recharge_number=RN$expandBrowsePlan=true");
        let getExtraRechargeNum_stub = sinon.stub(billReminderNotificationObjNew, 'getExtraRechargeNum').returns("");
        let getPushNotiData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getPushNotiData').returns({"templateName":"Bill Reminder Due Date Push","debug":false,"sendBroadcastPush":true,"messageCentrePush":{"iconImage":"","expiry":1641144741,"extraParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"}},"notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["100340"]},"extraCommonParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"},"deviceType":["ANDROIDAPP","IOSAPP"],"dynamicParams":{"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"}});

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(utm_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(getExtraRechargeNum_stub).to.have.callCount(1);
            expect(getPushNotiData_stub).to.have.callCount(1);

            utm_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            getExtraRechargeNum_stub.restore();
            getPushNotiData_stub.restore();
        });
    });
    
    it('sendNotification | notificationRecord.type = PUSH | service = mobile | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"PUSH","recipients":100340,"notificationType":"DUEDATE","template_id":4753},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","service": "mobile","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":27,"time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        
        let utm_stub = sinon.stub(billReminderNotificationObjNew, '_utmByTemplateId').returns("$utm_source=billGenReminder$utm_medium=push$utm_campaign=airtel");
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForChatAndPush').returns({
            operator : "airtel",
            amount : 700,
            product_id :*********,
            recharge_number :"**********",
            category_id : 17
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'getQueryParams').returns("product_id=194$recharge_number=RN$expandBrowsePlan=true");
        let getExtraRechargeNum_stub = sinon.stub(billReminderNotificationObjNew, 'getExtraRechargeNum').returns("");
        let getPushNotiData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getPushNotiData').returns({"templateName":"Bill Reminder Due Date Push","debug":false,"sendBroadcastPush":true,"messageCentrePush":{"iconImage":"","expiry":1641144741,"extraParams":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"}},"notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["100340"]},"extraCommonParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"},"deviceType":["ANDROIDAPP","IOSAPP"],"dynamicParams":{"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"mobile","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"}});
        
        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(utm_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(getExtraRechargeNum_stub).to.have.callCount(1);
            expect(getPushNotiData_stub).to.have.callCount(1);

            utm_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            getExtraRechargeNum_stub.restore();
            getPushNotiData_stub.restore();
        });
    });

    it('sendNotification | notificationRecord.type = PUSH | service = utility | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"PUSH","recipients":100340,"notificationType":"DUEDATE","template_id":4753},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","service": "utility","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":27,"time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        
        let utm_stub = sinon.stub(billReminderNotificationObjNew, '_utmByTemplateId').returns("$utm_source=billGenReminder$utm_medium=push$utm_campaign=airtel");
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForChatAndPush').returns({
            operator : "airtel",
            amount : 700,
            product_id :*********,
            recharge_number :"**********",
            category_id : 17
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'getQueryParams').returns("product_id=194$recharge_number=RN$expandBrowsePlan=true");
        let getExtraRechargeNum_stub = sinon.stub(billReminderNotificationObjNew, 'getExtraRechargeNum').returns("");
        let getPushNotiData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getPushNotiData').returns({"templateName":"Bill Reminder Due Date Push","debug":false,"sendBroadcastPush":true,"messageCentrePush":{"iconImage":"","expiry":1641144741,"extraParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"}},"notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["100340"]},"extraCommonParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"},"deviceType":["ANDROIDAPP","IOSAPP"],"dynamicParams":{"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"utility","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"}});
        
        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(utm_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(getExtraRechargeNum_stub).to.have.callCount(1);
            expect(getPushNotiData_stub).to.have.callCount(1);

            utm_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            getExtraRechargeNum_stub.restore();
            getPushNotiData_stub.restore();
        });
    });


    it('sendNotification | notificationRecord.type = PUSH | service = datacard | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"PUSH","recipients":100340,"notificationType":"DUEDATE","template_id":4753},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","service": "datacard","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":27,"time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        
        let utm_stub = sinon.stub(billReminderNotificationObjNew, '_utmByTemplateId').returns("$utm_source=billGenReminder$utm_medium=push$utm_campaign=airtel");
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForChatAndPush').returns({
            operator : "airtel",
            amount : 700,
            product_id :*********,
            recharge_number :"**********",
            category_id : 17
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'getQueryParams').returns("product_id=194$recharge_number=RN$expandBrowsePlan=true");
        let getExtraRechargeNum_stub = sinon.stub(billReminderNotificationObjNew, 'getExtraRechargeNum').returns("");
        let getPushNotiData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getPushNotiData').returns({"templateName":"Bill Reminder Due Date Push","debug":false,"sendBroadcastPush":true,"messageCentrePush":{"iconImage":"","expiry":1641144741,"extraParams":{"url":"paytmmp://datacard_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"}},"notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["100340"]},"extraCommonParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"},"deviceType":["ANDROIDAPP","IOSAPP"],"dynamicParams":{"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"datacard","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"}});

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(utm_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(getExtraRechargeNum_stub).to.have.callCount(1);
            expect(getPushNotiData_stub).to.have.callCount(1);

            utm_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            getExtraRechargeNum_stub.restore();
            getPushNotiData_stub.restore();
        });
    });

    it('sendNotification | notificationRecord.type = PUSH | service = dth | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {"type":"PUSH","recipients":100340,"notificationType":"DUEDATE","template_id":4753},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","service": "dth","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":27,"time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/Yd-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        
        let utm_stub = sinon.stub(billReminderNotificationObjNew, '_utmByTemplateId').returns("$utm_source=billGenReminder$utm_medium=push$utm_campaign=airtel");
        let paramsForUrl_stub = sinon.stub(billReminderNotificationObjNew, 'getParamsForChatAndPush').returns({
            operator : "airtel",
            amount : 700,
            product_id :*********,
            recharge_number :"**********",
            category_id : 17
        });
        let url_stub = sinon.stub(billReminderNotificationObjNew, 'getQueryParams').returns("product_id=194$recharge_number=RN$expandBrowsePlan=true");
        let getExtraRechargeNum_stub = sinon.stub(billReminderNotificationObjNew, 'getExtraRechargeNum').returns("");
        let getPushNotiData_stub = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getPushNotiData').returns({"templateName":"Bill Reminder Due Date Push","debug":false,"sendBroadcastPush":true,"messageCentrePush":{"iconImage":"","expiry":1641144741,"extraParams":{"url":"paytmmp://dth?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"}},"notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["100340"]},"extraCommonParams":{"url":"paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/46007?product_id=*********$price=700$recharge_number=**********$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Base%20Education","url_type":"external"},"deviceType":["ANDROIDAPP","IOSAPP"],"dynamicParams":{"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"dth","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"}});

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(utm_stub).to.have.callCount(1);
            expect(paramsForUrl_stub).to.have.callCount(1);
            expect(url_stub).to.have.callCount(1);
            expect(getExtraRechargeNum_stub).to.have.callCount(1);
            expect(getPushNotiData_stub).to.have.callCount(1);

            utm_stub.restore();
            paramsForUrl_stub.restore();
            url_stub.restore();
            getExtraRechargeNum_stub.restore();
            getPushNotiData_stub.restore();
        });
    });


    it('sendNotification | notificationRecord.type = CHAT | Error case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {type: "CHAT", "recipients":"9643825427","notificationType":"DUEDATE","template_id":7758},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObjNew, 'getDeepLinkUrl').returns("paytmmp://mobile_prepaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/17?product_id=63794098$recharge_number=9896464001$expandBrowsePlan=true$utm_source=prepaidReminderD$utm_medium=chat$utm_campaign=7758");
        let stub3 = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getChatNotiData').returns(["err", {"templateName":"Plan_Expiry_D_SMS_to_Transacted_Customers_Push","notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["1"]},"senderData":{"type":"CHANNEL","identifier":"b562cdef-b677-4784-ae8b-703a95023bc0"},"sendPush":false,"overrideMsg":false,"dynamicParams":{"amount":10,"recharge_number":"9896464001","operator_label":"Jio","operator":"jio","service":"Mobile","brand":"Jio","category_id":17,"thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/1487835240083.png","short_operator_name":"Jio","deeplink":"paytmmp://mobile_prepaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/17?product_id=63794098$recharge_number=9896464001$expandBrowsePlan=true$utm_source=prepaidReminderD$utm_medium=chat$utm_campaign=7758"}}]);

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
        });
    });

    it('sendNotification | notificationRecord.type = CHAT | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {type: "CHAT", "recipients":"9643825427","notificationType":"DUEDATE","template_id":7758},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObjNew, 'getDeepLinkUrl').returns("paytmmp://mobile_prepaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/17?product_id=63794098$recharge_number=9896464001$expandBrowsePlan=true$utm_source=prepaidReminderD$utm_medium=chat$utm_campaign=7758");
        let stub3 = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getChatNotiData').returns([null, {"templateName":"Plan_Expiry_D_SMS_to_Transacted_Customers_Push","notificationReceiver":{"notificationReceiverType":"CUSTOMERID","notificationReceiverIdentifier":["1"]},"senderData":{"type":"CHANNEL","identifier":"b562cdef-b677-4784-ae8b-703a95023bc0"},"sendPush":false,"overrideMsg":false,"dynamicParams":{"amount":10,"recharge_number":"9896464001","operator_label":"Jio","operator":"jio","service":"Mobile","brand":"Jio","category_id":17,"thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/1487835240083.png","short_operator_name":"Jio","deeplink":"paytmmp://mobile_prepaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/17?product_id=63794098$recharge_number=9896464001$expandBrowsePlan=true$utm_source=prepaidReminderD$utm_medium=chat$utm_campaign=7758"}}]);

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
        });
    });

    it('sendNotification | notificationRecord.type = EMAIL | Success case ', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {type: "EMAIL", "recipients":"9643825427","notificationType":"DUEDATE","template_id":4602},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({});
        let stub3 = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns([null, "https://paytm.com/credit-card-bill-payment?recharge_number=XXXXXXXXXXXX2102&recharge_number_2=2021110110001ee76738a6a6b8d2b2da101decda2253f&utm_source=billDueDateReminder&utm_medium=email&utm_campaign=Citibank"]);
        let stub4 = sinon.stub(billReminderNotificationObjNew.notificationLibrary, 'getEmailNotiData').returns({});

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
        });
    });

    it('sendNotification | notificationRecord.type = EMAIL | Error while creating URL', () => {
        let record = {"id":1916,"customer_id":100340,"recharge_number":"**********","old_product_id":*********,"product_id":*********,"operator":"airtel","amount":700,"due_date":"2022-01-01T00:00:00.000Z","bill_fetch_date":"2021-12-09T17:39:49.000Z","next_bill_fetch_date":"2021-12-26T01:00:00.000Z","gateway":"","paytype":"postpaid","service":"mobile","circle":"andhra pradesh","customer_mobile":"9643825427","customer_email":"","status":11,"user_data":"{}","notification_status":1,"payment_date":"2021-12-22T15:41:22.000Z","service_id":0,"customerOtherInfo":null,"is_automatic":0,"source":"reminderBillDuePublisher","notificationType":"DUEDATE","templates":null,"skipNotification":0,"time_interval":null,"extra":"{\"customer_type\":1,\"last_paid_amount\":200}","debugKey":"id-1916,customerId-100340,rechargeNumber-**********,operator-airtel"},
            notificationRecord = {type: "EMAIL", "recipients":"9643825427","notificationType":"DUEDATE","template_id":4602},
            data = {"amount":700,"recharge_number":"**********","operator":"Base Education","operator_label":"Base Education","brand":"Base Education","thumbnail":"","category_id":46007,"service":"Education","time_interval":null,"due_date":"1st Jan 2022","short_operator_name":"Base Education","unsubscribe_url":"http://staging.p-y.tm/iC-qoq5"},
            billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        let stub1 = sinon.stub(billReminderNotificationObjNew, 'sendProcessedNotification').callsFake(function fakeFn(record, smsNotificationData, notificationRecord, callback){
            return callback(null);
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObjNew, 'getParamsForUrl').returns({});
        let stub3 = sinon.stub(billReminderNotificationObjNew, 'createUrl').returns(["err", "url"]);

        billReminderNotificationObjNew.sendNotification(record, notificationRecord, data, (err)=>{
            expect(err).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
        });
    });

    it('prepareNotification | due_date = null | payload', () => {
        let billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        billReminderNotificationObjNew.cvrData = {
            123: {
                operator: "tata power delhi distribution limited",
                operator_label: "tata power delhi distribution limited",
                attributes: JSON.stringify({
                    short_operator_name: 'tatapower'
                })
            }
        };
        billReminderNotificationObjNew.createShortUnsubscribeUrl = (cb, a, b) => {
            return cb(null, 'http://sample_short_url');
        }
        billReminderNotificationObjNew.sendNotification = (a, b, payLoad, cb) => {
            expect(payLoad).to.not.have.property('due_date');
            expect(payLoad.amount).to.be.equal(10);
            return cb()
        }


        let record = {
            product_id: 123,
            due_date: null,
            amount: 10,
            operator: "tata power delhi distribution limited",
        }

        billReminderNotificationObjNew.prepareNotification(()=>{
            return;
        }, record, 'bills_tatapower');
    })

    it('prepareNotification | due_date = "2020-10-30" | payload', () => {
        let billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        billReminderNotificationObjNew.cvrData = {
            123: {
                operator: "tata power delhi distribution limited",
                operator_label: "tata power delhi distribution limited",
                attributes: JSON.stringify({
                    short_operator_name: 'tatapower'
                })
            }
        };
        billReminderNotificationObjNew.createShortUnsubscribeUrl = (cb, a, b) => {
            return cb(null, 'http://sample_short_url');
        }

        let record = {
            product_id: 123,
            due_date: "2020-10-30",
            amount: 10,
            operator: "tata power delhi distribution limited",
        }

        billReminderNotificationObjNew.sendNotification = (a, b, payLoad, cb) => {
            expect(payLoad.due_date).to.be.equal(MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').format('Do MMM YYYY'));
            expect(payLoad.amount).to.be.equal(10);
            return cb()
        }
        billReminderNotificationObjNew.prepareNotification(()=>{
            return;
        }, record, 'bills_tatapower');
    });
    

    //sending BG notification close to duedate, let service change it into due date notification if config exist
    it('prepareNotification | BG payload | payload', () => {
        let billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        billReminderNotificationObjNew.cvrData = {
            341207918: {
                operator: "tata power delhi distribution limited",
                operator_label: "tata power delhi distribution limited",
                service: "electricity",
                attributes: JSON.stringify({
                    short_operator_name: 'tatapower'
                })
            }
        };
        billReminderNotificationObjNew.createShortUnsubscribeUrl = (cb, a, b) => {
            return cb(null, 'http://sample_short_url');
        }

        let record = {
            product_id: 341207918,
            due_date: MOMENT().add(1, 'days').format("YYYY-MM-DD"),
            bill_fetch_date: MOMENT().format("YYYY-MM-DD"),
            amount: 10,
            operator: "tata power delhi distribution limited",
            notificationType : "BILLGEN",
            service : "electricity"
        }

        billReminderNotificationObjNew.sendNotification = (a, b, payLoad, cb) => {
            expect(payLoad.due_date).to.be.equal(MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').format('Do MMM YYYY'));
            expect(payLoad.amount).to.be.equal(10);
            return cb()
        }
        billReminderNotificationObjNew.prepareNotification(()=>{
            return;
        }, record, 'bills_tatapower');
    });

    it('prepareNotification | Operator:airteltv blacklisted for BILLGEN NOTIFICATION', () => { 
        record = {
            amount : 178,
            product_id : 972,
            recharge_number : "RN",
            user_data : "{\"recharge_number_2\":\"RN2\"}",
            paytype : "prepaid",
            operator : 'airteltv'
        };
        let billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        billReminderNotificationObjNew.prepareNotification( (err)=>{
            expect(err).to.be.equal("Operator:airteltv blacklisted for BILLGEN NOTIFICATION");
            return;
        }, record, 'bills_airteltv');
    });

    it('prepareNotification | Operator:airtel blacklisted for DUEDATE NOTIFICATION', () => { 
        record = {
            amount : 178,
            product_id : 1200763862,
            recharge_number : "RN",
            user_data : "{\"recharge_number_2\":\"RN2\"}",
            paytype : "credit card",
            operator : 'airtel',
            due_date : MOMENT( MOMENT().subtract(1,'day').format("YYYY-MM-DD 00:00:00"))
        };

        let billReminderNotificationObjNew = _.cloneDeep(billReminderNotificationObj);
        billReminderNotificationObjNew.prepareNotification( (err)=>{
            expect(err).to.be.equal("Operator:airtel blacklisted for DUEDATE NOTIFICATION");
            return;
        }, record, 'bills_airtel');
    })
  

    /**
     * ToDo for below methods
     * 1. createShortUnsubscribeUrl
     * 2. sendProcessedNotification
     * 3. createUrl
     * 4. prepareNotification
     * 5. processNotification
     * 6. decideNextDueDate --> add assetions
     */
});

describe("Module: notification-create service:: decideNextDueDate test suite", function () {
    // let record, notificationType, type, payload, templateIdByService, defaultTemplateId;
    let billReminderNotificationObj,options,
        record = {
            amount : 877,
            product_id : 194,
            recharge_number : "RN",
            user_data : "{\"recharge_number_2\":\"RN2\"}",
            paytype : "Loan Payment",
        },
        payLoad = {
            operator : 'vodafone idea',
            short_operator_name : 'vi',
            recharge_number : "RN",
            amount : 700.98,
            service : "Credit Card",
            category_id : 21
        },
        notificationRecord = {
            template_id : 7126
        }, notificationConfig;

    before(function (done) {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            notificationConfig = mock_options.config.NOTIFICATION;
            billReminderNotificationObj = new BILL_REMINDER_NOTIFICATION(options);
            billReminderNotificationObj.operatorsSendingNotificationToRegisteredUser = _.get(notificationConfig, 'registeredUserNotificationOperator', null);
            billReminderNotificationObj.blackListOperators = _.get(notificationConfig, 'BlackListOperator', null);
            billReminderNotificationObj.cvrData = _.get(mock_options.config, 'CVR_DATA', {});
            
            //billReminderNotificationObj.start();
            done();
        });
    });

    it("decideNextDueDate | Valid input with T-payment date < not in use interval ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(10,'days').format("YYYY-MM-DD 00:00:00"),
            customer_id : 1122,
            recharge_number : 'RN',
            product_id : 194,
            operator : 'tikona broadband',
            service: 'broadband'
        }, operator = "tikona broadband", tableName = "bills_broadband";

        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);
    });
    it("decideNextDueDate | Valid input with T-payment date >= not in use interval ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(1,'year').format("YYYY-MM-DD 00:00:00"),
            customer_id : 1122,
            recharge_number : 'RN',
            product_id : 194,
            operator : 'tikona broadband',
            service: 'broadband'
        }, operator = "tikona broadband", tableName = "bills_broadband";

        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);
    });
    it("decideNextDueDate | Insufficient data | Critical logs | input with T-payment date < not in use interval ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(10,'days').format("YYYY-MM-DD 00:00:00"),
        }, operator = "tikona broadband", tableName = "bills_broadband";

        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);
    });
    
    it("decideNextDueDate | Insufficient data | Critical logs | Valid input with T-payment date >= not in use interval ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(1,'year').format("YYYY-MM-DD 00:00:00"),
        }, operator = "tikona broadband", tableName = "bills_broadband";

        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);
    });

    it("decideNextDueDate | Critical logs | Unable to update Notification status for  rechargeNumber ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(6,'days').format("YYYY-MM-DD 00:00:00"),
        }, operator = "vodafone", tableName = "bills_vodafone";
        let stub1 = sinon.stub(billReminderNotificationObj.bills, 'updateNotificationStatus').callsFake(function fakeFn(callback){
            return callback("decideNextDueDate :: Unable to update Notification status for  rechargeNumber");
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObj.bills, "updateDueDateInReminder").callsFake(function fakeFn(callback){
            return callback(null, null);
        }, null, null);   
        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(0);
        stub1.restore();
        stub2.restore();
    });

    it("decideNextDueDate | Critical logs | Unable to update due date for  rechargeNumber ", () => {
        let record = {
            service_id : 4,
            payment_date : MOMENT().subtract(2,'days').format("YYYY-MM-DD 00:00:00"),
        }, operator = "vodafone", tableName = "bills_vodafone";
        let stub1 = sinon.stub(billReminderNotificationObj.bills, 'updateNotificationStatus').callsFake(function fakeFn(callback){
            return callback(null, null);
        }, null, null);   
        let stub2 = sinon.stub(billReminderNotificationObj.bills, "updateDueDateInReminder").callsFake(function fakeFn(callback){
            return callback("decideNextDueDate :: Unable to update due date for  rechargeNumber");
        }, null, null);   
        billReminderNotificationObj.decideNextDueDate(record, operator, tableName);

        expect(stub1).to.have.callCount(0);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });


    it("sendProcessedNotification | timing of send_at ", () => {
        let record = {},notificationData={},notificationRecord={};
        let stub1 = sinon.stub(billReminderNotificationObj.notify, '__createNotification').callsFake(function fakeFn(callback){
            return callback(null, null);
        }, null);     
        let stub2 = sinon.stub(billReminderNotificationObj.notify, 'getTopicToPublish').returns('COMMON_NOTIFICATION')
        billReminderNotificationObj.sendProcessedNotification(record, notificationData, notificationRecord, function(err){
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        });
    });


});

describe("Module: NotificationLibrary:: getWpPriceSuffix test suite", function () {
    let notificationLibObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            notificationLibObj = new NOTIFICATION(options);
            done();
        });
    });

    it("should append price for DTH service with valid amount", () => {
        const record = {
            service: 'DTH',
            amount: 599.99
        };
        expect(notificationLibObj.getWpPriceSuffix(record))
            .to.equal('&price=599.99');
    });

    it("should handle null amount for DTH service", () => {
        const record = {
            service: 'DTH',
            amount: null
        };
        expect(notificationLibObj.getWpPriceSuffix(record))
            .to.equal('&price=');
    });

    it("should handle undefined amount for DTH service", () => {
        const record = {
            service: 'DTH'
        };
        expect(notificationLibObj.getWpPriceSuffix(record))
            .to.equal('&price=');
    });

    it("should return empty string for non-DTH service", () => {
        const record = {
            service: 'Mobile',
            amount: 500
        };
        expect(notificationLibObj.getWpPriceSuffix(record))
            .to.equal('');
    });

    it("should handle case-insensitive service check", () => {
        const record = {
            service: 'dth',
            amount: 299
        };
        expect(notificationLibObj.getWpPriceSuffix(record))
            .to.equal('&price=299');
    });

    it("should use custom operator delimiter", () => {
        const record = {
            service: 'DTH',
            amount: 399.55
        };
        expect(notificationLibObj.getWpPriceSuffix(record, '$'))
            .to.equal('$price=399.55');
    });
});