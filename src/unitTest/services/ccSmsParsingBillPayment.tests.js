/*
  jshint
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach } from 'mocha';

import chai from "chai";
import sinon from 'sinon';
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import _ from 'lodash';

import chaiAsPromised from "chai-as-promised";
import creditCardStrategy from '../../services/smsParsingBillPayment/creditCardStrategy';
import parent from '../../services/smsParsingBillPayment';

import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Module: smsParsingBillPayment -> creditCardStrategy::", function () {
   let serviceObj;

   before(function () {
      STARTUP_MOCK.init(function (error, options) {
         options.config = options.config || {};
         options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
         options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
         options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
         options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
         serviceObj = new creditCardStrategy(options);
         done();
      });
   });
   beforeEach(function () {
      server = sinon.fakeServer.create();
   });

   afterEach(function () {
      server.restore();
   });

   it("executeStrategy | null data", (done) => {
      let data = null;
      let stub = sinon.stub(serviceObj, 'processRecords').returns(null);
      let processedRecord = serviceObj.executeStrategy(function(error){
         if(error){
            expect(error).to.be.equal(null);
         }
      },data, parent);
      expect(stub).to.have.callCount(0);
      expect(processedRecord).to.be.equal(undefined);
      stub.restore();
      done();
   })
   it("executeStrategy | null data", (done) => {
      let data = { "data": [{ "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": ["Array"], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": ["Object"], "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }], "kafka_topic": ["SMS_PARSING_CC_BILLS_PAYMENT"] }
      let stub = sinon.stub(serviceObj, 'processRecords').returns(null);
      let processedRecord = serviceObj.executeStrategy(function(error){
         if(error){
            expect(error).to.be.equal(null);
         }
      },data, parent);
      expect(stub).to.have.callCount(1);
      expect(processedRecord).to.equal(undefined);
      stub.restore();
      done();
   })
   it("executeStrategy | Error from processRecord", (done) => {
      let data = { "data": [{ "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": ["Array"], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": ["Object"], "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }], "kafka_topic": ["SMS_PARSING_CC_BILLS_PAYMENT"] }
      let stub = sinon.stub(serviceObj, 'processRecords').callsFake(function fakeFn(done){
         return done("Error from processRecord");
     });
      let processedRecord = serviceObj.executeStrategy(function(error){
         if(error){
            expect(error).to.be.equal(null);
         }
      },data, parent);
      expect(stub).to.have.callCount(1);
      expect(processedRecord).to.equal(undefined);
      stub.restore();
      done();
   })

   // it("validateAndProcessRecord | Valid Record | As per Production data", () => {
   //    let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
   //    let [error, record] = serviceObj.validateAndProcessRecord(recordData);
   //    expect(error).to.be.equal(null);
   //    expect(record.customerId).to.be.equal(*********);
   //    expect(record.lastCC).to.be.equal("2003");
   //    expect(record.paymentDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-05-30 13:44:33");
   //    expect(record.smsSenderID).to.be.equal("VM-KOTAKB");
   //    expect(record.bankName).to.be.equal("KOTAK");
   //    expect(record.currentPaidAmount).to.be.equal(10000);
   // });
   it("validateAndProcessRecord | Valid Record | Paid amount == 0", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 0, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal('Mandatory Params currentPaidAmount is Missing / Invalid');
   });
   it("validateAndProcessRecord | Invalid | Paid amount == -ve", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": "-100", "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal('Mandatory Params currentPaidAmount is Missing / Invalid');
   });
   it("validateAndProcessRecord | Invalid | Paid amount not a number", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": "XXX", "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal('Mandatory Params currentPaidAmount is Missing / Invalid');
   });
   it.skip("validateAndProcessRecord | Invalid Arg | record not passed", () => {
      let recordData = null;
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Invalid record");
   });
   it("validateAndProcessRecord | customerId not passed", () => {
      let recordData = { "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
   });
   it("validateAndProcessRecord | customerId - As Number Valid Case", () => {
      let recordData = { "cId": *********, "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal(null);
   });
   it("validateAndProcessRecord | Invalid customerId - alpha numeric", () => {
      let recordData = { "cId": "22VBN316909", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
   });
   it("validateAndProcessRecord | InValid Record | Missing Payment date", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Mandatory Params paymentDate is Missing / Invalid");
   });
   it("validateAndProcessRecord | InValid Record | Missing all mandatory params", () => {
      let recordData = { "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "available_Balance": null, "date": "2021-03-15" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Mandatory Params customerId,lastCC,currentPaidAmount,paymentDate,bankName,bankName is Missing / Invalid");
   });
   it("validateAndProcessRecord | InValid Record | lastCC length > 4", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "12003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(record.lastCC).to.be.equal("2003");
   });
   it("validateAndProcessRecord | InValid Record | lastCC length < 2", () => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "3", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      expect(error).to.be.equal("Mandatory Params lastCC-length-1 is Missing / Invalid");
   });

   it("parseAmount | Valid Record | Integer - Rs.590", () => {
      expect(serviceObj.parseAmount("Rs.590")).to.be.equal(590);
   });
   it("parseAmount | Valid Record | Float - Rs.590.78", () => {
      expect(serviceObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
   });
   it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
      expect(serviceObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
   });
   it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
      expect(serviceObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
   });
   it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
      expect(serviceObj.parseAmount("Rs.0")).to.be.equal(null);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("590")).to.be.equal(590);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("5000.05")).to.be.equal(5000.05);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("-5000.05")).to.be.equal(-5000.05);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("Rs.5000.05")).to.be.equal(5000.05);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("Rs.-5000.05")).to.be.equal(-5000.05);
   });
   it("parseAmount | Valid Record | without Rs. string", () => {
      expect(serviceObj.parseAmount("-590")).to.be.equal(-590);
   });
   it("parseAmount | Valid Record | as Number", () => {
      expect(serviceObj.parseAmount(590)).to.be.equal(590);
   });
   it("parseAmount | Valid Record | as Number", () => {
      expect(serviceObj.parseAmount(590.67)).to.be.equal(590.67);
   });
   it("parseAmount | InValid Record | as null", () => {
      expect(serviceObj.parseAmount(null)).to.be.equal(null);
   });
   it("parseAmount | InValid Record | as normal string", () => {
      expect(serviceObj.parseAmount("amount")).to.be.equal(null);
   });

   it("processRecords | customerId not passed", (done) => {
      let recordData = { "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'update', dbRecord, null);
      });
      let stub2 = sinon.spy(serviceObj, 'validateAndProcessRecord');
      let stub3 = sinon.stub(serviceObj, 'updateCCBill').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(null);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            expect(err).to.equal('Mandatory Params customerId is Missing / Invalid');
         }
      });
      expect(stub1).to.have.callCount(0);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(0);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record | Error in getAction function", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done("Error from getActionforCCBills");
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCCBill').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(null);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal('Error from getActionforCCBills');
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(0);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  action update | error in updateCCBill", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": 0, "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = { "id": "********" };
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'update', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCCBill').callsFake(function fakeFn(done) {
         return done('Error occured in updateCCBill');
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal('Error occured in updateCCBill');
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  action findAndUpdate | error in updateCCBill", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = { "id": "********" };
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'findAndUpdateToCassandra', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
         return done('Error occured in updateCassandra');
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal('Error occured in updateCassandra');
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  action findAndUpdate | error in updateCassandra", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = { "id": "********" };
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'findAndUpdateToCassandra', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal("Error in publish CT events");
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  action findAndUpdate | no error", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = { "id": "********" };
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'findAndUpdateToCassandra', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal("Error in publish CT events");
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  action not valid | no error", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = { "id": "********" };
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'findAndUpdate', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal("No action found for record");
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(0);
      expect(stub4).to.have.callCount(0);
      expect(stub5).to.have.callCount(0);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | error in publish ct", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": 0, "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = {};
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'update', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCCBill').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done("Error in publish CT events");
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal('Error in publish CT events');
         }
      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(1);
      expect(stub5).to.have.callCount(1);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });
   it("processRecords | Valid record |  success publish ct", (done) => {
      let recordData = { "cId": "*********", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": { "mode": "credit_card", "vendor": "KOTAK", "transaction_reference_no": null }, "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 10000 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": 0, "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let dbRecordToUpdate = {};
      let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
         return done(null, 'update', dbRecord, null);
      });
      let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
      let stub3 = sinon.stub(serviceObj, 'updateCCBill').callsFake(function fakeFn(done) {
         return done(null);
      })
      let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(dbRecordToUpdate);
      let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
         return done(null);
      })
      serviceObj.processRecords(recordData, function (err) {
         if (err) {
            console.log("error in testcase", err);
            expect(err).to.equal(null);
         }

      });
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub3).to.have.callCount(1);
      expect(stub4).to.have.callCount(1);
      expect(stub5).to.have.callCount(1);
      stub1.restore();
      stub2.restore();
      stub3.restore();
      stub4.restore();
      stub5.restore();
      done();
   });

   it("getSingleMatchingCardByCustomer | Valid record | Error in getBillByCustomer", (done) => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function (cb) {
         return cb('Error in getBillByCustomer', null);
      });
      serviceObj.getActionforCCBills(function (error, action, dbRecordResp, message) {
         expect(error).to.be.equal("Error in getBillByCustomer");
         expect(message).to.be.equal(undefined);
         expect(action).to.be.equal(undefined);
         expect(dbRecordResp).to.be.equal(undefined);
      }, processedRecord);
      stub1.restore();
      done();
   });
   it("getSingleMatchingCardByCustomer | Valid record | No record found for given custId", (done) => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function (cb) {
         return cb(null, null);
      });
      serviceObj.getActionforCCBills(function (error, action, dbRecordResp, message) {
         expect(error).to.be.equal(null);
         expect(action).to.be.equal('findAndUpdateToCassandra');
         expect(dbRecordResp).to.be.equal(null);
         expect(message).to.be.equal("No records found");
      }, processedRecord);
      stub1.restore();
      return done();
   });
   it("getSingleMatchingCardByCustomer | Valid record | No matching last4 digits MCN matching in db", (done) => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbdata = [{ recharge_number: "4166 44XX XXXX 2004", }, { recharge_number: "4166 44XX XXXX 2005", }]
      let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function (cb) {
         return cb(null, dbdata);
      });
      serviceObj.getActionforCCBills(function (error, action, dbRecordResp, message) {
         expect(error).to.be.equal(null);
         expect(action).to.be.equal('findAndUpdateToCassandra');
         expect(dbRecordResp).to.be.equal(null);
         expect(message).to.be.equal("No records found with given last4Digits");
      }, processedRecord);
      stub1.restore();
      return done();
   });
   it.skip("getSingleMatchingCardByCustomer | Valid record | Multiple matching last4 digits MCN matching in db", (done) => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbdata = [{ "recharge_number": "4166 44XX XXXX 2003" }, { "recharge_number": "4166 44XX XXXX 2003" }]
      let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function (cb) {
         return cb(null, dbdata);
      });
      let stub2 = sinon.stub(serviceObj.parent, 'smsParsingSyncCCBillLib').returns("Multiple matching transactions !!")
      serviceObj.getActionforCCBills(function (error, action, dbRecordResp, message) {
         expect(error).to.be.equal("Multiple matching transactions !!");
         expect(action).to.be.equal(undefined);
         expect(dbRecordResp).to.be.equal(undefined);
         expect(message).to.be.equal(undefined);
      }, processedRecord);
      stub1.restore();
      stub2.restore();
      done();
   });
   it("getSingleMatchingCardByCustomer | Valid record | Single matching last4 digits MCN matching in db but skiprecord=true", (done) => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbdata = [{ recharge_number: "4166 44XX XXXX 2003", }]
      let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function (cb) {
         return cb(null, dbdata);
      });
      let stub2 = sinon.stub(serviceObj, 'shouldRecordBeSkipped').returns(true);
      serviceObj.getActionforCCBills(function (error, action, dbRecordResp, message) {
         expect(error).to.be.equal("Record cant be updated due to either record already updated or bill has not generated yet !!");
         expect(action).to.be.equal(undefined);
         expect(dbRecordResp).to.be.equal(undefined);
         expect(message).to.be.equal(undefined);
      }, processedRecord);
      stub1.restore();
      stub2.restore();
      return done();
   });
   //   it.only("getSingleMatchingCardByCustomer | Valid record | Single matching last4 digits MCN matching in db isPaytmFirstCCFromRechargeNumber=true", (done) => {
   //    let processedRecord = {"customerId":*********,"lastCC":"2003","currentPaidAmount":10000,"paymentDate":"2022-06-04T13:44:33.487","smsSenderID":"VM-KOTAKB","bankName":"KOTAK"}
   //    let dbdata= [{"recharge_number":"4166 44XX XXXX 2003"}]
   //    let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function(cb){
   //       return cb(null, dbdata);
   //    });
   //    let stub2 = sinon.stub(serviceObj, 'shouldRecordBeSkipped').returns(false);
   //    let stub3 = sinon.stub(serviceObj, 'ref').returns(true);
   //    serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
   //           expect(error).to.be.equal("Skipping Paytm First CC update");
   //           expect(action).to.be.equal(undefined);
   //           expect(dbRecordResp).to.be.equal(undefined);
   //           expect(message).to.be.equal(undefined);
   //       }, processedRecord);
   //       stub1.restore();
   //       stub2.restore();
   //       stub3.restore();
   //       return done();
   //   });
   //   it.skip("getSingleMatchingCardByCustomer | Valid record | Single matching last4 digits MCN matching in db isPaytmFirstCCFromRechargeNumber=false", (done) => {
   //    let processedRecord = {"customerId":*********,"lastCC":"2003","currentPaidAmount":10000,"paymentDate":"2022-06-04T13:44:33.487","smsSenderID":"VM-KOTAKB","bankName":"KOTAK"}
   //    let dbdata= [{"recharge_number":"4166 44XX XXXX 2003"}]
   //    let stub1 = sinon.stub(serviceObj.bills, 'getBillByCustomer').callsFake(function(cb){
   //       return cb(null, dbdata);
   //    });
   //    let stub2 = sinon.stub(serviceObj, 'shouldRecordBeSkipped').returns(false);
   //    let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCFromRechargeNumber').returns(false);
   //    serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
   //       console.log("record", dbRecordResp);
   //           expect(error).to.be.equal(null);
   //           expect(action).to.be.equal('update');
   //           //expect(dbRecordResp).to.be.equal("{ recharge_number: '4166 44XX XXXX 2003', tokenisedCreditCard: false}");
   //           expect(message).to.be.equal(null);
   //       }, processedRecord);
   //       stub1.restore();
   //       stub2.restore();
   //       stub3.restore();
   //       done();
   //   });

   it("shouldRecordBeSkipped | different amount | same date", () => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 1500 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.false;
   })
   it("shouldRecordBeSkipped | amount within grace", () => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-05-30T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 9998 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.true;
   })
   it("shouldRecordBeSkipped | date within grace", () => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-03T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-06-12 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 1500 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-05-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.false;
   })
   it("shouldRecordBeSkipped | older bill (due date older than a month) ", () => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = { "id": "********", "customer_id": "*********", "recharge_number": "4166 44XX XXXX 2003", "product_id": "**********", "reference_id": "2022050607910ffcbdc09c479734025548de997f0698d", "operator": "neft_kotakmahindrabank", "amount": "-38507.20", "bill_date": "2022-05-29 00:00:00", "due_date": "2022-05-01 00:00:00", "bill_fetch_date": "2022-05-29 09:42:32", "next_bill_fetch_date": "2022-05-25 01:00:00", "gateway": "creditcardneft", "paytype": "credit card", "service": "financial services", "circle": "", "customer_mobile": "**********", "customer_email": "<EMAIL>", "payment_channel": "ANDROIDAPP 10.2.0", "retry_count": "0", "status": "14", "reason": "", "extra": { "customer_type": 1, "last_paid_amount": 1500 }, "published_date": null, "created_at": "2022-05-20 21:05:17", "updated_at": "2022-05-30 18:01:31", "user_data": { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" }, "notification_status": "1", "payment_date": "2022-01-30 13:44:33", "service_id": "0", "customerOtherInfo": { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" }, "is_automatic": "0", "par_id": "", "tin": "", "bank_name": "nkmb", "card_network": "visa" }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.true;
   })
   it("shouldRecordBeSkipped | older bill (due date is not older than a month) ", () => {
      let processedRecord = { "customerId": *********, "lastCC": "2003", "currentPaidAmount": 10000, "paymentDate": "2022-06-04T13:44:33.487", "smsSenderID": "VM-KOTAKB", "bankName": "KOTAK" }
      let dbRecord = {
         id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: { "customer_type": 1, "last_paid_amount": 1500 },published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" },notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" },is_automatic: "0",par_id: "",tin: "",bank_name: "nkmb",card_network: "visa",      }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.false;
   })
   it("shouldRecordBeSkipped | different amount | different date | due date recent ", () => {
      let processedRecord = {
         customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: "2022-06-04T13:44:33.487",smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
      }
      let dbRecord = {
         id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: { "customer_type": 1, "last_paid_amount": 1500 },published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: { "recharge_number_2": "20220520049111f5d804559123da1d2d3ce1772b33ba3", "recharge_number_3": "2022050607910ffcbdc09c479734025548de997f0698d" },notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: { "customerId": ********, "lastCC": "92", "currentBillAmount": 124300, "currentMinBillAmount": 0, "billDate": "2022-05-01 00:00:00", "billDueDate": "2022-05-21 00:00:00", "smsSenderID": "VM-KOTAKB", "billConsumeTimestamp": "2022-05-03 10:36:16", "debugKey": "smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank", "paymentDate": "2022-05-30 13:44:33" },is_automatic: "0",par_id: "",tin: "",bank_name: "nkmb",card_network: "visa",      }
      let action = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
      expect(action).to.be.false;
   })
           it("updateCCBill | Error in updateCCBillInSystem ", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: "2022-06-04T13:44:33.487",smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
            let stub2 = sinon.stub(serviceObj, 'updateCCBillInSystem').callsFake(function(done){
               return done("Error in updateCCBillInSystem");
            })
            let stub3 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(null);
            serviceObj.updateCCBill(function(error){
               if(error){
                  expect(error).to.be.equal("Error in updateCCBillInSystem");
               }
            }, processedRecord, dbRecord);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(1);
               stub2.restore();
               stub3.restore();
           })
           it("updateCCBill | Success in updateCCBill ", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: "2022-06-04T13:44:33.487",smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
            let stub2 = sinon.stub(serviceObj, 'updateCCBillInSystem').callsFake(function(done){
               return done(null);
            })
            let stub3 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(null);
            serviceObj.updateCCBill(function(error){
               if(error){
                  expect(error).to.be.equal(null);
               }
            }, processedRecord, dbRecord);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(1);
               stub2.restore();
               stub3.restore();
           })


           it("getDbRecordToUpdate | Valid Record ", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
               let response = serviceObj.getDbRecordToUpdate(processedRecord, (dbRecord));
               expect(response.amount).to.be.equal(-48507.20);
               expect(response.status).to.be.equal(14);
               expect(response.payment_date).to.be.equal(processedRecord.paymentDate.format('YYYY-MM-DD HH:mm:ss'));
               expect(JSON.parse(response.customerOtherInfo).paymentDate).to.be.equal(processedRecord.paymentDate.format('YYYY-MM-DD HH:mm:ss'));
               expect(JSON.parse(response.customerOtherInfo).currentBillAmount).to.be.equal(114300);
               expect(JSON.parse(response.customerOtherInfo).currentMinBillAmount).to.be.equal(0);
               expect(JSON.parse(response.extra).last_paid_amount).to.be.equal(10000);
           });

           it("updateCCBillInSystem | Error updateCCBillPaidByCustomerId ", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
              let stub1 = sinon.stub(serviceObj.bills, 'updateCCBillPaidByCustomerId').callsFake(function(cb){
                 return cb("Error in updateCCBillPaidByCustomerId", null);
              })
              let stub2 = sinon.stub(serviceObj, 'updateCCBillInRecentSystem').callsFake(function(cb){
               return cb("Error in updateCCBillInRecentSystem", null);
            })
              serviceObj.updateCCBillInSystem(function(error){
                 if(error)
                 {
                    expect(error).to.be.equal(null);
                 }
              }, processedRecord, dbRecord);
              expect(stub1).to.be.callCount(1);
              expect(stub2).to.be.callCount(0);
              stub1.restore();
              stub2.restore();
           });
           it("updateCCBillInSystem | success updateCCBillPaidByCustomerId ", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let recentRecord = {};
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
              let stub1 = sinon.stub(serviceObj.bills, 'updateCCBillPaidByCustomerId').callsFake(function(cb){
                 return cb(null);
              })
              let stub2 = sinon.stub(serviceObj, 'updateCCBillInRecentSystem').callsFake(function(cb){
               return cb(null);
            })
              serviceObj.updateCCBillInSystem(function(error){
                 if(error)
                 {
                    expect(error).to.be.equal(null);
                 }
              }, processedRecord, dbRecord, recentRecord);
              expect(stub1).to.be.callCount(1);
              expect(stub2).to.be.callCount(0);
              stub1.restore();
              stub2.restore();
           });

           it("updateCCBillInRecentSystem", () => {
            let processedRecord = {
               customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
            }
            let dbRecord = {
               id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
              let stub1 = sinon.stub(serviceObj.recentsLayerLib, 'update').callsFake(function(cb){
                 return cb(null);
              })
              serviceObj.updateCCBillInRecentSystem(function(error){
                 if(error)
                 {
                    expect(error).to.be.equal(null);
                 }
              }, processedRecord, dbRecord, null);
              expect(stub1).to.be.callCount(1);
              stub1.restore();
           });
         });

      //  describe("Module publisher:: smsBillPayment :: CT tests", function () {
      //    let serviceObj1,serviceObj2;

      //    let data;
      //    describe('#bar', function() {
      //       it('should call super', function() {
      //         new Bar(opts);

      //         sinon.assert.calledOnce(memcachedStub);
      //         sinon.assert.calledWithExactly(memcachedStub, opts);
      //       });
      //     });

      //     describe('#foo', function() {
      //       it('should call super', function() {
      //         new Foo(opts);

      //         sinon.assert.calledOnce(memcachedStub);
      //         sinon.assert.calledWithExactly(memcachedStub, opts);
      //       });
      //     });

      //    before(function () {
      //        STARTUP_MOCK.init(function(error, options){
      //            serviceObj1 = new creditCardStrategy(options);
      //            done();
      //        });
      //    });
      //    before(function () {
      //       STARTUP_MOCK.init(function(error, options){
      //           serviceObj2 = new parent(options);

      //           done();
      //       });
      //   });
      //      it.skip("updateCassandra | Error in getFinancialServicesPID", () => {
      //       let processedRecord = {
      //          customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
      //       }
      //       let dbRecord = {
      //          id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
      //         let stub1 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns('0_KOTAK_dummynetwork');
      //         let stub2 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(done){
      //            return done('Error in getFinancialServicesPID');
      //         })
      //         let stub3 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'formatDataForUpdateNonPaytmCards').returns(null)
      //       //   let stub4 = sinon.stub(serviceObj1.parent.nonPaytmKafkaPublisher, 'publishData').callsFake(function(done){
      //       //    return done('Error in publishData');
      //       //   })
      //         serviceObj1.updateCassandra(function(error){
      //            if(error)
      //            {
      //               expect(error).to.be.equal('Error in getFinancialServicesPID');
      //            }
      //         }, processedRecord, dbRecord, null);
      //         expect(stub1).to.have.callCount(1);
      //         expect(stub2).to.have.callCount(1);
      //         expect(stub3).to.have.callCount(0);
      //       //   expect(stub4).to.have.callCount(0);
      //         stub1.restore();
      //         stub2.restore();
      //         stub3.restore();
      //       //   stub4.restore();
      //      });
      //      it.skip("updateCassandra | CVR data doesnt exist for productid", () => {
      //       let processedRecord = {
      //          customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
      //       }
      //       let dbRecord = {
      //          id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
      //         let stub1 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns('0_KOTAK_dummynetwork');
      //         let stub2 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(done){
      //            return done(null, '**********');
      //         })
      //         let stub3 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'formatDataForUpdateNonPaytmCards').returns(null)
      //         serviceObj1.updateCassandra(function(error){
      //            if(error)
      //            {
      //               expect(error).to.be.equal('CVR data not exists for productId:**********');
      //            }
      //         }, processedRecord, dbRecord, null);
      //         expect(stub1).to.have.callCount(1);
      //         expect(stub2).to.have.callCount(1);
      //         expect(stub3).to.have.callCount(0);
      //         stub1.restore();
      //         stub2.restore();
      //         stub3.restore();
      //      });
      //      it("updateCassandra", () => {
      //         let productId= '**********';
      //       let processedRecord = {
      //          customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
      //       }
      //       let dbRecord = {
      //          id: "********",customer_id: "*********",recharge_number: "4166 44XX XXXX 2003",product_id: "**********",reference_id: "2022050607910ffcbdc09c479734025548de997f0698d",operator: "neft_kotakmahindrabank",amount: "-38507.20",bill_date: "2022-05-29 00:00:00",due_date: "2022-05-10 00:00:00",bill_fetch_date: "2022-05-29 09:42:32",next_bill_fetch_date: "2022-05-25 01:00:00",gateway: "creditcardneft",paytype: "credit card",service: "financial services",circle: "",customer_mobile: "**********",customer_email: "<EMAIL>",payment_channel: "ANDROIDAPP 10.2.0",retry_count: "0",status: "14",reason: "",extra: {"customer_type":1,"last_paid_amount":1500},published_date: null,created_at: "2022-05-20 21:05:17",updated_at: "2022-05-30 18:01:31",user_data: {"recharge_number_2":"20220520049111f5d804559123da1d2d3ce1772b33ba3","recharge_number_3":"2022050607910ffcbdc09c479734025548de997f0698d"},notification_status: "1",payment_date: "2022-01-30 13:44:33",service_id: "0",customerOtherInfo: {"customerId":********,"lastCC":"92","currentBillAmount":124300,"currentMinBillAmount":0,"billDate":"2022-05-01 00:00:00","billDueDate":"2022-05-21 00:00:00","smsSenderID":"VM-KOTAKB","billConsumeTimestamp":"2022-05-03 10:36:16","debugKey":"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003_Id:********_MCN:4166 44XX XXXX 2003_operator:neft_kotakmahindrabank","paymentDate":"2022-05-30 13:44:33"},is_automatic: "0",par_id:"",tin: "",bank_name: "nkmb",card_network: "visa",}
      //       serviceObj1.parent = serviceObj2;
      //       console.log("serviceob1", serviceObj1);

      //         let stub1 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns('0_KOTAK_dummynetwork');
      //         let stub2 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(done){
      //            return done(null, productId);
      //         })
      //         let stub3 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'formatDataForUpdateNonPaytmCards').returns({})
      //         let stub4 = sinon.stub(serviceObj1.parent.nonPaytmKafkaPublisher, 'publishData').returns(null);
      //       //   let superValidateStub = sinon.mock(serviceObj1.__super__)
      //         serviceObj1.updateCassandra(function(error){
      //            if(error)
      //            {
      //               expect(error).to.be.equal(null);
      //            }
      //         }, processedRecord, dbRecord, null);
      //         expect(stub1).to.have.callCount(1);
      //         expect(stub2).to.have.callCount(1);
      //         expect(stub3).to.have.callCount(1);
      //         expect(stub4).to.have.callCount(1);
      //         stub1.restore();
      //         stub2.restore();
      //         stub3.restore();
      //         stub4.restore();
      //      });

      //    it("publishCtAndPFCCEvents function | check function calls", (done) => {
      //       let record = {
      //          customerId: *********,lastCC: '2003',currentPaidAmount: 10000,paymentDate: MOMENT("2022-05-30T13:44:33.487"),smsSenderID: 'VM-KOTAKB',bankName: 'KOTAK'
      //       }
      //        let cb = sinon.spy();

      //        serviceObj.ctKafkaPublisher = {
      //            publishData : () => {
      //                return cb(null, data)
      //            }
      //        }
      //        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
      //        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
      //        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

      //        serviceObj.publishCtAndPFCCEvents(cb, record);
      //        stub1.restore();
      //        stub2.restore();
      //        stub3.restore();

      //        expect(stub1).to.have.callCount(1)
      //        expect(stub2).to.have.callCount(1)
      //        expect(stub3).to.have.callCount(1)
      //        expect(cb).to.have.been.calledWith(null)
      //        return done();
      //    })

      //    it("publishCtAndPFCCEvents function | no retailerStatus", (done) => {
      //        let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": 1574395093059, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "1000955756", "isReliable" : "1" }));
      //        let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      //        let cb = sinon.spy();

      //        serviceObj.ctKafkaPublisher = {
      //            publishData : () => {
      //                return cb(null, data)
      //            }
      //        }
      //        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
      //        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
      //        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

      //        serviceObj.publishCtAndPFCCEvents(cb, record);
      //        stub1.restore();
      //        stub2.restore();
      //        stub3.restore();

      //        expect(stub1).to.have.callCount(1)
      //        expect(stub2).to.have.callCount(0)
      //        expect(stub3).to.have.callCount(0)
      //        return done();
      //    })

      //    it("publishCtAndPFCCEvents function | no thumbnail", (done) => {
      //        let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": 1574395093059, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "1000955756", "isReliable" : "1" }));
      //        let [error, record] = serviceObj.validateAndProcessRecord(recordData);
      //        let cb = sinon.spy();

      //        serviceObj.ctKafkaPublisher = {
      //            publishData : () => {
      //                return cb(null, data)
      //            }
      //        }
      //        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
      //        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
      //        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

      //        serviceObj.publishCtAndPFCCEvents(cb, record);
      //        stub1.restore();
      //        stub2.restore();
      //        stub3.restore();

      //        expect(stub1).to.have.callCount(1)
      //        expect(stub2).to.have.callCount(1)
      //        expect(stub3).to.have.callCount(0)
      //        return done();
      //    })
// })



