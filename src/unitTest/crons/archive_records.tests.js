'use strict';

import { describe, it, before, afterEach, beforeEach } from 'mocha';
import sinon from 'sinon';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _ from 'lodash';

import ARCHIVAL_RECORDS from '../../crons/archive_records'
import STARTUP_MOCK from '../__mocks__/startUp'
import ASYNC from 'async'


chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("archival_records ::  test cases :: ", function () {
    let serviceObj, sandbox;

    let data, record;
    before(function (done) {
        STARTUP_MOCK.init(function (error, options) {
            serviceObj = new ARCHIVAL_RECORDS(options);
            // Initialize Kafka publishers
            serviceObj.nonPaytmKafkaPublisher = {
                publishData: (data, callback) => callback(null)
            };
            serviceObj.checkActiveUserKafkaPublisher = {
                publishData: (data, callback) => callback(null)
            };
            done(error);
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
        sandbox = sinon.createSandbox();
    });

    afterEach(function () {
        server.restore();
        sandbox.restore();
    });



    describe('insertingIntoCustIdRnMappingTable', () => {
        it('should successfully insert record into mapping table', function (done) {
            const record = {
                customer_id: '123',
                recharge_number: '987'
            };
            const callback = sinon.spy();
            const mockCassandraBillsDumpIntoCustIdRnMappingTable = sinon.stub().callsFake((callback, payload) => callback(null, [])); // Simulate no existing record
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.cassandraBills.dumpIntoCustIdRnMappingTable = mockCassandraBillsDumpIntoCustIdRnMappingTable;
            serviceObj.insertingIntoCustIdRnMappingTable((err) => {
                expect(err).to.be.undefined;
                expect(mockCassandraBillsDumpIntoCustIdRnMappingTable.calledOnce).to.be.true;
                // expect(metricsStub.calledWith(1, [
                //     'REQUEST_TYPE:ARCHIVAL_CRON',
                //     'STATUS:SUCCESS',
                //     'TYPE:DB_INSERTION_CUST_ID_RN_MAPPING'
                // ])).to.be.true;
                return done();
            }, record);
        });

        it('should handle database errors', (done) => {
            const record = {
                customer_id: '123',
                recharge_number: '987'
            };

            const callback = sinon.spy();
            const mockCassandraBillsDumpIntoCustIdRnMappingTable = sinon.stub().callsFake((callback, payload) => callback(new Error('DB Error'))); // Simulate no existing record
            serviceObj.cassandraBills.dumpIntoCustIdRnMappingTable = mockCassandraBillsDumpIntoCustIdRnMappingTable;
            serviceObj.insertingIntoCustIdRnMappingTable((err) => {
                expect(mockCassandraBillsDumpIntoCustIdRnMappingTable.calledOnce).to.be.true;
                done();
            }, record);
        });

        it('should skip insertion when feature flag is disabled', (done) => {
            const record = {
                customer_id: '123',
                recharge_number: '987'
            };
            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'ARCHIVAL_CRONS', 'SMART_FETCH_CONFIG', 'ALLOW_FOR_INSERT_INTO_CUST_ID_RN_MAPPING'], false)
            // Disable the feature flag
            const callback = sinon.spy();
            const mockCassandraBillsDumpIntoCustIdRnMappingTable = sinon.stub().callsFake((callback, payload) => callback(new Error('DB Error'))); // Simulate no existing record
            serviceObj.cassandraBills.dumpIntoCustIdRnMappingTable = mockCassandraBillsDumpIntoCustIdRnMappingTable;
            serviceObj.insertingIntoCustIdRnMappingTable((err) => {
                expect(err).to.be.null;
                expect(mockCassandraBillsDumpIntoCustIdRnMappingTable.called).to.be.false;
                done();
            }, record);
        });
    });

    describe('preparePayloadForCustIdRn', () => {
        it('should prepare correct payload from record', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                created_at: new Date(),
                updated_at: new Date()
            };

            const payload = serviceObj.preparePayloadForCustIdRn(record);

            expect(payload).to.have.property('customer_id', record.customer_id);
            expect(payload).to.have.property('recharge_number', record.recharge_number);
            expect(payload).to.have.property('status');
        });

        it('should handle missing dates in record', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987'
            };

            const payload = serviceObj.preparePayloadForCustIdRn(record);

            expect(payload).to.have.property('customer_id', record.customer_id);
            expect(payload).to.have.property('recharge_number', record.recharge_number);
            expect(payload).to.have.property('status');
        });
    });

    describe('archiveRecord', () => {
        afterEach(function () {
            sandbox.restore();
        });

        it('should successfully archive a record', (done) => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                service: 'electricity',
                operator: 'bses yamuna',
                extra: JSON.stringify({ customer_id: '123' })
            };

            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));

            const callback = sinon.spy();
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord('bills_bses', record, (err) => {
                expect(err).to.be.null;
                expect(insertStub.calledOnce).to.be.true;
                expect(mockInsertRecordInArchive.calledOnce).to.be.true;
                expect(mockRemoveRecord.calledOnce).to.be.true;
                insertStub.restore();
                done();
            });
        });

        it('should throw an error when source table is null archive a record', (done) => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                service: 'electricity',
                operator: 'bses yamuna',
                extra: JSON.stringify({ customer_id: '123' })
            };

            const callback = sinon.spy();
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord(null, record, (err) => {
                console.log("🚀 ~ serviceObj.archiveRecord ~ err:", err)
                expect(err).to.be.null;
                expect(mockInsertRecordInArchive.calledOnce).to.be.false;
                expect(mockRemoveRecord.calledOnce).to.be.false;
                done();
            });
        });

        it('should handle automatic bills archive a record', (done) => {
            const record = {
                customer_id: '123',
                is_automatic: 1,
                recharge_number: '987',
                service: 'electricity',
                operator: 'bses yamuna',
                extra: JSON.stringify({ customer_id: '123' })
            };

            const callback = sinon.spy();
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord("bills_bses", record, (err) => {
                console.log("🚀 ~ serviceObj.archiveRecord ~ err:", err)
                expect(err).to.be.null;
                expect(mockInsertRecordInArchive.calledOnce).to.be.false;
                expect(mockRemoveRecord.calledOnce).to.be.false;
                done();
            });
        });

        it('should handle user initiated deletion', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                reason: 'userInitDeletion',
                recharge_number: '9876543210',
                extra: JSON.stringify({ customer_id: '123' })
            };
            console.log("🚀 ~ it.only ~ serviceObj.nonPaytmKafkaPublisher:", serviceObj.nonPaytmKafkaPublisher)

            const publishStub = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData')
                .callsFake((data, callback) => callback(null));
            const callback = sinon.spy();
            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                expect(insertStub.calledOnce).to.be.true;
                expect(publishStub.calledOnce).to.be.true;
                expect(mockInsertRecordInArchive.calledOnce).to.be.true;
                expect(mockRemoveRecord.calledOnce).to.be.true;
                publishStub.restore();
                done();
            });
        });

        it('should handle expired user', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                reason: 'EXPIRED_USER',
                recharge_number: '9876543210',
                extra: JSON.stringify({ customer_id: '123' })
            };
            serviceObj.allowedServicesForSmartFetch = ['electricity'];
            const publishStub = sinon.stub(serviceObj.checkActiveUserKafkaPublisher, 'publishData')
                .callsFake((data, callback) => callback(null));
            const callback = sinon.spy();
            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            // const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            // const mockPublishCassandraCDCvents = sinon.stub();
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                expect(insertStub.calledOnce).to.be.true;
                expect(publishStub.calledOnce).to.be.true;
                expect(mockInsertRecordInArchive.calledOnce).to.be.true;
                expect(mockRemoveRecord.calledOnce).to.be.true;
                publishStub.restore();
                done();
            });
        });

        it('should handle expired user publishing to kafka error', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                reason: 'EXPIRED_USER',
                recharge_number: '9876543210',
                extra: JSON.stringify({ customer_id: '123' })
            };
            serviceObj.allowedServicesForSmartFetch = ['electricity'];
            const publishStub = sinon.stub(serviceObj.checkActiveUserKafkaPublisher, 'publishData')
                .callsFake((data, callback) => callback(new Error('DB Error')));

            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;

            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                expect(insertStub.calledOnce).to.be.true;
                expect(publishStub.calledOnce).to.be.true;
                expect(mockInsertRecordInArchive.calledOnce).to.be.true;
                expect(mockRemoveRecord.calledOnce).to.be.true;
                publishStub.restore();
                insertStub.restore();
                done();
            });
        });

        it('should handle error in insertRecordInArchive', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                extra: '{}'
            };

            const insertStubError = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(new Error('DB Error')));
            // serviceObj.billsModel.insertRecordInArchive = insertStubError;
            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                expect(insertStubError.calledOnce).to.be.true;
                insertStubError.restore();
                done();
            });
        });

        it('should handle error in removeRecord', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                extra: '{}'
            };
            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(new Error('DB Error')));
            serviceObj.billsModel.removeRecord = mockRemoveRecord;
            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                expect(mockRemoveRecord.calledOnce).to.be.true;
                done();
            });
        });

        it('should handle bills_airtelprepaid table', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                extra: '{}'
            };

            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));

            serviceObj.archiveRecord('bills_airtelprepaid', record, (err) => {
                expect(err).to.be.null;
                expect(insertStub.calledOnce).to.be.true;
                insertStub.restore();
                done();
            });
        });

        it('should handle invalid JSON in extra field', (done) => {
            const record = {
                id: 1,
                operator: 'airtel',
                service: 'electricity',
                extra: 'invalid-json',
                reference_id: '12345'
            };

            serviceObj.L = {
                error: sinon.stub(),
                log: sinon.stub()
            };

            const insertStub = sandbox.stub(serviceObj, 'insertingIntoCustIdRnMappingTable')
                .callsFake((callback) => callback(null));
            const mockInsertRecordInArchive = sinon.stub().callsFake((callback, payload) => callback(null));
            const mockRemoveRecord = sinon.stub().callsFake((callback, payload) => callback(null));
            serviceObj.billsModel.insertRecordInArchive = mockInsertRecordInArchive;
            serviceObj.billsModel.removeRecord = mockRemoveRecord;
            serviceObj.archiveRecord('bills_airtel', record, (err) => {
                expect(err).to.be.null;
                done();
            });
        });

    });

    describe('fetchAndArchive', () => {
        beforeEach(() => {
            // Initialize logger with stubs
            serviceObj.L = {
                log: sinon.stub(),
                error: sinon.stub(),
                critical: sinon.stub(),
                verbose: sinon.stub()
            };
        });

        it('should handle empty records', (done) => {
            const fetchStub = sinon.stub(serviceObj.billsModel, 'fetchNotInUseRecords')
                .callsFake((callback) => callback(null, []));

            serviceObj.fetchAndArchive('bills_airtel', (error) => {
                expect(error).to.be.undefined;
                expect(fetchStub.calledOnce).to.be.true;
                fetchStub.restore();
                done();
            });
        });

        it('should process records in batches', (done) => {
            const records = [
                { id: 1, operator: 'airtel' },
                { id: 2, operator: 'airtel' }
            ];

            // Stub fetchNotInUseRecords to return records once, then empty array
            const fetchStub = sinon.stub(serviceObj.billsModel, 'fetchNotInUseRecords');
            fetchStub.onFirstCall().callsFake((next, tableName, batchSize, offset) => next(null, records));
            fetchStub.onSecondCall().callsFake((next, tableName, batchSize, offset) => next(null, []));

            // Stub archiveRecords
            const archiveRecordsStub = sinon.stub(serviceObj, 'archiveRecords')
                .callsFake((done, records, tableName) => done());

            serviceObj.fetchAndArchive('bills_airtel', (error) => {
                try {
                    expect(error).to.be.undefined;
                    expect(fetchStub.calledTwice).to.be.true;
                    expect(archiveRecordsStub.calledOnce).to.be.true;
                    done();
                } catch (err) {
                    done(err);
                } finally {
                    fetchStub.restore();
                    archiveRecordsStub.restore();
                }
            });
        });

        it('should handle database fetch errors', (done) => {
            const fetchStub = sinon.stub(serviceObj.billsModel, 'fetchNotInUseRecords')
                .callsFake(function (next, tableName, batchSize, offset) {
                    next(new Error('DB Error'));
                });

            serviceObj.fetchAndArchive('bills_airtel', (error) => {
                try {
                    expect(error).to.be.undefined;
                    done();
                } catch (err) {
                    done(err);
                } finally {
                    fetchStub.restore();
                }
            });
        });

        it('should handle empty result sets', (done) => {
            const fetchStub = sinon.stub(serviceObj.billsModel, 'fetchNotInUseRecords')
                .callsFake((next, tableName, batchSize, offset) => next(null, []));

            serviceObj.fetchAndArchive('bills_airtel', (error) => {
                expect(error).to.be.undefined;
                expect(fetchStub.calledOnce).to.be.true;
                fetchStub.restore();
                done();
            });
        });
    });

    describe('preparePayloadForUserInitDeletion', () => {
        it('should prepare correct payload', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                operator: 'airtel',
                service: 'electricity',
                status: 1,
                amount: 100,
                due_date: '2024-03-20'
            };

            const payload = serviceObj.preparePayloadForUserInitDeletion(record);
            expect(payload).to.have.property('customerId', record.customer_id);
            expect(payload).to.have.property('source', 'archivalCronsUserInitDeletion');
            expect(payload).to.have.property('amount', record.amount);
            expect(payload).to.have.property('dueDate', record.due_date);
        });
    });

    describe('showAnalytics', () => {
        beforeEach(() => {
            // Initialize logger with stubs
            serviceObj.L = {
                log: sinon.stub(),
                error: sinon.stub(),
                critical: sinon.stub(),
                verbose: sinon.stub()
            };
        });

        it('should log analytics data', () => {
            serviceObj.showAnalytics();
            expect(serviceObj.L.log.calledOnce).to.be.false;
        });
    });

    describe('archiveRecords', () => {
        it('should process multiple records in sequence', (done) => {
            const records = [
                { id: 1, operator: 'airtel', service: 'electricity' },
                { id: 2, operator: 'tneb', service: 'electricity' }
            ];

            const archiveStub = sinon.stub(serviceObj, 'archiveRecord')
                .callsFake((tableName, record, callback) => callback(null));

            serviceObj.archiveRecords((error) => {
                expect(error).to.be.undefined;
                expect(archiveStub.callCount).to.equal(2);
                archiveStub.restore();
                done();
            }, records, 'bills_airtel');
        });

        it('should handle errors in individual record processing', (done) => {
            const records = [
                { id: 1, operator: 'airtel' },
                { id: 2, operator: 'tneb' }
            ];

            const archiveStub = sinon.stub(serviceObj, 'archiveRecord')
                .callsFake((tableName, record, callback) => {
                    if (record.id === 1) {
                        callback(new Error('Processing Error'));
                    } else {
                        callback(null);
                    }
                });

            serviceObj.archiveRecords((error) => {
                expect(archiveStub.calledTwice).to.be.false;
                archiveStub.restore();
                done();
            }, records, 'bills_airtel');
        });
    });

    describe('_start', () => {
        it('should process specific table when provided', (done) => {
            const options = { table: 'bills_bses' };

            const callback = sinon.spy();
            // Create stub with proper binding
            const fetchStub = sinon.stub(serviceObj, 'fetchAndArchive')
                .callsFake((tableName, callback) => callback(null));

            serviceObj._start((error) => {
                expect(error).to.be.undefined;
                // expect(fetchStub.calledOnce).to.be.true;
                expect(fetchStub.firstCall.args[0]).to.equal('bills_bses');
                fetchStub.restore();
                done();
            }, options);
        });

        it('should process for bills_airtelprepaid when specific table is null', (done) => {
            const options = { table: null };

            const callback = sinon.spy();
            // Create stub with proper binding
            const fetchStub = sinon.stub(serviceObj, 'fetchAndArchive')
                .callsFake((tableName, callback) => callback(null));

            serviceObj._start((error) => {
                expect(error).to.be.undefined;
                // expect(fetchStub.calledOnce).to.be.true;
                expect(fetchStub.firstCall.args[0]).to.equal('bills_tatapower');
                fetchStub.restore();
                done();
            }, options);
        });

        it('should process all tables including airtelprepaid variants', (done) => {
            const fetchStub = sinon.stub(serviceObj, 'fetchAndArchive')
                .callsFake((tableName, callback) => callback(null));

            serviceObj._start((error) => {
                expect(error).to.be.undefined;
                // Should be called for each table including airtelprepaid0-9
                expect(fetchStub.callCount).to.be.above(10);
                fetchStub.restore();
                done();
            });
        });
    });

    describe('start', () => {
        it('should start processing after successful kafka configuration', (done) => {
            const configureKafkaStub = sinon.stub(serviceObj, 'configureKafka')
                .callsFake((callback) => callback(null));

            const startStub = sinon.stub(serviceObj, '_start')
                .callsFake((callback) => callback(null));

            serviceObj.start();

            setTimeout(() => {
                expect(configureKafkaStub.calledOnce).to.be.true;
                expect(startStub.calledOnce).to.be.true;
                configureKafkaStub.restore();
                startStub.restore();
                done();
            }, 200);
        });

        it('should start processing after throwing error from kafka configuration', (done) => {
            const configureKafkaStub = sinon.stub(serviceObj, 'configureKafka')
                .callsFake((callback) => callback("Error from configureKafka"));

            const startStub = sinon.stub(serviceObj, '_start')
                .callsFake((callback) => callback(null));

            serviceObj.start();

            setTimeout(() => {
                expect(configureKafkaStub.calledOnce).to.be.true;
                expect(startStub.calledOnce).to.be.false;
                configureKafkaStub.restore();
                startStub.restore();
                done();
            }, 200);
        });

        it('should log and exit on error', (done) => {
            const configureKafkaStub = sinon.stub(serviceObj, 'configureKafka')
                .callsFake((callback) => callback(null));

            const startStub = sinon.stub(serviceObj, '_start')
                .callsFake((callback) => callback("Error from _start"));
            const consoleLogStub = sinon.stub(console, 'log');
            const consoleTimeEndStub = sinon.stub(console, 'timeEnd');

            serviceObj.start();

            setTimeout(() => {
                expect(consoleLogStub.calledWith('FAILURE')).to.be.true;
                expect(consoleTimeEndStub.calledWith('Execution Time')).to.be.true;
                consoleLogStub.restore();
                consoleTimeEndStub.restore();
                configureKafkaStub.restore();
                startStub.restore();
                done();
            }, 1000);
        });
    });

    describe('initializeVariable', () => {
        it('should update allowed operators and services from config', () => {
            const newOperators = ['operator1', 'operator2'];
            const newServices = ['service1', 'service2'];

            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], newOperators);
            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'SERVICES'], newServices);

            serviceObj.initializeVariable();

            expect(serviceObj.allowedOperatorsForSmartFetch).to.deep.equal(newOperators);
            expect(serviceObj.allowedServicesForSmartFetch).to.deep.equal(newServices);
        });
    });

    describe('pushRecordToNONPAYTM', () => {
        it('should successfully publish record to kafka', (done) => {
            const payload = {
                service: 'electricity',
                operator: 'airtel'
            };

            const publishStub = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData')
                .callsFake((data, callback) => callback(null));

            serviceObj.pushRecordToNONPAYTM((error) => {
                expect(error).to.be.null;
                expect(publishStub.calledOnce).to.be.true;
                publishStub.restore();
                done();
            }, payload);
        });

        it('should handle kafka publish errors', (done) => {
            const payload = {
                service: 'electricity',
                operator: 'airtel'
            };

            // Initialize logger with stubs
            serviceObj.L = {
                log: sinon.stub(),
                error: sinon.stub(),
                critical: sinon.stub(),
                verbose: sinon.stub()
            };

            const publishStub = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData')
                .callsFake((data, callback) => callback(new Error('Kafka Error')));

            serviceObj.pushRecordToNONPAYTM((error) => {
                try {
                    expect(error).to.be.null;
                    expect(publishStub.calledOnce).to.be.true;
                    expect(serviceObj.L.critical.calledOnce).to.be.true;
                    done();
                } catch (err) {
                    done(err);
                } finally {
                    publishStub.restore();
                }
            }, payload);
        });
    });

    describe('offset and analytics methods', () => {
        it('should handle offset operations', () => {
            serviceObj.resetFromIdOffset();
            expect(serviceObj.getFromIdOffset()).to.equal(0);

            serviceObj.updateFromIdOffset(5);
            expect(serviceObj.getFromIdOffset()).to.equal(5);

            serviceObj.updateFromIdOffset(3);
            expect(serviceObj.getFromIdOffset()).to.equal(5);
        });

        it('should handle analytics operations', () => {
            const tableName = 'bills_test';

            serviceObj.updateAnalytics(tableName, 10);
            expect(serviceObj.getArchivedRecordCount(tableName)).to.equal(10);

            serviceObj.updateAnalytics(tableName, 5);
            expect(serviceObj.getArchivedRecordCount(tableName)).to.equal(15);
        });
    });

    describe('configureKafka', () => {
        beforeEach(() => {
            // Initialize logger with stubs
            serviceObj.L = {
                log: sinon.stub(),
                error: sinon.stub(),
                critical: sinon.stub(),
                verbose: sinon.stub()
            };
        });

        it('should handle nonPaytmKafkaPublisher initialization error', (done) => {
            const kafkaError = new Error('Kafka Init Error');

            // Mock the kafka producer
            serviceObj.infraUtils = {
                kafka: {
                    producer: function () {
                        return {
                            initProducer: (priority, callback) => callback(kafkaError)
                        };
                    }
                }
            };

            serviceObj.configureKafka((error) => {
                try {
                    expect(error).to.equal(kafkaError);
                    expect(serviceObj.L.critical.calledOnce).to.be.true;
                    done();
                } catch (err) {
                    done(err);
                }
            });
        });

        it('should handle checkActiveUserKafkaPublisher initialization error', (done) => {
            let callCount = 0;
            serviceObj.infraUtils.kafka.producer = () => ({
                initProducer: (priority, callback) => {
                    callCount++;
                    if (callCount === 1) {
                        callback(null); // nonPaytmKafkaPublisher succeeds
                    } else {
                        callback(new Error('Kafka Error')); // checkActiveUserKafkaPublisher fails
                    }
                }
            });

            serviceObj.configureKafka((error) => {
                expect(error).to.exist;
                done();
            });
        });
    });

    describe('preparePayload', () => {
        it('should handle all optional fields', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                operator: 'airtel',
                service: 'electricity',
                customer_mobile: '9876543210',
                customer_email: '<EMAIL>',
                customer_other_info: '{"key":"value"}',
                product_id: 'pid123'
            };

            const payload = serviceObj.preparePayload(record);

            expect(payload).to.include({
                customerId: record.customer_id,
                rechargeNumber: record.recharge_number,
                operator: record.operator,
                service: record.service,
                customerMobile: record.customer_mobile,
                customerEmail: record.customer_email,
                customerOtherInfo: record.customer_other_info,
                productId: record.product_id,
                source: 'archivalCronsExpiredUser'
            });

            expect(JSON.parse(payload.extra)).to.include({
                eventState: 'bill_gen',
                billSource: 'archivalCronsExpiredUser'
            });
        });

        it('should handle missing optional fields', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                operator: 'airtel',
                service: 'electricity'
            };

            const payload = serviceObj.preparePayload(record);

            expect(payload.customerMobile).to.be.undefined;
            expect(payload.customerEmail).to.be.undefined;
            expect(payload.customerOtherInfo).to.be.undefined;
            expect(payload.source).to.equal('archivalCronsExpiredUser');
        });

        it('should include config values', () => {
            const record = {
                customer_id: '123',
                recharge_number: '987',
                operator: 'airtel',
                service: 'electricity',
                product_id: 'pid123'
            };

            serviceObj.config.COMMON = {
                bills_status: { PENDING: 2 },
                notification_status: { ENABLED: 3 }
            };

            const payload = serviceObj.preparePayload(record);

            expect(payload.status).to.equal(2);
            expect(payload.notificationStatus).to.equal(3);
        });
    });

});

