/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import nock from 'nock'

import recentBillLibrary from '../../lib/recentBills'
import STARTUP_MOCK from '../__mocks__/startUp'
import BillSubscriber from '../../services/billSubscriber';
// src/unitTest/lib/recentBills.tests.js
// src/services/billSubscriber.js

chai.use(chaiAsPromised);
chai.use(sinonChai);


const { expect } = chai;

let server;

describe("Library: Payment Gateway library test suite", function () {
    let libraryObj, billSubscriber;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new recentBillLibrary(options);
            billSubscriber = new BillSubscriber(options);

            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });
   
    it("_initRecentBillSpecificData || ", (done) => {
        let billTable = {
            "airteltv": "bills_airteltv",
            "central power distribution corporation of a.p ltd (apcpdcl)": "bills_apcpdcl",
            "neft_hdfcbank": "bills_creditcard",
            "neft_icicibank" : "bills_creditcard",
        };
        let recentBillsOperators = {
            "airteltv": {
                "firstBillDelay": 2,
                "paytypes": [
                    "prepaid"
                ],
                "BILL_DELAY_TIME_UNIT": "hours",
                "mandatoryUpdateNBFD": 1
            },
            "central power distribution corporation of a.p ltd (apcpdcl)": {
                "firstBillDelay": 15
            },
            "neft_hdfcbank": {
                "paytypes": [
                    "credit card"
                ]
            }
        };
        let recentBillSpecificData = libraryObj._initRecentBillSpecificData(billTable, recentBillsOperators);
        expect(_.get(recentBillSpecificData , ['airteltv' , 'firstBillDelay' ] , "default1" )).to.be.equal(_.get(recentBillsOperators , ['airteltv' , 'firstBillDelay' ] , "default2" ));
        expect(_.get(recentBillSpecificData , ['airteltv' , 'BILL_DELAY_TIME_UNIT' ] , "default1" )).to.be.equal(_.get(recentBillsOperators , ['airteltv' , 'BILL_DELAY_TIME_UNIT' ] , "default2" ));
        expect(_.get(recentBillSpecificData , ['neft_icicibank' ] , "default1" )).to.be.equal("bills_creditcard");

        return done();
    });


    it("_isRemindable || operator on-boarded || status is true ", (done) => {
        let rechargeData = {
                "id": "************",
                "catalogProductID": "*********",
                "reqType": "RECHARGE",
                "userData_recharge_number": "*************",
                "customerInfo_customer_id": **********,
                "userData_amount": 127,
                "customerInfo_customer_type": 1,
                "inStatusMap_responseCode": "00",
                "productInfo_operator": "central power distribution corporation of a.p ltd (apcpdcl)",
                "productInfo_paytype": "postpaid",
                "currentGw": "apspdcl",
                "productInfo_circle": "APCPDCL",
                "productInfo_service": "Electricity"
            } ,
            recent_bills_operators = {
                "airteltv": {
                    "firstBillDelay": 2,
                    "paytypes": [
                        "prepaid"
                    ],
                    "BILL_DELAY_TIME_UNIT": "hours",
                    "mandatoryUpdateNBFD": 1
                },
                "central power distribution corporation of a.p ltd (apcpdcl)": {
                    "firstBillDelay": 15
                },
                "neft_hdfcbank": {
                    "paytypes": [
                        "credit card"
                    ]
                },
                "Hero FinCorp": "bills_emiDue"
            }, 
            excluded = {
                "CUSTOMER_IDS": [
                    "********",
                    "********"
                ],
                "PAYTYPE": []
            };

        let status = libraryObj._isRemindable(rechargeData, recent_bills_operators, excluded);
        expect(status).to.be.equal(true);
        return done();
    });

    it.skip("_isRemindable || operator not on-boarding || status is false", (done) => {
        let rechargeData = {
                "id": "************",
                "catalogProductID": "*********",
                "reqType": "RECHARGE",
                "userData_recharge_number": "*************",
                "customerInfo_customer_id": **********,
                "userData_amount": 127,
                "customerInfo_customer_type": 1,
                "inStatusMap_responseCode": "00",
                "productInfo_operator": "central power distribution corporation of a.p ltd (apcpdcl)",
                "productInfo_paytype": "postpaid",
                "currentGw": "apspdcl",
                "productInfo_circle": "APCPDCL",
                "productInfo_service": "Electricity"
            } ,
            recent_bills_operators = {
                "airteltv": {
                    "firstBillDelay": 2,
                    "paytypes": [
                        "prepaid"
                    ],
                    "BILL_DELAY_TIME_UNIT": "hours",
                    "mandatoryUpdateNBFD": 1
                },
                "neft_hdfcbank": {
                    "paytypes": [
                        "credit card"
                    ]
                },
                "Hero FinCorp": "bills_emiDue"
            }, 
            excluded = {
                "CUSTOMER_IDS": [
                    "********",
                    "********"
                ],
                "PAYTYPE": []
            };

        let status = libraryObj._isRemindable(rechargeData, recent_bills_operators, excluded);
        expect(status).to.be.equal(false);
        return done();
    });

    it("_isRemindable || When CUSTOMER_IDS is excluded || status is false ", (done) => {
        let rechargeData = {
                "id": "************",
                "catalogProductID": "*********",
                "reqType": "RECHARGE",
                "userData_recharge_number": "*************",
                "customerInfo_customer_id": ********,
                "userData_amount": 127,
                "customerInfo_customer_type": 1,
                "inStatusMap_responseCode": "00",
                "productInfo_operator": "central power distribution corporation of a.p ltd (apcpdcl)",
                "productInfo_paytype": "postpaid",
                "currentGw": "apspdcl",
                "productInfo_circle": "APCPDCL",
                "productInfo_service": "Electricity"
            } ,
            recent_bills_operators = {
                "airteltv": {
                    "firstBillDelay": 2,
                    "paytypes": [
                        "prepaid"
                    ],
                    "BILL_DELAY_TIME_UNIT": "hours",
                    "mandatoryUpdateNBFD": 1
                },
                "central power distribution corporation of a.p ltd (apcpdcl)": {
                    "firstBillDelay": 15
                },
                "neft_hdfcbank": {
                    "paytypes": [
                        "credit card"
                    ]
                },
                "Hero FinCorp": "bills_emiDue"
            }, 
            excluded = {
                "CUSTOMER_IDS": [
                    "********",
                    "********"
                ],
                "PAYTYPE": []
            };

        let status = libraryObj._isRemindable(rechargeData, recent_bills_operators, excluded);
        expect(status).to.be.equal(true);
        return done();
    });
    
    it("_isRemindable || When PAYTYPE is excluded || status is false ", (done) => {
        let rechargeData = {
                "id": "************",
                "catalogProductID": "*********",
                "reqType": "RECHARGE",
                "userData_recharge_number": "*************",
                "customerInfo_customer_id": **********,
                "userData_amount": 127,
                "customerInfo_customer_type": 1,
                "inStatusMap_responseCode": "00",
                "productInfo_operator": "central power distribution corporation of a.p ltd (apcpdcl)",
                "productInfo_paytype": "postpaid",
                "currentGw": "apspdcl",
                "productInfo_circle": "APCPDCL",
                "productInfo_service": "Electricity"
            } ,
            recent_bills_operators = {
                "airteltv": {
                    "firstBillDelay": 2,
                    "paytypes": [
                        "prepaid"
                    ],
                    "BILL_DELAY_TIME_UNIT": "hours",
                    "mandatoryUpdateNBFD": 1
                },
                "central power distribution corporation of a.p ltd (apcpdcl)": {
                    "firstBillDelay": 15,
                    "paytypes": [
                        "postpaid"
                    ]
                },
                "neft_hdfcbank": {
                    "paytypes": [
                        "credit card"
                    ]
                },
                "Hero FinCorp": "bills_emiDue"
            }, 
            excluded = {
                "CUSTOMER_IDS": [
                    "********",
                    "********"
                ],
                "PAYTYPE": [
                    "postpaid"
                ]
            };

        let status = libraryObj._isRemindable(rechargeData, recent_bills_operators, excluded);
        expect(status).to.be.equal(false);
        return done();
    });

    it("_isRemindable || When paytype didn't match with config || status is false ", (done) => {
        let rechargeData = {
                "id": "************",
                "catalogProductID": "*********",
                "reqType": "RECHARGE",
                "userData_recharge_number": "*************",
                "customerInfo_customer_id": **********,
                "userData_amount": 127,
                "customerInfo_customer_type": 1,
                "inStatusMap_responseCode": "00",
                "productInfo_operator": "central power distribution corporation of a.p ltd (apcpdcl)",
                "productInfo_paytype": "postpaid",
                "currentGw": "apspdcl",
                "productInfo_circle": "APCPDCL",
                "productInfo_service": "Electricity"
            } ,
            recent_bills_operators = {
                "airteltv": {
                    "firstBillDelay": 2,
                    "paytypes": [
                        "prepaid"
                    ],
                    "BILL_DELAY_TIME_UNIT": "hours",
                    "mandatoryUpdateNBFD": 1
                },
                "central power distribution corporation of a.p ltd (apcpdcl)": {
                    "firstBillDelay": 15,
                    "paytypes": [
                        "prepaid"
                    ]
                },
                "neft_hdfcbank": {
                    "paytypes": [
                        "credit card"
                    ]
                },
                "Hero FinCorp": "bills_emiDue"
            }, 
            excluded = {
                "CUSTOMER_IDS": [
                    "********",
                    "********"
                ],
                "PAYTYPE": [
                ]
            };

        let status = libraryObj._isRemindable(rechargeData, recent_bills_operators, excluded);
        expect(status).to.be.equal(false);
        return done();
    });

    // it("prepareRecentBillData || Flow execution ", (done) => { 

    //     let rechargeData = {
    //             "id": "************",
    //             "catalogProductID": "972",
    //             "reqType": "RECHARGE",
    //             "userData_recharge_number": "*************",
    //             "customerInfo_customer_id": **********,
    //             "userData_amount": 127,
    //             "customerInfo_customer_type": 1,
    //             "inStatusMap_responseCode": "00",
    //             "productInfo_operator": "airteltv",
    //             "productInfo_paytype": "prepaid",
    //             "currentGw": "airteltv",
    //             "productInfo_circle": "",
    //             "productInfo_service": "DTH",
    //             "userInfoResponse" : {"billDate" : "2020-03-11 00:00:00" }
    //         },
    //         recent_bills_operators = {
    //             "airteltv": {
    //                 "firstBillDelay": 2,
    //                 "paytypes": [
    //                     "prepaid"
    //                 ],
    //                 "BILL_DELAY_TIME_UNIT": "hours",
    //                 "mandatoryUpdateNBFD": 1
    //             },
    //             "central power distribution corporation of a.p ltd (apcpdcl)": {
    //                 "firstBillDelay": 15
    //             },
    //             "neft_hdfcbank": {
    //                 "paytypes": [
    //                     "credit card"
    //                 ]
    //             }
    //         },
    //         userData = {};
    //     let billsObjs = libraryObj.prepareRecentBillData(rechargeData, recent_bills_operators, userData, billSubscriber);
    //     expect(billsObjs.customerId).to.be.equal(**********);

    //     expect(billsObjs.nextBillFetchDate).to.be.equal(MOMENT().add(2, 'hours').format('YYYY-MM-DD HH:mm:ss'));
    //     expect(billsObjs.retryCount).to.be.equal(0);
    //     expect(billsObjs.billDate).to.be.equal("2020-03-11");
    //     expect(billsObjs.notificationStatus).to.be.equal(1);

    //     return done();
    // });

    it("decideNextDueDate ||  ", (done) => { 
        let recent_bills_operators = {
            "airteltv": {
                "firstBillDelay": 2,
                "paytypes": [
                    "prepaid"
                ],
                "BILL_DELAY_TIME_UNIT": "hours",
                "mandatoryUpdateNBFD": 1
            },
            "central power distribution corporation of a.p ltd (apcpdcl)": {
                "firstBillDelay": 15
            },
            "neft_hdfcbank": {
                "paytypes": [
                    "credit card"
                ]
            },
            "Hero FinCorp": "bills_emiDue",
            "bharatgas": {
                "firstBillDelay": 210,
                "paytypes": [
                    "recharge"
                ]
            },
            "hp gas": {
                "firstBillDelay": 210,
                "paytypes": [
                    "recharge"
                ]
            },
            "indane": {
                "firstBillDelay": 210,
                "paytypes": [
                    "recharge"
                ]
            },
            "visa_sbi": "bills_creditcard",
            "surat municipal corporation - water": {
                "firstBillDelay": 10
            },
            "Aditya birla finance limited": "bills_emidue_abfl",
            "Fullerton India credit company limited": "bills_emidue_fullerton",
            "Fullerton India Housing Finance Limited": "bills_emidue_fullerton",
            "L&T Finance Limited-CAAS": "bills_emidue_lnt",
            "StashFin-CAAS": "bills_emidue_stashfin"
        }, 
        operator = "central power distribution corporation of a.p ltd (apcpdcl)", 
        amount = 127;
        let deducedDueDate = libraryObj.decideNextDueDate(recent_bills_operators, operator, amount);
        expect(deducedDueDate).to.be.equal(null);
        return done();
    });

    it("getUserData ||  ", (done) => { 
        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "*************",
            "userData_recharge_number_2": "RN2",
            "userData_recharge_number_3": "RN3",
            "userData_recharge_number_4": "",
            "userData_recharge_number_5": null,
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let userData = libraryObj.getUserData(rechargeData);
        expect(_.get(userData , ['recharge_number_2'] , 'defaultOutput_RN2')).to.be.equal(_.get(rechargeData, [ 'userData_recharge_number_2' ] , 'expectedOutput_RN2'));
        expect(_.get(userData , ['recharge_number_3'] , 'defaultOutput_RN3')).to.be.equal(_.get(rechargeData, [ 'userData_recharge_number_3' ] , 'expectedOutput_RN3'));
        expect(_.get(userData , ['recharge_number_4'] , 'defaultOutput_RN4')).to.be.equal('defaultOutput_RN4');
        expect(_.get(userData , ['recharge_number_5'] , 'defaultOutput_RN5')).to.be.equal('defaultOutput_RN5');
        return done();
    });

    it('isCreateBillDataValid || When Bill Data is Valid || true', (done)=>{
      let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "*************",
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);
        expect(isBillsDataValid).to.be.equal(true);
        return done();
    });


    it('isCreateBillDataValid || When Bill Data is Valid || true', (done)=>{
        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "*************",
            "customerInfo_customer_id": '**********',
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);
        expect(isBillsDataValid).to.be.equal(true);
        return done();
    });



    // it('isCreateBillDataValid || When Bill Data is invalid || false || customer id is not number   ', (done)=>{
    //     let rechargeData = {
    //         "id": "************",
    //         "catalogProductID": "972",
    //         "reqType": "RECHARGE",
    //         "userData_recharge_number": "*************",
    //         "customerInfo_customer_id": '11071993XX27',
    //         "userData_amount": 127,
    //         "customerInfo_customer_type": 1,
    //         "inStatusMap_responseCode": "00",
    //         "productInfo_operator": "airteltv",
    //         "productInfo_paytype": "prepaid",
    //         "currentGw": "airteltv",
    //         "productInfo_circle": "",
    //         "productInfo_service": "DTH"
    //     };
        
    //     let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);

    //     expect(isBillsDataValid).to.be.equal(false);
    //     return done();
    // });

    // it('isCreateBillDataValid || When Bill Data is invalid || false || catalogProductID is not number   ', (done)=>{
    //     let rechargeData = {
    //         "id": "************",
    //         "catalogProductID": "97XX2",
    //         "reqType": "RECHARGE",
    //         "userData_recharge_number": "*************",
    //         "customerInfo_customer_id": **********,
    //         "userData_amount": 127,
    //         "customerInfo_customer_type": 1,
    //         "inStatusMap_responseCode": "00",
    //         "productInfo_operator": "airteltv",
    //         "productInfo_paytype": "prepaid",
    //         "currentGw": "airteltv",
    //         "productInfo_circle": "",
    //         "productInfo_service": "DTH"
    //     };
    //     let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);

    //     expect(isBillsDataValid).to.be.equal(false);
    //     return done();
    // });

    it('isCreateBillDataValid || When Bill Data is invalid || false || userData_recharge_number is missing ', (done)=>{
        /** userData_recharge_number is missing */
        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);
        expect(isBillsDataValid).to.be.equal(false);
        
        return done();
    });

    it('isCreateBillDataValid || When Bill Data is invalid || false || userData_recharge_number is null ', (done)=>{
        /** userData_recharge_number is zero */

        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": null,
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);

        expect(isBillsDataValid).to.be.equal(false);
        return done();
    });

    it('isCreateBillDataValid || When Bill Data is invalid || false || userData_recharge_number is zero ', (done)=>{
        /** userData_recharge_number is zero */

        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "0",
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": "airteltv",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);

        expect(isBillsDataValid).to.be.equal(true);
        return done();
    });


    it('isCreateBillDataValid || When Bill Data is invalid || false || productInfo_operator is missing ', (done)=>{
        /** productInfo_operator is missing */
        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "*************",
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);
        expect(isBillsDataValid).to.be.equal(false);

        return done();
    });

    it('isCreateBillDataValid || When Bill Data is invalid || false || productInfo_operator is null   ', (done)=>{
        /** productInfo_operator is null */

        let rechargeData = {
            "id": "************",
            "catalogProductID": "972",
            "reqType": "RECHARGE",
            "userData_recharge_number": "*************",
            "customerInfo_customer_id": **********,
            "userData_amount": 127,
            "customerInfo_customer_type": 1,
            "inStatusMap_responseCode": "00",
            "productInfo_operator": null,
            "productInfo_paytype": "prepaid",
            "currentGw": "airteltv",
            "productInfo_circle": "",
            "productInfo_service": "DTH"
        };
        let isBillsDataValid = libraryObj.isCreateBillDataValid(rechargeData);

        expect(isBillsDataValid).to.be.equal(false);
        return done();
    });

    



    
    
});
/**
 * case let say we on-board new operator in emidue consumer
 * Will it be on-boarded in recent service flow?
 */