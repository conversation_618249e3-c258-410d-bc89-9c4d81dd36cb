

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert, expect } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import AWSCsvIngester from '../../lib/awscsvingester';
import STARTUP_MOCK from '../__mocks__/startUp'
import fs from 'fs'
import { Readable } from 'stream';
import { deepEqual } from 'assert';


describe.skip("Library: csvIngester library test suite", function(){
    let serviceInstance;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            serviceInstance = new AWSCsvIngester(options)
            done()
        });
    });

    it("configure ",()=>{
        serviceInstance.configure("digital-reminder","")
        assert(serviceInstance.bucketName === "digital-reminder")
        assert(serviceInstance.s3 != null)
        assert(serviceInstance.batchSize == 1)
        
    })

    it("start : should call the processBatch function when data is received ",(done)=>{
        let stub = sinon.stub(serviceInstance.s3,'getObject')
        let mockStream = new Readable({
            read(size) {
                return true;
              }
        })
        stub.returns({
            createReadStream : ()=>{return mockStream}
        })
        let processBatch = sinon.stub()
        serviceInstance.start(processBatch)
        for(let i=0;i<1000;i++)serviceInstance.csvStream._events.data()
        assert(processBatch.callCount == 1)
        done()

        stub.restore()
        
        
    })

    // it("start : should return callback when file ends ",(done)=>{
    //     let stub = sinon.stub(serviceInstance.s3,'getObject')
    //     let mockStream = new Readable({
    //         read(size) {
    //             return true;
    //           }
    //     })
    //     stub.returns({
    //         createReadStream : ()=>{return mockStream}
    //     })
    //     let processBatch = sinon.stub()
    //     let callback = sinon.stub()
    //     serviceInstance.start(processBatch,'',callback)
    //     for(let i=0;i<1000;i++)serviceInstance.csvStream._events.data()
    //     serviceInstance.batchRecords =[]
    //     serviceInstance.csvStream._events.end[1]()
        
    //     deepEqual(processBatch.callCount,1,"Process batch called")
        
    //     // deepEqual(processBatch.callCount == 1)
    //     deepEqual(callback.callCount ,1)
    //     done()
        
        
    // })

    
})