/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import nock from 'nock'

import notificationLibrary from '../../lib/notification'
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;


describe("Library: Notification library test suite", function () {
    let libraryObj, billSubscriber;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new notificationLibrary(options);

            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });


    it("getEncryptedEmailData || TYPE->1 All Params success check base64 URL", (done) => {
       let notifier = { "data": {
        "template_type": "email",
        "template_id": "7641",
        "options": {
            "notificationOpts": {
                "recipients": "**********",
                "channel_id": "both",
                "deepLinkObj": {
                    "extra": {
                        "url": null,
                        "url_type": null
                    }
                },
                "noRich": false
            },
            "type": "async",
            "data": {
                "amount": 310,
                "number": "123456789013",
                "recharge_number": "9936501502",
                "operator": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                "operator_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                "operatorRefNumber": "15328",
                "order_id": 100073580589,
                "brand": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                "short_operator_name": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                "payment_mode": "UPI",
                "due_date": "2023-05-12",
                "due_amount": 310,
                "product_id": 1200139386,
                "rechargeNumbersObj": {},
                "card_number": null,
                "category_id": 78640,
                "payment_date": "2022-05-12",
                "customer_id": **********,
                "service": "Electricity",
                "paytype": "postpaid",
                "pay_mode": "UPI",
                "operator_display_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)"
            }
        }
     }
    };

    let stub1 = sinon.stub(libraryObj, 'getTexttoImageDataUrl').returns('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAQCAYAAACWa2gIAAAESElEQVR4Ae3BQWjV9x0A8I/uf3iHQHPIIYzSvUMYwlIQ5sGC0oS9Q0pltdXSlToSmRJDK7XUYKU5RJZCXbVGtDQHQ19JHYZaSKiwCYKv1IOwjL3DO6QQWFpe4Sk5PEYOr/A/TPgGfvxJKoO5HYqfj8ce+0nZJpRwAUNCC5O4KSnhAoaEFiZx09bK+AfeQlWS4WMMIEMTR7Es6cMF9AsrGMOKJMPHGECGJo5i2dZOYQiDtnYKQxhUtAt/s9kZTAoZ3sQIupBjBudt+JlwCYcwinn0YQJf4r5wCYcwinn0YQJf4r6iDIv4JRZRl7yHl3AaV/EUpjCLDjLcRh/GcRU/x58wj38J7+ElnMZVPIUpzKKjaBTn8B0+tdkozuE7fKrot6jgKBaxiEXU0BKO40P8GefRwDTu4+8e2C6MYAoLqOEwcoxKRjCFBdRwGDlGbXYK3TbrwQlM4RpqGEOGd4Uh9OMNXEMNx9HC20IPTmAK11DDGDK8K+nF55hG22a9+BzTaNvar9BAFVVUUUVdKGEC5/AWbmEW1/GGDduFHLkkF3JJjlySC7mi3ZjA7202gBK+knRwC0PCLmFRkuMGKsIASvhK0sEtDEmOoYxn0LDZMZTxDBq2tht30IMyehXtRg8uKnoVT9uQCTM4iXtoYhg5LkpmcBL30MQwclyUdGEOE6jbrE9YUbSK/UJHKGFd0oUdQp+womgV+yUzmPTjZjDp4frxJE5IFvAycuzEGrrwF+xAB+cwa0MmfIEK5oQc02hKvkAFc0KOaTQlF7CK87ZWEnJFP6Ak1IRhfCR0YR8yoSTkin5ASdLycC0P148SrmMWbezEHC5hDN3IcBuXMY9f4wqauOmBDGV8jevYi3X047YwjjK+xnXsxTr6cVsYxwEcxNP+O3dRxWVU0MYAmujz/9XANkWruIyTOC104yTOC1WU8T5uemA7DiLDONaFBqZxRDiIDONYFxqYxhHhCpr4Iz7BJ8If8IGwJnQregItyWEcQRsdjOEOVoQ1oVvRE2j53/sGGbrRFhYVLWKnDRm6hLaie+gWuoS2onvoFupCWVEPnhSWhTLqkjLqQgm9mMWsZBR1YVkooy4po+7R+R1G8SLakl8gRwvLwg6sSEro2LAdS8Lzil7AkrAkPK/oBSwJgxjEIAYxKJzFq8IdtPGapBcVzAtl/BMVSRlDmBfuoI3XJL2oYN6js4oBvCnZgRNYQAc1tPGKJMMrqNmQ4QZquIIKvsez2IPnhBuo4Qoq+B7PYg+e85/r4B3MoAffYhgNfCYs46+YQxU5DuEuFoQO3sEMevAthtHAZx6du3gfE9iNFvajieNCB4ewgAzf4DfYib02bBMyvI59yLCGs1iSZHgd+5BhDWex5MdNYgF1RQdwDBmWcAbrki68jQFhAR8hV3QAx5BhCWewbmsjQtXWRoSqzXZhn9BCFR1Fe3AKXWjiLBoee+wn6d+XEDmUSYXPHAAAAABJRU5ErkJggg==');

    libraryObj.getEncryptedEmailData(notifier, (err)=>{
          expect(stub1).to.have.callCount(3);
    });
    stub1.restore();
    return done();
    });

    it("getEncryptedEmailData || TYPE->1 Recharge_number Null base64 URL", (done) => {
        let notifier = { "data": {
         "template_type": "email",
         "template_id": "7641",
         "options": {
             "notificationOpts": {
                 "recipients": "**********",
                 "channel_id": "both",
                 "deepLinkObj": {
                     "extra": {
                         "url": null,
                         "url_type": null
                     }
                 },
                 "noRich": false
             },
             "type": "async",
             "data": {
                 "amount": 310,
                 "number": "123456789013",
                 "recharge_number": null,
                 "operator": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operator_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operatorRefNumber": "15328",
                 "order_id": 100073580589,
                 "brand": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "short_operator_name": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "payment_mode": "UPI",
                 "due_date": "2023-05-12",
                 "due_amount": 310,
                 "product_id": 1200139386,
                 "rechargeNumbersObj": {},
                 "card_number": null,
                 "category_id": 78640,
                 "payment_date": "2022-05-12",
                 "customer_id": **********,
                 "service": "Electricity",
                 "paytype": "postpaid",
                 "pay_mode": "UPI",
                 "operator_display_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)"
             }
         }
      }
     };
 
     let stub1 = sinon.stub(libraryObj, 'getTexttoImageDataUrl').returns('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAQCAYAAACWa2gIAAAESElEQVR4Ae3BQWjV9x0A8I/uf3iHQHPIIYzSvUMYwlIQ5sGC0oS9Q0pltdXSlToSmRJDK7XUYKU5RJZCXbVGtDQHQ19JHYZaSKiwCYKv1IOwjL3DO6QQWFpe4Sk5PEYOr/A/TPgGfvxJKoO5HYqfj8ce+0nZJpRwAUNCC5O4KSnhAoaEFiZx09bK+AfeQlWS4WMMIEMTR7Es6cMF9AsrGMOKJMPHGECGJo5i2dZOYQiDtnYKQxhUtAt/s9kZTAoZ3sQIupBjBudt+JlwCYcwinn0YQJf4r5wCYcwinn0YQJf4r6iDIv4JRZRl7yHl3AaV/EUpjCLDjLcRh/GcRU/x58wj38J7+ElnMZVPIUpzKKjaBTn8B0+tdkozuE7fKrot6jgKBaxiEXU0BKO40P8GefRwDTu4+8e2C6MYAoLqOEwcoxKRjCFBdRwGDlGbXYK3TbrwQlM4RpqGEOGd4Uh9OMNXEMNx9HC20IPTmAK11DDGDK8K+nF55hG22a9+BzTaNvar9BAFVVUUUVdKGEC5/AWbmEW1/GGDduFHLkkF3JJjlySC7mi3ZjA7202gBK+knRwC0PCLmFRkuMGKsIASvhK0sEtDEmOoYxn0LDZMZTxDBq2tht30IMyehXtRg8uKnoVT9uQCTM4iXtoYhg5LkpmcBL30MQwclyUdGEOE6jbrE9YUbSK/UJHKGFd0oUdQp+womgV+yUzmPTjZjDp4frxJE5IFvAycuzEGrrwF+xAB+cwa0MmfIEK5oQc02hKvkAFc0KOaTQlF7CK87ZWEnJFP6Ak1IRhfCR0YR8yoSTkin5ASdLycC0P148SrmMWbezEHC5hDN3IcBuXMY9f4wqauOmBDGV8jevYi3X047YwjjK+xnXsxTr6cVsYxwEcxNP+O3dRxWVU0MYAmujz/9XANkWruIyTOC104yTOC1WU8T5uemA7DiLDONaFBqZxRDiIDONYFxqYxhHhCpr4Iz7BJ8If8IGwJnQregItyWEcQRsdjOEOVoQ1oVvRE2j53/sGGbrRFhYVLWKnDRm6hLaie+gWuoS2onvoFupCWVEPnhSWhTLqkjLqQgm9mMWsZBR1YVkooy4po+7R+R1G8SLakl8gRwvLwg6sSEro2LAdS8Lzil7AkrAkPK/oBSwJgxjEIAYxKJzFq8IdtPGapBcVzAtl/BMVSRlDmBfuoI3XJL2oYN6js4oBvCnZgRNYQAc1tPGKJMMrqNmQ4QZquIIKvsez2IPnhBuo4Qoq+B7PYg+e85/r4B3MoAffYhgNfCYs46+YQxU5DuEuFoQO3sEMevAthtHAZx6du3gfE9iNFvajieNCB4ewgAzf4DfYib02bBMyvI59yLCGs1iSZHgd+5BhDWex5MdNYgF1RQdwDBmWcAbrki68jQFhAR8hV3QAx5BhCWewbmsjQtXWRoSqzXZhn9BCFR1Fe3AKXWjiLBoee+wn6d+XEDmUSYXPHAAAAABJRU5ErkJggg==');
 
     libraryObj.getEncryptedEmailData(notifier, (err)=>{
           expect(stub1).to.have.callCount(1);
     });
    stub1.restore();
     return done();
     });


     it("getEncryptedEmailData || TYPE->1  DUE_DATE Null base64 URL", (done) => {
        let notifier = { "data": {
         "template_type": "email",
         "template_id": "7641",
         "options": {
             "notificationOpts": {
                 "recipients": "**********",
                 "channel_id": "both",
                 "deepLinkObj": {
                     "extra": {
                         "url": null,
                         "url_type": null
                     }
                 },
                 "noRich": false
             },
             "type": "async",
             "data": {
                 "amount": 310,
                 "number": "123456789013",
                 "recharge_number": '876543210',
                 "operator": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operator_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operatorRefNumber": "15328",
                 "order_id": 100073580589,
                 "brand": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "short_operator_name": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "payment_mode": "UPI",
                 "due_date": null,
                 "due_amount": 310,
                 "product_id": 1200139386,
                 "rechargeNumbersObj": {},
                 "card_number": null,
                 "category_id": 78640,
                 "payment_date": "2022-05-12",
                 "customer_id": **********,
                 "service": "Electricity",
                 "paytype": "postpaid",
                 "pay_mode": "UPI",
                 "operator_display_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)"
             }
         }
      }
     };
 
     let stub1 = sinon.stub(libraryObj, 'getTexttoImageDataUrl').returns('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAQCAYAAACWa2gIAAAESElEQVR4Ae3BQWjV9x0A8I/uf3iHQHPIIYzSvUMYwlIQ5sGC0oS9Q0pltdXSlToSmRJDK7XUYKU5RJZCXbVGtDQHQ19JHYZaSKiwCYKv1IOwjL3DO6QQWFpe4Sk5PEYOr/A/TPgGfvxJKoO5HYqfj8ce+0nZJpRwAUNCC5O4KSnhAoaEFiZx09bK+AfeQlWS4WMMIEMTR7Es6cMF9AsrGMOKJMPHGECGJo5i2dZOYQiDtnYKQxhUtAt/s9kZTAoZ3sQIupBjBudt+JlwCYcwinn0YQJf4r5wCYcwinn0YQJf4r6iDIv4JRZRl7yHl3AaV/EUpjCLDjLcRh/GcRU/x58wj38J7+ElnMZVPIUpzKKjaBTn8B0+tdkozuE7fKrot6jgKBaxiEXU0BKO40P8GefRwDTu4+8e2C6MYAoLqOEwcoxKRjCFBdRwGDlGbXYK3TbrwQlM4RpqGEOGd4Uh9OMNXEMNx9HC20IPTmAK11DDGDK8K+nF55hG22a9+BzTaNvar9BAFVVUUUVdKGEC5/AWbmEW1/GGDduFHLkkF3JJjlySC7mi3ZjA7202gBK+knRwC0PCLmFRkuMGKsIASvhK0sEtDEmOoYxn0LDZMZTxDBq2tht30IMyehXtRg8uKnoVT9uQCTM4iXtoYhg5LkpmcBL30MQwclyUdGEOE6jbrE9YUbSK/UJHKGFd0oUdQp+womgV+yUzmPTjZjDp4frxJE5IFvAycuzEGrrwF+xAB+cwa0MmfIEK5oQc02hKvkAFc0KOaTQlF7CK87ZWEnJFP6Ak1IRhfCR0YR8yoSTkin5ASdLycC0P148SrmMWbezEHC5hDN3IcBuXMY9f4wqauOmBDGV8jevYi3X047YwjjK+xnXsxTr6cVsYxwEcxNP+O3dRxWVU0MYAmujz/9XANkWruIyTOC104yTOC1WU8T5uemA7DiLDONaFBqZxRDiIDONYFxqYxhHhCpr4Iz7BJ8If8IGwJnQregItyWEcQRsdjOEOVoQ1oVvRE2j53/sGGbrRFhYVLWKnDRm6hLaie+gWuoS2onvoFupCWVEPnhSWhTLqkjLqQgm9mMWsZBR1YVkooy4po+7R+R1G8SLakl8gRwvLwg6sSEro2LAdS8Lzil7AkrAkPK/oBSwJgxjEIAYxKJzFq8IdtPGapBcVzAtl/BMVSRlDmBfuoI3XJL2oYN6js4oBvCnZgRNYQAc1tPGKJMMrqNmQ4QZquIIKvsez2IPnhBuo4Qoq+B7PYg+e85/r4B3MoAffYhgNfCYs46+YQxU5DuEuFoQO3sEMevAthtHAZx6du3gfE9iNFvajieNCB4ewgAzf4DfYib02bBMyvI59yLCGs1iSZHgd+5BhDWex5MdNYgF1RQdwDBmWcAbrki68jQFhAR8hV3QAx5BhCWewbmsjQtXWRoSqzXZhn9BCFR1Fe3AKXWjiLBoee+wn6d+XEDmUSYXPHAAAAABJRU5ErkJggg==');
 
     libraryObj.getEncryptedEmailData(notifier, (err)=>{
           expect(stub1).to.have.callCount(1);
     });
    stub1.restore();
     return done();
     });

     it("getEncryptedEmailData || TYPE->1 AMOUNT Null base64 URL", (done) => {
        let notifier = { "data": {
         "template_type": "email",
         "template_id": "7641",
         "options": {
             "notificationOpts": {
                 "recipients": "**********",
                 "channel_id": "both",
                 "deepLinkObj": {
                     "extra": {
                         "url": null,
                         "url_type": null
                     }
                 },
                 "noRich": false
             },
             "type": "async",
             "data": {
                 "amount": null,
                 "number": "123456789013",
                 "recharge_number": "0987654322",
                 "operator": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operator_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operatorRefNumber": "15328",
                 "order_id": 100073580589,
                 "brand": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "short_operator_name": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "payment_mode": "UPI",
                 "due_date": "2023-05-12",
                 "due_amount": null,
                 "product_id": 1200139386,
                 "rechargeNumbersObj": {},
                 "card_number": null,
                 "category_id": 78640,
                 "payment_date": "2022-05-12",
                 "customer_id": **********,
                 "service": "Electricity",
                 "paytype": "postpaid",
                 "pay_mode": "UPI",
                 "operator_display_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)"
             }
         }
      }
     };
 
     let stub1 = sinon.stub(libraryObj, 'getTexttoImageDataUrl').returns('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAQCAYAAACWa2gIAAAESElEQVR4Ae3BQWjV9x0A8I/uf3iHQHPIIYzSvUMYwlIQ5sGC0oS9Q0pltdXSlToSmRJDK7XUYKU5RJZCXbVGtDQHQ19JHYZaSKiwCYKv1IOwjL3DO6QQWFpe4Sk5PEYOr/A/TPgGfvxJKoO5HYqfj8ce+0nZJpRwAUNCC5O4KSnhAoaEFiZx09bK+AfeQlWS4WMMIEMTR7Es6cMF9AsrGMOKJMPHGECGJo5i2dZOYQiDtnYKQxhUtAt/s9kZTAoZ3sQIupBjBudt+JlwCYcwinn0YQJf4r5wCYcwinn0YQJf4r6iDIv4JRZRl7yHl3AaV/EUpjCLDjLcRh/GcRU/x58wj38J7+ElnMZVPIUpzKKjaBTn8B0+tdkozuE7fKrot6jgKBaxiEXU0BKO40P8GefRwDTu4+8e2C6MYAoLqOEwcoxKRjCFBdRwGDlGbXYK3TbrwQlM4RpqGEOGd4Uh9OMNXEMNx9HC20IPTmAK11DDGDK8K+nF55hG22a9+BzTaNvar9BAFVVUUUVdKGEC5/AWbmEW1/GGDduFHLkkF3JJjlySC7mi3ZjA7202gBK+knRwC0PCLmFRkuMGKsIASvhK0sEtDEmOoYxn0LDZMZTxDBq2tht30IMyehXtRg8uKnoVT9uQCTM4iXtoYhg5LkpmcBL30MQwclyUdGEOE6jbrE9YUbSK/UJHKGFd0oUdQp+womgV+yUzmPTjZjDp4frxJE5IFvAycuzEGrrwF+xAB+cwa0MmfIEK5oQc02hKvkAFc0KOaTQlF7CK87ZWEnJFP6Ak1IRhfCR0YR8yoSTkin5ASdLycC0P148SrmMWbezEHC5hDN3IcBuXMY9f4wqauOmBDGV8jevYi3X047YwjjK+xnXsxTr6cVsYxwEcxNP+O3dRxWVU0MYAmujz/9XANkWruIyTOC104yTOC1WU8T5uemA7DiLDONaFBqZxRDiIDONYFxqYxhHhCpr4Iz7BJ8If8IGwJnQregItyWEcQRsdjOEOVoQ1oVvRE2j53/sGGbrRFhYVLWKnDRm6hLaie+gWuoS2onvoFupCWVEPnhSWhTLqkjLqQgm9mMWsZBR1YVkooy4po+7R+R1G8SLakl8gRwvLwg6sSEro2LAdS8Lzil7AkrAkPK/oBSwJgxjEIAYxKJzFq8IdtPGapBcVzAtl/BMVSRlDmBfuoI3XJL2oYN6js4oBvCnZgRNYQAc1tPGKJMMrqNmQ4QZquIIKvsez2IPnhBuo4Qoq+B7PYg+e85/r4B3MoAffYhgNfCYs46+YQxU5DuEuFoQO3sEMevAthtHAZx6du3gfE9iNFvajieNCB4ewgAzf4DfYib02bBMyvI59yLCGs1iSZHgd+5BhDWex5MdNYgF1RQdwDBmWcAbrki68jQFhAR8hV3QAx5BhCWewbmsjQtXWRoSqzXZhn9BCFR1Fe3AKXWjiLBoee+wn6d+XEDmUSYXPHAAAAABJRU5ErkJggg==');
 
     libraryObj.getEncryptedEmailData(notifier, (err)=>{
           expect(stub1).to.have.callCount(1);
     });
    stub1.restore();
     return done();
     });


     it("getEncryptedEmailData ||  TYPE 2 -> SUCCESS base64 URL", (done) => {
        let notifier = { "data": {
         "template_type": "email",
         "template_id": "7641",
             "type": "async",
             "dynamicParams": {
                 "amount": 200,
                 "number": "123456789013",
                 "recharge_number": "8964789",
                 "operator": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operator_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "operatorRefNumber": "15328",
                 "order_id": 100073580589,
                 "brand": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "short_operator_name": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)",
                 "payment_mode": "UPI",
                 "due_date": '2023-03-25',
                 "due_amount": 200,
                 "product_id": 1200139386,
                 "rechargeNumbersObj": {},
                 "card_number": null,
                 "category_id": 78640,
                 "payment_date": "2022-05-12",
                 "customer_id": **********,
                 "service": "Electricity",
                 "paytype": "postpaid",
                 "pay_mode": "UPI",
                 "operator_display_label": "Jaipur Vidyut Vitran Nigam Ltd. (JVVNL)"
      }
    }
     };
 
     let stub1 = sinon.stub(libraryObj, 'getTexttoImageDataUrl').returns('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAQCAYAAACWa2gIAAAESElEQVR4Ae3BQWjV9x0A8I/uf3iHQHPIIYzSvUMYwlIQ5sGC0oS9Q0pltdXSlToSmRJDK7XUYKU5RJZCXbVGtDQHQ19JHYZaSKiwCYKv1IOwjL3DO6QQWFpe4Sk5PEYOr/A/TPgGfvxJKoO5HYqfj8ce+0nZJpRwAUNCC5O4KSnhAoaEFiZx09bK+AfeQlWS4WMMIEMTR7Es6cMF9AsrGMOKJMPHGECGJo5i2dZOYQiDtnYKQxhUtAt/s9kZTAoZ3sQIupBjBudt+JlwCYcwinn0YQJf4r5wCYcwinn0YQJf4r6iDIv4JRZRl7yHl3AaV/EUpjCLDjLcRh/GcRU/x58wj38J7+ElnMZVPIUpzKKjaBTn8B0+tdkozuE7fKrot6jgKBaxiEXU0BKO40P8GefRwDTu4+8e2C6MYAoLqOEwcoxKRjCFBdRwGDlGbXYK3TbrwQlM4RpqGEOGd4Uh9OMNXEMNx9HC20IPTmAK11DDGDK8K+nF55hG22a9+BzTaNvar9BAFVVUUUVdKGEC5/AWbmEW1/GGDduFHLkkF3JJjlySC7mi3ZjA7202gBK+knRwC0PCLmFRkuMGKsIASvhK0sEtDEmOoYxn0LDZMZTxDBq2tht30IMyehXtRg8uKnoVT9uQCTM4iXtoYhg5LkpmcBL30MQwclyUdGEOE6jbrE9YUbSK/UJHKGFd0oUdQp+womgV+yUzmPTjZjDp4frxJE5IFvAycuzEGrrwF+xAB+cwa0MmfIEK5oQc02hKvkAFc0KOaTQlF7CK87ZWEnJFP6Ak1IRhfCR0YR8yoSTkin5ASdLycC0P148SrmMWbezEHC5hDN3IcBuXMY9f4wqauOmBDGV8jevYi3X047YwjjK+xnXsxTr6cVsYxwEcxNP+O3dRxWVU0MYAmujz/9XANkWruIyTOC104yTOC1WU8T5uemA7DiLDONaFBqZxRDiIDONYFxqYxhHhCpr4Iz7BJ8If8IGwJnQregItyWEcQRsdjOEOVoQ1oVvRE2j53/sGGbrRFhYVLWKnDRm6hLaie+gWuoS2onvoFupCWVEPnhSWhTLqkjLqQgm9mMWsZBR1YVkooy4po+7R+R1G8SLakl8gRwvLwg6sSEro2LAdS8Lzil7AkrAkPK/oBSwJgxjEIAYxKJzFq8IdtPGapBcVzAtl/BMVSRlDmBfuoI3XJL2oYN6js4oBvCnZgRNYQAc1tPGKJMMrqNmQ4QZquIIKvsez2IPnhBuo4Qoq+B7PYg+e85/r4B3MoAffYhgNfCYs46+YQxU5DuEuFoQO3sEMevAthtHAZx6du3gfE9iNFvajieNCB4ewgAzf4DfYib02bBMyvI59yLCGs1iSZHgd+5BhDWex5MdNYgF1RQdwDBmWcAbrki68jQFhAR8hV3QAx5BhCWewbmsjQtXWRoSqzXZhn9BCFR1Fe3AKXWjiLBoee+wn6d+XEDmUSYXPHAAAAABJRU5ErkJggg==');
 
     libraryObj.getEncryptedEmailData(notifier, (err)=>{
           expect(stub1).to.have.callCount(3);
     });
    stub1.restore();
     return done();
     });

});