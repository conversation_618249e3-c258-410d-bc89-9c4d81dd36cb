

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert, expect } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import AWSCsvIngester from '../../lib/awscsvingester';
import STARTUP_MOCK from '../__mocks__/startUp'
import fs from 'fs'
import { Readable } from 'stream';
let CSVUpdate
import OAuth from '../../lib/oauth';

var proxyquire =  require('proxyquire')




describe("Scripts : ElectricityCSV test suite", function(){
    let serviceInstance;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            try{
                CSVUpdate=proxyquire.noCallThru()('../../crons/CommonCAAS.js',{'../lib/oauth': class c { 
                    fetchCustomerDetail(){return "data"}
                }}).default
                console.log(CSVUpdate)
                serviceInstance = new CSVUpdate(options)
            }catch(err){
                console.error(err)
            }
            done()
        });
    });

    it("start ",()=>{
        let sandbox = sinon.createSandbox()
        let stub = sinon.stub(serviceInstance.csvIngester,'configure')
        sandbox.stub(serviceInstance.csvIngester,'start')
        sandbox.stub(serviceInstance.csvIngester,'getFileNames').yields(null ,['a','b'])
       
        serviceInstance.start(()=>{})
        assert(serviceInstance.bucketName === "digital-reminder")
        assert(stub.callCount == 1 , `Configure should be called once but called ${stub.callCount}` )
        
    })


    it("processRecordinBatch : call processRecord for each record in batch ",async ()=>{
            let stub = sinon.stub(serviceInstance,'processRecord').yields()
            await serviceInstance.processRecordinBatch([{},{}])
            assert.equal(stub.callCount , 2)
            stub.restore()
    })

    it("processRecordinBatch : error case ",async ()=>{
        let stub = sinon.stub(serviceInstance,'processRecord').yields("Error")
        try{
            await serviceInstance.processRecordinBatch([{},{}])
        }catch(err){
            assert.ok(err)
        }
        assert.equal(stub.callCount , 2)
        stub.restore()
    })

    it("processRecord : success case", (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });

        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
         
            assert.deepEqual(stub.getCall(0).args[1],
            {
                'service': 'electricity',
                'operator': 'tata power delhi distribution limited',
                'productid': 167610850,
                'rechargeNumber': 123,
                'customer_id':123,
                'status':'0',
                'service_id':'0',
                'is_automatic':'0',
                'paytype':'postpaid',
                'next_bill_fetch_date':  "2023-05-06 00:00:00",
                'userData': {}
            })
            assert.deepEqual(stub.getCall(0).args[0],'bills_tatapower')
            done()
        },{ customer_id: 123, recharge_number: 123 , service:'electricity','paytype':'postpaid',operator:"tatapowerdelhidistributionlimited",product_id:167610850 , NBFD:'06-May-23' })
        stub.restore()
        stub2.restore()

    })

    it("processRecord : if nbfd not available make it d+1", (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        this.clock = date => sinon.useFakeTimers(new Date(date));
        let clock=this.clock('2023-05-07');

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            clock.restore()
            assert.deepEqual(stub.getCall(0).args[1],
            {
                'service': 'electricity',
                'operator': 'tata power delhi distribution limited',
                'productid': 167610850,
                'rechargeNumber': 123,
                'customer_id':123,
                'status':'0',
                'service_id':'0',
                'paytype':'postpaid',
                'is_automatic':'0',
                'next_bill_fetch_date':  "2023-05-08 00:00:00",
                'userData': {}
            })
            assert.deepEqual(stub.getCall(0).args[0],'bills_tatapower')
            done()
        },{ customer_id: 123, recharge_number: 123 , service:'electricity',operator:"tatapowerdelhidistributionlimited",product_id:167610850,'paytype':'postpaid'  })
        stub.restore()
        stub2.restore()

    })

    it("processRecord : nbfd in DD-MMM-YYY format", (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        this.clock = date => sinon.useFakeTimers(new Date(date));
        let clock=this.clock('2023-05-07');

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            clock.restore()
            assert.deepEqual(stub.getCall(0).args[1],
            {
                'service': 'electricity',
                'operator': 'tata power delhi distribution limited',
                'productid': 167610850,
                'rechargeNumber': 123,
                'customer_id':123,
                'status':'0',
                'service_id':'0',
                'paytype':'postpaid',
                'is_automatic':'0',
                'next_bill_fetch_date':  "2023-06-02 00:00:00",
                'userData': {}
            })
            assert.deepEqual(stub.getCall(0).args[0],'bills_tatapower')
            done()
        },{ customer_id: 123, recharge_number: 123 , service:'electricity',operator:"tatapowerdelhidistributionlimited",product_id:167610850,'paytype':'postpaid',NBFD : "02-Jun-23"  })
        stub.restore()
        stub2.restore()

    })

    it("processRecord : nbfd in YYYY-MM-DD format", (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        this.clock = date => sinon.useFakeTimers(new Date(date));
        let clock=this.clock('2023-05-07');

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            clock.restore()
            assert.deepEqual(stub.getCall(0).args[1],
            {
                'service': 'electricity',
                'operator': 'tata power delhi distribution limited',
                'productid': 167610850,
                'rechargeNumber': 123,
                'customer_id':123,
                'status':'0',
                'service_id':'0',
                'paytype':'postpaid',
                'is_automatic':'0',
                'next_bill_fetch_date':  "2023-06-05 00:00:00",
                'userData': {}
            })
            assert.deepEqual(stub.getCall(0).args[0],'bills_tatapower')
            done()
        },{ customer_id: 123, recharge_number: 123 , service:'electricity',operator:"tatapowerdelhidistributionlimited",product_id:167610850,'paytype':'postpaid',NBFD : "2023-06-05"  })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  missing customer_id and mobile number not available dont update db" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  recharge_number: 123 , service:'electricity',operator:"BESCOM",product_id:167610850 })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  missing customer_id and mobile number available call Oauth" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        let stub2 = sinon.stub(serviceInstance ,'getCustDetails')
        stub2.returns(new Promise(res=>res(124)))

        let stub3 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,1)
            stub.restore()
            stub2.restore()
            stub3.restore()
            done()
        }, { mobileNumber: 123, recharge_number: 123 , service:'electricity',operator:"tatapowerdelhidistributionlimited",product_id:167610850,'paytype':'postpaid'  } )
    })


    it("processRecord :if  missing service dont update DB" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  customer_id:123 , recharge_number: 123 ,operator:"tatapowerdelhidistributionlimited",product_id:167610850 })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  missing product_id dont update DB" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  customer_id:123 , service:'electricity',recharge_number: 123 ,operator:"tatapowerdelhidistributionlimited",product_id2:167610850 })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  missing paytype dont update DB" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  customer_id:123 , service:'electricity',recharge_number: 123 ,operator:"tatapowerdelhidistributionlimited",product_id:167610850 })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  missing recharge_number dont update DB" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  customer_id:123 , service:'electricity',operator:"BESCOM",product_id:167610850 })
        stub.restore()
        stub2.restore()

    })

    it("processRecord :if  table name not found for operator   dont update DB" , (done)=>{
        let stub = sinon.stub(serviceInstance,'insertRecords').yields()
        
        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.processRecord(function callback(err, data){
            assert.deepEqual(stub.callCount,0)
            done()
        },{  recharge_number:123,customer_id:123 , service:'electricity',operator:"BESCOM",product_id:167610850 })
        stub.restore()
        stub2.restore()

    })



    it("insertRecords : Update db throws error", (done)=>{
        let stub = sinon.stub(serviceInstance.dbInstance,'exec').yields("Error")

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.insertRecords('bills_airteltv',{},function(err){
            assert.ok(err)
            done()
        })
        stub.restore()
        stub2.restore()

    })

    it("insertRecords : success", (done)=>{
        let stub = sinon.stub(serviceInstance.dbInstance,'exec').yields(null ,"Success")

        let stub2 = sinon.stub(serviceInstance, 'pushRecordToNONPAYTM').callsFake(function(cb){
            return cb(null);
        });
        
        serviceInstance.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }
        
        serviceInstance.insertRecords('bills_airteltv',{},function(err,res){
            assert.ok(res)
            done()
        })
        stub.restore()
        stub2.restore()

    })

    it("getCustDetails: success", (done)=>{
        let stub = sinon.stub(serviceInstance.OAuth,'fetchCustomerDetail').returns("data")
        
        serviceInstance.getCustDetails({mobileNumber:'123'}).then((data)=>{
            done()
        })
        stub.restore()

    })

    it("filterFileByMonth", ()=>{
        // let stub = sinon.stub(serviceInstance.OAuth,'fetchCustomerDetail').returns("data")
        let filename = "digital-reminder/ElectricityNotificationCapping/AirtelTV$2023-06-04.csv"
        this.clock = date => sinon.useFakeTimers(new Date(date));
        let clock=this.clock('2023-05-07');
        assert.deepEqual(serviceInstance.filterFileByMonth(filename),false)
        clock.restore()
    })

    it("filterFileByMonth", ()=>{
        // let stub = sinon.stub(serviceInstance.OAuth,'fetchCustomerDetail').returns("data")
        let filename = "digital-reminder/ElectricityNotificationCapping/AirtelTV$2023-06-04.csv"
        this.clock = date => sinon.useFakeTimers(new Date(date));
        let clock=this.clock('2023-06-10');
        assert.deepEqual(serviceInstance.filterFileByMonth(filename),true)
        clock.restore()
    })

    
})