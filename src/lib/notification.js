"use strict";

import _ from 'lodash'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator'
import MOMENT from 'moment'
import utility from '../lib'
import transactionCounterModel from '../models/allTransactionsCounter'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import cassandraBills from '../models/cassandraBills';
import RemindableUsersLibrary from '../lib/remindableUser';

const DTH_SERVICE = "DTH";
const LOAN_SERVICE = "LOAN";

/* 
 * This class contains helper functions that are being used in
 * billReminder and planValidity services
*/

class Notification {

    constructor(options) {
        this.L = options.L,
        this.config = options.config;
        this.transactionCounterModel = new transactionCounterModel(options);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.cassandraBills = new cassandraBills(options);
        this.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        this.allowedTemplatesForWhatsapp = _.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','WHATSAPP_NOTIFICATION','ALLOWED_TEMPLATE_IDS_FOR_WHATSAPP'],[]);
        this.whitelistingTemplateConfigForWhatsapp = _.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','WHATSAPP_NOTIFICATION','WHITELISTING_TEMPLATE_CONFIG_FOR_WHATSAPP'],0);
        this.cvrData ={};
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable() {
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        this.allowedTemplatesForWhatsapp =_.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','WHATSAPP_NOTIFICATION','ALLOWED_TEMPLATE_IDS_FOR_WHATSAPP'],[]);
        this.whitelistingTemplateConfigForWhatsapp = _.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','WHATSAPP_NOTIFICATION','WHITELISTING_TEMPLATE_CONFIG_FOR_WHATSAPP'],0);

    }


    getEmailNotiData(payLoad, notificationRecord) {
        let self = this;
        let templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id', null), 'TEMPLATE_NAME'], null);
        let body = {};
        if (!templateName) {
            body = {
                "template_type": _.get(notificationRecord, 'type', null) != null ? _.get(notificationRecord, 'type', null).toLowerCase() : null,
                "template_id": _.get(notificationRecord, 'template_id', null),
                "options": {
                    "notificationOpts": {
                        "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                    },
                    "type": "async",
                    "data": payLoad
                }
            };
        } else {
            body = {
                "debug": false,
                "notificationReceiver": {
                    "notificationReceiverType": "EMAIL",
                    "notificationReceiverIdentifier": [VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))]
                },
                "templateName": templateName,
                "sender": {
                    "name": "Paytm",
                    "email": "<EMAIL>"
                },
                "replyTo": "<EMAIL>",
                "dynamicParams": payLoad
            };
        }
        return body;
    }

    getPushNotiData(data, notificationRecord, categoryId) {
        let self = this;
        let templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id', null), 'TEMPLATE_NAME'], null);
        let notiExpiry;
        let body = {};
        let styleValueImg = '';
        let payLoad = _.get(data, 'payLoad', null);
        if (!templateName && (categoryId == 13 || categoryId == 14 ||categoryId == 41 )) {
            templateName = _.get(payLoad, 'template_name', null)
        }

        if(payLoad){
            styleValueImg = self.fetchBannerForPushNotification(notificationRecord, payLoad);
        }
        if (!templateName) {
            body = {
                "template_type": _.get(notificationRecord, 'type', null) != null ? _.get(notificationRecord, 'type', null).toLowerCase() : null,
                "template_id": _.get(notificationRecord, 'template_id', null),
                "options": {
                    "notificationOpts": {
                        "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null)),
                        "channel_id": "both",
                        "deepLinkObj": {
                            "extra": _.get(data, 'extra')
                        },
                        "noRich": false
                    },
                    "type": "async",
                    "data": _.get(data, 'payLoad')
                }
            };
        } else {
            let hrs;
            if (categoryId == 1) {
                hrs = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id'), 'NOTIFICATION_EXPIRY'], self.evalBillNotiExpiry(data)) || 48;
            } else {
                hrs = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id'), 'NOTIFICATION_EXPIRY'], 48); // default 48 hrs
            }
            notiExpiry = MOMENT().add(hrs, 'hour').unix();

            body = {
                "templateName": templateName,
                "debug": false,
                "sendBroadcastPush": true,
                "messageCentrePush": {
                    "iconImage": _.get(data, ['payLoad', 'thumbnail']),
                    "expiry": notiExpiry,
                    "extraParams": _.get(data, 'extra')
                },
                "notificationReceiver": {
                    "notificationReceiverType": "CUSTOMERID",
                    "notificationReceiverIdentifier": [VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))]
                },
                "extraCommonParams": _.get(data, 'extra'),
                "deviceType": ["ANDROIDAPP", "IOSAPP"],
                "dynamicParams": _.get(data, 'payLoad')
            };
            
            let template_id =  _.get(notificationRecord, 'template_id');

            let templatesIgnoreImgUrl = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'TEMPLATES_NO_IMG'], ['27274', '32823', '32824']);

            if (template_id && templatesIgnoreImgUrl.includes(template_id.toString())){
                body.messageCentrePush.iconImage = null;
            } else {
                if (styleValueImg && styleValueImg != '') {
                    body.stylePush = { 
                        "styleType": "BIG_PICTURE",
                        "styleValue": styleValueImg
                    }
                }
            }
            self.append2CTAPayload(body, notificationRecord);
        }
        return body;
    }

    append2CTAPayload(body, notificationRecord) {
        let self = this;
        let custId = _.get(notificationRecord, 'recipients', _.get(notificationRecord, 'recipient', null));
        let templateId = _.get(notificationRecord, 'template_id', null);

        let ctaConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CTA_CONFIG', templateId, 'MAPPED_CTA_CONFIG'], null);
        try {
            if (self.isDefaultCTAConfigEnabled(ctaConfig)) {
                if (self.isWhitelistedCustIDFor2CTA(custId)) {
                    self.addCTA(body, ctaConfig, notificationRecord);
                }  
                else {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:APPEND_2CTA_PAYLOAD`,
                        `STATUS:CUSTID_NOT_WHITELISTED`
                    ]);
                }
            } else {
            
                self.L.log('append2CTAPayload::', `ctaConfig is not enabled`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:APPEND_2CTA_PAYLOAD`,
                    `STATUS:2CTA_CONFIG_NOT_ENABLED`
                ]);

                if (!self.isWhitelistedForRemindLaterCTA(custId, templateId) )  {
                    self.L.log('append2CTAPayload::', `not whitelisted for remindlaterCTA`, custId);
                    return;
                }
                else if(self.isShowRemindLaterCTA(notificationRecord)) {
                    self.L.log('append2CTAPayload::', `showing remindlaterCTA`, custId);
                    let ctaConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CTA_CONFIG', 'REMIND_LATER'], null);
                    self.addCTA(body, ctaConfig, notificationRecord);
                } else  {
                    self.L.log('append2CTAPayload::', `not applicable remindlaterCTA`, custId);
                    let ctaConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG',  'CTA_CONFIG', 'ALREADY_PAID'], null);
                    self.addCTA(body, ctaConfig, notificationRecord);
                
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:APPEND_REMIND_LATER_CTA`,
                        `STATUS:REMIND_LATER_CTA_NOT_APPLICABLE`
                    ]);

                }
            }
        } catch (error) {
            self.L.error('append2CTAPayload::', `error while adding 2 CTA`, error);
        }

        return body;
    }


    isDefaultCTAConfigEnabled(ctaConfig) {
        if (ctaConfig && !_.isNull(ctaConfig) && _.get(ctaConfig, 'enabled', false)) 
            return true;
        return false;
    }

    addCTA(body, ctaConfig, data) {
        let self = this;
        try {

            if(ctaConfig == null)
                return;
        
            let interactiveType = _.get(ctaConfig, 'interactiveType', null);
            let primaryDeepLink = _.get(ctaConfig, 'primaryDeepLink', null);
            let secondaryDeepLink = _.get(ctaConfig, 'secondaryDeepLink', null);

            if(!secondaryDeepLink || _.isEmpty(secondaryDeepLink)){
                secondaryDeepLink = _.get(data, ['extra', 'url'], _.get(data, ['data','extraCommonParams','url'], null));
            }
            primaryDeepLink = primaryDeepLink + "?$" + self.getPrimaryLinkQueryParam(data, '$');
            
            let actionTypeRequest = {
                "type": "OPEN_DEEP_LINK",
                "primaryDeeplink": primaryDeepLink,
                "secondaryDeeplink": secondaryDeepLink
            };
            
            body.broadcastPush = {
                "interactiveActionRequest": {
                    "interactiveType": interactiveType, 
                    "actionTypeRequest": actionTypeRequest
                }
            };
            self.L.log('append2CTAPayload::', `2CTA payload appended successfully, body is `, JSON.stringify(body));
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:APPEND_2CTA_PAYLOAD`, 
                `STATUS:SUCCESS`,
                `INTERACTIVE_TYPE:${interactiveType}`
            
            ]);
        } catch (error) {
            self.L.error('append2CTAPayload::', `error during parsing ctaConfig`, error);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:APPEND_2CTA_PAYLOAD`, 
                `STATUS:ERROR`
            ]);
        }
            
    }

    isWhitelistedCustIDFor2CTA(custId) {
        let self = this;
        let cugCustIds = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', '2CTA_CONFIG', 'CUG_CUSTOMERID_LIST'], []);
        let percentageRollout = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', '2CTA_CONFIG', 'PERCENTAGE_ROLLOUT'], 0);

        if (cugCustIds.includes(custId) || Number(custId) % 100 < Number(percentageRollout)) {
            return true;
        }

        return false;
    }

    isShowRemindLaterCTA(data) {
        let self = this;
        let notificationData = _.get(data, 'data', null);

        if(_.get(notificationData, ['dynamicParams', 'due_date']) == null ) {
            self.L.error('isShowRemindLaterCTA : due_date or remindLaterDate is null');
            return false;
        } 

        let dueDateMoment = MOMENT(_.get(notificationData, ['dynamicParams', 'due_date']), 'Do MMM YYYY', true);

        const remindLaterDate = _.get(data, 'remindLaterDate', null) != null ? MOMENT(_.get(data, 'remindLaterDate', null)) : null;
      
    
        self.L.log('inside isShowRemindLaterCTA : dueDate is ' , dueDateMoment, 'remindLaterDate is ', remindLaterDate);
        // oldRemindLaterDateThreshold is used if there is some problem in resetting old remind later date. 
        //Then if different dueDate and remindLater date diff is beyong this we need to show remindLaterCTA
        const oldRemindLaterDateThreshold =  _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CTA_CONFIG', 'OLD_REMIND_LATER_DATE_THRESHOLD'], 30);
       
       
        const threshold =  _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CTA_CONFIG', 'REMIND_LATER_DUE_DATE_DIFF_THRESHOLD'], 2);
       
        const now = MOMENT();

        const daysDifference = Math.ceil(dueDateMoment.diff(now, 'days', true));
        

        let remindLaterCTAConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CTA_CONFIG', 'REMIND_LATER'], null);
        self.L.log('daysDifference is ', daysDifference, 'threshold is ', threshold);

        if(!remindLaterCTAConfig)
            return false;
        if (daysDifference >= threshold && (!remindLaterDate || (remindLaterDate && remindLaterDate < Date.now() && Math.abs(dueDateMoment.diff(remindLaterDate, 'days')) > oldRemindLaterDateThreshold))) {
            return true;
            
        }

        return false;
    }


    isWhitelistedForRemindLaterCTA(custId, templateId) {
        let self = this;
        let cugCustIds = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'REMIND_LATER_CTA_CONFIG', 'CUG_CUSTOMERID_LIST'], []);
        let templateIds = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'REMIND_LATER_CTA_CONFIG', 'TEMPLATE_IDS'], []);
        let percentageRollout = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'REMIND_LATER_CTA_CONFIG', 'PERCENTAGE_ROLLOUT'], 0);
       
        if (!templateIds.includes(Number(templateId))) {
            self.L.log('templateId not whitelisted {} {}', custId, templateId);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:APPEND_REMIND_LATER_CTA`,
                `STATUS:TEMPLATE_NOT_WHITELISTED`
            ]);
            return false;
        }
        
        if (cugCustIds.includes(custId) || Number(custId) % 100 < Number(percentageRollout)) {
            return true;
        }

        self.L.log('customerid not whitelisted');

        utility._sendMetricsToDD(1, [
            `REQUEST_TYPE:APPEND_REMIND_LATER_CTA`,
            `STATUS:CUSTID_NOT_WHITELISTED`
        ]);

        return false;
    }
    
    fetchBannerForPushNotification(notificationRecord, payLoad) {
        let self = this;
        let templateId = _.get(notificationRecord, 'template_id', null);
        let bannerFlag = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', 'BANNER_CONFIG', templateId], -1);
        let bannerLink = '';
        let operator = _.lowerCase(_.get(payLoad, "operator", null)),
            service = _.lowerCase(_.get(payLoad, "service", null)),
            paytype = _.lowerCase(_.get(payLoad, "paytype", null)),
            thumbnail = _.get(payLoad, "thumbnail", ''),
            notificationType = _.get(notificationRecord, 'notificationType', null);
        
        // console.log(`operator: ${operator}, service: ${service}, paytype: ${paytype}, thumbnail: ${thumbnail}, notificationType: ${notificationType}`);

        if (bannerFlag == -1) {
            // if banner config not found for templateId - fetch based on previous logic
            bannerLink = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_NOTIFICATION_IMAGE_MAPPING', `${operator}_${paytype}_${notificationType}` , 'IMAGE_URL'], 
                _.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_NOTIFICATION_IMAGE_MAPPING', `${service}_${paytype}_${notificationType}` , 'IMAGE_URL'], 
                    thumbnail));
            
            self.L.log('notification::fetchBannerForPushNotification::', `banner config doesn't exist for templateId ${templateId}, banner is ${bannerLink}`);
        } else if (bannerFlag == 1) {
            bannerLink = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', 'BANNER_LINK', templateId], 
                _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_NOTIFICATION_IMAGE_MAPPING', `${operator}_${paytype}_${notificationType}` , 'IMAGE_URL'], 
                    _.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_NOTIFICATION_IMAGE_MAPPING', `${service}_${paytype}_${notificationType}` , 'IMAGE_URL'], 
                        thumbnail)));

            self.L.log('notification::fetchBannerForPushNotification::', `banner config is enabled for templateId ${templateId}, banner is ${bannerLink}`);
        } else {
            // bannerFlag == 0 stop banner
            bannerLink = '';
            self.L.log('notification::fetchBannerForPushNotification::', `banner config is disabled for templateId ${templateId}, banner is ${bannerLink}`);
        }
        return bannerLink;
    }

    getPrimaryLinkQueryParam(data, delimiter = '&') {
        let self = this;
        try{
            let
            recharge_number = _.get(data, ['paramsForUrl', 'recharge_number'], _.get(data,['data','dynamicParams','recharge_number'], '')),
            category_id = _.get(data, ['paramsForUrl', 'category_id'] ,  _.get(data,['data','dynamicParams','category_id'], null));
            return `category_id=${category_id}${delimiter}recharge_number=${recharge_number}`;
        
        }catch(error){
            self.L.error("Error in getPrimaryLinkQueryParam of data fetched: ", error, JSON.stringify(data));
            return null;
        }
    }

    evalBillNotiExpiry(data) {
        let self = this;
        try {
            let dueDate = MOMENT(_.get(data, ['payLoad', 'due_date']), 'Do MMM YYYY', true);
            let expiryTime;
            if (dueDate.diff(MOMENT(), 'days') <= 1) {
                expiryTime = dueDate.add(2, 'days').diff(MOMENT(), 'hour');
            } else {
                expiryTime = dueDate.subtract(1, 'days').diff(MOMENT(), 'hour');
            }
            return expiryTime;
        } catch (error) {
            self.L.error('notificationLib :: evalBillExpiry', 'Error while evaluating notification expiry time', error);
            return 48; // default 48 hrs
        }
    }

    getSmsNotiData(payLoad, notificationRecord) {
        let self = this;
        let templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id', null), 'TEMPLATE_NAME'], null);
        let body = {};
        if (!templateName) {
            body = {
                "template_type": _.get(notificationRecord, 'type', null) != null ? _.get(notificationRecord, 'type', null).toLowerCase() : null,
                "template_id": _.get(notificationRecord, 'template_id', null),
                "options": {
                    "notificationOpts": {
                        "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                    },
                    "type": "async",
                    "data": payLoad
                }
            };
        }
        else {
            body = {
                "templateName": templateName,
                "notificationReceiver": {
                    "notificationReceiverType": "MOBILENUMBER",
                    "notificationReceiverIdentifier": [VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))]
                },
                "dynamicParams": payLoad
            };
        }
        return body;
    }

    /**
     * Whatsapp is supported in v3 only so will try to create v3 payload only
     * If required details not found we will pass error
     */
    getWhatsappNotiData(payLoad, notificationRecord) {
        let self = this;
        let templateName = _.get(notificationRecord, 'template_name', null) || _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id', null), 'TEMPLATE_NAME'], null);
        let body = {};
        if (!templateName) {
            return ["template name not found", null];
        }
        else {
            let dynamicParams = payLoad;
            if (dynamicParams.operator) {
                dynamicParams = {
                    ...payLoad,
                    operator: _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION', 'OPERATOR_MAPPING', _.toUpper(dynamicParams.operator)], dynamicParams.operator)
                };
            }
            body = {
                "templateName": templateName,
                "notificationReceiver": {
                    "notificationReceiverType": "MOBILENUMBER",
                    "notificationReceiverIdentifier": [VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))]
                },
                "dynamicParams": dynamicParams,
                "logDetails": {
                    "customer_id": VALIDATOR.toString(_.get(payLoad, 'customer_id', null))
                }
            };
        }
        return [null,body];
    }

    /**
     * Chat is supported in v3 only so will try to create v3 payload only
     * If required details not found we will pass error
     */
    getChatNotiData(payLoad, notificationRecord) {
        let
            self = this,
            templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(notificationRecord, 'template_id', null), 'TEMPLATE_NAME'], null),
            channelId = _.get(self.config, ['NOTIFICATION','notificationapi','CHANNEL_ID_MAPPING', _.get(payLoad, 'category_id', '')], null);

        if (!templateName) return ["template name not found", null];
        if (!channelId) return ["channelId not found", null];

        let body = {
            "templateName": templateName,
            "notificationReceiver": {
                "notificationReceiverType": "CUSTOMERID",
                "notificationReceiverIdentifier": [VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))]
            },
            "senderData": {
                "type": "CHANNEL",
                "identifier": channelId
            },
            "sendPush": false,
            "overrideMsg": false,
            "dynamicParams": payLoad
            //"referenceId": "will be added by notify service while sending notification",
        }

        return [null, body]
    }
    /**
     * 
     * @param {*} operator 
     * @param {*} tableName 
     * @returns [dueDateInterval1, dueDateInterval2]
     */

    getIntervalForOldDueDates(operator, tableName) {
        let self = this,
            dueDates = [];
        dueDates = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'operatorTableOldBillDueDateMapping', tableName],
            _.get(self.notificationConfig, ['operatorTableOldBillDueDateMapping', tableName],
                _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'scheduletimeinterval', 'BR_DUEDATE_OLD_BILL_INTERVAL'],
                    _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_DUEDATE_OLD_BILL_INTERVAL'], null)
                )
            )
        )
        self.L.log("getIntervalForOldDueDates::", `operator:${operator}_tableName:${tableName}_dueDates:${dueDates}`);
        return dueDates;
    }

    getPossibleDueDates(operator, tableName) {
        let self = this,
            dueDates = [];     

        dueDates =  _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'operatorTableDueDateMapping', tableName ] ,  
                                _.get(self.notificationConfig, ['operatorTableDueDateMapping', tableName], 
                                    _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'scheduletimeinterval', 'BR_DUEDATE_INTERVAL' ] , 
                                        _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_DUEDATE_INTERVAL'], null)
                                    )
                                )
                            )
        self.L.log("getPossibleDueDates::", `operator:${operator}_tableName:${tableName}_dueDates:${dueDates}`);
        return dueDates;
    }

    getPossibleBillGenDates(operator, tableName) {
        let self = this,
            billGenDates = [];     

        billGenDates =  _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'operatorTableBillGenDateMapping', tableName ] ,  
                                _.get(self.notificationConfig, ['operatorTableBillGenDateMapping', tableName], 
                                    _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'scheduletimeinterval', 'BR_BILLGENDATE_INTERVAL' ] , 
                                        _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_BILLGENDATE_INTERVAL'], null)
                                    )
                                )
                            )
        self.L.log("getPossibleBillGenDates::", `operator:${operator}_tableName:${tableName}_billGenDates:${billGenDates}`);
        return billGenDates;
    }

    /**
     * returns true if we should block SMS for users.
     * @param {*} record should contain customer_type of the user
     */

    disableSmsForUser(record, cb) {
        let self = this;
        if(!record) {
            return cb("disableSmsForUser:: Invalid data received",false);
        }

        try {
            let blockedCustomerTypes = [];
            let extra;
            let customerId = record.customer_id;
            let customer_type;
        
            if(record.extra) {
                if (typeof record.extra === 'string'){
                    extra = JSON.parse(record.extra);
                }
    
                customer_type = _.get(extra, 'customer_type', null);
                //currently we have only blocked customer_type retailer 
                blockedCustomerTypes.push(_.get(self.config, ['NOTIFICATION', 'customerType', 'RETAILER'], null));
                if(blockedCustomerTypes.includes(customer_type)) {
                    self.L.log(`disableSmsForUser:: SMS blocked for retailer ${record.customer_id}`)
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:RETAILER_SMS_SKIPPED', 'STATUS:RETAILER']);
                    return cb(null, true)
                }
            }

            return self.transactionCounterModel.fetchCustomerRetailerStatus(cb, customerId);
        } catch (error) {
            return cb(error, false)
        }
    }
    createDeepLink(notifier, callback) {

        if (_.get(notifier, 'recipient', null) && _.get(notifier, 'template_id', null)) {   
            let self = this,
                paramsForUrl;
                self.cvrData = _.get(self.config, 'CVR_DATA', {});
                var productInfo = self.cvrData[_.get(notifier, 'product_id', null)];
                var thumbnail = _.get(productInfo, 'thumbnail');

                self.createShortUnsubscribeUrl(function (err, short_url) {
                    {
                        if(err)
                        {
                            short_url=null;
                        }
                        if(_.get(notifier,['data', 'options'], null)==null)
                        {
                            _.set(notifier,['data','dynamicParams', 'unsubscribe_url'], short_url);
                            _.set(notifier,['data','dynamicParams', 'thumbnail'], thumbnail);
                        }
                        else
                        {
                            _.set(notifier,['data','options','data','unsubscribe_url'], short_url);
                            _.set(notifier,['data','options','data','thumbnail'], thumbnail);
                        }
                        if (_.get(notifier, 'type', null).toUpperCase() == 'SMS') {
                            paramsForUrl = self.getParamsForUrl(notifier);
                            let [err, url] = self.createUrl(paramsForUrl);
                            if (!err && url) {
                                self.createTinyUrl(url, function (err, tiny_url) {
                                    if (!err && tiny_url) {
                                        _.set(notifier, ['data','options','data','sms_short_link'], tiny_url);
                                        callback(err, notifier);
                                    }
                                    else {
                                        self.L.error(err);
                                        callback();
                                    }
                                });
                            } else {
                                self.L.error(err);
                                callback();
                            }
                    } else if (_.get(notifier, 'type', null).toUpperCase() == 'PUSH') {
                        let deepLinkData = {};
                        let url_type = "external";
                        let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(notifier, ['data', 'options' , 'data', 'category_id'], null)], null);   
                        let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
                        let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(notifier, ['data', 'options' , 'data', 'category_id'], null);
                        let product_service = _.get(notifier, ['data', 'options', 'data' , 'service'], null) != null ? _.get(notifier, ['data', 'options', 'data' , 'service'], null).toLowerCase() : null;
                        let short_operator_name = _.get(notifier, ['data', 'options', 'data' , 'short_operator_name'], null);
                        let template_id = _.get(notifier, "template_id", null);
                        let template_name = _.get(notifier, "template_name", null);
                        let utm_source =_.get(notifier, "utm_source", null);
                        let utm_medium= _.get(notifier, "utm_medium", null);
                        let utm_campaign = _.get(notifier, "utm_campaign", null);
                        //let utm = self._utmByTemplateId(template_id, short_operator_name, '$');
                        self.cvrData = _.get(self.config, 'CVR_DATA', {});
        
                        var productInfo = self.cvrData[_.get(notifier, 'product_id', null)];
                        if(!short_operator_name){
                            try {
                                short_operator_name = _.get(JSON.parse(productInfo.attributes), 'short_operator_name', null);
                            }
                            catch (error) {
                                self.L.error('Error while parsing attributes for PID', _.get(notifier, 'product_id', null), error);
                            }
                
                            short_operator_name = short_operator_name || _.get(productInfo, 'brand');
                        }
                        let utm = `&utm_source=${self.getUtmParam(utm_source)}&utm_medium=${self.getUtmParam(utm_medium)}&utm_campaign=${self.getUtmParam(short_operator_name)}`;
        
                        if (!landing_path) {
                            if (product_service === 'mobile') {
                                landing_path = "mobile_postpaid";
                            } else if (product_service === 'datacard') {
                                landing_path = "datacard_postpaid";
                            } else if (product_service === 'dth') {
                                landing_path = "dth";
                            } else {
                                landing_path = 'utility';
                            }
                        }
        
                        let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
                        paramsForUrl = self.getParamsForPush(notifier);
                        let completeUrl = null;
                        _.set(paramsForUrl, 'product_service', product_service);
                        if(product_service=='mobile'){
                            completeUrl= url +"?$" + self.getQueryParams(paramsForUrl, '$')+ self.getExtraRechargeNum(notifier, '$') + utm;
                        }else{
                            completeUrl= url +"?" + self.getQueryParams(paramsForUrl, '$')+ self.getExtraRechargeNum(notifier, '$') + utm;
                        }
                        deepLinkData = {
                            "payLoad": notifier,
                            "extra": {
                                "url": completeUrl, 
                                "url_type": _.get(notifier, ['data', 'options',  'notificationOpts', 'deepLinkObj', 'extra', 'url'], null)==null? url_type: _.get(notifier, ['data', 'options',  'notificationOpts', 'deepLinkObj', 'extra', 'url'], null)
                            }
                        }
                        notifier.data.options.notificationOpts.deepLinkObj.extra.url = _.get(deepLinkData,['extra', 'url'], null);
                        notifier.data.options.notificationOpts.deepLinkObj.extra.url_type = _.get(deepLinkData,['extra', 'url_type'], null);
                        callback(null, notifier);
                        
                    } else if (_.get(notifier, 'type', null).toUpperCase() == 'CHAT') {
                        let deepLinkUrl = self.getDeepLinkUrl(notifier);
                        _.set(notifier, ['data', 'dynamicParams', 'deeplink'], deepLinkUrl);
                        callback(null, notifier);
                    } else {
                        paramsForUrl = self.getParamsForUrl(notifier);
                        let [err, url] = self.createUrl(paramsForUrl);
                        if (!err && url) {
                            _.set(notifier, ['data','options','data', 'url'], url);
                            callback(null, notifier);
                        } else {
                            self.L.error(err);
                            callback();
                        }
                    }
                        
                    }
                }, _.get(productInfo, 'operator'), _.get(notifier, 'recharge_number', null));

        } else {
            let self = this;
            self.L.error('Create deeplink', `Invalid Recipient:${_.get(notifier, 'recipient', null)} or templateId:${_.get(notifier, 'template_id', null)}`);
            callback();
        }
    }

    createShortUnsubscribeUrl(cb, operator, recharge_number) {
        let self = this,
            unsubscribe_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null);
        unsubscribe_url = unsubscribe_url + `/unsubscribe?operator=${operator}&recharge_number=${recharge_number}`;
        let object = new utility.TinyUrl();
        object.createShortUrl(cb, _.get(self.config, ['TINYURL_CONFIG'], null), unsubscribe_url);
    }

    //getting used by sms and email
    getParamsForUrl(notifier) {
        let self = this,
            recharge_number = _.get(notifier, 'recharge_number', null) != null ? _.get(notifier, 'recharge_number', null) : null,
            amount = _.get(notifier, ['data', 'options', 'data' , 'amount'], null) != null ? _.get(notifier,  ['data', 'options', 'data' , 'amount'], null) : null,
            product_id = _.get(notifier, 'product_id', null) != null ? _.get(notifier, 'product_id', null) : null,
            product_service = _.get(notifier, ['data', 'options', 'data' , 'service'], null) != null ? _.get(notifier, ['data', 'options', 'data' , 'service'], null).toLowerCase() : null, 
            user_data = _.get(notifier, ['data', 'options', 'data' , 'rechargeNumbersObj'], null) != null ? _.get(notifier, ['data', 'options', 'data' , 'rechargeNumbersObj'], null) : null,   
            paytype = _.get(notifier, ['data', 'options', 'data' , 'paytype'], null),              
            category_id = _.get(notifier, ['data', 'options', 'data' , 'category_id'], null),      
            bank_name = _.get(notifier, 'bank_name', null),          
            card_network = _.get(notifier, 'card_network', null),    
            template_id = _.get(notifier, 'template_id', null),
            template_name = _.get(notifier, "template_name", null),
            utm_source =_.get(notifier, "utm_source", null),
            utm_medium= _.get(notifier, "utm_medium", null),
            utm_campaign = _.get(notifier, "utm_campaign", null),
            short_operator_name = _.get(notifier, ['data', 'options', 'data' , 'short_operator_name'], null),
            type =  _.get(notifier,'type', null);

            var productInfo = self.cvrData[_.get(notifier, 'product_id', null)];
            if(!short_operator_name){
                try {
                    short_operator_name = _.get(JSON.parse(productInfo.attributes), 'short_operator_name', null);
                }
                catch (error) {
                    self.L.error('Error while parsing attributes for PID', _.get(notifier, 'product_id', null), error);
                }
    
                short_operator_name = short_operator_name || _.get(productInfo, 'brand');
            }
            let paramsForUrl = {
                "product_id": product_id,
                "recharge_number": recharge_number,
                "user_data": user_data,
                "amount": amount,
                "product_service": product_service,
                "notifier": notifier,
                "paytype": paytype,
                "category_id": category_id,
                "bank_name": bank_name,
                "card_network": card_network,
                "short_operator_name": short_operator_name,
                "operator": _.get(notifier, ['data', 'options', 'data' , 'operator'], null),
                "ignoreAmountInDeeplink": _.get(notifier, "ignoreAmountInDeeplink", false), 
                "template_id" : template_id,
                "utm_source" : utm_source,
                "utm_medium" : utm_medium,
                "utm_campaign" : utm_campaign,
                "type" : type,
                "operator_validated_at": _.get(notifier, "operator_validated_at", null)
            };
        return paramsForUrl;
    }

    //used by sms and email
    createUrl(paramsForUrl) {
        let self = this,
            tinyurl = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'TINY_URL_API'], null),
            product_id = _.get(paramsForUrl, 'product_id', null),
            category_id = _.get(paramsForUrl, 'category_id', null),
            recharge_number = _.get(paramsForUrl, 'recharge_number', null),
            user_data = _.get(paramsForUrl, 'user_data', '{}'),
            amount = _.get(paramsForUrl, 'amount', null),
            product_service = _.get(paramsForUrl, 'product_service', null),
            notifier = _.get(paramsForUrl, 'notifier', null),
            operator = _.get(paramsForUrl, 'operator', null),
            short_operator_name = _.get(paramsForUrl, 'short_operator_name', null),
            notificationType = _.get(notifier, 'type', null),              
            type = _.get(notifier, 'type', null),
            paytype = _.get(paramsForUrl, 'paytype', null),
            template_id = _.get(paramsForUrl, 'template_id', null),
            utm_source = _.get(paramsForUrl, 'utm_source', null),
            utm_medium = _.get(paramsForUrl, 'utm_medium', null),
            utm_campaign = _.get(paramsForUrl, 'utm_campaign', null),
            smart_url =
                _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_PRODUCT_ID', `${product_id}`],
                    _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_CATEGORY_ID', `${category_id}`],
                        _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE', `${product_service}::${paytype}`], null))),
            sms_url = '',
            rech_num_count = -1;


        if (product_id == null || category_id == null || recharge_number == null || amount == null || product_service == null || notifier == null) {
            return ["paramsForUrl cannot have null values", null];
        }


        if (smart_url && smart_url.length) {
            if(product_service=='mobile'){
                sms_url = smart_url[0] + `?$category_id=${category_id}&`;
            }else{
                sms_url = smart_url[0] + `?category_id=${category_id}&`;
            }
            if (smart_url.length > 1) {
                rech_num_count = Number(smart_url[1]);
            }
        } else {
            self.L.error('createUrl:: Smart URL is not picked for operator: ', operator, ' service: ', product_service, ' paytype: ', paytype);
            let product_service_sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'PRODUCT_SERVICE_SMS_URL'], null);
            if(product_service=='mobile'){
                sms_url = _.get(product_service_sms_url, product_service, null) + '?$';
            }
            else{
                sms_url = _.get(product_service_sms_url, product_service, null) + '?';
            }
        }

        if (sms_url == null) {
            sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null) + "?";
        } else {
            sms_url = sms_url + `${self.getQueryParams(paramsForUrl, '&')}`;

            if (user_data) {
                try {
                    //user_data = JSON.parse(user_data) || {};
                    if (smart_url && smart_url.length) {
                        for (let i = 2; i <= rech_num_count; i++) {
                            let key = 'recharge_number_' + i;
                            let value = user_data[key] || '';
                            sms_url = sms_url + `&${key}=${value}`;
                        }
                    } else {
                        for (let i = 2; i < 10; i++) {
                            let key = 'recharge_number_' + i,
                                value = '';

                            if (!_.has(user_data, [key], null)) break;

                            value = _.get(user_data, [key], '');
                            sms_url = sms_url + `&${key}=${value}`;
                        }
                    }
                } catch (error) {
                    self.L.error("Error in parsing of data fetched: ", error);
                }
            }

        }
        sms_url = sms_url + `&utm_source=${self.getUtmParam(utm_source)}&utm_medium=${self.getUtmParam(utm_medium)}&utm_campaign=${self.getUtmParam(short_operator_name)}`;
        return [null, sms_url];
    }

    createTinyUrl(url, callback) {
        let
            self = this,
            object = new utility.TinyUrl();
        object.createShortUrl(callback, _.get(self.config, ['TINYURL_CONFIG'], null), url);
    }

    _utmByTemplateId(template_id, short_operator_name, operator = '&') {
        let self = this;
        //fetch utm details by notificationType
        //utm_source, utm_medium, utm_campaign
        let utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', template_id]);
        if (!utm) {
            self.L.critical(`UTM config not found for template: ${template_id}`);
            utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', 'notFound'], {});
        }

        return `${operator}utm_source=${self.getUtmParam(utm.utm_source)}${operator}utm_medium=${self.getUtmParam(utm.utm_medium)}${operator}utm_campaign=${self.getUtmParam(short_operator_name)}`;
        
    }

    getUtmParam(utmparam) {
        if (!utmparam) return '';
        let utmProcessed = encodeURIComponent(utmparam.replace(/[^a-zA-Z0-9 ]/g, ''))
        return utmProcessed;
    }

    getParamsForPush(notifier) {
        let paramsForUrl = {
                operator : _.toLower(_.get(notifier, ['data', 'options', 'data', 'operator'], null)),
                amount : _.get(notifier, ['data', 'options', 'data', 'amount'], null),
                product_id : _.get(notifier, 'product_id', null),
                recharge_number : _.get(notifier, 'recharge_number', ''),
                category_id : _.get(notifier, ['data', 'options', 'data', 'category_id'] , null),
                bank_name : _.get(notifier, 'bank_name', null),        
                card_network : _.get(notifier, 'card_network', null),
                operator_validated_at: _.get(notifier, "operator_validated_at", null), 
            };
        return paramsForUrl;
    }
    getParamsForChat(notifier) {
        let paramsForUrl = {
                operator : _.toLower(_.get(notifier, ['data', 'dynamicParams', 'operator'], null)),
                amount : _.get(notifier, ['data', 'dynamicParams', 'amount'], null),
                product_id : _.get(notifier, 'product_id', null),
                recharge_number : _.get(notifier, 'recharge_number', ''),
                category_id : _.get(notifier, ['data', 'dynamicParams', 'category_id'] , null),
                bank_name : _.get(notifier, 'bank_name', null),        
                card_network : _.get(notifier, 'card_network', null),
                operator_validated_at: _.get(notifier, "operator_validated_at", null) 
            };
        return paramsForUrl;
    }

    //getting used by push and chat and sms
    getQueryParams(data, delimiter = '&') {
        let self = this,
            operator = _.toLower(_.get(data, 'operator', null)),
            amount = _.get(data, 'amount', null),
            product_id = _.get(data, 'product_id', null),
            recharge_number = _.get(data, 'recharge_number', ''),
            bank_name = _.get(data, 'bank_name', ''),            
            card_network = _.get(data, 'card_network', ''),      
            category_id = _.get(data, 'category_id' , null),
            notifier = _.get(data, 'notifier', null),
            operator_validated_at = _.get(data, 'operator_validated_at', null),
            operatorKey = _.get(self.config,['CVR_DATA',product_id,'operator']),
            service = _.get(self.config,['CVR_DATA', product_id, 'service']),
            circleKey = _.get(self.config,['CVR_DATA',product_id,'circle']);

        if (_.toLower(_.get(data, 'product_service', service)) == 'financial services') {
            let enableNewFormatMCN = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'DEEP_LINK'], 0)
            if(enableNewFormatMCN){
                let lastIndex = recharge_number.lastIndexOf('X');
                recharge_number = recharge_number.substring(0, lastIndex).replace(/[0-9]/g, "X") + recharge_number.substring(lastIndex) // ensuring only last 4 digits remain in MCN
            }
            return `recharge_number=${recharge_number.replace(/ /g, '')}${delimiter}bank_name=${bank_name}${delimiter}card_network=${card_network}`;
        } else {
            if(_.get(data, "operator_validated_at", null)){
                if (data.ignoreAmountInDeeplink || _.get(notifier, 'type', null).toUpperCase() == 'EMAIL') {
                    return `product_id=${product_id}${delimiter}price=${delimiter}recharge_number=${recharge_number}${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                } else if (category_id === 17 && _.get( self.ignoreAmountInURLForMobilePrepaidOperators ,  operator  , null)) {
                    // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
                    return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}expandBrowsePlan=true${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                } else {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                }
            }
            else{
            if (data.ignoreAmountInDeeplink || _.get(notifier, 'type', null).toUpperCase() == 'EMAIL') {
                return `product_id=${product_id}${delimiter}price=${delimiter}recharge_number=${recharge_number}`;
            } else if (category_id === 17 && _.get( self.ignoreAmountInURLForMobilePrepaidOperators ,  operator  , null)) {
                // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
                return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}expandBrowsePlan=true`;
            } else {
                return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}`;
            }
        }
            
        }
    }

    // mask_recharge_number(rechargeNumber){
    //     var first2 = rechargeNumber.substring(0, 2);
    //     var last2 = rechargeNumber.substring(rechargeNumber.length - 2);
    //     var len = rechargeNumber.substring(2, rechargeNumber.length - 2).length;
    //     var mask = '';
    //     for(var i=0; i< len; i++) mask +='*'
    //     return first2 + mask + last2;
    // }

    //getting used by push
    getExtraRechargeNum(record, operator = '&') {
        let self = this;
        let user_data = _.get(record, ['data', 'options', 'data', 'rechargeNumbersObj'], null);
        let extra_recharge_num_url = "";
        if (user_data) {
            try {
                //user_data = JSON.parse(user_data);
                for (var i = 2; i < 10; i++) {
                    var key = 'recharge_number_' + i,
                        value = '';

                    if (!_.has(user_data, [key], null)) break;

                    value = _.get(user_data, [key], '');
                    extra_recharge_num_url = extra_recharge_num_url + `${operator}${key}=${value}`;
                }
            } catch (error) {
                self.L.error("Error in parsing of data fetched: ", error);
                return extra_recharge_num_url;
            }
        }

        return extra_recharge_num_url;
    }
    //getting used by chat
    getExtraRechargeNumForChat(record, operator = '&') {
        let self = this;
        let user_data = _.get(record, ['data', 'dynamicParams', 'data', 'rechargeNumbersObj'], null);
        let extra_recharge_num_url = "";
        if (user_data) {
            try {
                //user_data = JSON.parse(user_data);
                for (var i = 2; i < 10; i++) {
                    var key = 'recharge_number_' + i,
                        value = '';

                    if (!_.has(user_data, [key], null)) break;

                    value = _.get(user_data, [key], '');
                    extra_recharge_num_url = extra_recharge_num_url + `${operator}${key}=${value}`;
                }
            } catch (error) {
                self.L.error("Error in parsing of data fetched: ", error);
                return extra_recharge_num_url;
            }
        }

        return extra_recharge_num_url;
    }

    //getting used by chat
    getDeepLinkUrl(notifier) {
        let self = this;
        let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(notifier, ['data','dynamicParams', 'category_id'], null)], null);
        let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
        let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(notifier, ['data','dynamicParams', 'category_id'], null);
        let product_service = _.get(notifier, ['data','dynamicParams', 'service'], null) != null ? _.get(notifier, ['data','dynamicParams', 'service'], null).toLowerCase() : null;
        let short_operator_name = _.get(notifier, ['data', 'dynamicParams', 'short_operator_name'], null);
        let template_id = _.get(notifier, "template_id", null);
        let template_name = _.get(notifier, "template_name", null);
        let utm_source =_.get(notifier, "utm_source", null);
        let utm_medium= _.get(notifier, "utm_medium", null);
        let utm_campaign = _.get(notifier, "utm_campaign", null);
        
        //let utm = self._utmByTemplateId(template_id, short_operator_name, '$');
        self.cvrData = _.get(self.config, 'CVR_DATA', {});

                var productInfo = self.cvrData[_.get(notifier, 'product_id', null)];
                if(!short_operator_name){
                    try {
                        short_operator_name = _.get(JSON.parse(productInfo.attributes), 'short_operator_name', null);
                    }
                    catch (error) {
                        self.L.error('Error while parsing attributes for PID', _.get(notifier, 'product_id', null), error);
                    }
        
                    short_operator_name = short_operator_name || _.get(productInfo, 'brand');
                }
                let utm = `&utm_source=${self.getUtmParam(utm_source)}&utm_medium=${self.getUtmParam(utm_medium)}&utm_campaign=${self.getUtmParam(short_operator_name)}`;

        if (!landing_path) {
            if (product_service === 'mobile') {
                landing_path = "mobile_postpaid"; 
            } else if (product_service === 'datacard') {
                landing_path = "datacard_postpaid";
            } else if (product_service === 'dth') {
                landing_path = "dth";
            } else {
                landing_path = 'utility';
            }
        }
 
        let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
        let paramsForUrl = self.getParamsForChat(notifier);
        let deepLinkUrl = null;
        _.set(paramsForUrl, 'product_service', product_service);
        if(product_service=='mobile'){
            deepLinkUrl = url + "?$" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNumForChat(notifier, '$') + utm;
        }else{
            deepLinkUrl = url + "?" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNumForChat(notifier, '$') + utm;   
        }
        return deepLinkUrl;
    }

    async checkForWhatsappNotification(record, payLoad, notificationType) {

        let self = this;
        let customerId = _.get(payLoad, 'customer_id', null);
        let service = _.get(payLoad, 'service', null);
        let paytype = _.get(payLoad, 'paytype', null);
        let dueDate = _.get(payLoad, 'due_date', null);
        let recharge_number = _.get(record, 'recharge_number', null);
        let operator = _.get(record, 'operator', null);
        let dayValue = _.get(record, 'timepoint', null);

        if (_.toLower(service) == 'rent payment') {
            let notificationRecordWhatsapp = {
                type: 'WHATSAPP',
                recipients: _.get(record, 'customer_mobile', _.get(record, 'cust_mobile', null)),
                notificationType: notificationType
            }
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                `STATUS: SKIP_CHECK_FOR_RENT`,
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
            self.L.log('checkForWhatsappNotification::', `skipping checks for rent service for record rech num ${recharge_number} and custId ${customerId}`);
            return notificationRecordWhatsapp;
        }

        if(notificationType === "DUEDATE"){
            notificationType="BILLDUE";
        }
        if(notificationType === "OLD_BILL_NOTIFICATION"){
            notificationType="BILLGEN";
            self.L.log('checkForWhatsappNotification::', `converting notifType from old_bill_notification to  bill_gen for ${customerId}_${service}_${paytype}_${notificationType}_${dayValue}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: OLD_BILL_TO_BILL_GEN`,   
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
        }

      
        if (_.get(payLoad, 'service', '').toLowerCase()  === "mobile" && _.get(payLoad, 'paytype', '').toLowerCase()  === "prepaid") {
            dayValue=(-1*dayValue);
        }
        let debugKey = `${customerId}_${service}_${paytype}_${notificationType}_${dayValue}`;
        
        self.L.log('checkForWhatsappNotification::', `checking if whitelisted record exists for ${debugKey} in cassandra`);
        utility._sendMetricsToDD(1, [
            `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
            `STATUS: TRAFFIC`,
            `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
            `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
        ]);

        let percentageRollout = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'WHATSAPP_NOTIFICATION', 'PERCENTAGE_ROLLOUT'], 0);
        if (Number(customerId) % 100 < Number(percentageRollout)) {
            self.L.log('checkForWhatsappNotification::', `customer id ${customerId} is eligible for attempting whatsapp notification`);
        } else {
            self.L.log('checkForWhatsappNotification::', `customer id ${customerId} is not eligible for attempting whatsapp notification`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                `STATUS: CUSTOMER_ID_NOT_ELIGIBLE`,
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
            return {error:'CUSTOMER_ID_NOT_ELIGIBLE'};
        }

        try {
            if (_.isNull(customerId) || _.isNull(service) || _.isNull(paytype) || _.isNull(notificationType) || _.isNull(dayValue)) {
                self.L.log('checkForWhatsappNotification::', `not checking for whatsapp, one or more mandatory fields are null ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: MISSING_MANDATORY_FIELD`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'MISSING_MANDATORY_FIELD'};
            }
            if (_.toUpper(service) == 'ELECTRICITY' && !_.get(payLoad, 'amount', null)) {
                self.L.log('checkForWhatsappNotification::', `not sending whatsapp as amount is null for service ${service}, ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: AMOUNT_VALUE_NULL`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'AMOUNT_VALUE_NULL'};
            }
            if (_.toUpper(service) == 'ELECTRICITY' && _.get(payLoad, 'convertBGtoDueDate', false)) {
                notificationType = 'BILLGEN';
                let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;
                dayValue = MOMENT().diff(billFetchDate, 'days');
                self.L.log('checkForWhatsappNotification::', `resetting notification type to BILLGEN for rech num${_.get(record, 'recharge_number', null)}, key ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: RESETTING_TO_BILLGEN`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
            }
            
            if (_.toUpper(service) == LOAN_SERVICE && (!dueDate || !operator || !recharge_number || !_.get(payLoad, 'amount', null))) {
                self.L.log('checkForWhatsappNotification::', `not sending whatsapp as something from dueDate operator rn amount is null for service ${service}, ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: LOAN_MANDATORY_FIELD_MISSING`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'LOAN_MANDATORY_FIELD_MISSING'};
            }

            if (_.toUpper(service) == DTH_SERVICE && (!dueDate || !operator || !recharge_number)) {
                self.L.log('checkForWhatsappNotification::', `not sending whatsapp as something from dueDate operator rn is null for service ${service}, ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: DTH_MANDATORY_FIELD_MISSING`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'DTH_MANDATORY_FIELD_MISSING'};
            }

            let whatsappWhitelistedCustomerRecords = await self.cassandraBills.getWhatsappWhitelistedCustomerRecords(customerId, service, paytype, notificationType, dayValue);
            let whatsappWhitelistedCustomerRecord = whatsappWhitelistedCustomerRecords.filter(rec => _.toLower(rec.operator) === _.toLower(_.get(payLoad, 'operator', null)));
            if (_.isNull(whatsappWhitelistedCustomerRecord) || whatsappWhitelistedCustomerRecord.length == 0) {
                whatsappWhitelistedCustomerRecord = whatsappWhitelistedCustomerRecords.filter(rec => rec.operator === 'ALL');
            }
            if (_.isNull(whatsappWhitelistedCustomerRecord) || whatsappWhitelistedCustomerRecord.length == 0) {
                self.L.log('checkForWhatsappNotification::', `No whitelisted record found for ${debugKey} in cassandra`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: NO_WHITELISTED_RECORD_FOUND`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'NO_WHITELISTED_RECORD_FOUND'};
            }
            if (whatsappWhitelistedCustomerRecord.length > 1) {
                self.L.error('checkForWhatsappNotification::', `more than one template_name found for ${debugKey} in whatsapp_whitelisted_customers`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: MULTIPLE_TEMPLATE_NAMES_FOUND_FOR_RECORD`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'MULTIPLE_TEMPLATE_NAMES_FOUND_FOR_RECORD'};
            }
            
            whatsappWhitelistedCustomerRecord = whatsappWhitelistedCustomerRecord[0];
            let expiryDate = MOMENT(_.get(whatsappWhitelistedCustomerRecord, 'expiry_date', null));
            if (!expiryDate.isAfter(MOMENT())) {
                self.L.log('checkForWhatsappNotification::', `not sending whatsapp, record ${debugKey} is expired with expiry date ${_.get(whatsappWhitelistedCustomerRecord, 'expiry_date', null)}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: RECORD_EXPIRED`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'RECORD_EXPIRED'};
            }
            
            if ((_.get(record, 'bill_source') == 'NONRU') && !_.get(record, 'customer_mobile', null)) {
                await new Promise((resolve, reject) => {
                    self.remindableUsersLibrary._getUserDetails((err, res) => {
                        self.L.log('checkForWhatsappNotification::', `fetching customer mobile for customer ${customerId}`);
                        resolve();
                    }, record);
                });                

            }

            if (!_.get(record, 'customer_mobile', _.get(record, 'cust_mobile', null))) {
                self.L.log('checkForWhatsappNotification::', `cannot send whatsapp as customer mobile not found for ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: CUSTOMER_MOBILE_NOT_FOUND`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'CUSTOMER_MOBILE_NOT_FOUND'};
            }
            let templateIdForWhatsapp= _.get(whatsappWhitelistedCustomerRecord, 'template_id', null);
            if(self.whitelistingTemplateConfigForWhatsapp && self.whitelistingTemplateConfigForWhatsapp==1 && self.allowedTemplatesForWhatsapp && !self.allowedTemplatesForWhatsapp.includes(Number(templateIdForWhatsapp))){
                self.L.log('checkForWhatsappNotification::', `cannot send whatsapp as template_id not whitelisted ${debugKey}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: WHATSAPP_TEMPLATE_ID_NOT_WHITELISTED`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
                return {error:'WHATSAPP_TEMPLATE_ID_NOT_WHITELISTED'};

            }



            let notificationRecordWhatsapp = {
                type: 'WHATSAPP',
                recipients: _.get(record, 'customer_mobile', _.get(record, 'cust_mobile', null)),
                notificationType: notificationType,
                template_name: _.get(whatsappWhitelistedCustomerRecord, 'template_name', null),
                template_id: _.get(whatsappWhitelistedCustomerRecord, 'template_id', null)
            };
            
            self.L.log('checkForWhatsappNotification::', `checks passed, sending whatsapp notification for ${debugKey}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                `STATUS: SENDING_WHATSAPP_NOTIFICATION`,
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
            return notificationRecordWhatsapp;
        } catch (error) {
            self.L.error('checkForWhatsappNotification::', `not sending whatsapp for ${debugKey}, error occurred ${error}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                `STATUS: ERROR_OCCURRED`,
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
            return {error:'ERROR_OCCURRED'};
        }
    }

    getWpPriceSuffix(record, operator = '&') {
        const service = _.toUpper(_.get(record, 'service'));
        const amount = _.get(record, 'amount');
        
        // Early return if not DTH service
        if (service !== DTH_SERVICE) {
            return '';
        }
        
        // Handle null/undefined amount
        if (_.isNil(amount)) {
            return `${operator}price=`;
        }
    
        // append price suffix
        return `${operator}price=${amount}`;
    }

}

export default Notification;

