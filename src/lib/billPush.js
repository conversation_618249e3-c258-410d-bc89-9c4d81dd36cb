"use strict";


import _ from 'lodash';
import UTIL from 'util'
import uuidv1 from 'uuidv1'
import RequestWrapper from './requestWrapper'
import REQUEST from 'request'
import utility from '.';
import Logger from './logger';


class BillPush {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;

        this.requestWraper = new RequestWrapper({
            requestType: "BILL_PUSH",
            retryCount: 3
        });

        this.appName = options.appName;
        this.logger = new Logger(options);
    }

    pushToRegistrationProcess(ref, record, payLoad, source, flag = 'reminder') {
        let self = this;
        let finalPayload = self.preparePayload(record, payLoad, source);
        self.publishBillPushUser(ref, finalPayload, source, flag);
    }

    preparePayload(record, payLoad, source) {
        let userData = _.get(record, 'user_data', null);
        if (typeof userData == 'string') {
            try {
                userData = JSON.parse(userData);
            } catch (e) {
                userData = {};
            }
        }

        let newRecord = {
            "operation": "Create",
            "productId": _.get(record, 'productId', _.get(record, 'product_id', null)),
            "rechargeNumber": _.get(record, 'rechargeNumber', _.get(record, 'recharge_number', null)),
            "source": source ? source : "reminder",
            "operator": _.get(record, 'operator', null),
            "onboardingOperator": "euronet",
            "service": _.get(record, 'service', null),
            "customerId": _.get(record, 'customerId', _.get(record, 'customer_id', null)),
            "paytype": _.get(record, 'paytype', null),
            "rechargeNumber2": _.get(userData, 'recharge_number_2', null),
            "rechargeNumber3": _.get(userData, 'recharge_number_3', null),
            "rechargeNumber4": _.get(userData, 'recharge_number_4', null),
            "rechargeNumber5": _.get(userData, 'recharge_number_5', null),
            "rechargeNumber6": _.get(userData, 'recharge_number_6', null),
            "rechargeNumber7": _.get(userData, 'recharge_number_7', null),
            "rechargeNumber8": _.get(userData, 'recharge_number_8', null),
            "mobileNo": _.get(record, 'customerMobile', _.get(record, 'customer_mobile', null))
        }
        return newRecord;
    }

    publishBillPushUser(ref, payload, source, flag) {
        if (payload != null || payload != undefined) {
            let self = this;
            if (ref.upmsPublisher) {
                ref.upmsPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.UPMS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(payload),
                    attributes: 1
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'service', null)}`,
                            'STATUS:ERROR',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.critical(`billPush :: publishFailedRecordInKafka Error while publishing message in Kafka ${error} - MSG:- `, payload, _.get(payload, 'service', null));
                    } else {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'category', null)}`,
                            'STATUS:PUBLISHED',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.log(`billPush :: publishFailedRecordInKafka Message published successfully in Kafka - MSG:- `, payload, _.get(payload, 'service', null));
                    }
                    return;
                }, [200, 800]);
            }
        }

    }

}

export default BillPush;