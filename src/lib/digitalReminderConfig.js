import Q from 'q'
import _ from 'lodash'
import DigitalReminderConfigModel from '../models/digitalReminderConfig'
import Helper from './helper'
import publisherConfigTemporary from '../config/publisherConfigTemporary'

class DigitalReminderConfigLib {

    constructor(options) {
        this.refreshInterval = 15 * 60 * 1000;
        this.L = options.L;
        this.config = options.config;
        this.digitalReminderConfigLoader = new DigitalReminderConfigModel(options);
        // Get configs list which needs to be fetched from Dynamic Config
        this.configsList = _.get(this.config, ['COMMON', 'CONFIGS_LIST'], []);
    }

    load() {
        let self = this;
        let deferred = Q.defer();
        self.getParsedConfig(function (err, parsedConfig) {
            if (err) {
                deferred.reject(err);
            }
            _.extend(self.config.DYNAMIC_CONFIG, parsedConfig);
            setInterval(function () {
                self.getParsedConfig(function (err, parsedConfig) {
                    if (err) {
                        self.L.critical(`Unable to parse dynamic config: ${err}`);
                    } else {
                        _.extend(self.config.DYNAMIC_CONFIG, parsedConfig);
                    }
                });
            }, self.refreshInterval);
            deferred.resolve();
        });

        return deferred.promise;
    }

    getParsedConfig(cb) {
        let self = this;
        self.digitalReminderConfigLoader.getDynamicConfig(function (err, data) {
            if (err) {
                return cb(err);
            }
            let parsedConfig = {},deletedConfig = {};
            try {
                data.forEach(function (row) {
                    let configName = row.name.trim();
                    let nodeName = row.node.trim();
                    let key_name = row.key_name.trim();
                    let type = row.type.trim();
                    let value = row.value;
                    let status = row.status
                    if (typeof value == 'string') {
                        value = value.trim();
                    }
                    try {
                        if (type == 'number') {
                            if(value !=null && value!="")
                            {
                                value = Number(value);
                            }
                        } else if (type == 'str_list') {
                            value = Helper.list(value);
                        } else if (type == 'num_list') {
                            value = Helper.num_list(value);
                        } else if (type == "json") {
                            value = Helper.getParsedJSON(value);
                        }
                    } catch (e) {
                        throw new Error(`Unable to parse ${configName}.${nodeName}.${key_name}.${value}.${type}`);
                    }
                    if(status == 1){
                        if (!parsedConfig[configName]) {
                            parsedConfig[configName] = {};
                        }
                        if (!parsedConfig[configName][nodeName]) {
                            parsedConfig[configName][nodeName] = {};
                        }
                        parsedConfig[configName][nodeName][key_name] = value;
                    }
                    else{
                        if (!deletedConfig[configName]) {
                            deletedConfig[configName] = {};
                        }
                        if (!deletedConfig[configName][nodeName]) {
                            deletedConfig[configName][nodeName] = {};
                        }
                        deletedConfig[configName][nodeName][key_name] = value;
                    }
                });
                parsedConfig = self.parseBasedOnConfig(parsedConfig);
                self.parseBasedOnConfig(deletedConfig , true)
             //   self.L.verbose(`Parsed dynamic config: ${JSON.stringify(parsedConfig, null, 4)}`);
            } catch (err) {
                return cb(err);
            }
            return cb(null, parsedConfig);
        });
    }

    /**
     * Method will parse based on the name field in digital_reminder_config table
     * if config name is present in the configsList declared in the constructor then call their respective methods
     * extend the desired format to the respective config files
     * delete the key and data from the dynamic config object
     * @param {*} parsedConfig from getParsedConfig method
     */
    parseBasedOnConfig(parsedConfig,deleted=false) {
        const self = this;
        const keys = Object.keys(parsedConfig);
        keys.forEach((key) => {
            let generatedConfig;
            if (this.configsList.includes(key)) {
                switch (key) {
                    case 'RECENT_BILL_CONFIG':
                        generatedConfig = self.parseRecentConfigs(parsedConfig[key]);
                    case 'CC_BILL_FETCH_CONFIG':
                        generatedConfig = self.parseRecentConfigs(parsedConfig[key]);
                        break;
                    case 'OPERATOR_TABLE_REGISTRY':
                        generatedConfig = self.parseOperatorTableRegistryConfigs(parsedConfig[key]);
                        break;
                    case 'OPERATOR_PREPAID_TABLE_REGISTRY':
                        generatedConfig = self.parsePrepaidOperatorTableRegistryConfigs(parsedConfig[key]);
                        break;
                    case 'PREPAID_TABLE_REGISTRY':
                        generatedConfig = self.parsePrepaidTableRegistryConfigs(parsedConfig[key]);
                        break;
                    case 'SERVICE_TABLE_REGISTRY':
                        generatedConfig = self.parseServiceTableRegistryConfigs(parsedConfig[key]);
                        break;    
                    case 'OPERATOR_TEMPLATE_MAPPING':
                        generatedConfig = self.parseOperatorTemplateMappingConfig(parsedConfig[key]);
                        break;
                    case 'OPERATOR_NOT_IN_USE_CONFIG':
                        generatedConfig = self.parseOperatorNotInUseConfig(parsedConfig[key]);
                        break;
                    case 'SUBSCRIBER_CONFIG':
                        generatedConfig = self.parseSubscriberConfig(parsedConfig[key]);
                        break;
                    case 'PUBLISHER_CONFIG':
                        generatedConfig = self.parsePublisherConfig(parsedConfig[key]);
                        break;
                    case 'NOTIFICATION': 
                        generatedConfig = self.parseNotificationConfig(parsedConfig[key]);
                        break;
                    case 'OPERATOR_GATEWAY_REGISTRY':
                        generatedConfig = self.parseOperatorGatewayRegistryConfig(parsedConfig[key]);
                        break;
                    case 'AIRTEL_PUBLISHER_CONFIG':
                        generatedConfig = self.parseAirtelPublisherConfig(parsedConfig[key]);
                        break;
                    default:
                        break;
                }
                if (generatedConfig && Object.keys(generatedConfig).length > 0) {
                    // Extend the configs fetched and formatted to their respective config files.
                    if(deleted == false)
                        _.extend(self.config[key], generatedConfig);
                    if(deleted == true){
                        self.L.info("Deleting following properties",JSON.stringify(generatedConfig))
                       self.deleteMatchingProperties(generatedConfig,self.config[key])
                    }
                }

                // Remove the config data fetched for formatting. Since it's already extended to their respective config files. So not required in Dynamic COnfig
                // self.L.log("digitalReminderConfig::ParsedConfig", JSON.stringify(parsedConfig));
                delete parsedConfig[key];
            }
        });
        return parsedConfig;
    }

    
    deleteMatchingProperties(source, target) {
        for (let key in source) {
            if (target.hasOwnProperty(key)) {
                if (Array.isArray(source[key]) && Array.isArray(target[key])) {
                    target[key] = target[key].filter(item => !source[key].includes(item));
                } else if (typeof source[key] === 'object' && typeof target[key] === 'object') {
                    this.deleteMatchingProperties(source[key], target[key]); // Recursively delete matching properties
                    if (Object.keys(target[key]).length === 0) {
                        delete target[key]; // If the object becomes empty after deleting properties, delete the object itself
                    }
                } else {
                    console.log("Deleted",target[key])
                    delete target[key]; // Delete the property
                }
            }
        }
    }
    

    
    /**
     * Method will parse and return the result on the required format for recentBillConfig config and OPERATORS key
     * @param {*} configuration from parseBasedOnConfig method
     */
    parseRecentConfigs(configuration) {
        const configStructure = { 
            OPERATORS : {},
            COMMON: {}
        };
        for (const [node, value] of Object.entries(configuration)) {
            if (value.EXCLUDE_CHANNEL_ID) {
                configStructure.COMMON.EXCLUDE_CHANNEL_ID = value.EXCLUDE_CHANNEL_ID;
            } else if (!configStructure.OPERATORS[node.toLowerCase()]) {
                configStructure.OPERATORS[node.toLowerCase()] = value;
            }
        }
        return configStructure;
    }

    /**
     * Method will parse and return the result on the required format for operatorTableRegistry config
     * @param {*} configuration from parseBasedOnConfig method
     */
    parseOperatorTableRegistryConfigs(configuration) {
        const configStructure = {};
        for (const [node, value] of Object.entries(configuration)) {
            configStructure[node] = value['TABLE_NAME'];
        }
        return configStructure;
    }

    parsePrepaidOperatorTableRegistryConfigs(configuration) {
        const configStructure = {};
        for (const [node, value] of Object.entries(configuration)) {
            configStructure[node] = value['TABLE_NAME'];
        }
        return configStructure;
    }

     parseServiceTableRegistryConfigs(configuration) {
        const configStructure = {};
        for (const [node, value] of Object.entries(configuration)) {
            configStructure[node] = value['TABLE_NAME'];
        }
        return configStructure;
    }

    /**
     * Method will parse and return the result on the required format for prepaidTableRegistry config
     * @param {*} configuration from parseBasedOnConfig method
     */
     parsePrepaidTableRegistryConfigs(configuration) {
        const configStructure = {};
        for (const [node, value] of Object.entries(configuration)) {
            configStructure[node] = value['TABLE_NAME'];
        }
        return configStructure;
    }

    /**
     * Method will parse and return the result on the required format for operatorTableMapping config
     * @param {*} configuration from parseBasedOnConfig method
     */
    parseOperatorTemplateMappingConfig(configuration) {
        return configuration;
    }

    /**
     * Method will parse and return the result on the required format for operatorNotInUseToDaysMapping config
     * @param {*} configuration from parseBasedOnConfig method
     */
    parseOperatorNotInUseConfig(configuration) {
        const configStructure = {};
        for (const [node, value] of Object.entries(configuration)) {
            configStructure[node] = value['NOTIFICATION_EXPIRY_PERIOD'];
        }
        return configStructure;
    }

    /**
     * Method will parse and return the result on the required format for subscriber config
     * @param {*} configuration from parseBasedOnConfig method
     */
    parseSubscriberConfig(configuration) {
        const configStructure = {
            NEXT_BILL_FETCH_DATES: {
            },
            AUTOPAY_NEXT_BILL_FETCH_DATES: {
            },
            NEXT_RETRY_FREQUENCY: {
            },
            AUTOPAY_NEXT_RETRY_FREQUENCY: {
            },
            BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS: [],
            EARLIEST_BILL_FETCH_DELAY : {
            },
            AUTOPAY_EARLIEST_BILL_FETCH_DELAY : {
            }
        };
        for(const [node, value] of Object.entries(configuration)) {
            if (value.NEXT_BILL_FETCH_DATES) {
                configStructure.NEXT_BILL_FETCH_DATES[node] = value.NEXT_BILL_FETCH_DATES;
            }
            if (value.AUTOPAY_NEXT_BILL_FETCH_DATES) {
                configStructure.AUTOPAY_NEXT_BILL_FETCH_DATES[node] = value.AUTOPAY_NEXT_BILL_FETCH_DATES;
            }
            if (value.NEXT_RETRY_FREQUENCY) {
                configStructure.NEXT_RETRY_FREQUENCY[node] = value.NEXT_RETRY_FREQUENCY;
            }
            if (value.AUTOPAY_NEXT_RETRY_FREQUENCY) {
                configStructure.AUTOPAY_NEXT_RETRY_FREQUENCY[node] = value.AUTOPAY_NEXT_RETRY_FREQUENCY;
            }
            if (value.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS) {
                configStructure.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS.push(node);
            }
            if (_.has(value, 'EARLIEST_BILL_FETCH_DELAY')) {
                configStructure.EARLIEST_BILL_FETCH_DELAY[node] = value.EARLIEST_BILL_FETCH_DELAY;
            }
            if (_.has(value, 'AUTOPAY_EARLIEST_BILL_FETCH_DELAY')) {
                configStructure.AUTOPAY_EARLIEST_BILL_FETCH_DELAY[node] = value.AUTOPAY_EARLIEST_BILL_FETCH_DELAY;
            }
        }
        return configStructure;
    }

    /**
     * Method will parse and return the result on the required format for publisher config
     * @param {*} configuration from parseBasedOnConfig method
     */
    parsePublisherConfig(configuration) {
        const configStructure = {
            PUBLISHER : {
                ...publisherConfigTemporary
            },
            MAX_WAIT_TIME_FOR_NEXT_PUSH : {
            },
            BILL_FETCH_PATTERN: {

            }, 
            TPS_WINDOW_TIME: {},
        };
        for (const [node, value] of Object.entries(configuration)) {
            if (_.has(value, 'PUBLISHER')) {
                configStructure.PUBLISHER[value.PUBLISHER] ? configStructure.PUBLISHER[value.PUBLISHER].push(node) : configStructure.PUBLISHER[value.PUBLISHER] = [node];    
            } else if (_.has(value, 'MAX_WAIT_TIME_FOR_NEXT_PUSH')) {
                configStructure.MAX_WAIT_TIME_FOR_NEXT_PUSH[node] = value.MAX_WAIT_TIME_FOR_NEXT_PUSH;
            } 
            if (_.has(value, 'BILL_FETCH_PATTERN')) {
                configStructure.BILL_FETCH_PATTERN[node] = value.BILL_FETCH_PATTERN;
            }
            if (_.has(value, 'TPS_WINDOW_TIME')) {
                configStructure.TPS_WINDOW_TIME[node] = value.TPS_WINDOW_TIME;
            } 
        }
        return configStructure;
    }

    /**
     * 
     * @param {*} configuration : {
            SNOOZE_TIME : {
                DAYS : 7
            }
        } 
     */
    parseAirtelPublisherConfig(configuration){
        const configStructure = {
            SNOOZE_TIME : {
            }
        };
        for (const [node, value] of Object.entries(configuration)) {
            if (node == 'SNOOZE_TIME' && _.has(value, ['DAYS'])) {
                configStructure.SNOOZE_TIME.DAYS = value.DAYS;
            }
        }
        return configStructure;
    }
    /**
     * 
     * @param {*} configuration : {
        "TEMPLATE_UTM" : {
            "4597": {
                "utm_source": "billGenReminder",
                "utm_medium": "email",
                "utm_campaign": "4597"
            },
            "notFound": {
                "utm_source": "billReminderNotFound",
                "utm_medium": "null",
                "utm_campaign": "null"
            }
        },
        "TEMPLATE_ID_BY_SERVICE": {
            "BR_MOBILE_BILLGEN_SMS": 7127,
            "BR_MOBILE_BILLGEN_PUSH": 7126,
            "BR_MOBILE_BILLGEN_CHAT": 7123
        }
    }
     */
    parseNotificationConfig(configuration){
        const configStructure = {
            TEMPLATE_UTM : {
            },
            TEMPLATE_ID_BY_SERVICE : {
            },
            BlackListOperator : {
            },
            BlackListOperatorBillGen : {
            },
            AllowedListOperatorBillGen : {
            }
        };
        for (const [node, value] of Object.entries(configuration)) {
            if (node == 'TEMPLATE_UTM' ) {
                configStructure.TEMPLATE_UTM = value;
            } else if (node == 'TEMPLATE_ID_BY_SERVICE' ) {
                configStructure.TEMPLATE_ID_BY_SERVICE = value;
            } else if (node == 'BlackListOperator' ) {
                configStructure.BlackListOperator = Object.keys(value);
            }
            else if (node == 'BlackListOperatorBillGen' ) {
                configStructure.BlackListOperatorBillGen = Object.keys(value);
            }
            else if (node == 'AllowedListOperatorBillGen' ) {
                configStructure.AllowedListOperatorBillGen = Object.keys(value);
            }
        }
        return configStructure;
    }

    /**
     * 
     * @param {*} configuration 
     */
    parseOperatorGatewayRegistryConfig(configuration){
        const configStructure = {
        };
        for (const [node, value] of Object.entries(configuration)) {
            if ( _.has(value , 'GATEWAY_NAME') ) {
                configStructure[node] = value.GATEWAY_NAME;
            } 
        }
        return configStructure;
    }
}


export default {
    DigitalReminderConfigLib
};
