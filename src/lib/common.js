import _ from 'lodash'
import transactionCounterModel from '../models/allTransactionsCounter'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import utility from '../lib'
import moment from 'moment'
import cassandra from 'cassandra-driver';

class commonLib {
    constructor(options) {
        this.config = options && options.config;
        this.transactionCounterModel = new transactionCounterModel(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.cvrDataLoader = options.cvrDataLoader;
        this.L = options.L;
        this.client = options.cassandraDbClient;
    }

    mapBillsTableColumns(data) {
        let
            self = this,
            mappings = _.get(self.config, 'COMMON.BILLS_COLUMN_MAPPING', {}),
            response = {};

        _.keys(data).forEach(function (key) {
            let mappedKey = key;
            if (_.get(mappings, key, null)) {
                mappedKey = _.get(mappings, key, null);
            }
            _.set(response, mappedKey, _.get(data, key));
        });

        return response;
    }

    calledAfterTime(timeout) {
        return new Promise((resolve, reject) => {
            if (!timeout) {
                return resolve();
            }

            setTimeout(() => {
                resolve();
            }, timeout);

        });
    }

    getRetailerData(done, customerId, dataRow) {
        let self = this;
        if(!customerId){
            return done('common::getRetailerData, invalid customerId')
        }
        self.transactionCounterModel.fetchCustomerDetails((err, data) => {
            if(err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS", 'STATUS:CT_RETAILER_NOT_FOUND', "TYPE:CT_EVENTS"]);
                return done('common::getRetailerData, retailer status not found')
            }
            const isRetailer = _.get(data[0], 'is_retailer', 0);
            const whatsappNotificationStatus = _.get(data[0], "whatsapp_notification_status", -1)
            _.set(dataRow, 'retailerStatus', isRetailer);
            _.set(dataRow, 'whatsappNotificationStatus', whatsappNotificationStatus);

            return done(null)
        }, customerId)
    }

    getCvrData(done, productId, dataRow) {
        let self = this;
        const thumbnail = _.get(self.config, ['CVR_DATA', productId, 'thumbnail'], null);
        const attributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
        const shortOperatorName = _.get(attributes, ['short_operator_name'], '');

        if(thumbnail != null){       // only considering thumbnail as mandatory
            _.set(dataRow, 'thumbnail', thumbnail);
            _.set(dataRow, 'shortOperatorName', shortOperatorName);
            return done(null)
        } else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS", 'STATUS:CT_THUMBNAIL_NOT_FOUND', "TYPE:CT_EVENTS"]);
            return done(`common::getCvrData, thumbnail not found for ${productId}`);
        }
    }

    getUtmParam(utmparam, allowedSpecialChar) {
        if (!utmparam) return '';

        let utmProcessed;
        if (allowedSpecialChar !== "") { 
            let regex = new RegExp(`[^a-zA-Z0-9 ${allowedSpecialChar}]`, "g");
            utmProcessed = utmparam.replace(regex, '');
        } else {
            utmProcessed = utmparam.replace(/[^a-zA-Z0-9]/g, '');
        }
        utmProcessed = encodeURIComponent(utmProcessed);
        return utmProcessed;
    }
    decideTopicToPublishBillGen(){

        let
            self = this,
            slotFrom = _.get(self.config, ['DYNAMIC_CONFIG', 'BG_NOTIFICATION', 'REALTIME_NOTIFY_PERIOD', `START_TIME`], null),
            slotTo = _.get(self.config, ['DYNAMIC_CONFIG', 'BG_NOTIFICATION', 'REALTIME_NOTIFY_PERIOD', `END_TIME`], null);

        if (!(slotFrom && slotTo)) return false;

        let
            slotFromArr = slotFrom.split(':'),
            slotToArr = slotTo.split(':');

        if (!(slotFromArr.length > 0 && slotToArr.length > 0)) return false;

        slotFrom = moment().set({ hour: _.get(slotFromArr, '0', '00'), minute: _.get(slotFromArr, '1', '00'), second: _.get(slotFromArr, '2', '00') });
        slotTo = moment().set({ hour: _.get(slotToArr, '0', '00'), minute: _.get(slotToArr, '1', '00'), second: _.get(slotToArr, '2', '00') });

        if (!(moment().isBetween(slotFrom, slotTo))) {
            return false;
        } 
        else {
            return true;
        }
    }
    validateAndCreatePayload(error, payload) {
        let self = this;
        try {
            let params = {};
            
            if (!_.get(payload, "smsRcvdTime", null)) { 
                self.L.log("validateAndCreatePayload: Mandatory Parameter: smsRcvdTime is not  avaliable");
            }
            else if (!_.get(payload, "smsParsingEntryTime", null)) {
                self.L.log("validateAndCreatePayload: Mandatory Parameter: smsParsingEntryTime is missing");
            }
            else if(!_.get(payload, "refId", null)){
                self.L.log("validateAndCreatePayload: Mandatory Parameter: refId is not  avaliable");
            }
            params.category = _.get(payload, "category") ? _.get(payload, "category") : "cc";
            params.smsRcvdTime = _.get(payload, "smsRcvdTime");
            params.smsParsingEntryTime = _.get(payload, "smsParsingEntryTime");
            params.rtspId = _.get(payload, "rtspId", null)
            params.refId = _.get(payload, "refId");
            params.notificationCreationTime = _.get(payload, "notificationCreationTime", null);
            params.notificationPublishTime = _.get(payload, "notificationPublishTime", null);
            params.smsParsingExitTime = moment().format('YYYY-MM-DD HH:mm:ss');
            params.dataSource = _.get(payload, "dataSource", null);
            params.customerId = _.get(payload, "customerId", null);
            params.status = error ? 0 : 1;


            if(error && error.message && error.stack)
                params.failureReason = JSON.stringify(error, self.replaceError);
            else{
                try{
                    params.failureReason = JSON.stringify(error);
                }
                catch(err){
                    params.failureReason = error;
                }
            }
            return Promise.resolve(params);
        }
        catch (err) {
            self.L.error(err);
            return Promise.reject(err);
        }
    }

    replaceError(key, value) {
        if(value instanceof Error) {
            const newValue = Object.getOwnPropertyNames(value)
                .reduce((obj, propName) => { 
                    obj[propName] = value[propName];
                    return obj; 
                }, { name: value.name });
            return newValue;
        } else {
            return value;
        }
    }
    validateCTEventCondition(done,customerId,rechargeNumber,eventName){
        let self = this;
        let blockedCustIDs = _.get(self.config, ['DYNAMIC_CONFIG','CT_CONFIG','BLOCKED_CUSTOMER_IDS', 'LIST_OF_CUST_IDS'], []);
        if(blockedCustIDs.includes(customerId)){
            return done(`Customer ID is blocked for CT events`);
        }else{
            self.getRecordsForCTFilter(customerId,eventName, rechargeNumber)
            .then(ctFilterData =>{
                if (ctFilterData.length) {   // records found in ct_filter table check for event_date and diff must be greater that 0
                    if (moment(ctFilterData[0].event_date, 'YYYY-MM-DD', true).isValid()) { 
                        let eventDate = moment(ctFilterData[0].event_date,'YYYY-MM-DD')
                        let today = moment();
                        let dayValue = today.diff(eventDate,'days');
                        if(dayValue>0){
                            return Promise.resolve('update')
                        }else{
                            return Promise.reject('CT event already sent for ' + eventDate)
                        }
                    }
                }else{
                    return Promise.resolve('create')
                }
            }).then(operation => {
                self.insertOrUpdateCTFilter(customerId,eventName, rechargeNumber,operation)
                .then(()=>{
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS_FILTER", 'STATUS:SUCCESS', "TYPE:CT_EVENTS_FILTER","EVENTNAME:"+eventName]);
                    done(null);
                }).catch(error => {
                    self.L.error(`insertOrUpdateCTFilter:error`,`failed with error ${error}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS_FILTER", 'STATUS:FAILURE', "TYPE:CT_EVENTS_FILTER","EVENTNAME:"+eventName]);
                    done(error);
                })
            })
            .catch(error => {
                self.L.error(`validateCTEventCondition:error`,`failed with error ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS_FILTER", 'STATUS:FAILURE', "TYPE:CT_EVENTS_FILTER","EVENTNAME:"+eventName]);
                done(error);
            })
        }
    }
    insertOrUpdateCTFilter(customerId, eventName, rechargeNumber, operation) {
        let self = this;
        let consistencyLevel = cassandra.types.consistencies.localQuorum;
        let query,queryParams
        if(operation === 'create'){
            query = 'INSERT into CT_FILTER  (recharge_number, customer_id, event_name, event_date, updated_at, created_at) values(?,?,?,?,?,?)',
            queryParams = [rechargeNumber, customerId, eventName, moment().format('YYYY-MM-DD'), Number(moment().format('x')),Number(moment().format('x'))];
        }else{
            query = 'UPDATE  CT_FILTER set event_date = ?, updated_at = ? WHERE recharge_number = ? and customer_id = ? and event_name = ?',
            queryParams = [moment().format('YYYY-MM-DD'), Number(moment().format('x')),rechargeNumber, customerId, eventName];
        }
        self.L.log('insertOrUpdateCTFilter', 'updating/inserting insertOrUpdateCTFilter to CT_FILTER...');

        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true, consistency: consistencyLevel })
            .then(result =>{
                this.L.log(`insertOrUpdateCTFilter inserted for ${JSON.stringify(queryParams)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('insertOrUpdateCTFilter DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }
    getRecordsForCTFilter(customerId, eventName, rechargeNumber) {
        let self = this,
         consistencyLevel = cassandra.types.consistencies.localQuorum,
            query = 'SELECT * FROM CT_FILTER WHERE recharge_number = ? and customer_id = ? and event_name = ?',
            queryParams = [rechargeNumber, customerId, eventName];

        self.L.log('getRecordsForCTFilter', 'Fetching getRecordsForCTFilter from ct_filter...');

        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true, consistency: consistencyLevel })
            .then(result =>{
                this.L.log(`nonPaytmBills::readBills fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }

    isCTEventBlocked(eventName){
        if(_.get(this.config ,['DYNAMIC_CONFIG','CT_CONFIG','BLOCKED_EVENTS',eventName],null) == 1 ){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CT_EVENTS", 'STATUS:BLOCKED', "TYPE:CT_EVENTS",`EVENT_NAME:${eventName}`]);
            return true
        }
        else return false
    }
    
    formatQueryAndParams(query, params, rechargeNumber, operator){
        let self = this;
        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.getAlternateRechargeNumber(rechargeNumber, operator);
        if(isOperatorPrefixEnabled){
            query += ' recharge_number in (?,?)';
            params.push(rechargeNumber, alternateRechargeNumber);
        }else{
            query += ' recharge_number = ?';
            params.push(rechargeNumber);
        }
        return [query, params];
    }

    getAlternateRechargeNumber(rechargeNumber, operator){
        let self = this;
        let alternateRechargeNumber = '';
        let operatorAndPrefixMap = _.get(this.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR'], {});
        if(_.get(operatorAndPrefixMap, [operator, 'PREFIX'], 'N.A.') != 'N.A.'){
            let prefix = _.get(operatorAndPrefixMap, [operator, 'PREFIX'], 'N.A.');
            alternateRechargeNumber = rechargeNumber && rechargeNumber.startsWith(prefix) ? rechargeNumber.substring(prefix.length) : prefix + rechargeNumber;
            return [true, alternateRechargeNumber];
        }
        return [false, rechargeNumber];
    }

    isCreditCardOperator(serviceOrPaytype) {
        if(_.toLower(serviceOrPaytype) == 'financial services' || _.toLower(serviceOrPaytype) == 'credit card') {
            return true;
        }
        return false;
    }
}

export default {
    commonLib
};