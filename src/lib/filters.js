import MOMENT from 'moment'
import L from 'lgr'
import COMMON_CONFIG from '../config/common'

let getFilteredAmount = (amount) => {
    //TODO: decimal amounts to be handled
   if (amount === undefined || amount === null) {
      L.log("Invalid amount received: ", amount);
      return 0;
   }
	let parsedAmount = parseFloat(amount)
	if(isNaN(parsedAmount)) {
		console.log("its a NaN: => ", amount)
		parsedAmount = 0
   }
   const factor = Math.pow(10, 2);
   return Math.round(parsedAmount * factor) / factor;
}

let getFilteredDate = (date) => {
    if(!date) {
        return {
          value : null,
          isDateFmtValid : true
        }
    }

    if(typeof date !== 'string') {
      L.error("getFilteredDate:: invalid value received for date", date);
      return {
         value: null,
         isDateFmtValid: false
      };
    }

    let formattedDate,
        //Fmt:  DD-MM-YYYY
        regex1 = new RegExp(/([0][1-9]|[1-2][0-9]|[3][0-1])-([0][1-9]|[1][0-2])-[2-9][0-9][0-9][0-9]/g),
        //Fmt:  YYYY-MM-DD
        regex2 = new RegExp(/[2-9][0-9][0-9][0-9]-([0][1-9]|[1][0-2])-([0][1-9]|[1-2][0-9]|[3][0-1])/g),
        //fmt: MMM D, YYYY
        regex3 = new RegExp(/(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ([0][1-9]|[1-9]|[1-2][0-9]|[3][0-1]), ([2-9][0-9][0-9][0-9])/g);
    
    if(date.match(regex1) !== null) {
       formattedDate = MOMENT(date,'DD-MM-YYYY');    
    } 
    else if(date.match(regex2) !== null) {
       formattedDate = MOMENT(date,'YYYY-MM-DD');
    }
    else if(date.match(regex3) !== null) {
      formattedDate = MOMENT(date,'MMM D, YYYY', true);
    }
    else {
       formattedDate = null;
    }
    let maxDateValue = MOMENT(COMMON_CONFIG.common.MAX_VALID_DATE);
    if(formattedDate && formattedDate.isValid() && formattedDate.diff(maxDateValue) <= 0)
       return {
         value : formattedDate,
         isDateFmtValid : true
       };
    else {
       return {
         value : null,
         isDateFmtValid : false
       };
    }
}

export default {
	getFilteredAmount,
	getFilteredDate
}
