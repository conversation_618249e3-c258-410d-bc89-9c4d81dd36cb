import filters         from './filters'
import email           from './email'
import indefiniteDelay from './indefiniteDelay'
import tinyUrl         from './tinyUrl'
import localisation    from './localisation'
import datadog         from './datadog'
import remindableUser  from './remindableUser'
import activePid       from './activePid'
import DigitalReminderConfigLib from './digitalReminderConfig'
import Notification from './notification'
import commonLib from './common'
import startup from './startup'
import PG from './pg'
import SAGA from './saga'
import RecentsLayer from './recentsLayer'
import ctPromoCodes from './ctPromoCodes'
import generateID from './generateID'
import generateChannelId from './generateChannelId';
import Logger from './logger'
import FetchServiceConfig from './fetchServiceConfig';



export default {
   ...filters,
   ...email,
   ...tinyUrl,
   ...indefiniteDelay,
   ...localisation,
   ...datadog,
   ...remindableUser,
   ...activePid,
   ...DigitalReminderConfigLib,
   ...Notification,
   ...commonLib,
   ...startup,
   ...PG,
   ...SAGA,
   RecentsLayer,
   ...ctPromoCodes,
   ...generateID,
   ...generateChannelId,
   Logger,
   ...FetchServiceConfig
}