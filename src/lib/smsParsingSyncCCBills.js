"use strict";

import _ from 'lodash'
import DigitalCatalog from '../lib/digitalCatalog'
import MOMENT from 'moment'
import utility from '../lib';
import SAGA from '../lib/saga';
import BILLS from '../models/bills';
import EncryptionDecryptioinHelper from './EncryptionDecryptioinHelper';

/**
 * SMS Parsing Sync CC Libary will perfrom all operation related to data manipuation
 */
class SmsParsingSyncCCBillLibrary {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.digitalCatalogLib = new DigitalCatalog(options);
        this.sagaUtils = new SAGA(options);
        this.dcatCategoryCacheData = null;
        // this.activePidLib = options.activePidLib;
        this.bills = new BILLS(options);
        this.payloadIngestionTable = "sms_parsing_payload_ingestion"
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
    }

    getProcessedSagaSavedCardsData(cb, processedRecord, sagaSavedCardsData) {
        let self = this;
        try {
            let nonPaytmData = {
                nonPaytmCreditCard : true,
                product: {
                    bankName : processedRecord.bankName,
                    cardNetwork : _.get(self.config, 'COMMON.NON_PAYTM_CC.cardScheme', 'dummyNetwork')
                }
            }
            if(typeof sagaSavedCardsData == 'string' && sagaSavedCardsData !== 'Not found') { // fix in dev testing
                sagaSavedCardsData = JSON.parse(sagaSavedCardsData);
            }
            // if(!sagaSavedCardsData.body.responseStatus === 'SUCCESS') { 
            //     return cb({
            //         status : "ERROR",
            //         type : 'API_STATUS_NOT_SUCCESS',
            //         data : null
            //     });    
            // }
            self.L.verbose(`getProcessedSagaSavedCardsData `, sagaSavedCardsData );
            if( !sagaSavedCardsData || !_.has(sagaSavedCardsData, 'recents') ) {
                return cb({
                    status : "ERROR",
                    type : 'SAVED_CARD_DETAILS_NOT_EXISTS',
                    data : null
                });
            }
            let savedCardDetails = sagaSavedCardsData.recents;
            if(!savedCardDetails || !_.isArray(savedCardDetails) || !savedCardDetails.length) {
                return cb({
                    status : "ERROR",
                    type : 'NO_SAVE_CARDS_FOUND',
                    data : nonPaytmData
                });
            } 
            if( savedCardDetails.length >= _.get(self.config, 'COMMON.PG_SAVED_CARDS_BY_USER_ID_API_MAX_RECORDS_LIMIT', 10) ) {
                return cb({
                    status : "ERROR",
                    type : 'MAX_SAVE_CARDS_LIMIT_EXCEED',
                    data : null
                });
            }
            let last4DigitsMatchingRecords = [];

            for (let index = 0; index < savedCardDetails.length; index++) {
                let record = savedCardDetails[index];

                // let sms_bank_name = _.toLower(_.get(processedRecord,'bankName','')),
                //     saga_recents_bank_name = _.toLower(_.get(record,['product','bankName'], ''));
                // if(saga_recents_bank_name == '') {
                //     let saga_recents_product_id = _.get(record,['product','productId'], null);
                //     saga_recents_product_id = self.activePidLib.getActivePID(saga_recents_product_id);
                //     let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', saga_recents_product_id, 'attributes'] , '{}'));
                //     saga_recents_bank_name = _.toLower(_.get(attributes, ['bank_code'] , ''));

                //     if(!saga_recents_bank_name || saga_recents_bank_name == ''){
                //         self.L.error(`getProcessedSagaSavedCardsData :: bank code unavailable for ${processedRecord.debugKey} `);
                //     }
                // }
                // if(record.product.cardType == "CC" && self.matchingMCN(record.recharge_number,processedRecord.lastCC) && ((sms_bank_name != '' && saga_recents_bank_name != '') ? sms_bank_name == saga_recents_bank_name : true)){
                if(record.product.cardType == "CC" && self.matchingMCN(record.recharge_number,processedRecord.lastCC)){
                    record.maskedCardNumber = record.recharge_number;
                    last4DigitsMatchingRecords.push(record);
                }
            }
            if (last4DigitsMatchingRecords.length === 0) {
                return cb({
                    status : "SUCCESS",
                    type : 'NO_MATCHING_MCN',
                    data : nonPaytmData
                });
            } else if (last4DigitsMatchingRecords.length === 1) {
                return cb({
                    status : "SUCCESS",
                    type : 'MATCHING_MCN',
                    data : last4DigitsMatchingRecords[0]
                });
            } else {
                return cb({
                    status : "ERROR",
                    type : 'MULTIPLE_MATCHING_MCN',
                    data : null
                });
            }
        } catch(err) {
            return cb({
                status : "ERROR",
                type : 'PROCESSING_FAILURE',
                data : null
            });
        }
    }

    matchingMCN(recharge_number, lastCC){
        recharge_number = recharge_number.replace(/ /g,'');
        let lastCCofSaga = recharge_number.split('X');
        lastCCofSaga = lastCCofSaga[lastCCofSaga.length-1];
        if(lastCCofSaga.length < lastCC.length)
        {
            lastCC = lastCC.substr(-lastCCofSaga.length);
            if(lastCC == lastCCofSaga) return true;
        }
        else{
            lastCCofSaga = lastCCofSaga.substr(-lastCC.length);
            if(lastCCofSaga == lastCC) return true;
        }
        return false;
    }

    createMaskedCC(last4DigitsMatchingRecords){
        let maskedCardNumber = "XXXXXXXXXXXXXXXX";
        let endIndex = maskedCardNumber.length - last4DigitsMatchingRecords.length
        maskedCardNumber = maskedCardNumber.substring(0, endIndex)  + last4DigitsMatchingRecords
        maskedCardNumber = maskedCardNumber.replace(/(.{4})/g, '$1 ').trim();
        return maskedCardNumber;
    }

    createMaskedCCBasedOnCCLen(recharge_number, lastcc){
        let revRechargeNumber = recharge_number.split("").reverse()
        let lastCClen=4, i = 0, count = 0;

        while (i < revRechargeNumber.length) {
          if(count <= lastCClen) {
             if(!isNaN(revRechargeNumber[i])){
                 count = count+1
             }
          }
          else{
            if(revRechargeNumber[i] !=' ' && !isNaN(revRechargeNumber[i])){
               revRechargeNumber[i] = 'X'
           }
        }
          i++
        }
        return revRechargeNumber.reverse().join("")
    }
    
    /**
     * 
     * @param {*} sagaSavedCCData 
     * @returns 
     */

     isPaytmFirstCCInSagaCCDetails(sagaSavedCCData) {
        let self = this;
        if (_.get(sagaSavedCCData, ['product', 'bankName'], null) === "Paytm First") {
            return true;
        }
        let maskedCardNumber = _.get(sagaSavedCCData, 'maskedCardNumber', null);
        let binNumber = maskedCardNumber ? maskedCardNumber.toString().replace(/ /g, '').substr(0,6) : null;
        if(  _.has(self.config, ['COMMON' , 'PG_SAVED_CARDS_BY_USER_ID_API_PAYTMFIRSTCC_SUPPORTED_BIN_IDS' , binNumber ]) ) {
            return true;
        }
        return false;
    }

    isPaytmFirstCCFromRechargeNumber(record){
        let self = this,
            recharge_number = _.get(record, ['recharge_number'] , '' ),
            user_data =  _.get(record, ['user_data'] , ''),
            gateway =  _.get(record, ['gateway'] , ''),
            binNumber = recharge_number ? recharge_number.toString().replace(/ /g, '').substr(0,6) : null;

        if( _.has(self.config, ['COMMON' , 'PG_SAVED_CARDS_BY_USER_ID_API_PAYTMFIRSTCC_SUPPORTED_BIN_IDS' , binNumber ]) ) {
            return true;
        }
        return false;
    }
    /**
     * 
     * @param {*} CCData {isPaytmFirstCard : '1/0', bankName : '' , cardScheme : ''} 
     * @returns 
     */
    
    getUniqueKeyForSavedCardsData(CCData) {
        let uniqueKey = _.get(CCData , 'isPaytmFirstCard' , '0');

        uniqueKey += '_' + ( _.get(CCData , 'bankName' , null) ? _.get(CCData , 'bankName' , null) : '');
        uniqueKey += '_' + ( _.get(CCData , 'cardScheme' , null) ? _.get(CCData , 'cardScheme' , null) : '');
        uniqueKey = _.toLower(uniqueKey);
        return uniqueKey;
    }


    formatDataForUpdateNonPaytmCards(params){
        let self = this,
        productId = params.productId,
            //  dateFormat = 'YYYY-MM-DD HH:mm:ss',
             processedRecord = params.processedRecord,
             rechargeNumber = self.createMaskedCC(_.get(processedRecord, 'lastCC', null)),

            updatedDbRecord = { 
                customerId : _.get(processedRecord, 'customerId', null),
                rechargeNumber : rechargeNumber,
                productId : productId,
                operator : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'operator'] , null)),
                amount : _.get(processedRecord, 'currentPaidAmount', 0),   
                customerOtherInfo : JSON.stringify(_.clone(processedRecord)),                     
                paytype : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'paytype'], null)),
                service : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'service'], null)),
                customerMobile : null,                                                 // Will fill with Auth API otherwise it can be filled in notification create service
                customerEmail : null,                                                  // Will fill with Auth API otherwise it can be filled in notification create service
                notificationStatus : _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
                bankName : _.get(processedRecord, 'bankName', null),
                cardNetwork: _.get(self.config, 'COMMON.NON_PAYTM_CC.cardScheme', 'dummyNetwork'),
                status : _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14), 
                source: _.get(params, "source", "NO_SOURCE"),
            };
            return updatedDbRecord;
        }
    formatDataForDeletingNonPaytmCards(params){
        let self = this,
        productId = _.get(params, 'product_id', null),
            // dateFormat = 'YYYY-MM-DD HH:mm:ss',

            updatedDbRecord = { 
                customerId : _.get(params, 'customer_id', null),
                rechargeNumber : _.get(params, 'recharge_number', null),
                productId : productId,
                operator : _.get(params, 'operator', null),
                amount : _.get(params, 'amount', null),                // totalAmt
                dueDate : _.get(params, 'due_date', null),
                billDate : _.get(params, 'bill_date', null),    
                billFetchDate : _.get(params, 'bill_fetch_date', null),
                customerOtherInfo : _.get(params, 'customerOtherInfo', null),                     
                paytype : _.get(params, 'paytype', null),
                service : _.get(params, 'service', null),
                categoryId: _.get(self.config, ['CVR_DATA', productId, 'category_id'], null),
                customerMobile : _.get(params, 'customer_mobile', null),                                                 // Will fill with Auth API otherwise it can be filled in notification create service
                customerEmail : _.get(params, 'customer_email', null),                                                  // Will fill with Auth API otherwise it can be filled in notification create service
                notificationStatus : _.get(params, 'notification_status', null),
                bankName : _.get(params, 'bank_name', null),
                cardNetwork: _.get(params, 'card_network', null),
                status : _.get(params, 'status', null),
                dbEvent :  _.get(params, 'dbEvent', null),
                source: _.get(params, 'origin', 'NO_SOURCE'),
                RUreadsKafkaTime: _.get(params, 'RUreadsKafkaTime', null),
            };

        return updatedDbRecord;
    }


    formatDataForNonPaytmCards(params){
        let self = this,
            productId = params.productId,
            // dateFormat = 'YYYY-MM-DD HH:mm:ss',
            processedRecord = params.processedRecord,
            rechargeNumber = self.createMaskedCC(_.get(processedRecord, 'lastCC', null));

        let extra = {};
        extra.updated_data_source = "SMS_PARSING_DWH";
        if (_.get(processedRecord, 'isRuSmsParsing', false)) {
            extra.isRuSmsParsing = true;
            extra.updated_data_source = "SMS_PARSING_REALTIME";
        }
        else if (_.get(processedRecord, 'isDwhSmsParsingRealtime', false)) {
            extra.isRuSmsParsing = true;
            extra.updated_data_source = "SMS_PARSING_DWH_REALTIME"
        }
        let updatedDbRecord = { 
                customerId : _.get(processedRecord, 'customerId', null),
                rechargeNumber : rechargeNumber,
                productId : productId,
                operator : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'operator'] , null)),
                amount : _.get(processedRecord, 'currentBillAmount', 0),                // totalAmt
                dueDate : processedRecord.billDueDate ? MOMENT(processedRecord.billDueDate).format() : null,
                billDate :  MOMENT().format(),    
                billFetchDate : MOMENT().format(),
                customerOtherInfo : JSON.stringify(_.clone(processedRecord)),
            extra: JSON.stringify(extra),                    
                paytype : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'paytype'], null)),
                service : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'service'], null)),
                categoryId: _.get(self.config, ['CVR_DATA', productId, 'category_id'], null),
                customerMobile : null,                                                 // Will fill with Auth API otherwise it can be filled in notification create service
                customerEmail : null,                                                  // Will fill with Auth API otherwise it can be filled in notification create service
                notificationStatus : _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
                bankName : _.get(processedRecord, 'bankName', null),
                cardNetwork: _.get(self.config, 'COMMON.NON_PAYTM_CC.cardScheme', 'dummyNetwork'),
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                refId : _.get(params, ["analyticsPayload", "refId"]),
                rtspId : _.get(params, ["analyticsPayload", "rtspId"]),
                smsRcvdTime: _.get(params, ["analyticsPayload" , "smsRcvdTime"]),
                smsParsingEntryTime: _.get(params, ["analyticsPayload", "smsParsingEntryTime"]),
                category: _.get(params, ["analyticsPayload", "category"]),
                dataSource: _.get(params, ["analyticsPayload", "dataSource"]),
                source: _.get(params, 'origin', 'NO_SOURCE'),
                RUreadsKafkaTime: _.get(params, 'RUreadsKafkaTime', null),
                dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
            };

        return updatedDbRecord;
    }

    getFormattedData(params){
        let self = this,
            productId = params.productId,
            sagaSavedCCData = params.sagaSavedCCData,
            dateFormat = 'YYYY-MM-DD HH:mm:ss',
            processedRecord = params.processedRecord;
        let bankName = sagaSavedCCData.product.bankName
        let cardNetwork = sagaSavedCCData.product.cardNetwork;
        let isCardCoft = sagaSavedCCData.additional_info.cardCoft;
        let user_data = {};
        if(isCardCoft){
            _.set(user_data, 'recharge_number_4', _.get(sagaSavedCCData, ['additional_info', 'tin'], null))
        }
        else{
            if(_.get(sagaSavedCCData, ['additional_info', 'cin'], null) && _.get(sagaSavedCCData, ['additional_info', 'cin'], null).includes('CIN_')){
                _.set(user_data, 'recharge_number_2', _.get(sagaSavedCCData, ['additional_info', 'cin'], null))
            }
            else{
                _.set(user_data, 'recharge_number_3', _.get(sagaSavedCCData, ['additional_info', 'cin'], null))
            }
        }
        let extra = {
            created_source:'savedCard', 
            updated_source: 'savedCard',
            updated_data_source: 'savedCard',
            source_subtype_2 : 'FULL_BILL'
        }
        if(_.get(processedRecord,'isRuSmsParsing', false)==true){
            _.set(extra, 'isRuSmsParsing', true)
        } else if (_.get(processedRecord,'isDwhSmsParsingRealtime', false)==true) {
            _.set(extra, 'isDwhSmsParsingRealtime', true)
        }

        let updatedDbRecord = { 
                customer_id : _.get(processedRecord, 'customerId', null),
                recharge_number : _.get(sagaSavedCCData, 'recharge_number', null),
                reference_id: isCardCoft ? null : _.get(sagaSavedCCData, ['additional_info', 'cin'], null),
                tin: isCardCoft ? _.get(sagaSavedCCData, ['additional_info', 'tin'], null) : null,
                par_id: _.get(sagaSavedCCData, ['additional_info','panUniqueReference'], null),
                product_id : productId,
                operator : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'operator'] , null)),
                amount : _.get(processedRecord, 'currentBillAmount', 0),                // totalAmt
                due_date : MOMENT(processedRecord.billDueDate).format(dateFormat),
                bill_date : (MOMENT(processedRecord.billDate).format(dateFormat) || MOMENT().format(dateFormat)),    
                bill_fetch_date : MOMENT().format(dateFormat),
                next_bill_fetch_date : _.get(params, ['extras', 'next_bill_fetch_date'], null),
                service_id : 0,                                                         
                customerOtherInfo : JSON.stringify(_.clone(processedRecord)),                     
                paytype : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'paytype'], null)),
                service : _.toLower(_.get(self.config, ['CVR_DATA', productId, 'service'], null)),
                circle :  _.get(self.config, ['CVR_DATA', productId, 'circle'], null),
                gateway : null,                                                         
                customer_mobile : null,                                                 // Will fill with Auth API otherwise it can be filled in notification create service
                customer_email : null,                                                  // Will fill with Auth API otherwise it can be filled in notification create service
                payment_channel : null,                                                 
                retry_count : 0,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                reason : null,
                extra : JSON.stringify(extra) ,                                                   // for normal flow its value is filled by recent service as {"customer_type":1}
                published_date : null,                                                    
                user_data : JSON.stringify(user_data),                                                          
                notification_status : _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
                payment_date :  null,
                is_automatic : 0,
                bank_name : bankName,
                card_network : cardNetwork,
                refId : _.get(params, ["analyticsPayload", "refId"]),
                rtspId : _.get(params, ["analyticsPayload", "rtspId"]),
                source: _.get(params, 'source'),
                RUreadsKafkaTime: _.get(params, 'RUreadsKafkaTime', null),
                dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
            };

        return updatedDbRecord;
    }

    getFinancialServicesPID(done, sagaSavedCCDataUniqueKey) {
        let self = this, productId;
        try {
            self.dcatCategoryCacheData = { "0_amex_amex": *********, "0_amex_dummynetwork": *********, "0_aubl_rupay": *********, "0_aubl_visa": *********, "0_aubl_dummynetwork": *********, "0_aubl_viabbps": *********, "0_alh_dummynetwork": *********, "0_andb_dummynetwork": *********, "0_axis_amex": *********, "0_axis_master": *********, "0_axis_rupay": *********, "0_axis_visa": *********, "0_axis_dummynetwork": 555008553, "0_axis_viabbps": 541864302, "0_bob_master": 480500724, "0_bob_rupay": 480500729, "0_bob_visa": 480500728, "0_bob_dummynetwork": 555008554, "0_bob_viabbps": 480500719, "0_boi_master": 465104151, "0_boi_rupay": 465104147, "0_boi_visa": 465104149, "0_boi_dummynetwork": 341208174, "0_bom_rupay": 577106356, "0_bom_visa": 577106357, "0_bom_dummynetwork": 577106353, "0_bom_viabbps": 577106352, "0_csb_rupay": 465104166, "0_canara_master": 480500726, "0_canara_rupay": 480500703, "0_canara_visa": 480500727, "0_canara_dummynetwork": 555008552, "0_canara_viabbps": 480500723, "0_cbi_rupay": 355322331, "0_cbi_visa": 178514658, "0_cbi_dummynetwork": 341208176, "0_citiub_master": 382337863, "0_citiub_rupay": 355870915, "0_corp_dummynetwork": 341208161, "0_cosmos_dummynetwork": 341208152, "0_dbs_amex": 565468493, "0_dbs_master": 565468497, "0_dbs_rupay": 565468496, "0_dbs_visa": 565468495, "0_dbs_dummynetwork": 565468498, "0_dbs_viabbps": 565468494, "0_dcb_visa": 355305885, "0_dena_dummynetwork": 341208169, "0_dhan_visa": 603353603, "0_dhan_dummynetwork": 603353605, "0_dhan_viabbps": 603353604, "0_equi_rupay": 355870914, "0_fdeb_master": 480500702, "0_fdeb_rupay": 480500708, "0_fdeb_visa": 480500730, "0_fdeb_dummynetwork": 555008551, "0_fdeb_viabbps": 480500718, "0_hdfc_amex": 538418075, "0_hdfc_diners": 538418074, "0_hdfc_master": 538418076, "0_hdfc_rupay": 538418080, "0_hdfc_visa": 538418068, "0_hdfc_dummynetwork": 555008548, "0_hdfc_viabbps": 538418070, "0_hsbc_amex": 561751705, "0_hsbc_master": 561751706, "0_hsbc_rupay": 561751707, "0_hsbc_visa": 561751704, "0_hsbc_dummynetwork": 561751708, "0_hsbc_viabbps": 561751709, "0_icici_amex": 536018948, "0_icici_master": 536018917, "0_icici_rupay": 536018947, "0_icici_visa": 536018949, "0_icici_dummynetwork": 555008550, "0_icici_viabbps": 536018950, "0_idbi_master": 515866004, "0_idbi_rupay": 515866325, "0_idbi_visa": 515866327, "0_idbi_dummynetwork": 515866326, "0_idbi_viabbps": 515866328, "0_idfc_amex": 538418069, "0_idfc_master": 538418082, "0_idfc_rupay": 538418072, "0_idfc_visa": 538418081, "0_idfc_dummynetwork": 555008546, "0_idfc_viabbps": 538418073, "0_ing_dummynetwork": 341208155, "0_indb_rupay": 465104193, "0_indb_visa": 465104197, "0_indb_dummynetwork": 555008555, "0_iob_rupay": 577106350, "0_iob_visa": 577106351, "0_iob_dummynetwork": 577106354, "0_iob_viabbps": 577106355, "0_inds_amex": 480500705, "0_inds_master": 480500706, "0_inds_rupay": 480500710, "0_inds_visa": 480500707, "0_inds_dummynetwork": 555008558, "0_inds_viabbps": 480500717, "0_ibok_rupay": 355870913, "0_jkb_master": 270919889, "0_jkb_visa": 178514648, "0_jkb_dummynetwork": 341208172, "0_ktkb_dummynetwork": 341208145, "0_kvb_visa": 465104208, "0_kvb_dummynetwork": 341208165, "0_nkmb_master": 480500712, "0_nkmb_rupay": 480500704, "0_nkmb_visa": 480500711, "0_nkmb_dummynetwork": 555008547, "0_nkmb_viabbps": 480500721, "0_ocbob_viabbps": 606781342, "0_ocib_viabbps": 578032578, "0_ocsib_viabbps": 577106358, "0_onec_master": 382337865, "0_onec_visa": 354082597, "0_obprf_dummynetwork": 341208163, "1_citi_visa": 465104217, "1_hdfc_visa": 538418071, "1_sbi_rupay": 483873768, "1_sbi_visa": 483873767, "0_pnb_amex": 536018923, "0_pnb_master": 536018926, "0_pnb_rupay": 536018919, "0_pnb_visa": 536018929, "0_pnb_dummynetwork": 555008559, "0_pnb_viabbps": 536018951, "0_ratn_amex": 564281461, "0_ratn_master": 564281456, "0_ratn_rupay": 564281452, "0_ratn_visa": 564281454, "0_ratn_dummynetwork": 564281455, "0_ratn_viabbps": 564281453, "0_rbs_dummynetwork": 341208170, "0_sbi_amex": 480500715, "0_sbi_master": 480500713, "0_sbi_rupay": 480500716, "0_sbi_visa": 480500714, "0_sbi_dummynetwork": 554584192, "0_sbi_viabbps": 480500722, "0_sbmi_rupay": 465104234, "0_stb_amex": 536018928, "0_stb_master": 536018920, "0_stb_rupay": 536018924, "0_stb_visa": 536018927, "0_stb_dummynetwork": 555008556, "0_stb_viabbps": 536018925, "0_sib_visa": 178513902, "0_sib_dummynetwork": 341208162, "0_scb_master": 465104237, "0_scb_visa": 465104235, "0_scb_dummynetwork": 341208150, "0_sbmb_visa": 458934493, "0_synbk_dummynetwork": 341208167, "0_tnmb_rupay": 355870910, "0_tnmb_visa": 355305884, "0_uco_rupay": 465104240, "0_uco_dummynetwork": 341208160, "0_uni_amex": 536018937, "0_uni_master": 536018943, "0_uni_rupay": 536018941, "0_uni_visa": 536018939, "0_uni_dummynetwork": 555008545, "0_uni_viabbps": 536018940, "0_ubi_dummynetwork": 341208158, "0_vjya_dummynetwork": 341208149, "0_yes_amex": 547893974, "0_yes_master": 547892733, "0_yes_rupay": 547892735, "0_yes_visa": 547892736, "0_yes_dummynetwork": 555008557, "0_yes_viabbps": 547892734 };
            if(self.dcatCategoryCacheData) {
                productId = self.getPIDFromDcatCategoryCacheData(sagaSavedCCDataUniqueKey);
                if (productId == null) {
                    return done(`Product Id not found for unique key ${sagaSavedCCDataUniqueKey}`);
                }                
                return done(null, productId);
            } else {
                return done(`dcatCategoryCacheData is not initialized.`);
            }
            /** 
            self.reinitializeDCATCacheData(function(error) {
                if(error) return done(error);

                productId = self.getPIDFromDcatCategoryCacheData(sagaSavedCCDataUniqueKey);
                if (productId == null) {
                    return done(`Product Id not found for unique key ${sagaSavedCCDataUniqueKey}`);
                }
                return done(null, productId);
            });
            */

        } catch (error) {
            self.L.error(`getFinancialServicesPID`, `Error Msg:${error}`);
            return done(error);
        }
    }
    refreshDCATCacheData(refreshInterval) {
        let self = this;
        self.reinitializeDCATCacheData(function (error) {
            if(error) {
                self.L.critical(`refreshDCATCacheData`,`Unable to initialized DCAT Cache Data_ErrorMSG:${error}`);
            } else {
                self.L.log(`refreshDCATCacheData`,`DCAT Cache Data reinitialized`);
            }
            setInterval(()=>{            
                self.reinitializeDCATCacheData(function (error) {
                    if(error) {
                        self.L.critical(`refreshDCATCacheData`,`Unable to initialized DCAT Cache Data_ErrorMSG:${error}`);
                    } else {
                        self.L.log(`refreshDCATCacheData`,`DCAT Cache Data reinitialized`);
                    }
                 });
            },refreshInterval);

        });
        
    }
    reinitializeDCATCacheData(done){
        let self = this;

        self.digitalCatalogLib.getCategoryProductDetail(function (error, categoryData) {
            if(error) {
                return done(error);
            } else {
                /** check for error case when DCAT category data from API data is not consistent
                 *  do we have to only reset after getting successful response
                 *  And check for multiple times failure of this API
                 */
                
                let {status : status , type : type , data : data } =  self.getDcatCacheParams(categoryData);
                
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:GET_CATEGORY_DATA_FROM_DCAT', `STATUS:${status}`, `TYPE:${type}`]);

                if(status === "ERROR") {
                    self.L.error(`getFinancialServicesPID::getFinancialServicesPID`, `Error type:${type} Msg:${data}`);
                    return done(`getFinancialServicesPID::getFinancialServicesPID Error type:${type}`);
                } else {
                    self.dcatCategoryCacheData = {};
                    self.dcatCategoryCacheData = _.cloneDeep(data);
                    
                    self.L.log(`getFinancialServicesPID::getFinancialServicesPID`,`dcatCategoryCacheData`, self.dcatCategoryCacheData);
                }
                
                return done(null);
            }
        });
    }
    /**
     * 
     * @param {*} sagaSavedCCDataUniqueKey 
     * @returns 
     */

    getPIDFromDcatCategoryCacheData(sagaSavedCCDataUniqueKey) {
        let self = this;

        if(_.has(self.dcatCategoryCacheData, sagaSavedCCDataUniqueKey )) {
            return self.dcatCategoryCacheData[sagaSavedCCDataUniqueKey]
        }
        return null;
    }

    async ingestIncomingPayloads(record){
        var self=this;
        return new Promise((resolve)=>{
            self.bills.ingestRealtimePayloads(record,self.payloadIngestionTable,function(err,data){
                if(err){
                    self.L.error(`ingestCCPayloadInDB :: Error while inserting payload in table ${self.payloadIngestionTable} err:: ${err} for record ${JSON.stringify(record)}`);
                }else{
                    self.L.log(`ingestCCPayloadInDB :: Payload inserted successfully in table ${self.payloadIngestionTable} for record ${JSON.stringify(record)}`);
                }
                return resolve(null);
            })
        })
    }

    getDcatCacheParams(categoryData) {
        let self = this;
        try {
            const serviceCategory = 'Financial Services';
            const paytype = 'Credit card';
            let dcatCategoryCacheData = { "0_amex_amex": *********, "0_amex_dummynetwork": *********, "0_aubl_rupay": *********, "0_aubl_visa": *********, "0_aubl_dummynetwork": *********, "0_aubl_viabbps": *********, "0_alh_dummynetwork": *********, "0_andb_dummynetwork": *********, "0_axis_amex": *********, "0_axis_master": *********, "0_axis_rupay": *********, "0_axis_visa": *********, "0_axis_dummynetwork": 555008553, "0_axis_viabbps": 541864302, "0_bob_master": 480500724, "0_bob_rupay": 480500729, "0_bob_visa": 480500728, "0_bob_dummynetwork": 555008554, "0_bob_viabbps": 480500719, "0_boi_master": 465104151, "0_boi_rupay": 465104147, "0_boi_visa": 465104149, "0_boi_dummynetwork": 341208174, "0_bom_rupay": 577106356, "0_bom_visa": 577106357, "0_bom_dummynetwork": 577106353, "0_bom_viabbps": 577106352, "0_csb_rupay": 465104166, "0_canara_master": 480500726, "0_canara_rupay": 480500703, "0_canara_visa": 480500727, "0_canara_dummynetwork": 555008552, "0_canara_viabbps": 480500723, "0_cbi_rupay": 355322331, "0_cbi_visa": 178514658, "0_cbi_dummynetwork": 341208176, "0_citiub_master": 382337863, "0_citiub_rupay": 355870915, "0_corp_dummynetwork": 341208161, "0_cosmos_dummynetwork": 341208152, "0_dbs_amex": 565468493, "0_dbs_master": 565468497, "0_dbs_rupay": 565468496, "0_dbs_visa": 565468495, "0_dbs_dummynetwork": 565468498, "0_dbs_viabbps": 565468494, "0_dcb_visa": 355305885, "0_dena_dummynetwork": 341208169, "0_dhan_visa": 603353603, "0_dhan_dummynetwork": 603353605, "0_dhan_viabbps": 603353604, "0_equi_rupay": 355870914, "0_fdeb_master": 480500702, "0_fdeb_rupay": 480500708, "0_fdeb_visa": 480500730, "0_fdeb_dummynetwork": 555008551, "0_fdeb_viabbps": 480500718, "0_hdfc_amex": 538418075, "0_hdfc_diners": 538418074, "0_hdfc_master": 538418076, "0_hdfc_rupay": 538418080, "0_hdfc_visa": 538418068, "0_hdfc_dummynetwork": 555008548, "0_hdfc_viabbps": 538418070, "0_hsbc_amex": 561751705, "0_hsbc_master": 561751706, "0_hsbc_rupay": 561751707, "0_hsbc_visa": 561751704, "0_hsbc_dummynetwork": 561751708, "0_hsbc_viabbps": 561751709, "0_icici_amex": 536018948, "0_icici_master": 536018917, "0_icici_rupay": 536018947, "0_icici_visa": 536018949, "0_icici_dummynetwork": 555008550, "0_icici_viabbps": 536018950, "0_idbi_master": 515866004, "0_idbi_rupay": 515866325, "0_idbi_visa": 515866327, "0_idbi_dummynetwork": 515866326, "0_idbi_viabbps": 515866328, "0_idfc_amex": 538418069, "0_idfc_master": 538418082, "0_idfc_rupay": 538418072, "0_idfc_visa": 538418081, "0_idfc_dummynetwork": 555008546, "0_idfc_viabbps": 538418073, "0_ing_dummynetwork": 341208155, "0_indb_rupay": 465104193, "0_indb_visa": 465104197, "0_indb_dummynetwork": 555008555, "0_iob_rupay": 577106350, "0_iob_visa": 577106351, "0_iob_dummynetwork": 577106354, "0_iob_viabbps": 577106355, "0_inds_amex": 480500705, "0_inds_master": 480500706, "0_inds_rupay": 480500710, "0_inds_visa": 480500707, "0_inds_dummynetwork": 555008558, "0_inds_viabbps": 480500717, "0_ibok_rupay": 355870913, "0_jkb_master": 270919889, "0_jkb_visa": 178514648, "0_jkb_dummynetwork": 341208172, "0_ktkb_dummynetwork": 341208145, "0_kvb_visa": 465104208, "0_kvb_dummynetwork": 341208165, "0_nkmb_master": 480500712, "0_nkmb_rupay": 480500704, "0_nkmb_visa": 480500711, "0_nkmb_dummynetwork": 555008547, "0_nkmb_viabbps": 480500721, "0_ocbob_viabbps": 606781342, "0_ocib_viabbps": 578032578, "0_ocsib_viabbps": 577106358, "0_onec_master": 382337865, "0_onec_visa": 354082597, "0_obprf_dummynetwork": 341208163, "1_citi_visa": 465104217, "1_hdfc_visa": 538418071, "1_sbi_rupay": 483873768, "1_sbi_visa": 483873767, "0_pnb_amex": 536018923, "0_pnb_master": 536018926, "0_pnb_rupay": 536018919, "0_pnb_visa": 536018929, "0_pnb_dummynetwork": 555008559, "0_pnb_viabbps": 536018951, "0_ratn_amex": 564281461, "0_ratn_master": 564281456, "0_ratn_rupay": 564281452, "0_ratn_visa": 564281454, "0_ratn_dummynetwork": 564281455, "0_ratn_viabbps": 564281453, "0_rbs_dummynetwork": 341208170, "0_sbi_amex": 480500715, "0_sbi_master": 480500713, "0_sbi_rupay": 480500716, "0_sbi_visa": 480500714, "0_sbi_dummynetwork": 554584192, "0_sbi_viabbps": 480500722, "0_sbmi_rupay": 465104234, "0_stb_amex": 536018928, "0_stb_master": 536018920, "0_stb_rupay": 536018924, "0_stb_visa": 536018927, "0_stb_dummynetwork": 555008556, "0_stb_viabbps": 536018925, "0_sib_visa": 178513902, "0_sib_dummynetwork": 341208162, "0_scb_master": 465104237, "0_scb_visa": 465104235, "0_scb_dummynetwork": 341208150, "0_sbmb_visa": 458934493, "0_synbk_dummynetwork": 341208167, "0_tnmb_rupay": 355870910, "0_tnmb_visa": 355305884, "0_uco_rupay": 465104240, "0_uco_dummynetwork": 341208160, "0_uni_amex": 536018937, "0_uni_master": 536018943, "0_uni_rupay": 536018941, "0_uni_visa": 536018939, "0_uni_dummynetwork": 555008545, "0_uni_viabbps": 536018940, "0_ubi_dummynetwork": 341208158, "0_vjya_dummynetwork": 341208149, "0_yes_amex": 547893974, "0_yes_master": 547892733, "0_yes_rupay": 547892735, "0_yes_visa": 547892736, "0_yes_dummynetwork": 555008557, "0_yes_viabbps": 547892734 };

            return {
                status: "SUCCESS",
                type: 'MATCHING_PID_FOUND',
                data: dcatCategoryCacheData
            };

            for(let itrOperatorLabel = 0; itrOperatorLabel <  categoryData.groupings.aggs.length ; itrOperatorLabel++) {
                let operatorLabelDetails = categoryData.groupings.aggs[itrOperatorLabel];
    
                let isPaytmFirstCard = operatorLabelDetails.is_paytm_first_card ? "1" : "0";
                let displayValue = operatorLabelDetails.displayValue;
    
                for (let itrCardNetwork = 0; itrCardNetwork < operatorLabelDetails.aggs.length; itrCardNetwork++) {
                    let cardNewtworkDetails = operatorLabelDetails.aggs[itrCardNetwork];
    
                    for (let itrProductList = 0; itrProductList < cardNewtworkDetails.productList.length; itrProductList++) {
                        let productDetails  = cardNewtworkDetails.productList[itrProductList];
    
                        let bankCode = productDetails.bank_code;
                        let cardNetwork = productDetails.card_network;
                        let service = productDetails.service;
                        let productId = productDetails.productId;
                        let operator = productDetails.operator;
    
                        /**
                         * Validations
                         */
                        if(operatorLabelDetails.bank_code != bankCode) {
                            return {
                                status : "ERROR",
                                type : 'DIFFERENT_BANK_CODE', 
                                data : `OPERATOR_LABEL_DETAIL_BANKCODE_${operatorLabelDetails.bank_code}_bankCode_${bankCode}`
                            };
                        }
                        if(serviceCategory != service) {
                            return {
                                status : "ERROR",
                                type : 'DIFFERENT_SERVICE_CATEGORY',
                                data : service
                            };
                        }
                        if(paytype != productDetails.paytype) {
                            return {
                                status : "ERROR",
                                type : 'DIFFERENT_PAYTYPE',
                                data : productDetails.paytype
                            };
                        }

                        /** uniqueKey = (isPaytmFirstCard + '_' + bankCode + '_' + cardNetwork) */

                        let uniqueKey = self.getUniqueKeyForSavedCardsData({
                            isPaytmFirstCard : isPaytmFirstCard,
                            bankName : bankCode,
                            cardScheme : cardNetwork
                        });
    
                        if(! _.has(dcatCategoryCacheData , uniqueKey) ) {
                            _.set(dcatCategoryCacheData , [ uniqueKey ] ,  productId);
                        } else {
                            return {
                                status : "ERROR",
                                type : 'DUPLICATE_UNIQUE_KEY',
                                data : uniqueKey
                            };
                        }
                    }
                }
            }       
            return {
                status : "SUCCESS",
                type : 'MATCHING_PID_FOUND',
                data : dcatCategoryCacheData
            };
        } catch (error) {
            return {
                status : "ERROR",
                type : 'ERROR_WHILE_CHACHING_DCAT_CATEGORY_DATA',
                data: error
            };
        }
    }

    checkEncryptedCards(cb,cards){
        let self=this;
        let encryptedCards = [];
        let encryptedCardCount = 0;
        for(var index = 0; index < cards.length; index++){
            let encryptedRecord = _.get(cards[index], 'is_encrypted', null);
            if(encryptedRecord){
                encryptedCards.push(cards[index]);
                encryptedCardCount++;
            }
        }
        if(encryptedCardCount==1) return cb(null,encryptedCards[0]);
        else return cb('Multiple encrypted cards exist');
    }

    formatDataForCustomLogger(dataToBeInsertedInDB){
        let loggerData = {
            customerId : _.get(dataToBeInsertedInDB, 'customerId', null),
            rechargeNumber : _.get(dataToBeInsertedInDB, 'rechargeNumber', null),
            amount : this.encryptionDecryptionHelper.encryptData(_.get(dataToBeInsertedInDB, 'amount', null)),
            dueDate : this.encryptionDecryptionHelper.encryptData(_.get(dataToBeInsertedInDB, 'dueDate', null)),
            billDate : _.get(dataToBeInsertedInDB, 'billDate', null),
            bankName : _.get(dataToBeInsertedInDB, 'bankName', null),
            cardNetwork : _.get(dataToBeInsertedInDB, 'cardNetwork', null),
            dbEvent : _.get(dataToBeInsertedInDB, 'dbEvent', null),
            dataSource : _.get(dataToBeInsertedInDB, 'dataSource', null),
            source : _.get(dataToBeInsertedInDB, 'source', null),
        }
        return loggerData;
    }

}

export default SmsParsingSyncCCBillLibrary;