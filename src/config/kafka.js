"use strict";

module.exports = {
    common: {
        SERVICES: {
            RECENTBILL: {
                REC<PERSON><PERSON><PERSON>_CONSUMER_TOPICS: ['RECHAR<PERSON>_ORDER', 'EDUCATION_ORDER',
                    '<PERSON><PERSON><PERSON>_INSURANCE_ORDER', '<PERSON><PERSON><PERSON>_METRO_ORDER',
                    'UTILITY_ELECTRICITY_ORDER', 'B<PERSON>I_GOLD_ORDER',
                    'B<PERSON>I_TOLL_ORDER', 'B<PERSON>I_GOOGLEPLAY_ORDER',
                    'UTILITY_CHALLAN_ORDER', 'DONATION_ORDER',
                    'TAPTOPAY_ORDER', 'DEFAULT_ORDER']
            },
            NOTIFICATION: {
                CONSUMER_TOPICS: ['EDUCATION_ORDER']
            },
            RECHARGE_NUDGE_CONSUMERS: {
                VALIDATION_TOPICS: ['UTILITY_ELECTRICITY_VALIDATION'],
                REC<PERSON><PERSON><PERSON>_TOPICS: ['UTILITY_ELECTRICITY_ORDER', 'DEFAULT_ORDER'],
                R<PERSON><PERSON><PERSON><PERSON>_CC_TOPICS: ['BFSI_INSURANCE_ORDER'],
            },
            REMINDER_SYNC: {
                REMINDER_SYNC_TOPICS: ['REMINDER_SYNC']
            },
            AUTOMATIC_SYNC: {
                AUTOMATIC_SYNC_TOPIC: 'AUTOMATIC_SYNC'
            },
            AUTOMATIC_SYNC_RECENT: {
                AUTOMATIC_SYNC_RECENT_TOPIC: 'AUTOMATIC_SUBS_DATA'
            },
            CUSTOMER_UPDATES: {
                TOPIC: 'customer-updates'
            },
            REMINDER_BILLFETCH_PIPELINE: {
                REMINDER_BILL_FETCH: "REMINDER_BILL_FETCH",
            },
            REMINDER_BILLFETCH_PIPELINE_REALTIME: {
                REMINDER_BILL_FETCH_REALTIME: "REMINDER_BILL_FETCH_REALTIME",
            },
            NONRU_NOTIFICATION_PIPELINE_REALTIME: {
                NONRU_NOTIFICATION_REALTIME: "NONRU_REMINDER_BILL_FETCH_REALTIME",
            },
            NONRU_NOTIFICATION_PIPELINE: {
                NONRU_NOTIFICATION: "NONRU_REMINDER_BILL_FETCH",
            },
            VALIDATION_SYNC: {
                VALIDATION_TOPICS:['UTILITY_ELECTRICITY_VALIDATION','DEFAULT_VALIDATION','BFSI_INSURANCE_VALIDATION','RECHARGE_VALIDATION','MNP_VALIDATION']
            },
            SMS_PARSING_CC: {
                SMS_PARSING_CC_TOPIC: "dwh-ingest-SMS_PARSING_CC_BILLS",
            },
            SMS_PARSING_CC_RU_REALTIME: {
                SMS_PARSING_CC_RU_REALTIME_TOPIC: "ru_sms_reminder",
            },
            SMS_PARSING_CC_DWH_REALTIME: {
                SMS_PARSING_CC_DWH_REALTIME_TOPIC: "REALTIME_SMS_PARSER_CC_BILLS",
            },
            SMS_PARSING_FASTAG : {
                SMS_PARSING_FASTAG_TOPIC : "dwh-ingest-SMS_PARSING_FASTAG",
            },
            EMI_DUE_DETAILS: {
                EMI_DUE_DETAILS_TOPIC : 'EMI_DUE_DETAILS'
            },
            EMI_DUE_COMMON_DETAILS: {
                EMI_DUE_COMMON_DETAILS_TOPIC : 'EMI_DUE_COMMON_DETAILS'
            },
            PLAN_VALIDITY_SYNC_DB: {
                TOPIC: "SYNC_DB_ORDER"
            },
            REMINDER_MAXWELL: {
                TOPIC: "REMINDER_MAXWELL",
            },
            PLAN_VALIDITY_MAXWELL: {
                TOPIC: "PLAN_VALIDITY_MAXWELL",
            },
            REMINDER_CYLINDER: {
                TOPIC: "ivrs",
            },
            CT_EVENTS_PUBLISHER: {
                TOPIC: "cdo-ru-reminders-reminderEvents"
            },
            NON_PAYTM_RECORDS_CONSUMER: {
                 TOPIC: ["NON_PAYTM_RECORDS", "NON_PAYTM_RECORDS_DWH"]
            },
            UPDATE_RECENTS: {
                TOPIC: "mongo.inUsers.users"
            },
            NON_PAYTM_RECORDS: {
                TOPIC: "NON_PAYTM_RECORDS"
            },
            NON_PAYTM_RECORDS_DWH:{
                TOPIC: "NON_PAYTM_RECORDS_DWH"
            },
            SMS_PARSING_BILL_PAYMENT: {
              TOPIC: "SMS_PARSING_TELECOM"
            },
            SMS_PARSING_BILL_PAYMENT_DWH_REALTIME: {
                TOPIC: "REALTIME_SMS_PARSER_TELECOM"
            },
            CC_SMS_PARSING_BILL_PAYMENT: {
                TOPIC: "dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
                TOPIC : "SMS_PARSER_ELECTRICITY"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME:{
                TOPIC : "REALTIME_SMS_PARSER_ELECTRICITY"
            },
            RENT_SMS_PARSING_BILL_PAYMENT:{
                TOPIC : "SMS_PARSING_RENT"
            },
            PAYTM_FIRST_CC_EVENTS_PUBLISHER : {
                TOPIC : "lending-cc-ccbp-event"
            },
            UPPCL_DEMERGER_SYNC:{
                TOPIC: "MIGRATION_RECORDS"
            },
            FAILED_SMS_PARSING_PUBLISHER : {
                TOPIC : "FAILED_SMS_PARSING_REGEX_BACKUP"
            },
            UPMS_PUBLISHER: {
                TOPIC: "UPMS_SUBSCRIPTIONS"
            },
            UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER: {
                TOPIC: "UPMS_SUBSCRIPTION_FAILURE_DWH"
            },
            SMS_PARSING_LOAN_EMI: {
                TOPIC : "SMS_PARSING_LOANEMI"
            },
            OPERATOR_HEALTH: {
                TOPIC : "OPERATOR_HEALTH"
            },
            PAYTM_POSTPAID:{
                TOPIC : "PAYTM_POSTPAID_BILLS"
            },
            PERSONAL_LOAN : {
                TOPIC : "PERSONAL_LOAN_BILLS"
            },
            CASSANDRA_CDC: {
                TOPIC : "CDC_RECOVERY"
            },
            REALTIME_SMS_PARSING_PREPAID: {
                TOPIC : "ru_sms_parsing_prepaid"
            },
            REALTIME_SMS_PARSING_POSTPAID: {
                TOPIC : "ru_sms_parsing_postpaid"
            },
            NONRU_BILL_FETCH:{
                TOPIC : "NONRU_REMINDER_BILL_FETCH"
            },
            NOTIFICATION_FALLBACK: {
                TOPIC : ["push_delivery_topic", "delivery_status_topic"],
                WHATSAPP_TOPIC : "whatsapp_callback_reporting_data_rent_pay"
            },
            NONRU_BILL_FETCH:{
                TOPIC : "NONRU_REMINDER_BILL_FETCH"
            },
            AIRTEL_BILL_FETCH:{
                TOPIC : "AIRTEL_PREPAID_RECORDS"
            },
            PUBLISHER_NON_RU:{
                CANARA_TOPIC:"PUBLISHER_BILL_FETCH_CANARA",
                BOB_TOPIC:"PUBLISHER_BILL_FETCH_BOB",
                NKMB_TOPIC:"PUBLISHER_BILL_FETCH_NKMB",
                AUBL_TOPIC:"PUBLISHER_BILL_FETCH_AUBL",
                INDS_TOPIC:"PUBLISHER_BILL_FETCH_INDS",
                SBI_TOPIC:"PUBLISHER_BILL_FETCH_SBI",
                FDEB_TOPIC:"PUBLISHER_BILL_FETCH_FDEB",
                TOPIC:"PUBLISHER_BILL_FETCH"
            },
            PUBLISHER_NON_RU_MULTIPLE_PID:{
                TOPIC:"MULTIPLE_PID_TOPIC"
            },
            NOTIFICATION_REJECTS: {
                TOPIC : "REMINDER_NOTIFICATION_REJECT_DATA"
            },
            BILL_FETCH_ANALYTICS:{
                TOPIC: "bills_analytics_data"
            },
            CUSTOM_NOTIFICATIONS_PIPELINE:{
                CUSTOM_NOTIFICATIONS:"CUSTOM_NOTIFICATIONS"

            },
            HEURISTIC_CUSTOM_NOTIFICATIONS_PIPELINE:{
                HEURISTIC_CUSTOM_NOTIFICATIONS:"HEURISTIC_CUSTOM_NOTIFICATIONS"

            },
            CASSANDRA_NOTIFICATION_DWH: {
                TOPIC : "REMINDER_NOTIFICATION_DATA_NONRU"
            },
            PUBLISHER_NON_RU_MULTIPLE_PID: {
                TOPIC: "MULTIPLE_PID_TOPIC"
            },
            NOTIFICATIONS_DLQ: {
                TOPIC: "NOTIFICATIONS_DLQ"
            },
            CUSTOM_NOTIFICATIONS_PIPELINE:{
                CUSTOM_NOTIFICATIONS:"CUSTOM_NOTIFICATIONS"

            },
            CC_INGESTION_NONRU: {
                TOPIC: "CC_INGESTION_NONRU"
            },
            BILL_FETCH_ANALYTICS:{
                TOPIC: "bills_analytics_data"
            },
            PUBLISHER_NON_RU_MULTIPLE_PID: {
                TOPIC: "MULTIPLE_PID_TOPIC"
            },
            NOTIFICATIONS_DLQ: {
                TOPIC: "NOTIFICATIONS_DLQ"
            },
            ACTIVE_PAYTM_USERS: {
                TOPIC: "ACTIVE_PAYTM_USERS"
            },
            CHECK_ACTIVE_USERS: {
                TOPIC: "CHECK_ACTIVE_USERS_TOPIC"
            },
            SMS_PARSING_DTH_DWH: {
                TOPIC: "SMS_PARSER_DTH_CABLE_CYLINDER"
            },
            REMINDER_CONSENT_CONSUMER: {
                CONSENT_TOPIC: "REMINDER_BILL_CONSENT"
            },
            USER_SCORE_INGESTOR: {
                TOPIC: "USER_SCORE_INGESTOR"
            },
            USER_SCORE_INGESTOR_FAILED_RECORDS: {
                TOPIC: "USER_SCORE_INGESTOR_FAILED"
            },
            CRON_USER_SCORE_INGESTOR: {
                TOPIC: "CRON_USER_SCORE_INGESTOR"
            },
            CRON_USER_SCORE_INGESTION_CONSUMER: {
                TOPIC: "CRON_USER_SCORE_INGESTOR"
            },
            BILLS_MIGRATION: {
                TOPIC: "USER_SCORE_INGESTOR"
            },
            REMINDER_CONSENT_CONSUMER: {
                CONSENT_TOPIC: "REMINDER_BILL_CONSENT"
            },
            WHATSAPP_CUSTOMER_WHITELIST_INGESTOR: {
                TOPIC: "WHATSAPP_WHITELIST_CSV_INGESTOR"
            },
            ACTIVE_PAYTM_USERS: {
                TOPIC: "ACTIVE_PAYTM_USERS"
            }
        }
    },
    production: {
        TOPICS: {
            RECHARGE: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            REMINDER_SYNC: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            AUTOMATIC_SYNC: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            AUTOMATIC_SYNC_RECENT: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            REMINDER_BILLFETCH_PIPELINE: {
                HOSTS: "**********:9092,***********:9092,***********:9092", //  recharges kafka
            },
            REMINDER_BILLFETCH_PIPELINE_REALTIME: {
                HOSTS: "**********:9092,***********:9092,***********:9092", // reminder kafka
            },
            REMINDER_KAFKA_CLUSTER: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            SMS_PARSING_CC: {
                HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
               // HOSTS: "************:9092,************:9092,************:9092",
            },
            NONRU_NOTIFICATION_REALTIME:{
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            SMS_PARSING_CC_RU_REALTIME: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            SMS_PARSING_FASTAG : {
                HOSTS : "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092",
            },
            EMI_DUE_DETAILS: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            EMI_DUE_COMMON_DETAILS : {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            PLAN_VALIDITY_NOTIFICATION: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            PLAN_VALIDITY_NOTIFICATION_REALTIME: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            PLAN_VALIDITY_SYNC_DB: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            REMINDER_MAXWELL: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            PLAN_VALIDITY_MAXWELL: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            REMINDER_CYLINDER: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            CT_EVENTS_PUBLISHER: {
                HOSTS: "events-kafka.production.midgar:9092",
            },
            NON_PAYTM_RECORDS: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            CHECK_ACTIVE_USERS: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
                // HOSTS: "************:9092,************:9092,************:9092"
            },
            CC_SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
                // HOSTS: "************:9092,************:9092,************:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
                HOSTS:"internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
            },
            SMS_PARSING_CC_DWH_REALTIME: {
                HOSTS: "internal-sms-parser-kafka-lb-1204640012.ap-south-1.elb.amazonaws.com:9092"
            },
            SMS_PARSING_BILL_PAYMENT_DWH_REALTIME: {
                HOSTS: "internal-sms-parser-kafka-lb-1204640012.ap-south-1.elb.amazonaws.com:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME:{
                HOSTS: "internal-sms-parser-kafka-lb-1204640012.ap-south-1.elb.amazonaws.com:9092"
            },
            RENT_SMS_PARSING_BILL_PAYMENT: {
                HOSTS:"internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
            },
            PAYTM_FIRST_CC_EVENTS_PUBLISHER: {
                HOSTS: "creditcard-prod-kafka-broker-1.lending.paytm.com:9092,creditcard-prod-kafka-broker-2.lending.paytm.com:9092,creditcard-prod-kafka-broker-3.lending.paytm.com:9092"
                //HOSTS: "***********:9092,***********:9092,***********:9092"
                //This ips are of paytm-first team
            },
            UPPCL_DEMERGER_SYNC:{
                HOSTS:"**********:9092,***********:9092,***********:9092"
            },
            FAILED_SMS_PARSING_PUBLISHER: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            UPMS_PUBLISHER: {
                HOSTS: "**********:9092,***********:9092,**********:9092"
            },
            UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER: {
                HOSTS: "to be added"
            },
            SMS_PARSING_LOAN_EMI: {
                HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
            },
            REALTIME_SMS_PARSING: {
                HOSTS: "internal-datalake-kafka-prod-clb-internal-502257930.ap-south-1.elb.amazonaws.com:9092"
            },
            CASSANDRA_CDC: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            REALTIME_SMS_PARSING_POSTPAID: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            REALTIME_SMS_PARSING_PREPAID: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            PAYTM_POSTPAID : {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            PERSONAL_LOAN : {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "**********:9092,***********:9092,***********:9092"
            },
            NOTIFICATION_FALLBACK: {
                HOSTS: "************:9092,***********:9092,**********:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "**********:9092,***********:9092,***********:9092"
            },
            PUBLISHER_NON_RU:{
                HOSTS:"**********:9092,***********:9092,***********:9092"
            },
            AIRTEL_BILL_FETCH: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            PUBLISHER_NON_RU:{
                HOSTS:"**********:9092,***********:9092,***********:9092"
            },
            BILL_FETCH_ANALYTICS: {
                HOSTS: "**********:9092,**********:9092,***********:9092"
            },
            NOTIFICATION_REJECTS: {
                HOSTS:"**********:9092,**********:9092,***********:9092"
            },
            CUSTOM_NOTIFICATIONS_PIPELINE:{
                HOSTS :"**********:9092,***********:9092,***********:9092"
            },
            HEURISTIC_CUSTOM_NOTIFICATIONS_PIPELINE:{
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            NOTIFICATIONS_DROPPED_CASSANDRA_DOWN: {
                HOSTS: "**********:9092,**********:9092,***********:9092"
            },
            CUSTOM_NOTIFICATIONS_PIPELINE:{
                HOSTS :"**********:9092,***********:9092,***********:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            SMS_PARSING_DTH_DWH: {
                HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"
            },
            BILLS_MIGRATION: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            NOTIFICATIONS_DROPPED_CASSANDRA_DOWN: {
                HOSTS: "**********:9092,**********:9092,***********:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "**********:9092,***********:9092,***********:9092",
            },
            AUXILIARY_KAFKA: {
                HOSTS: "**********:9092,***********:9092,***********:9092"
            },
            SMS_PARSING_VALIDATOR:{
                 HOSTS: "internal-datalake-kafka-prod-graviton-557053207.ap-south-1.elb.amazonaws.com:9092"

            },
            SMS_PARSING_VALIDATOR_DWH:{
                HOSTS: "**********:9092,***********:9092,***********:9092"

            },
            FFR_HIT_TOPIC:{
                HOSTS: "**********:9092,***********:9092,***********:9092"

            }
        }
    },
    staging: {
        TOPICS: {
            RECHARGE: {
                HOSTS: "***********:9092"
            },
            REMINDER_SYNC: {
                HOSTS: "***********:9092",
            },
            AUTOMATIC_SYNC: {
                HOSTS: "***********:9092",
            },
            AUTOMATIC_SYNC_RECENT: {
                HOSTS: "***********:9092",
            },
            REMINDER_BILLFETCH_PIPELINE: {
                HOSTS: "***********:9092",
            },
            NONRU_NOTIFICATION_REALTIME:{
                HOSTS: "***********:9092"
            },
            REMINDER_BILLFETCH_PIPELINE_REALTIME: {
                HOSTS: "***********:9092",
            },
            REMINDER_KAFKA_CLUSTER: {
                HOSTS: "***********:9092",
            },
            SMS_PARSING_CC: {
                HOSTS: "***********:9092,*************:9092,************:9092,**************:9092",
                //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            // SMS_PARSING_CC: {
            //     HOSTS: "internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092",
            // },
            SMS_PARSING_CC_RU_REALTIME: {
                HOSTS: "***********:9092",
            },
            SMS_PARSING_FASTAG : {
                //HOSTS : "*************:9092,************:9092,**************:9092",
                 HOSTS: "***********:9092",
              //  HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
                // HOSTS : "internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            EMI_DUE_DETAILS: {
                HOSTS: "***********:9092",
            },
            EMI_DUE_COMMON_DETAILS : {
                HOSTS: "***********:9092",
            },
            PLAN_VALIDITY_NOTIFICATION: {
                HOSTS: "***********:9092",
            },
            PLAN_VALIDITY_NOTIFICATION_REALTIME: {
                HOSTS: "***********:9092",
            },
            PLAN_VALIDITY_SYNC_DB: {
                HOSTS: "***********:9092",
            },
            REMINDER_MAXWELL: {
                HOSTS: "***********:9092",
            },
            PLAN_VALIDITY_MAXWELL: {
                HOSTS: "***********:9092",
            },
            REMINDER_CYLINDER: {
                HOSTS: "***********:9092",
            },
            CT_EVENTS_PUBLISHER: {
                HOSTS: "***********:9092"
            },
            NON_PAYTM_RECORDS: {
                HOSTS: "***********:9092"
            },
            CHECK_ACTIVE_USERS: {
                HOSTS: "***********:9092"
            },
            SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "***********:9092"//"*************:9092,************:9092,**************:9092"
                //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            // SMS_PARSING_BILL_PAYMENT: {
            //     HOSTS: "internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"//"*************:9092,************:9092,**************:9092"
            // },
            CC_SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "***********:9092"//"*************:9092,************:9092,**************:9092"
                //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
                HOSTS : "***********:9092"
               //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            SMS_PARSING_CC_DWH_REALTIME: {
                HOSTS: "***********:9092"
            },
            SMS_PARSING_BILL_PAYMENT_DWH_REALTIME: {
                HOSTS: "***********:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME:{
                HOSTS: "***********:9092"
            },
            // ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
            //     HOSTS : "internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            // },
            RENT_SMS_PARSING_BILL_PAYMENT:{
                HOSTS : "***********:9092"
                //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            PAYTM_FIRST_CC_EVENTS_PUBLISHER: {
                HOSTS: "***********:9092"
            },
            UPPCL_DEMERGER_SYNC:{
                HOSTS:"***********:9092"
            },
            FAILED_SMS_PARSING_PUBLISHER: {
                HOSTS: "***********:9092"
            },
            UPMS_PUBLISHER: {
                HOSTS: "***********:9092"
            },
            UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER: {
                HOSTS: "***********:9092"
            },
            SMS_PARSING_LOAN_EMI: {
               HOSTS: "***********:9092"
               //HOSTS:"internal-datalake-kafka-staging-26372960.ap-south-1.elb.amazonaws.com:9092"
            },
            SMS_PARSING_LOAN_EMI: {
                HOSTS: "***********:9092"
            },
            PERSONAL_LOAN : {
                HOSTS: "***********:9092"
            },
            CASSANDRA_CDC: {
                HOSTS: "***********:9092"
            },
            REALTIME_SMS_PARSING_POSTPAID: {
                HOSTS: "***********:9092"
            },
            REALTIME_SMS_PARSING_PREPAID: {
                HOSTS: "***********:9092"
            },
            PAYTM_POSTPAID : {
                HOSTS: "***********:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "***********:9092"
            },
            NOTIFICATION_FALLBACK: {
                HOSTS: "***********:9092"
                // HOSTS: "************:9092,************:9092,***********:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "***********:9092"
            },
            AIRTEL_BILL_FETCH: {
                HOSTS: "***********:9092"
            },
            PUBLISHER_NON_RU:{
                HOSTS:"***********:9092"
            },
            PG_TOKEN_DELETION:{
                HOSTS:"***********:9092"
            },
            NOTIFICATION_REJECTS : {
                HOSTS : "***********:9092"},
            BILL_FETCH_ANALYTICS: {
                HOSTS: "***********:9092",
            },
            NOTIFICATIONS_DROPPED_CASSANDRA_DOWN: {
                HOSTS: "***********:9092"
            },
            BILL_FETCH_ANALYTICS: {
                HOSTS: "***********:9092",
            },
            CC_INGESTION_NONRU: {
                HOSTS: "***********:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "***********:9092"
            },
            CUSTOM_NOTIFICATIONS_PIPELINE:{
                HOSTS: "***********:9092"
            },
            CC_INGESTION_NONRU: {
                HOSTS: "***********:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "***********:9092"
            },
            SMS_PARSING_DTH_DWH: {
                HOSTS: "***********:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "***********:9092",
            },
            AUXILIARY_KAFKA: {
                HOSTS: "***********:9092"
            },
            SMS_PARSING_VALIDATOR:{
                HOSTS: "***********:9092",

            },
            SMS_PARSING_VALIDATOR_DWH:{
                HOSTS: "***********:9092"

            }
        }
    },
    development: {
        TOPICS: {
            RECHARGE: {
                HOSTS: "localhost:9092",
            },
            REMINDER_SYNC: {
                HOSTS: "localhost:9092",
            },
            AUTOMATIC_SYNC: {
                HOSTS: "localhost:9092",
            },
            AUTOMATIC_SYNC_RECENT: {
                HOSTS: "localhost:9092",
            },
            CUSTOM_NOTIFICATIONS_PIPELINE: {
                HOSTS: "localhost:9092"
            },
            REMINDER_BILLFETCH_PIPELINE: {
                HOSTS: "localhost:9092",
            },
            REMINDER_BILLFETCH_PIPELINE_REALTIME: {
                HOSTS: "localhost:9092",
            },
            REMINDER_KAFKA_CLUSTER: {
                HOSTS: "localhost:9092",
            },
            NONRU_NOTIFICATION_REALTIME:{
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_CC: {
                HOSTS: "localhost:9092",
            },
            SMS_PARSING_CC_RU_REALTIME: {
                HOSTS: "localhost:9092",
            },
            SMS_PARSING_FASTAG:{
                HOSTS: "localhost:9092",
            },
            EMI_DUE_DETAILS: {
                HOSTS: "localhost:9092",
            },
            EMI_DUE_COMMON_DETAILS : {
                HOSTS: "localhost:9092",
            },
            PLAN_VALIDITY_NOTIFICATION: {
                HOSTS: "localhost:9092",
            },
            PLAN_VALIDITY_NOTIFICATION_REALTIME: {
                HOSTS: "localhost:9092",
            },
            PLAN_VALIDITY_SYNC_DB: {
                HOSTS: "localhost:9092",
            },
            REMINDER_MAXWELL: {
                HOSTS: "localhost:9092",
            },
            PLAN_VALIDITY_MAXWELL: {
                HOSTS: "localhost:9092",
            },
            BILL_FETCH_ANALYTICS: {
                HOSTS: "localhost:9092"
            },
            REMINDER_CYLINDER: {
                HOSTS: "localhost:9092",
            },
            CT_EVENTS_PUBLISHER: {
                HOSTS: "localhost:9092",
            },
            NON_PAYTM_RECORDS: {
                HOSTS: "localhost:9092"
            },
            CHECK_ACTIVE_USERS: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "localhost:9092"
            },
            CC_SMS_PARSING_BILL_PAYMENT: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_CC_DWH_REALTIME: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_BILL_PAYMENT_DWH_REALTIME: {
                HOSTS: "localhost:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME:{
                HOSTS: "localhost:9092"
            },
            ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
                HOSTS : "localhost:9092"
            },
            RENT_SMS_PARSING_BILL_PAYMENT:{
                HOSTS : "localhost:9092"
            },
            PAYTM_FIRST_CC_EVENTS_PUBLISHER: {
                HOSTS: "localhost:9092"
            },
            UPPCL_DEMERGER_SYNC:{
                HOSTS:"localhost:9092"
            },
            FAILED_SMS_PARSING_PUBLISHER: {
                HOSTS: "localhost:9092"
            },
            UPMS_PUBLISHER: {
                HOSTS: "localhost:9092"
            },
            UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_LOAN_EMI: {
                HOSTS: "localhost:9092"
            },
            REALTIME_SMS_PARSING: {
                HOSTS: "localhost:9092"
            },
            PERSONAL_LOAN : {
                HOSTS: "localhost:9092"
            },
            CASSANDRA_CDC: {
                HOSTS: "localhost:9092"
            },
            REALTIME_SMS_PARSING_PREPAID: {
                HOSTS: "localhost:9092"
            },
            REALTIME_SMS_PARSING_POSTPAID: {
                HOSTS: "localhost:9092"
            },
            PAYTM_POSTPAID : {
                HOSTS: "localhost:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "localhost:9092"
            },
            NOTIFICATION_FALLBACK: {
                HOSTS: "localhost:9092"
            },
            NONRU_BILL_FETCH:{
                HOSTS : "localhost:9092"
            },
            NOTIFICATION_FALLBACK: {
                HOSTS: "localhost:9092"
            },
            PUBLISHER_NON_RU:{
                HOSTS:"localhost:9092"
            },
            AIRTEL_BILL_FETCH: {
                HOSTS: "localhost:9092"
            },
            PUBLISHER_NON_RU:{
                HOSTS:"localhost:9092"
            },
            PG_TOKEN_DELETION:{
                HOSTS:"localhost:9092"
            },
            NOTIFICATION_REJECTS:{
                HOSTS:"localhost:9092"
            },
            NOTIFICATIONS_DROPPED_CASSANDRA_DOWN: {
                HOSTS: "localhost:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_DTH_DWH: {
                HOSTS: "localhost:9092"
            },
            BILLS_MIGRATION: {
                HOSTS: "localhost:9092"
            },
            ACTIVE_PAYTM_USERS: {
                HOSTS: "localhost:9092"
            },
            AUXILIARY_KAFKA: {
                HOSTS: "localhost:9092"
            },
            SMS_PARSING_VALIDATOR_DWH: {
                HOSTS: "localhost:9092"
            }
        }
    }
};
