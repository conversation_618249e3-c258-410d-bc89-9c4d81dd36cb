/*jshint multistr: true ,node: true*/
'use strict';

module.exports = {
    common: {
       
    },
    production: {
        RECENT_UPDATE_URL: 'http://recents-crud.prod.paytmdgt.io/v1/recentupdate/admin',
        RECENT_FETCH_URL : 'http://recents-crud.prod.paytmdgt.io/v1/recentfetch/admin',
        REMOVE_RECENTS : 'http://favourites.prod.paytmdgt.io/user/favourite/v2/removerecent',
        FETCH_RECENT_URL : 'http://recents-crud.prod.paytmdgt.io/v1/fetchRecents',
        RECENT_CLEAR_CACHE: 'http://recents-crud.prod.paytmdgt.io/api/customer/clearCache',
        MASTER: {
            //username      : '',
            //password      : '',
            host          : 'favoritemongodb.prod.paytmdgt.io',
            replicaHostStr: 'favoritemongodb.prod.paytmdgt.io:27017',
            replicas      : ['favoritemongodb.prod.paytmdgt.io'],
            options       : 'replicaSet=rs0&w=0&readPreference=nearest',
            db            : 'inUsers',
            poolSize      : 5,
            port          : 27017
        },
        SLAVE: {
            //username      : '',
            //password      : '',
            host          : 'favoritemongodb.prod.paytmdgt.io',
            replicaHostStr: 'favoritemongodb.prod.paytmdgt.io:27017',
            replicas      : ['favoritemongodb.prod.paytmdgt.io'],
            options       : 'replicaSet=rs0&w=0&readPreference=nearest',
            db            : 'inUsers',
            poolSize      : 5,
            port          : 27017
        },
        HIDDEN_SLAVE: {
            // username      : '',
            // password      : '',
            host          : '**********',
            replicaHostStr: '**********:27017',
            replicas      : ['**********'],
            options       : 'replicaSet=rs0&w=0&readPreference=nearest',
            db            : 'inUsers',
            poolSize      : 5,
            port          : 27017
        }
        
    },
    development: {
        //RECENT_UPDATE_URL: 'http://127.0.0.1:4000/v1/recentUpdate/admin',
        //RECENT_FETCH_URL : 'http://127.0.0.1:4000/v1/recentfetch/admin',
        RECENT_FETCH_URL : 'https://run.mocky.io/v3/bd7ea8a1-c8ec-4f52-83ea-2b9dc95a6799',
        REMOVE_RECENTS : 'https://run.mocky.io/v3/6d46cb5e-ff57-420d-bef6-52cdaf6179de',
        RECENT_UPDATE_URL: 'https://run.mocky.io/v3/5e3d1d08-79cb-4c1b-b61b-0bf6ee0edbdb',
        //RECENT_FETCH_URL : 'https://run.mocky.io/v3/886d25e7-b8cf-4f14-a9c6-54ef8fa9bd83',
        //RECENT_FETCH_URL : 'https://run.mocky.io/v3/bcd70131-d791-43b3-941f-d2e5304b52d2',
        FETCH_RECENT_URL : 'http://run.mocky.io/v1/fetchRecents',
        RECENT_CLEAR_CACHE: 'https://run.mocky.io/api/customer/clearCache',
        MASTER: {
            host: '127.0.0.1',
            replicaHost: '127.0.0.1',
            replicaHostStr: '127.0.0.1:27017',
            replicas: ['127.0.0.1'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
            port: 27017,
            username: 'app',
            password: 'one97897abc123'
            },
            SLAVE: {
                host: '127.0.0.1',
                replicaHost: '127.0.0.1',
                replicaHostStr: '127.0.0.1:27017',
                replicas: ['127.0.0.1'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port: 27017,
                username: 'app',
                password: 'one97897abc123'
            },
            HIDDEN_SLAVE: {
                host: '127.0.0.1',
                replicaHost: '127.0.0.1',
                replicaHostStr: '127.0.0.1:27017',
                replicas: ['127.0.0.1'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port: 27017,
                username: 'app',
                password: 'one97897abc123'
            }
        
    },
    staging: {
            RECENT_UPDATE_URL:'http://favouritenode.nonprod.onus.paytmdgt.io/v1/recentupdate/admin',
            RECENT_FETCH_URL : 'http://favouritenode.nonprod.onus.paytmdgt.io/v1/recentfetch/admin',
            REMOVE_RECENTS : 'http://favouritenode.nonprod.onus.paytmdgt.io/user/favourite/v2/removerecent',
            FETCH_RECENT_URL: 'http://poccassandra.nonprod.onus.paytmdgt.io/fetchRecents',
            RECENT_CLEAR_CACHE: 'http://poccassandra.nonprod.onus.paytmdgt.io/api/customer/clearCache',
            MASTER: {
                //username      : '',
                //password      : '',
                host          : '***********',
                replicaHostStr: '**********:27017',
                replicas      : ['**********'],
                options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            },
            SLAVE: {
                //username      : '',
                //password      : '',
                host          : '***********',
                replicaHostStr: '**********:27017',
                replicas      : ['**********'],
                options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            },
            HIDDEN_SLAVE: {
                //username      : '',
                //password      : '',
                host          : '***********',
                replicaHostStr: '***********:27017',
                replicas      : ['***********'],
                options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            }
        }
    
};