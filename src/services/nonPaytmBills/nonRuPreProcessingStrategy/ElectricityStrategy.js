import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import _ from 'lodash';
import NonPaytmBillsModel from '../../../models/nonPaytmBills.js';
import utility from '../../../lib/index.js';

/**
 * Strategy for preprocessing electricity bills
 */
class ElectricityStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.config = options.config;
        this.L = options.L;
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
    }

    /**
     * Preprocess electricity bill data
     * @param {Object} billsKafkaRow - Electricity bill data
     * @returns {Promise<Object>} Preprocessed electricity bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            // Fetch data from pid mapping table and update product id and ambiguous status
            const result = await this.fetchAndUpdatePidMapping(billsKafkaRow);
            this.L.log('ElectricityStrategy', `Successfully preprocessed electricity bill for debugKey: ${billsKafkaRow.debugKey}`);
            return result;
        } catch (error) {
            this.L.error('ElectricityStrategy', `Failed to preprocess electricity bill: ${error}`);
            throw error;
        }
    }

    /**
     * Fetch and update PID mapping for bill data
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Promise<Object>} Updated bill data
     * @throws {Error} If PID mapping update fails
     */
    async fetchAndUpdatePidMapping(billsKafkaRow) {
        const self = this;
        try {
            const pidMappingTableData = await self.nonPaytmBillsModel.readPidMappingData(billsKafkaRow);
            return await self.updateProductIdAndAmbiguous(billsKafkaRow, pidMappingTableData);
        } catch (error) {
            self.L.error('fetchAndUpdatePidMapping', `Error while fetching pid mapping data for debugKey: ${billsKafkaRow.debugKey} with error: ${error}`);
            throw new Error(`Failed to update PID mapping: ${error.message}`);
        }
    }

    /**
         * Update product ID and ambiguous status based on PID mapping data
         * @param {Object} billsKafkaRow - Bill data
         * @param {Array|Object} pidMappingData - PID mapping data
         * @returns {Promise<Object>} Updated bill data
         * @throws {Error} If update fails
    */
    async updateProductIdAndAmbiguous(billsKafkaRow, pidMappingData) {
        try {
            const extra = utility.parseExtra(billsKafkaRow.extra);
            if (pidMappingData.length === 1) {
                const singleMapping = pidMappingData[0];
                const oldProductId = _.get(billsKafkaRow, 'productId', null);
                const newProductId = _.get(singleMapping, 'product_id', oldProductId);
                if (newProductId === null) {
                    return billsKafkaRow;
                }
                extra.ambiguous = null;
                _.set(billsKafkaRow, 'productId', Number(newProductId));
                if (_.get(billsKafkaRow, 'demergerOperatorsList', null) !== null) {
                    const oldOperator = _.get(billsKafkaRow, 'operator', null);
                    const newOperatorName = _.get(singleMapping, 'newoperator', oldOperator);
                    if (newOperatorName === null) {
                        return billsKafkaRow;
                    }
                    _.set(billsKafkaRow, 'oldOperatorName', oldOperator);
                    _.set(billsKafkaRow, 'operator', newOperatorName);
                }
            } else {
                return billsKafkaRow;
            }
            billsKafkaRow.extra = utility.stringifyExtra(extra);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('updateProductIdAndAmbiguous', `Failed to update product ID and ambiguous status: ${error}`);
            throw error;
        }
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'electricity';
    }
}

export default ElectricityStrategy;
