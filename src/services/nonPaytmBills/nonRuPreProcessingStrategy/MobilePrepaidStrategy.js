import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import utility from '../../../lib/index.js';
import MOMENT from 'moment';
import EncryptorDecryptor from 'encrypt_decrypt';
import _ from 'lodash';

/**
 * Strategy for preprocessing mobile prepaid bills
 */
class MobilePrepaidStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.cryptr = new EncryptorDecryptor();
        this.L = options.L;
        this.notificationConfig = _.get(options.config, 'NOTIFICATION');
    }

    /**
     * Preprocess mobile prepaid bill data
     * @param {Object} billsKafkaRow - Mobile prepaid bill data
     * @returns {Promise<Object>} Preprocessed mobile prepaid bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            this.processRechargeNumberAndSource(billsKafkaRow);
            this.validateDueDate(billsKafkaRow);
            this.updateExtra(billsKafkaRow);
            this.handlePartialBillState(billsKafkaRow);
            // Fetch and set templates
            const templates = await this.fetchAndSetDataTemplates(billsKafkaRow);
            billsKafkaRow.templates = templates;
            this.L.log('MobilePrepaidStrategy', `Successfully preprocessed mobile prepaid bill for debugKey: ${billsKafkaRow.debugKey}`);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('MobilePrepaidStrategy', `Failed to preprocess mobile prepaid bill for debugKey: ${billsKafkaRow.debugKey} with error: ${error}`);
            throw error;
        }
    }

    /**
     * Process recharge number encryption and source tracking
     * @param {Object} billsKafkaRow - Mobile prepaid bill data
     */
    async processRechargeNumberAndSource(billsKafkaRow) {
        // Encrypt recharge number
        if (billsKafkaRow.rechargeNumber) {
            billsKafkaRow.rechargeNumber = this.cryptr.encrypt(billsKafkaRow.rechargeNumber);
            billsKafkaRow.is_encrypted_done = true;
        }

        // If real time data is exhausted, due date should be null
        if (billsKafkaRow.isRealTimeDataExhausted) {
            billsKafkaRow.dueDate = null;
        }
    }

    handlePartialBillState(billsKafkaRow) {
        if (_.get(billsKafkaRow, 'partialBillState', null)) {
            _.set(billsKafkaRow, 'dueDate', null);
        }
    }

    updateExtra(billsKafkaRow) {
        let extra = utility.parseExtra(_.get(billsKafkaRow, 'extra', null));
        if (_.get(billsKafkaRow, 'partialBillState', null)) {
            _.set(billsKafkaRow, ['extra', 'partialBillState'], _.get(billsKafkaRow, 'partialBillState'));
        } else {
            // cases with full bill only
            if ((_.get(billsKafkaRow, 'dueDate', null) != null && _.get(billsKafkaRow, 'amount', null) != null)) {
                if (_.get(billsKafkaRow, ['extra', 'partialBillState'], null)) {
                    delete billsKafkaRow.extra.partialBillState;
                }
            }
        }
        billsKafkaRow.extra = utility.stringifyExtra(extra);
    }
    /**
     * Validate the due date of the bill
     * @param {Object} billsKafkaRow - Mobile prepaid bill data
     * @throws {Error} If the bill has an old due date
     */
    async validateDueDate(billsKafkaRow) {
        if (billsKafkaRow.dueDate && MOMENT(billsKafkaRow.dueDate).isValid() && MOMENT(billsKafkaRow.dueDate).diff(MOMENT().endOf('day'), 'days') < 0) {
            this.L.log("upsertData::  skipping inserting new record in DB as Old Due Date Bill Found", billsKafkaRow);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_OLD_BILL', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:OLD_DUE_DATE_IN_PAYLOAD']);
            throw new Error("skipping inserting new record in DB as Old Due Date Bill Found");
        }
    }

    /**
     * Fetch and set data templates for the bill record
     * @param {Object} record - Bill record
     * @returns {Promise<Object>} Templates object
     */
    async fetchAndSetDataTemplates(record) {
        const self = this;
        let partialBillState = _.get(record, 'partialBillState', null);
        if (partialBillState == null) {
            self.L.error("Missing partialBillState field for debugKey", record.debugKey);
        }
        try {
            const templates = await self.fetchDataTemplates(record);
            return templates;
        } catch (error) {
            self.L.error("fetchDataTemplates:: error in fetching templates for debugKey", record.debugKey);
            throw error;
        }
    }

    async fetchDataTemplates(record) {
        let self = this,
            notificationType = 'NODUEDATE',
            partialBillState = _.get(record, 'partialBillState', null)
        if (partialBillState == null) {
            self.L.log("fetchDataTemplates:: Missing partialBillState field for debugKey", record.debugKey);
        }
        let templates = {};
        for (let key in this.notificationConfig.type) {
            if (this.notificationConfig.type[key]) {
                templates[key] = this.getTemplateId(key, record, notificationType, _.get(record, 'dueDate', null), partialBillState);
            }
        }
        return templates;
    }

    getTemplateId(type, record, notificationType, dueDate, partialBillState) {
        let self = this;
        let serviceBasedKey = `PARTIALBILL_${_.toUpper(_.get(record, 'service'))}_${partialBillState}_${notificationType}_${type}`;
        self.L.log(`getTemplateId:: serviceBasedKey: ${serviceBasedKey} for debugKey: ${record.debugKey}`);
        let templateId = _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey], null);
        self.L.log(`getTemplateId:: operator:${_.get(record, 'operator', null)} _notificationType:${notificationType} _dueDate:${dueDate} _partialBillState:${partialBillState} _type:${type} _templateId:${templateId} for debugKey: ${record.debugKey}`);
        return templateId;
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        //if the bill is not encrypted, then it should be sent to publisher
        this.L.log("MobilePrepaidStrategy::_getToBeSentToPublisherBillPush", `Bill will not be sent to publisher for debugKey: ${billsKafkaRow.debugKey}`);
        return false;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'mobile';
    }

    /**
     * Get the paytype this strategy handles
     * @returns {string} Paytype
     */
    getPaytype() {
        return 'prepaid';
    }
}

export default MobilePrepaidStrategy;
