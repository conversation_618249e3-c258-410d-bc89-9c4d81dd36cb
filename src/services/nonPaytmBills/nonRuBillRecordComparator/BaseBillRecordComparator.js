import _ from 'lodash';
import MOMENT from 'moment';
import utility from '../../../lib';

class BaseBillRecordComparator {
    constructor(options) {
        if (this.constructor === BaseBillRecordComparator) {
            throw new Error("Abstract class cannot be instantiated directly");
        }
        this.nonPaytmBillsModel = options.nonPaytmBillsModel;
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
        this.L = options.L;
        this.config = options.config;
    }

    /**
         * Abstract method to process bill data
         * Must be implemented by concrete strategy classes
         * @param {Object} billsKafkaRow - Bill data to process
         * @returns {Promise<Object>} Processed bill data
         */
    async process(billsKafkaRow, existingRecord) {
        const self = this;
        self.L.log("BaseBillRecordComparator::process", `Processing bill with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        throw new Error("process method must be implemented by concrete strategy");
    }

    /**
     * Process and compare bill records based on service type
     * @param {string} serviceType - Type of service (credit_card, fastag, etc.)
     * @param {string} paytype - Payment type
     * @param {Object} billsKafkaRow - New bill record
     * @param {Object} existingRecord - Existing bill record
     * @returns {Promise<Object>} Processed bill record
     */
    async processAndCompare(billsKafkaRow, existingRecord) {
        try {
            //handle common processing for all services
            await this._processCommonFeatures(billsKafkaRow, existingRecord);
            // Get the appropriate processor
            await this.process(billsKafkaRow, existingRecord);
        } catch (error) {
            this.L.error('processAndCompare',
                `Failed to process and compare records for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}: ${error}`);
            throw error;
        }
    }

    async getTotalRecordsToProcess(billsKafkaRow, existingRecords) {
        const self = this;
        let smsParsedCustId = null,
            isSmsParsedCustIdPresentInSql = false,
            listOfExistingCustIds = [];

        isSmsParsedCustIdPresentInSql = _.get(billsKafkaRow, 'recordFoundOfSameCustId', false) || (billsKafkaRow.isDuplicateCANumberOperator && billsKafkaRow.alternateCAExistsInDB);
        smsParsedCustId = _.get(billsKafkaRow, 'customerId', null);
        self.L.log("getTotalRecordsToProcess", `Processing for smsParsedCustId: ${smsParsedCustId} for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);

        // Create a map of customer IDs to existing records
        const existingRecordsMap = new Map();
        existingRecords.forEach(record => {
            existingRecordsMap.set(record.customer_id, record);
        });

        listOfExistingCustIds = Array.from(existingRecordsMap.keys());
        self.L.log("getTotalRecordsToProcess", `Existing customer IDs: ${JSON.stringify(listOfExistingCustIds)} for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);

        if (listOfExistingCustIds.length == 0 && isSmsParsedCustIdPresentInSql) {
            self.L.log("getTotalRecordsToProcess", `No existing records found, returning empty array for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return [];
        }
        // Add new customer ID to map if not present
        if (!isSmsParsedCustIdPresentInSql && !existingRecordsMap.has(smsParsedCustId)) {
            existingRecordsMap.set(smsParsedCustId, []);
        }

        return existingRecordsMap;
    }


    makeExistingRecords(cust_id, billsKafkaRow, extra = null) {
        const self = this;
        return {
            customer_id: cust_id,
            recharge_number: billsKafkaRow.rechargeNumber,
            operator: billsKafkaRow.operator,
            service: billsKafkaRow.service,
            paytype: billsKafkaRow.paytype,
            product_id: billsKafkaRow.productId,
            notification_status: 1,
            extra: extra,
            dummy_record: true
        }
    }

    /**
  * Process common fields for all services
  * @param {Object} billsKafkaRow - New bill data
  * @param {Object} existingRecord - Existing database record
  * @returns {Promise<Object>} Updated record
  * @private
  */
    async _processCommonFeatures(billsKafkaRow, existingRecord) {
        // Update remind later date
        billsKafkaRow = await this._updateRemindLaterDate(billsKafkaRow, existingRecord);
        //update status and notification status and create_source and created_at and updated_at
        billsKafkaRow = await this._updateStatusAndNotificationStatusNextBillFetchDate(billsKafkaRow, existingRecord);
        //update the created_at
        billsKafkaRow = await this._updateCreatedAt(billsKafkaRow, existingRecord);
        //update the extra of the billsKafkaRow for common
        billsKafkaRow = await this._updateExtra(billsKafkaRow, existingRecord);
        //update the customerOtherInfo of the billsKafkaRow
        billsKafkaRow = await this._updateCustomerOtherInfo(billsKafkaRow, existingRecord);
        //update the bill based on due date and amount
        billsKafkaRow = await this._shouldUpdateBillBasedOnDueDateAndAmount(billsKafkaRow, existingRecord);
        return billsKafkaRow;
    }

    /**
     * Update create_at field in billsKafkaRow
     * @param {Object} billsKafkaRow - New bill data
     * @param {Object} existingRecord - Existing database record
     * @returns {Promise<Object>} Updated bill data
     * @private
     */
    async _updateCreatedAt(billsKafkaRow, existingRecord) {
        _.set(billsKafkaRow, "created_at", _.get(existingRecord, "created_at", null));
        return billsKafkaRow;
    }

    /**
         * Update create source, created at and updated at fields
         * @param {Object} billsKafkaRow - New bill data
         * @param {Object} existingRecord - Existing database record
         * @returns {Promise<Object>} Updated bill data
         * @private
    */
    async _updateStatusAndNotificationStatusNextBillFetchDate(billsKafkaRow, existingRecord) {
        //update status and notification status
        let existingNotificationStatus = _.get(existingRecord, 'notification_status', null);
        let existingStatus = _.get(existingRecord, 'status', null);
        billsKafkaRow.notificationStatus = existingNotificationStatus;
        if (existingStatus == 13 && existingNotificationStatus == 0) {
            billsKafkaRow.status = existingStatus;
        }
        //update the notification status
        if (existingNotificationStatus == 0) {
            _.set(billsKafkaRow, 'toBeNotified', false);
            _.set(billsKafkaRow, 'toBeNotifiedForRealTime', false);
            _.set(billsKafkaRow, 'toBeNotifiedForCtAndPFCCE', false);
        }
        //update next_bill_fetch_date
        billsKafkaRow.nextBillFetchDate = _.get(existingRecord, 'next_bill_fetch_date', null);
        return billsKafkaRow;
    }

    async _updateRemindLaterDate(billsKafkaRow, existingRecord) {
        _.set(billsKafkaRow, "remind_later_date", _.get(existingRecord, "remind_later_date", null));
        const existingDueDate = _.get(existingRecord, 'due_date') ?
            MOMENT(_.get(existingRecord, 'due_date')).utc().startOf('day') : null;
        const newDueDate = _.get(billsKafkaRow, 'dueDate') ?
            MOMENT(_.get(billsKafkaRow, 'dueDate')).utc().startOf('day') : null;
        if (newDueDate) {
            newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days');
        }
        if (existingDueDate && newDueDate && newDueDate.isAfter(existingDueDate, 'day')) {
            this.L.log("[nonPaytmBillsConsumer :: Setting remind later date as null because of new bill");
            _.set(billsKafkaRow, "resetRemindLaterDate", true);
        }
        return billsKafkaRow;
    }

    /**
     * Update extra fields by merging with existing record's extra data
     * @param {Object} billsKafkaRow - New bill data
     * @param {Object} existingRecord - Existing database record
     * @returns {Promise<Object>} Updated bill data with merged extra fields
     * @private
     */
    async _updateExtra(billsKafkaRow, existingRecord) {
        try {
            // Get existing extra data and ensure it's an object
            let existingExtra = utility.parseExtra(_.get(existingRecord, 'extra', '{}'));
            // Get current extra data and ensure it's an object
            let currentExtra = utility.parseExtra(_.get(billsKafkaRow, 'extra', '{}'));
            // Create a new object to avoid modifying frozen objects
            const updatedExtra = { ...currentExtra };
            // Update created source
            updatedExtra.created_source = await this._updateCreatedSource(existingExtra, currentExtra);
            //update the errorCounters
            updatedExtra.errorCounters = existingExtra.errorCounters;
            //update the source_subtype_2
            updatedExtra.source_subtype_2 = this._updateSourceSubtype2(billsKafkaRow);
            this.L.log('updateExtra', `Updated extra fields for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            // Set the updated extra back to billsKafkaRow
            billsKafkaRow.extra = utility.stringifyExtra(updatedExtra);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('updateExtra', `Failed to update extra fields: ${error}`);
            throw error;
        }
    }

    /**
     * Update source_subtype_2 field in extra data
     * @param {Object} billsKafkaRow - New bill data
     * @returns {string} Updated source_subtype_2 value
     * @private
     */
    _updateSourceSubtype2(billsKafkaRow) {
        let partialRecordFound = _.get(billsKafkaRow, 'partialRecordFound', false);
        if (partialRecordFound) {
            return 'PARTIAL_BILL';
        } else {
            return 'FULL_BILL';
        }
    }

    /**
     * Update created_source field in extra data
     * @param {Object} existingExtra - Existing extra data
     * @param {Object} currentExtra - Current extra data
     * @returns {Promise<string>} Updated created_source value
     * @private
     */
    async _updateCreatedSource(existingExtra, currentExtra) {
        try {
            const existingCreatedSource = _.get(existingExtra, 'created_source');
            const currentCreatedSource = _.get(currentExtra, 'created_source');
            if (currentCreatedSource && existingCreatedSource && currentCreatedSource != 'archivalCronsExpiredUser') {
                return existingCreatedSource;
            } else {
                return currentCreatedSource;
            }
        } catch (error) {
            this.L.error('updateCreatedSource', `Failed to update created_source: ${error.message}`);
            throw error;
        }
    }

    /**
     * Parse and merge extra and customerOtherInfo fields from billsKafkaRow and existingRecord
     * @param {Object} billsKafkaRow - New bill data
     * @param {Object} existingRecord - Existing database record
     * @returns {Promise<Object>} Updated bill data with parsed and merged fields
     * @private
     */
    async _updateCustomerOtherInfo(billsKafkaRow, existingRecord) {
        try {
            // Parse customerOtherInfo field
            let customerOtherInfo = utility.parseCustomerOtherInfo(_.get(billsKafkaRow, 'customerOtherInfo', '{}'));
            if (_.get(billsKafkaRow, 'partialRecordFound', false)) {
                _.set(customerOtherInfo, 'partialBillfound', true);
            }
            //update the customerId in customerOtherInfo
            _.set(customerOtherInfo, 'customerId', _.get(existingRecord, 'customer_id', null));
            billsKafkaRow.customerOtherInfo = utility.stringifyCustomerOtherInfo(customerOtherInfo);
            this.L.log('updateCustomerOtherInfo', `Updated customerOtherInfo for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('parseExtraAndCustomerOtherInfo', `Failed to parse and merge extra and customerOtherInfo fields: ${error}`);
            throw error;
        }
    }

    /**
         * Validates if the bill should be updated based on due date and amount changes
         * @param {Object} billsKafkaRow - New bill data
         * @param {Object} existingRecord - Existing database record
         * @returns {Promise<boolean>} True if bill should be updated
         * @private
         */
    async _shouldUpdateBillBasedOnDueDateAndAmount(billsKafkaRow, existingRecord) {
        try {
            const currentDueDate = _.get(billsKafkaRow, 'dueDate', null);
            const existingDueDate = _.get(existingRecord, 'due_date', null);
            const currentAmount = _.get(billsKafkaRow, 'amount', null);
            const existingAmount = _.get(existingRecord, 'due_amount', null);       
            // Consider due date changed if either value is null or they differ
            const hasDueDateChanged = currentDueDate && (!existingDueDate ||
                MOMENT(currentDueDate).utc().startOf('day').diff(MOMENT(existingDueDate).utc().startOf('day'), 'days') !== 0);

            // Consider amount changed if either value is null or they differ
            const hasAmountChanged = currentAmount && (!existingAmount || currentAmount !== existingAmount);

            if (hasDueDateChanged || hasAmountChanged) {
                this.L.log('shouldUpdateBill', `Updating the bill for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                return billsKafkaRow;
            } else {
                // Log as info instead of error since this is expected behavior for duplicate records
                this.L.log('handleCommonDueDateAndAmountFeatures', `Skipping duplicate record - currentDueDate and existingDueDate and currentAmount and existingAmount are same for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error(`currentDueDate and existingDueDate and currentAmount and existingAmount are same`);
            }
        } catch (error) {
            this.L.error('handleCommonSMSParsedFeatures', `Failed to process common SMS features: ${error} for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
            throw error;
        }
    }

    getServiceType() {
        throw new Error('getServiceType method must be implemented by subclass');
    }

    getPaytype() {
        throw new Error('getPaytype method must be implemented by subclass');
    }
}

export default BaseBillRecordComparator;