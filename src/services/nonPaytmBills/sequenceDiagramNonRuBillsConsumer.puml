@startuml NonPaytmBills Consumer Sequence

participant "NonPaytmBills" as NPB
participant "BillPreprocessor" as BPP
participant "BillDataValidator" as BDV
participant "BillPreprocessingStrategy" as BPS
participant "DatabaseEventStrategy" as DES
participant "DatabaseEventBaseStrategy" as DEBS
participant "DatabaseRecordPostProcessor" as DRPP
participant "PostDbOperations" as PDO
participant "AnalyticsHandler" as AH
participant "KafkaConsumer" as KC
participant "KafkaProducer" as KP
participant "NonPaytmBillsModel" as NPBM

== Initialization Phase ==

NPB -> NPB: constructor(options)
activate NPB
    NPB -> NPB: initializeOperatorWisePartialToFullConfig()
    NPB -> NPB: setVarFromDynamicConfig()
    NPB -> NPB: initializeProducers()
    NPB -> BPP: new BillPreprocessor(options)
    activate BPP
        BPP -> BDV: new BillDataValidator(options)
        activate BDV
        deactivate BDV
        BPP -> DES: new DatabaseEventStrategy(options)
        activate DES
            DES -> DRPP: new DatabaseRecordPostProcessor(options)
            activate DRPP
            deactivate DRPP
            DES -> DES: _initializeStrategies(options)
            note right: Initialize database strategies:\n- UpsertStrategy\n- UpsertWithReadStrategy\n- UpsertWithoutReadStrategy\n- UpdateMultipleRecordsStrategy\n- FindAndUpdateStrategy\n- FindAndCreateStrategy\n- DeleteStrategy
        deactivate DES
        BPP -> AH: new AnalyticsHandler(options)
        activate AH
        deactivate AH
        BPP -> BPP: _initializeStrategies(options)
        note right: Register preprocessing strategies:\n- MobilePostpaidStrategy\n- MobilePrepaidStrategy\n- ElectricityStrategy\n- CreditCardStrategy\n- DTHStrategy\n- FasTagStrategy\n- RentPreProcessingStrategy\n- DefaultStrategy
    deactivate BPP
    NPB -> KC: initializeConsumer()
    note right: Initialize Kafka consumer and all required components
deactivate NPB

== Start Phase ==

NPB -> NPB: start()
activate NPB
    NPB -> NPB: startProducer()
    NPB -> NPB: startConsumer()
    note right: Start consuming messages from Kafka topics
deactivate NPB

== Message Processing Flow ==

KC -> NPB: _processKafkaData(records)
activate NPB
    NPB -> NPB: _processBillsData(billsKafkaRow)
    NPB -> BPP: processBillData(billsKafkaRow)
    activate BPP
        note right: Main processing method that orchestrates\nvalidation, preprocessing, and database operations

        == Validation Phase ==
        BPP -> BDV: validateBillsData(billsKafkaRow, dbEvent)
        activate BDV
            BDV -> BDV: _checkMandatoryFields(billsKafkaRow)
            BDV -> BDV: _validateAmountLimits(billsKafkaRow)
            BDV -> BDV: _validateOldBillDueDate(billsKafkaRow)
            BDV -> BDV: _generateDebugKeys(billsKafkaRow)
        BDV --> BPP: validationResult
        deactivate BDV

        alt validation successful
            == Preprocessing Phase ==
            BPP -> BPP: preprocessBillData(billsKafkaRow)
            activate BPP
                BPP -> BPP: getStrategy(service, paytype)
                note right: Strategy selection:\n- mobile:postpaid → MobilePostpaidStrategy\n- mobile:prepaid → MobilePrepaidStrategy\n- electricity → ElectricityStrategy\n- creditcard → CreditCardStrategy\n- dth → DTHStrategy\n- fastag:prepaid → FasTagStrategy\n- rent → RentPreProcessingStrategy\n- default → DefaultStrategy

                BPP -> BPS: process(billsKafkaRow)
                activate BPS
                    BPS -> BPS: handleCommonPreprocessing(billsKafkaRow)
                    note right: Common preprocessing:\n- _updatePartialBillFlag()\n- _getToBeNotifiedForBillFetch()\n- _getToBeNotifiedForRealTime()\n- _getToBeSentToPublisherBillFetch()\n- _getToBeSentToPublisherMultiPid()\n- _getToBeNotifiedForCtAndPFCCE()\n- _updateAmbiguousStatus()\n- _updateCustomerOtherInfo()\n- _processExtraField()

                    BPS -> BPS: preprocess(billsKafkaRow)
                    note right: Service-specific preprocessing:\n- Mobile: Encrypt recharge number, handle data exhaustion\n- Electricity: Fetch PID mapping, update operator\n- Credit Card: Extract bank name, validate bank code\n- DTH: Encrypt subscriber ID\n- FasTag: Encrypt vehicle number, handle low balance\n- Rent: Process rent-specific data\n- Default: Minimal processing
                BPS --> BPP: preprocessedData
                deactivate BPS
            deactivate BPP

            == Database Operation Phase ==
            BPP -> DES: execute(dbEvent, billsKafkaRow)
            activate DES
                note right: Database event routing:\n- upsert → UpsertStrategy\n- upsertWithRead → UpsertWithReadStrategy\n- upsertWithoutRead → UpsertWithoutReadStrategy\n- updateMultipleRecordsWithSameRN → UpdateMultipleRecordsStrategy\n- findAndUpdateData → FindAndUpdateStrategy\n- findAndCreate → FindAndCreateStrategy\n- delete → DeleteStrategy

                DES -> DEBS: execute(billsKafkaRow)
                activate DEBS
                    note right: Example flow for UpsertStrategy

                    DEBS -> NPBM: readBillsWithoutCustId(billsKafkaRow)
                    activate NPBM
                    NPBM --> DEBS: existingRecords
                    deactivate NPBM

                    alt hasCustomerIdMismatch || noExistingRecords
                        DEBS -> DEBS: processRecordForNewRecord(billsKafkaRow)
                        DEBS -> NPBM: writeBatchRecordsNew(billsKafkaRow)
                        activate NPBM
                        NPBM --> DEBS: writeResult
                        deactivate NPBM
                    else existingRecordsFound
                        DEBS -> DEBS: processRecordForExistingRecord(existingRecord, billsKafkaRow)
                        DEBS -> NPBM: readBills(params)
                        activate NPBM
                        NPBM --> DEBS: existingRecord
                        deactivate NPBM

                        DEBS -> DRPP: postProcessExistingRecord(billsKafkaRow, existingRecord)
                        activate DRPP
                            DRPP -> DRPP: _processCommonFeatures(billsKafkaRow, existingRecord)
                            DRPP -> DRPP: _prioritizeBillSource(billsKafkaRow, existingRecord)
                            
                            alt source handling
                                DRPP -> DRPP: _handleSystemFetchSource()
                            else
                                DRPP -> DRPP: _handleUPMSSource()
                            else
                                DRPP -> DRPP: _handleSMSParsedSource()
                            end

                            DRPP -> DRPP: _handleDueDateComparison()
                            DRPP -> DRPP: _handleSmsDateComparison()
                        DRPP --> DEBS: processedRecord
                        deactivate DRPP

                        DEBS -> NPBM: writeBatchRecordsNew(billsKafkaRow)
                        activate NPBM
                        NPBM --> DEBS: writeResult
                        deactivate NPBM
                    end

                    == Post-Database Operations Phase ==
                    DEBS -> PDO: execute(billsKafkaRow)
                    activate PDO
                        note right: Flag-based post-processing using\npreprocessing results

                        PDO -> PDO: publishCassandraCDCvents(billsKafkaRow, cassandraCdcPublisher)
                        PDO -> PDO: _validateCDCInputs(billsKafkaRow, cassandraCdcPublisher)
                        PDO -> PDO: _prepareCDCData(billsKafkaRow)
                        PDO -> PDO: _processRechargeNumberDecryption(billData)
                        PDO -> KP: publishMessageToKafka(cassandraCdcPublisher)
                        note right: Always publish CDC events

                        alt toBeNotified == true
                            PDO -> PDO: _handleBillFetchNotification(billsKafkaRow, dbEvent)
                            PDO -> KP: publishMessageToKafka(nonRubillFetchKafkaPublisher)
                        end

                        alt toBeSentToPublisherBillFetch == true
                            PDO -> PDO: _handleNonRuBillFetchPublisher(billsKafkaRow, false)
                            PDO -> KP: publishMessageToKafka(nonRuPublisher)
                        end

                        alt toBeSentToPublisherMultiPid == true
                            PDO -> PDO: _handleNonRuBillFetchPublisher(billsKafkaRow, true)
                            PDO -> KP: publishMessageToKafka(nonRuPublisher)
                        end

                        alt toBeNotifiedForRealTime == true
                            PDO -> PDO: _handleRealTimeNotifications(billsKafkaRow)
                            PDO -> KP: publishMessageToKafka(nonRubillFetchKafkaPublisherRealtime)
                        end

                        alt toBeNotifiedForCtAndPFCCE == true
                            PDO -> PDO: _handleCtAndPFCCEvents(billsKafkaRow)
                            PDO -> PDO: _determineEventName(billsKafkaRow)
                            PDO -> KP: publishMessageToKafka(ctKafkaPublisher)
                            PDO -> KP: publishMessageToKafka(paytmFirstKafkaPublisher)
                        end

                        PDO -> PDO: _publishToUPMSRegistration(billsKafkaRow)
                        PDO -> KP: publishMessageToKafka(upmsPublisher)
                        note right: UPMS Registration based on service\nand operator eligibility
                    PDO --> DEBS: postDbResult
                    deactivate PDO
                DEBS --> DES: executionResult
                deactivate DEBS
            DES --> BPP: executionResult
            deactivate DES

        else validation failed
            BPP -> AH: handleInvalidBillData(billsKafkaRow, validationError)
            activate AH
                AH -> AH: createRecordForAnalytics(billsKafkaRow, billStatus, 'NON_RU')
                note right: Track validation failures and send metrics
            AH --> BPP: analyticsResult
            deactivate AH
        end

    BPP --> NPB: processingResult
    deactivate BPP

    NPB -> KC: commitOffset()
NPB --> KC: processingComplete
deactivate NPB

== Error Handling Flow ==

alt Processing Error occurs
    BPP -> BPP: catch(error)
    activate BPP
        BPP -> AH: handleInvalidBillData(billsKafkaRow, error.message)
        activate AH
            note right: Log error details, send metrics,\nand create analytics records
            AH -> AH: _getCustomerOtherInfo(customerOtherInfo)
            AH -> AH: _getExtra(record)
        AH --> BPP: errorHandled
        deactivate AH
        note right: Utility.sendNonPaytmBillsMetrics\nfor error tracking
    deactivate BPP
end

== Configuration Updates ==

NPB -> NPB: setVarFromDynamicConfig()
activate NPB
    note right: Periodic updates every 15 minutes:\n- Update multiplePidOperators\n- Refresh dynamic configuration\n- Update operational parameters
    NPB -> BPP: setVarFromDynamicConfig()
    activate BPP
        BPP -> BPP: update multiplePidOperators from config
    deactivate BPP
deactivate NPB

== Analytics and Monitoring ==

note over AH: Analytics are integrated throughout the flow
AH -> AH: createRecordForAnalytics(record, billStatus, source)
activate AH
    AH -> AH: _getCustomerOtherInfo(customerOtherInfo)
    AH -> AH: _getExtra(record)
    note right: Create comprehensive analytics records\nfor monitoring and reporting
AH --> AH: analyticsRecord
deactivate AH

@enduml