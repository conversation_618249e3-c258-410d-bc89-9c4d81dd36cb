/**
 * Base class for database event strategies
 * Provides common functionality for all database event strategies
 */
class DatabaseEventBaseStrategy {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
    }

    /**
     * Abstract method to execute the strategy
     * Must be implemented by concrete strategy classes
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Promise<void>}
     */
    async execute(billsKafkaRow) {
        throw new Error(`execute method must be implemented by concrete strategy for debugKey: ${billsKafkaRow.debugKey}`);
    }
}

export default DatabaseEventBaseStrategy;
