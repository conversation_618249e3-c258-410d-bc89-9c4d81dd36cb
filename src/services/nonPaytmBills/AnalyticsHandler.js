import _ from 'lodash';
import MOMENT from 'moment';
import utility from '../../lib'
import BillFetchAnalytics from '../../lib/billFetchAnalytics';
import EncryptorDecryptor from 'encrypt_decrypt';

class AnalyticsHandler {
    constructor(options) {
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.cryptr = new EncryptorDecryptor();
        this.L = options.L;
    }

    /**
         * Handle invalid bill data
         * @param {Object} billsKafkaRow - Bill data from Kafka
         * @param {Error} error - Error object
         * @param {Function} done - Callback function
         * @returns {Promise<void>}
         */
    async handleInvalidBillData(billsKafkaRow, error, done) {
        try {
            const analyticsRecord = this.createRecordForAnalytics(
                billsKafkaRow,
                this.getBillStatus(billsKafkaRow),
                'NON_RU'
            );
            await this.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                analyticsRecord,
                error,
                done,
                false
            );
        } catch (error) {
            this.L.error('handleInvalidBillData', `Failed to handle invalid bill data: ${error}`);
            throw error;
        }
    }

    /**
     * Create analytics record for bill processing
     * @param {Object} billsKafkaRow - Bill data from Kafka
     * @param {string} status - Bill status
     * @param {string} source - Source of the bill
     * @returns {Object} Analytics record
     */
    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let self = this;
        let recordForAnalytics = {};
        let extraInfo = utility.parseExtra(_.get(record, 'extra', null));
        let customerOtherInfo = utility.parseCustomerOtherInfo(_.get(record, 'customerOtherInfo', null));
        recordForAnalytics.source = extraInfo != null ? _.get(extraInfo, 'updated_data_source', null) : null;
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'customerId', null);
        recordForAnalytics.service = _.get(record, 'service', null);
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null);
        try {
            if (record && record.is_encrypted_done) {
                recordForAnalytics.recharge_number = this.cryptr.decrypt(recordForAnalytics.recharge_number);
            }

        }
        catch (error) {
            self.L.log("Error in encryption recharge number");
        }

        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'amount', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'smsId', null);
        recordForAnalytics.paytype = _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(customerOtherInfo, 'sender_id', null) !== null ? _.get(customerOtherInfo, 'sender_id', null) : _.get(customerOtherInfo, 'senderId', null);
        recordForAnalytics.sms_date_time = _.get(customerOtherInfo, 'sms_date_time', null) !== null ? _.get(customerOtherInfo, 'sms_date_time', null) : _.get(customerOtherInfo, 'smsDateTime', null);
        recordForAnalytics.sms_class_id = _.get(customerOtherInfo, 'dwh_classId', null) !== null ? _.get(customerOtherInfo, 'dwh_classId', null) : _.get(customerOtherInfo, 'dwhClassId', null);
        recordForAnalytics.rawlastcc = _.get(customerOtherInfo, 'rawLastCC', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', null);
        recordForAnalytics.bill_date = _.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));

        return recordForAnalytics;
    }

    /**
     * Get bill status based on bill data
     * @param {Object} billsKafkaRow - Bill data from Kafka
     * @returns {string} Bill status
     */
    getBillStatus(record) {
        var self = this;
        if (_.get(record, 'partialSmsFound', null)) {
            return 'PARTIAL_BILL';
        }
        if ((_.get(record, 'dueDate', null) == null || _.get(record, 'amount', null) == null) && self.partialRecordAllowedServices && self.partialRecordAllowedServices.includes(_.toLower(_.get(record, 'service', "")))) {
            return 'PARTIAL_BILL';
        }
        return 'FULL_BILL'
    }
}

export default AnalyticsHandler; 