@startuml NonPaytmBills Consumer Flow

' Main Classes
class NonPaytmBills {
    - config: Object
    - L: Logger
    - NonPaytmBillProcessor: NonPaytmBillProcessor
    - kafkaBatchSize: number
    - kafkaResumeTimeout: number
    + constructor(options: Object)
    + initializeOperatorWisePartialToFullConfig(): void
    + setVarFromDynamicConfig(): void
    + initializeProducers(): void
    + startProducer(cb: Function): void
    + start(): void
    + startConsumer(cb: Function): void
    + _processKafkaData(records: Object, resolveOffset: Function, topic: string, partition: number, cb: Function): void
    + _processBillsData(billsKafkaRow: Object, done: Function): void
}

class NonPaytmBillProcessor {
    - config: Object
    - L: Logger
    - cryptr: EncryptorDecryptor
    - nonPaytmBillsModel: NonPaytmBillsModel
    - upmsPublisher: Object
    - analyticsManager: AnalyticsHandler
    - validator: NonPaytmBillDataValidator
    - cassandraCdcPublisher: Object
    - preprocessingStrategies: Map
    - databaseEventStrategies: Map
    - defaultStrategy: DefaultStrategy
    - multiplePidOperators: Array
    + constructor(options: Object)
    + setVarFromDynamicConfig(): void
    + processBillData(billsKafkaRow: Object): Promise<void>
    + preprocessBillData(billsKafkaRow: Object): Promise<Object>
    + getPreprocessingStrategy(service: string, paytype: string): BillPreprocessingStrategy
    + getDatabaseEventStrategy(dbEvent: string): DatabaseEventStrategy
    + registerPreprocessingStrategy(strategy: Object): void
    + registerDatabaseEventStrategy(eventType: string, strategy: Object): void
    + getStrategyStatistics(): Object
    - _initializePreprocessingStrategies(options: Object): void
    - _initializeDatabaseEventStrategies(options: Object): void
    - _createStrategyKey(service: string, paytype: string): string
}

' Preprocessing Strategy Pattern Classes
abstract class BillPreprocessingStrategy {
    # preprocessor: NonPaytmBillProcessor
    # L: Logger
    # config: Object
    # cryptr: EncryptorDecryptor
    # nonPaytmBillsModel: NonPaytmBillsModel
    # allowedOperatorForSmartFetch: Array
    + constructor(preprocessor: NonPaytmBillProcessor)
    + {abstract} preprocess(billsKafkaRow: Object): Promise<Object>
    + process(billsKafkaRow: Object): Promise<Object>
    + handleCommonPreprocessing(billsKafkaRow: Object): Promise<Object>
    + {abstract} getServiceType(): string
    + getPaytype(): string
    + canHandle(service: string, paytype: string): boolean
    # _processExtraField(billsKafkaRow: Object): Object
    # _isArchivalCronsExpiredUser(billsKafkaRow: Object): boolean
    # _isOperatorAllowedForSmartFetch(billsKafkaRow: Object): boolean
    # _getToBeNotifiedForBillFetch(billsKafkaRow: Object): boolean
    # _getToBeNotifiedForRealTime(billsKafkaRow: Object): boolean
    # _getToBeNotifiedForCtAndPFCCE(billsKafkaRow: Object): boolean
    # _isPublishableBill(billsKafkaRow: Object): boolean
    # _isMultiplePidOperatorAllowed(billsKafkaRow: Object): boolean
    # _getToBeSentToPublisherBillFetch(billsKafkaRow: Object, isPrepaid: boolean): boolean
    # _getToBeSentToPublisherMultiPid(billsKafkaRow: Object, isPrepaid: boolean): boolean
    # _updateAmbiguousStatus(billsKafkaRow: Object): void
    # _updatePartialBillFlag(billsKafkaRow: Object): void
    # _updateCustomerOtherInfo(billsKafkaRow: Object): Object
}

' Concrete Strategy Classes
class MobilePostpaidStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
    + getPaytype(): string
}

class MobilePrepaidStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + processRechargeNumberAndSource(billsKafkaRow: Object): Promise<void>
    + validateDueDate(billsKafkaRow: Object): Promise<void>
    + fetchAndSetDataTemplates(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
    + getPaytype(): string
}

class ElectricityStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + fetchAndUpdatePidMapping(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
}

class CreditCardStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    - _extractAndValidateBankName(billsKafkaRow: Object): Promise<string>
    + getServiceType(): string
}

class DTHStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
}

class FasTagStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + _getToBeNotifiedForBillFetch(billsKafkaRow: Object): boolean
    + _getToBeNotifiedForRealTime(billsKafkaRow: Object): boolean
    + _getToBeNotifiedForCtAndPFCCE(billsKafkaRow: Object): boolean
    + handleLowBalanceProcessing(billsKafkaRow: Object): Promise<void>
    + checkIfFastagBalanceIsLow(billsKafkaRow: Object): boolean
    + getServiceType(): string
    + getPaytype(): string
}

class FastagUpsertStrategy {
    - cassandraCdcPublisher: Object
    - postDbOperations: PostDbOperations
    - billFetchAnalytics: BillFetchAnalytics
    - config: Object
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + execute(billsKafkaRow: Object): Promise<void>
    + checkSmsTimeDuplication(existingRecord: Object, newRecord: Object): boolean
    + extractSmsTimes(existingRecord: Object, newRecord: Object): Object
}

class RentPreProcessingStrategy {
    + constructor(options: Object)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
}

class DefaultStrategy {
    + constructor(preprocessor: NonPaytmBillProcessor)
    + preprocess(billsKafkaRow: Object): Promise<Object>
    + getServiceType(): string
}

class NonPaytmBillDataValidator {
    - config: Object
    - L: Logger
    + constructor(options: Object)
    + validateBillsData(billsKafkaRow: Object, dbEvent: string): Object
    - _checkMandatoryFields(billsKafkaRow: Object): Object
    - _validateAmountLimits(billsKafkaRow: Object): Object
    - _validateOldBillDueDate(billsKafkaRow: Object): Object
    - _generateDebugKeys(billsKafkaRow: Object): void
}

class NonPaytmDatabaseEventBaseStrategy {
    - postProcessor: BillRecordComparatorManager
    - nonPaytmBillsModel: NonPaytmBillsModel
    - cassandraCdcPublisher: Object
    - L: Logger
    - strategies: Object
    + constructor(options: Object)
    + execute(dbEvent: string, billsKafkaRow: Object): Promise<void>
    - _initializeStrategies(options: Object): Object
}

' Bill Record Comparator Inheritance
abstract class BaseBillRecordComparator {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - cassandraCdcPublisher: Object
    - L: Logger
    - config: Object
    + constructor(options: Object)
    + {abstract} process(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + processAndCompare(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + getTotalRecordsToProcess(billsKafkaRow: Object, existingRecords: Object): Promise<Object>
    + makeExistingRecords(cust_id: string, billsKafkaRow: Object, extra: Object): Object
    # _processCommonFeatures(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    # _updateStatusAndNotificationStatusNextBillFetchDate(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    # _updateRemindLaterDate(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    # _updateExtra(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    # _updateCreatedSource(existingExtra: Object, currentExtra: Object): Promise<string>
    # _parseExtraAndCustomerOtherInfo(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    # _shouldUpdateBillBasedOnDueDateAndAmount(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + {abstract} getServiceType(): string
    + {abstract} getPaytype(): string
}

' Update BillRecordComparator inheritance
BillRecordComparatorManager <|-- BaseBillRecordComparator : manages
BaseBillRecordComparator <|-- CreditCardBillRecordComparator : extends
BaseBillRecordComparator <|-- MobilePrepaidBillRecordComparator : extends
BaseBillRecordComparator <|-- UtilityBillRecordComparator : extends

note right of BaseBillRecordComparator
  Abstract base class for bill record comparison that:
  1. Provides common functionality for all bill types
  2. Handles customer ID mapping and record processing
  3. Manages common field updates and validations
  4. Implements template method pattern
  5. Requires concrete implementations for:
     - process()
     - getServiceType()
     - getPaytype()
  
  Key Features:
  - Common record comparison logic
  - Customer ID management
  - Extra field handling
  - Due date and amount validation
  - Status and notification updates
end note

class BillRecordComparatorManager {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - cassandraCdcPublisher: Object
    - L: Logger
    - analyticsManager: AnalyticsManager
    - config: Object
    - strategies: Map
    + constructor(options: Object)
    + process(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + getTotalRecordsToProcess(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    - _initializeProcessors(options: Object): void
    - _createStrategyKey(serviceType: string, paytype: string): string
    - _getProcessor(billsKafkaRow: Object): BaseBillRecordComparator
}

note right of BillRecordComparatorManager
  Strategy Manager class that:
  1. Manages different bill record comparison strategies
  2. Routes bill processing to appropriate strategy
  3. Maintains a map of service:paytype to strategy
  4. Provides fallback to utility:default strategy
  
  Key Features:
  - Strategy initialization and registration
  - Dynamic strategy selection based on service/paytype
  - Error handling and logging
  - Delegation to concrete strategies
end note

class CreditCardBillRecordComparator {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + process(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + postProcessExistingRecord(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + getDataToUpdate(billsKafkaRow: Object, existingRecord: Object): Object
    + validateExistingRecord(billsKafkaRow: Object, existingRecord: Object): void
    + getServiceType(): string
    + getPaytype(): string
}

class MobilePrepaidBillRecordComparator {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + process(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + postProcessExistingRecord(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + getTotalRecordsToProcess(billsKafkaRow: Object, existingRecords: Object): Promise<Object>
    + makeExistingRecords(cust_id: string, billsKafkaRow: Object, extra: Object): Object
    + getServiceType(): string
    + getPaytype(): string
}

class UtilityBillRecordComparator {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + process(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _prioritizeBillSource(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _getSource(record: Object): string
    + _getSmsAndCurrentDates(billsKafkaRow: Object): Object
    + _logAndThrowError(method: string, message: string, errorMessage: string): void
    + _handleSystemFetchSource(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _handleUpmsSource(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _handleSmsParsedSource(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _handleDueDateComparison(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + _handleSmsDateComparison(billsKafkaRow: Object, existingRecord: Object): Promise<Object>
    + getServiceType(): string
    + getPaytype(): string
}

class AnalyticsHandler {
    - config: Object
    - L: Logger
    - billFetchAnalytics: BillFetchAnalytics
    - cryptr: EncryptorDecryptor
    + constructor(options: Object)
    + handleInvalidBillData(billsKafkaRow: Object, error: string): void
    - createRecordForAnalytics(billsKafkaRow: Object, billStatus: string, source: string): Object
}

class UpsertStrategy {
    - cassandraCdcPublisher: Object
    - postProcessor: DatabasePostProcessor
    - postDbOperations: PostDbOperations
    - billFetchAnalytics: BillFetchAnalytics
    - analyticsHandler: AnalyticsHandler
    - config: Object
    + constructor(options: Object)
    + execute(billsKafkaRow: Object): Promise<void>
    + processRecordForNewRecord(billsKafkaRow: Object): Promise<void>
    + processRecordForExistingRecord(existingRecord: Object, billsKafkaRow: Object): Promise<Object>
    + hasCustomerIdMismatch(existingRecord: Array, billsKafkaRow: Object): boolean
    + shouldCreateNewRecord(billsKafkaRow: Object): boolean
    + processDateUpdates(existingRecord: Object, billsKafkaRow: Object): Promise<Object>
    + generateParamsForReadBill(existingRecord: Object, billsKafkaRow: Object): Object
    + shouldUpdateRecord(record: Object, dateField: string): boolean
    + updatePrviousDueDateMonthlyTable(existingRecord: Object, billsKafkaRow: Object): Promise<Object>
    + updatePreviousBillGenMonthlyTable(existingRecord: Object, billsKafkaRow: Object): Promise<Object>
}

class DeleteStrategy {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + execute(billsKafkaRow: Object): Promise<void>
    + deleteData(billsKafkaRow: Object): Promise<void>
    + deleteDataExtended(billsKafkaRow: Object): Promise<void>
    + generateBucketNameUsingHash(params: Object, currentRecord: Object): Promise<string>
}

class FindAndCreateStrategy {
    - nonPaytmBillsModel: NonPaytmBillsModel
    - L: Logger
    + constructor(options: Object)
    + execute(billsKafkaRow: Object): Promise<void>
    + findAndCreateData(billsKafkaRow: Object): Promise<void>
    + formatDataToUpdate(billsKafkaRow: Object): Object
}

class PostDbOperations {
    - L: Logger
    - config: Object
    - payloadPreparator: PayloadPreparator
    - publishToKafka: PublishToKafka
    - commonLib: CommonLib
    - reminderUtils: ReminderUtils
    - BillPush: BillPush
    - ctKafkaPublisher: KafkaProducer
    - paytmFirstKafkaPublisher: KafkaProducer
    - nonRubillFetchKafkaPublisher: KafkaProducer
    - nonRubillFetchKafkaPublisherRealtime: KafkaProducer
    - upmsPublisher: KafkaProducer
    - nonRuPublisher: KafkaProducer
    - cassandraCdcPublisher: KafkaProducer
    + constructor(options: Object)
    + execute(billsKafkaRow: Object): Promise<void>
    + publishCassandraCDCvents(billsKafkaRow: Object, cassandraCdcPublisher: Object): Promise<void>
    - _validateCDCInputs(billsKafkaRow: Object, cassandraCdcPublisher: Object): boolean
    - _prepareCDCData(billsKafkaRow: Object): Object
    - _processRechargeNumberDecryption(billData: Object): Object
    - _getCDCPartitionKey(billsKafkaRow: Object): string
    - _publishToUPMSRegistration(billsKafkaRow: Object): void
    - _handleBillFetchNotification(billsKafkaRow: Object, dbEvent: string): Promise<void>
    - _handleNonRuBillFetchPublisher(billsKafkaRow: Object, isMultiPid: boolean): Promise<void>
    - _handleRealTimeNotifications(billsKafkaRow: Object): Promise<void>
    - _handleCtAndPFCCEvents(billsKafkaRow: Object): Promise<void>
    - _postProcessSpecificPayloadHandling(record: Object, payload: Object): Promise<Object>
    - _publishCTEvents(mappedData: Object, clonedData: Object, billsKafkaRow: Object, eventName: string): Promise<void>
    - _publishPaytmFirstCCEvents(mappedData: Object, clonedData: Object, billsKafkaRow: Object): Promise<void>
    - _sendMetricsWithConditionalTags(billsKafkaRow: Object, status: string, type: string, source: string, error: string): void
    - _determineEventName(billsKafkaRow: Object): string
    - _getMobileEventName(billsKafkaRow: Object): string
    - _getLoanEventName(billsKafkaRow: Object): string
    - _getElectricityEventName(billsKafkaRow: Object): string
    - _getRentEventName(billsKafkaRow: Object): string
    - _getReminderNonRuEventName(billsKafkaRow: Object): string
    - _promisifyCallback(fn: Function): Promise<void>
    - _sendCTMetrics(billsKafkaRow: Object, eventName: string, customerOtherInfo: Object, isError: boolean): void
}

' Relationships
NonPaytmBills "1" *-- "1" NonPaytmBillProcessor : contains

' NonPaytmBillProcessor relationships
NonPaytmBillProcessor "1" *-- "1" NonPaytmBillDataValidator : uses
NonPaytmBillProcessor "1" *-- "1" NonPaytmDatabaseEventBaseStrategy : uses
NonPaytmBillProcessor "1" *-- "1" AnalyticsHandler : uses
NonPaytmBillProcessor "1" *-- "*" BillPreprocessingStrategy : manages

' Preprocessing Strategy Pattern Relationships
BillPreprocessingStrategy <|-- MobilePostpaidStrategy : implements
BillPreprocessingStrategy <|-- MobilePrepaidStrategy : implements
BillPreprocessingStrategy <|-- ElectricityStrategy : implements
BillPreprocessingStrategy <|-- CreditCardStrategy : implements
BillPreprocessingStrategy <|-- DTHStrategy : implements
BillPreprocessingStrategy <|-- FasTagStrategy : implements
BillPreprocessingStrategy <|-- RentPreProcessingStrategy : implements
BillPreprocessingStrategy <|-- DefaultStrategy : implements

' Database Strategy Pattern Relationships
NonPaytmDatabaseEventBaseStrategy "1" *-- "*" UpsertStrategy : extends
NonPaytmDatabaseEventBaseStrategy "1" *-- "*" FastagUpsertStrategy : extends
NonPaytmDatabaseEventBaseStrategy "1" *-- "*" DeleteStrategy : extends
NonPaytmDatabaseEventBaseStrategy "1" *-- "*" FindAndCreateStrategy : extends

' Database Strategy Internal Relationships
UpsertStrategy "1" *-- "1" BillRecordComparatorManager : uses
UpsertStrategy "1" *-- "1" PostDbOperations : uses
FastagUpsertStrategy "1" *-- "1" PostDbOperations : uses
DeleteStrategy "1" *-- "1" PostDbOperations : uses
FindAndCreateStrategy "1" *-- "1" PostDbOperations : uses

' Flow Notes
note right of NonPaytmBills
  Main consumer class that:
  1. Initializes producers and consumers
  2. Processes Kafka messages
  3. Delegates to NonPaytmBillProcessor for processing
end note

note right of NonPaytmBillProcessor
  Enhanced bill processor that:
  1. Validates bill data using NonPaytmBillDataValidator
  2. Preprocesses bills using strategy pattern
  3. Executes database operations via NonPaytmDatabaseEventBaseStrategy
  4. Manages preprocessing strategies directly (no factory)
  5. Manages the analytics and error logging
  
  Key Features:
  - Direct strategy management with Map<string, Strategy>
  - Integrated validation and preprocessing
  - Common preprocessing in base strategy class
  - Support for strategy registration and statistics
  - Manages the analytics
end note

note right of BillPreprocessingStrategy
  Abstract base class providing:
  1. Common preprocessing logic in handleCommonPreprocessing()
  2. Flag setting for notifications and publishing
  3. Template method pattern via process() method
  4. Extensible architecture for new bill types
  
  Common Processing:
  - Sets toBeNotified flags for various notifications
  - Updates ambiguous status and customer info  
  - Processes extra fields and encryption
  - Validates business rules and constraints
end note

note right of NonPaytmDatabaseEventBaseStrategy
  Manages database operations using strategy pattern:
  - Maps dbEvent strings to strategy instances
  - Supports: upsertWithoutRead, upsertWithRead, updateMultipleRecordsWithSameRN, findAndUpdate
  - Handles: upsert, delete, findAndCreate, FastagUpsert
  Each strategy encapsulates specific database logic
  while sharing common base functionality
end note

note right of PostDbOperations
  Handles all post-database operations:
  1. Cassandra CDC events publishing
  2. Bill fetch notifications (toBeNotified flag)
  3. Non-RU publisher events (toBeSentToPublisher flags)
  4. Real-time notifications (toBeNotifiedForRealTime)
  5. CT and PFCCE events (toBeNotifiedForCtAndPFCCE)
  6. UPMS registration publishing
  
  Uses flags set during preprocessing to determine
  which operations to execute for each bill
end note

note right of CreditCardBillRecordComparator
  Credit card specific record comparator that:
  1. Extends base BaseBillRecordComparator
  2. Implements credit card specific validation
  3. Handles payment information updates
  4. Manages amount calculations
  5. Processes credit card specific business rules
end note

note right of MobilePrepaidBillRecordComparator
  Mobile prepaid specific record comparator that:
  1. Handles Airtel prepaid publisher migration
  2. Manages multiple customer IDs
  3. Processes existing records and new creations
  4. Handles duplicate CA number operators
  5. Sets appropriate source flags for migrated records
end note

note right of UtilityBillRecordComparator
  Utility bill specific record comparator that:
  1. Prioritizes bill sources based on due date and amount
  2. Handles different source combinations (SYSTEM_FETCH, UPMS, SMS_PARSED)
  3. Validates SMS dates and due dates
  4. Manages source comparison logic
  5. Implements utility-specific business rules
end note

@enduml