import PayloadPreparator from '../../models/preparePayload';
import PublishToKafka from '../../models/publishToKafka';
import utility from '../../lib';
import _ from 'lodash';
import MOMENT from 'moment';
import ASYNC from 'async';
import digitalUtility from 'digital-in-util'
import BillPush from '../../lib/billPush';

class NonPaytmPostDbOperations {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.payloadPreparator = new PayloadPreparator(options);
        this.publishToKafka = new PublishToKafka(options);
        this.commonLib = new utility.commonLib(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.BillPush = new BillPush(options);
        this.ctKafkaPublisher = options.ctKafkaPublisher;
        this.paytmFirstKafkaPublisher = options.paytmFirstKafkaPublisher;
        this.nonRubillFetchKafkaPublisher = options.nonRubillFetchKafkaPublisher;
        this.nonRubillFetchKafkaPublisherRealtime = options.nonRubillFetchKafkaPublisherRealtime;
        this.upmsPublisher = options.upmsPublisher;
        this.nonRuPublisher = options.nonRuPublisher;
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
    }

    async execute(billsKafkaRow) {
        let dbEvent = _.get(billsKafkaRow, 'dbEvent', null);

        try {
            //publish to cassandra cdc
            this.publishCassandraCDCvents(billsKafkaRow, this.cassandraCdcPublisher);

            // Use flags directly from preprocessing
            if (_.get(billsKafkaRow, 'toBeNotified', false)) {
                await this._handleBillFetchNotification(billsKafkaRow, dbEvent);
            }

            if (_.get(billsKafkaRow, 'toBeSentToPublisherBillFetch', false)) {
                await this._handleNonRuBillFetchPublisher(billsKafkaRow, false);
            }

            if (_.get(billsKafkaRow, 'toBeSentToPublisherMultiPid', false)) {
                await this._handleNonRuBillFetchPublisher(billsKafkaRow, true);
            }

            if (_.get(billsKafkaRow, 'toBeNotifiedForRealTime', false)) {
                await this._handleRealTimeNotifications(billsKafkaRow);
            }

            if (_.get(billsKafkaRow, 'toBeNotifiedForCtAndPFCCE', false)) {
                await this._handleCtAndPFCCEvents(billsKafkaRow);
            }

            //publish to UPMSRegistration
            this._publishToUPMSRegistration(billsKafkaRow);
        } catch (error) {
            this.L.error("NonPaytmPostDbOperations::execute", `Error executing operations: ${error}`);
            throw error;
        }
    }

    /**
     * Publishes Cassandra CDC events to Kafka with improved error handling and readability
     * @param {Function} done - Callback function to execute after publishing
     * @param {Object} billsKafkaRow - Bill data to be published
     * @param {Object} cassandraCdcPublisher - Kafka publisher instance
     * @returns {void}
     */
    async publishCassandraCDCvents(billsKafkaRow, cassandraCdcPublisher) {
        const self = this;
        try {
            // Step 1: Validate input parameters
            self._validateCDCInputs(billsKafkaRow, cassandraCdcPublisher);
            // Step 2: Convert and prepare CDC recovery data
            const processedBillData = self._prepareCDCData(billsKafkaRow);
            // Step 3: Create Kafka message payload
            const keyForCDC = self._getCDCPartitionKey(processedBillData);
            // Step 4: Publish to Kafka with proper error handling
            this.publishToKafka.publishMessageToKafka(cassandraCdcPublisher, processedBillData, processedBillData.topic, processedBillData.source, keyForCDC);
        } catch (error) {
            self.L.error("publishCassandraCDCvents", `Error publishing Cassandra CDC events:for debugKey ${billsKafkaRow.debugKey} with error ${error}`);
            utility.sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'PUBLISH_CASSANDRA_CDC_EVENTS', billsKafkaRow, error.message);
            throw error;
        }
    }

    /**
     * Validates input parameters for CDC publishing
     * @param {Object} billsKafkaRow - Bill data
     * @param {Object} cassandraCdcPublisher - Kafka publisher
     * @returns {boolean} True if valid, false otherwise
     * @private
     */
    _validateCDCInputs(billsKafkaRow, cassandraCdcPublisher) {
        const self = this;

        if (!billsKafkaRow) {
            self.L.error('publishCassandraCDCvents :: Validation Error', 'billsKafkaRow is required');
            throw new Error('billsKafkaRow parameter is required');
        }

        if (!cassandraCdcPublisher || typeof cassandraCdcPublisher.publishData !== 'function') {
            self.L.error('publishCassandraCDCvents :: Validation Error', 'Valid cassandraCdcPublisher is required');
            throw new Error('Valid cassandraCdcPublisher is required');
        }

        return true;
    }

    /**
     * Prepares and processes bill data for CDC publishing
     * @param {Object} billsKafkaRow - Original bill data
     * @returns {Object} Processed bill data
     * @private
     */
    _prepareCDCData(billsKafkaRow) {
        const self = this;
        // Convert CDC recovery data
        let processedData = self.payloadPreparator.preparePayloadForCassandraCDC(billsKafkaRow);
        // Handle recharge number decryption for mobile services
        processedData = self._processRechargeNumberDecryption(processedData);
        let extra = _.get(processedData, 'extra', {});
        let customerOtherInfo = _.get(processedData, 'customerOtherInfo', {});
        processedData.extra = utility.stringifyExtra(extra);
        processedData.customerOtherInfo = utility.stringifyCustomerOtherInfo(customerOtherInfo);
        return processedData;
    }

    /**
     * Processes recharge number decryption for mobile services
     * @param {Object} billData - Bill data to process
     * @returns {Object} Bill data with processed recharge number
     * @private
     */
    _processRechargeNumberDecryption(billData) {
        const self = this;
        const service = _.toLower(_.get(billData, 'service', ''));
        const rechargeNumber = _.get(billData, 'rechargeNumber', null);

        if (service === 'mobile' && rechargeNumber) {
            try {
                const decryptedRechargeNumber = self.cryptr.decrypt(rechargeNumber);
                _.set(billData, 'decryptedRechargeNumber', decryptedRechargeNumber);

                self.L.log('publishCassandraCDCvents :: Mobile service recharge number decrypted successfully');
            } catch (error) {
                self.L.error('publishCassandraCDCvents :: CASSANDRA_CDC',
                    'Error while decrypting recharge number for mobile service', error);
                // Set original recharge number as fallback
                _.set(billData, 'decryptedRechargeNumber', rechargeNumber);
            }
        } else {
            // For non-mobile services, use original recharge number
            _.set(billData, 'decryptedRechargeNumber', rechargeNumber);
        }

        return billData;
    }

    /**
         * Publish bill data to UPMS registration if conditions are met
         * @param {Object} billsKafkaRow - Bill data
         * @returns {Promise<void>}
         * @throws {Error} If publishing fails
         */
    _publishToUPMSRegistration(billsKafkaRow) {
        const self = this;
        try {
            const extra = utility.parseExtra(billsKafkaRow.extra);
            const service = _.get(billsKafkaRow, 'service', null);
            const operator = _.get(billsKafkaRow, 'operator', null);
            const isAmbiguous = _.get(extra, 'ambiguous', null);
            const billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
            const billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION'], ['electricity']);
            const isServiceAllowed = billPushServiceRegistration &&
                billPushServiceRegistration.includes(service);
            const isOperatorAllowed = billPushOperatorRegistrationAllowed &&
                billPushOperatorRegistrationAllowed.includes(operator);

            if (!isAmbiguous && (isServiceAllowed || isOperatorAllowed)) {
                self.L.log('publishToUPMSRegistration', 'Going to push data for billPush Registration as it is confirmed nonRU and non ambiguous');
                self.BillPush.pushToRegistrationProcess(self, billsKafkaRow, billsKafkaRow, 'smsParsed', 'nonru');
            } else {
                self.L.log('publishToUPMSRegistration', 'Skipping billPush Registration as it is ambiguous or service/operator is not allowed');
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS",
                    'STATUS:SKIP',
                    'TYPE:UPMS_REGISTRATION',
                    'SOURCE:UPSERT_DATA',
                    `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`,
                    `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`,
                    `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`
                ]);
            }
        } catch (error) {
            this.L.error('publishToUPMSRegistration', `Failed to publish to UPMS registration: ${error}`);
            throw error;
        }
    }

    async _postProcessSpecificPayloadHandling(record, payload) {
        let self = this;
        if (_.toLower(_.get(record, 'paytype')) == 'prepaid' && _.toLower(_.get(record, 'service')) == 'mobile') {
            let dueDate = MOMENT(_.get(record, 'dueDate', null)).isValid() ? MOMENT(_.get(record, 'dueDate', null)).format('YYYY-MM-DD') : null;
            let currentDate = MOMENT().format('YYYY-MM-DD');

            if (MOMENT(dueDate).diff(currentDate, 'days') < _.get(self.config, ['REALTIME_SMS_PARSING', 'DUE_DATE_NOTIFICATION', 'EARLIEST_DAY_VALUE'], 10) &&
                MOMENT(dueDate).diff(currentDate, 'days') > _.get(self.config, ['REALTIME_SMS_PARSING', 'DUE_DATE_NOTIFICATION', 'LATEST_DAY_VALUE'], -7)) {
                self.L.log("publishToPvKafka:: ", 'going to publish on NONRU_BILL_FETCH kafka');
                _.set(payload, 'notificationType', 'BILLDUE');
            }
            else if (_.get(record, "partialBillState", null)) {
                _.set(payload, ["data", "time_interval"], self.notificationTimeOut);
            }
            else if (!_.get(record, "partialBillState", null)) {
                self.L.log("publishToPvKafka:: ", 'Not publishing on NONRU_BILL_FETCH kafka due to failing eligibility')
                return null;
            }
        }
        if (_.toLower(_.get(record, 'paytype')) == 'prepaid' && _.toLower(_.get(record, 'service')) == 'fastag recharge') {
            _.set(payload, "source", "nonRUbillGenPublisherRealtime");
            _.set(payload, ["data", "time_interval"], self.fastTagTimeOut);
        }
        if (record.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) {
            _.set(payload, "notificationType", "OLD_BILL_NOTIFICATION");
            _.set(payload, ["data", "status"], _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5));
        }
        return payload;
    }

    async _handleBillFetchNotification(billsKafkaRow, dbEvent) {
        let payload = await this.payloadPreparator.prepareNonRuBillNotificationPayload(billsKafkaRow, false);
        //postProcess the payload generated by prepareBillFetchPayload
        payload = await this._postProcessSpecificPayloadHandling(billsKafkaRow, payload);
        if (payload) {
            this.publishToKafka.publishMessageToKafka(this.nonRubillFetchKafkaPublisher, payload, payload.topic, payload.source, payload.data.customer_id);
        } else {
            this.L.error("NonPaytmPostDbOperations::_handleBillFetchNotification", `Error preparing payload for non-RU bill notification`);
        }
    }

    async _handleNonRuBillFetchPublisher(billsKafkaRow, isMultiPid) {
        const payload = await this.payloadPreparator.prepareNonRuBillFetchPublisherPayload(billsKafkaRow, isMultiPid);
        if (payload) {
            this.publishToKafka.publishMessageToKafka(this.nonRuPublisher, payload, payload.topic, payload.source, payload.customerId);
        } else {
            this.L.error("NonPaytmPostDbOperations::_handleNonRuBillFetchPublisher", `Error preparing payload for non-RU bill fetch publisher: ${error}`);
        }
    }

    async _handleRealTimeNotifications(billsKafkaRow) {
        const isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);

        if (isRealTimeDataExhausted) {
            // Need to discuss this with team
            // await new Promise((resolve, reject) => {
            //     this.publishInBillFetchKafkaForDataExhaust((err) => {
            //         if (err) {
            //             this.L.error("NonPaytmPostDbOperations::_handleRealTimeNotifications",
            //                 `Error publishing to data exhaust kafka: ${err}`);
            //         }
            //         resolve();
            //     }, billsKafkaRow, billsKafkaRowCloneForDataExhaust);
            // });
        } else {
            const payload = await this.payloadPreparator.prepareNonRuBillNotificationPayload(billsKafkaRow, true);
            if (payload) {
                this.publishToKafka.publishMessageToKafka(this.nonRubillFetchKafkaPublisherRealtime, payload, payload.topic, payload.source, payload.customer_id);
            } else {
                this.L.error("NonPaytmPostDbOperations::_handleRealTimeNotifications", `Error preparing payload for non-RU bill notification: ${error}`);
            }
        }
    }

    async _handleCtAndPFCCEvents(billsKafkaRow) {
        const self = this;
        const billsKafkaRowCloned = _.clone(billsKafkaRow);
        const productId = billsKafkaRowCloned.productId;
        const dbDebugKey = `rech:${billsKafkaRowCloned.rechargeNumber}::cust:${billsKafkaRowCloned.customerId}::op:${billsKafkaRowCloned.operator}`;

        if (billsKafkaRowCloned.is_encrypted_done) {
            billsKafkaRowCloned.rechargeNumber = this.cryptr.decrypt(billsKafkaRowCloned.rechargeNumber);
        }
        const eventName = this._determineEventName(billsKafkaRowCloned);
        const rechargeNumber = _.get(billsKafkaRowCloned, "rechargeNumber", null);
        const customerId = _.get(billsKafkaRowCloned, "customerId", null);

        try {
            // Validate CT event if needed
            if (_.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENT_FILTER', eventName], null) === 1) {
                await this._promisifyCallback(cb =>
                    self.commonLib.validateCTEventCondition(cb, customerId, rechargeNumber, eventName)
                );
            }

            // Get retailer data
            await this._promisifyCallback(cb =>
                self.commonLib.getRetailerData(cb, billsKafkaRowCloned.customerId, billsKafkaRowCloned)
            );

            // Get CVR data
            await this._promisifyCallback(cb =>
                self.commonLib.getCvrData(cb, productId, billsKafkaRowCloned)
            );

            const mappedData = self.reminderUtils.createCTPipelinePayload(billsKafkaRowCloned, eventName, dbDebugKey);
            const clonedData = _.cloneDeep(mappedData);

            // Handle CT events
            if (!self.commonLib.isCTEventBlocked(eventName) && _.get(billsKafkaRowCloned, 'notificationStatus', 1)) {
                await this._publishCTEvents(mappedData, clonedData, billsKafkaRowCloned, eventName);
            } else {
                self.L.error(`Skipping CT event: ${eventName} - Blocked or notification status: ${_.get(billsKafkaRowCloned, 'notificationStatus', 0)}`);
            }

            // Handle Paytm First CC events
            if (_.get(mappedData, 'service', null) === 'financial services') {
                await this._publishPaytmFirstCCEvents(mappedData, clonedData, billsKafkaRowCloned);
            }
        } catch (error) {
            self.L.error('Error in _handleCtAndPFCCEvents:', error);
            throw error;
        }
    }

    _determineEventName(billsKafkaRow) {
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_BILLS'], 'smsParsedBillPayment');

        if (_.get(billsKafkaRow, 'dbEvent', '') === 'delete') {
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_BILLS_DELETED'], 'smsParsedBillPaymentDeleted');
        }

        const service = _.get(billsKafkaRow, 'service', '');
        const source = _.get(billsKafkaRow, 'source', '');

        if (service === 'mobile') {
            eventName = this._getMobileEventName(billsKafkaRow);
        } else if (service === 'loan') {
            eventName = this._getLoanEventName(billsKafkaRow);
        } else if (service === 'electricity') {
            eventName = this._getElectricityEventName(billsKafkaRow);
        } else if (service === 'rent payment') {
            eventName = this._getRentEventName(billsKafkaRow);
        } else if (source === "reminderNonRuBillFetch") {
            eventName = this._getReminderNonRuEventName(billsKafkaRow);
        }

        return eventName;
    }

    async _publishCTEvents(mappedData, clonedData, billsKafkaRow, eventName) {
        const self = this;
        let dataToPush = mappedData;
        let clonedDataToPush = clonedData;
        const customerOtherInfo = JSON.parse(_.get(clonedData, 'customerOtherInfo', "{}"));

        if (_.get(customerOtherInfo, 'partialBillfound', false)) {
            dataToPush = _.cloneDeep(dataToPush);
            clonedDataToPush = _.cloneDeep(clonedDataToPush);
            _.set(dataToPush, "eventName", "partialBillfound");
            _.set(clonedDataToPush, "eventName", "partialBillfound");
        }
        const topic = _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', '');
        if (clonedDataToPush) {
            this.publishToKafka.publishMessageToKafka(this.ctKafkaPublisher, mappedData, topic, "NON_PAYTM_BILLS", mappedData.customer_id);
        } else {
            self.L.error("NonPaytmPostDbOperations::_publishCTEvents", `Error preparing payload for CT events`);
        }
    }

    async _publishPaytmFirstCCEvents(mappedData, clonedData, billsKafkaRow) {
        const self = this;
        const topic = _.get(self.config.KAFKA, 'SERVICES.PAYTM_FIRST_CC_EVENTS_PUBLISHER.TOPIC', '');
        if (mappedData) {
            this.publishToKafka.publishMessageToKafka(this.paytmFirstKafkaPublisher, mappedData, topic, "NON_PAYTM_BILLS", mappedData.customer_id);
        } else {
            self.L.error("NonPaytmPostDbOperations::_publishPaytmFirstCCEvents", `Error preparing payload for Paytm First CC events`);
        }
    }

    _promisifyCallback(fn) {
        return new Promise((resolve, reject) => {
            fn((error) => {
                if (error) reject(error);
                else resolve();
            });
        });
    }

    _sendCTMetrics(billsKafkaRow, eventName, customerOtherInfo, isError) {
        const status = isError ? 'ERROR' : 'PUBLISHED';
        const metrics = [
            "REQUEST_TYPE:NON_PAYTM_BILLS",
            `STATUS:${status}`,
            "TYPE:CT_EVENTS",
            `OPERATOR:${billsKafkaRow.operator}`,
            `SERVICE:${_.get(billsKafkaRow, 'service', 'NO_SERVICE')}`,
            `SOURCE:${_.get(billsKafkaRow, 'source', 'NO_SOURCE')}`,
            `EVENT_NAME:${eventName}`
        ];

        if (_.get(customerOtherInfo, 'partialBillfound', false)) {
            metrics.push("PARTIAL_BILL_FOUND:TRUE");
        }

        utility._sendMetricsToDD(1, metrics);
    }

    _getMobileEventName(billsKafkaRow) {
        const self = this;
        let eventName;

        if (_.get(billsKafkaRow, 'paytype', '') === 'postpaid') {
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_MOBILE_POSTPAID'], 'smsParsedMobilePostpaid');
            if (_.get(billsKafkaRow, 'dbEvent', '') === 'delete') {
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_MOBILE_POSTPAID_DELETED'], 'smsParsedMobilePostpaidDeleted');
            }
        } else {
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_PREPAID'], 'smsParsedPrepaidRecharge');
            if (_.get(billsKafkaRow, 'dbEvent', '') === 'delete') {
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_PREPAID_DELETED'], 'smsParsedPrepaidRechargeDel');
            }
        }

        if (_.get(billsKafkaRow, 'smsparsedPlanRecharged', false)) {
            eventName = "smsparsedPlanRecharged";
        }

        if (_.get(billsKafkaRow, "dwhClassId", null)) {
            if (billsKafkaRow.dwhClassId === 7) {
                eventName = "smsparsedIncomingStopped";
            }
            if (self.validityExpiryIds.includes(billsKafkaRow.dwhClassId)) {
                if (_.get(billsKafkaRow, "dueDate", null) === null) {
                    eventName = 'PrepaidRechargedWODate';
                }
            }
        }

        return eventName;
    }

    _getLoanEventName(billsKafkaRow) {
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_LOAN'], 'smsparsedloanpayment');
        if (_.get(billsKafkaRow, 'dbEvent', '') === 'delete') {
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_LOAN_DELETED'], 'smsparsedloanpaymentdeleted');
        }
        return eventName;
    }

    _getElectricityEventName(billsKafkaRow) {
        if (_.get(billsKafkaRow, 'source', '') === 'postpaidSmsParsingBillPaid') {
            return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_ELECTRICITY_POSTPAID_BILL_PAID'], 'smsParsedBillPaidElectricityPostpaid');
        }
        return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_ELECTRICITY_POSTPAID'], 'smsParsedElectricityPostpaid');
    }

    _getRentEventName(billsKafkaRow) {
        let extra;
        try {
            extra = _.get(billsKafkaRow, 'extra', '{}');
            extra = JSON.parse(extra);
            const level = _.get(extra, 'level_2_category', '');

            if (level === 1) {
                return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_SMS_RENT_LANDLORD'], 'smsParsedRentLandlord');
            } else if (level === 2) {
                return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_SMS_RENT_TENANT'], 'smsParsedRentTenant');
            }
        } catch (error) {
            this.L.error('Error parsing rent payment extra data:', error);
        }

        if (_.get(billsKafkaRow, 'dbEvent', '') === 'delete') {
            return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_SMS_RENT_DELETE'], 'smsParsedRentDeleted');
        }

        return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_BILLS'], 'smsParsedBillPayment');
    }

    _getReminderNonRuEventName(billsKafkaRow) {
        return _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_PUBLISHER'], 'SMSParsedPartialElectrcityPostpaid');
    }

    _getCDCPartitionKey(billsKafkaRow) {
        let partionKey = '';
        let customerIdForCDCPartitionKey = _.get(billsKafkaRow, 'customerId', null);
        let rnForCDCPartitionKey = _.get(billsKafkaRow, 'rechargeNumber', null);
        if (customerIdForCDCPartitionKey && rnForCDCPartitionKey) {
            partionKey = customerIdForCDCPartitionKey + "_" + rnForCDCPartitionKey;
        }
        return partionKey;
    }
}

export default NonPaytmPostDbOperations;