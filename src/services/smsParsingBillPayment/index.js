import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import ASYNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import processCreditCardStrategy from './creditCardStrategy';
import prepaidSmsparsing from './prepaidSmsParsing';
import postpaidSmsParsing from './postpaidSmsParsing';
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills';
import DigitalCatalog from '../../lib/digitalReminderConfig'
import INFRAUTILS from 'infra-utils'
import { promisify } from 'util'
import Q from 'q'
import BillFetchAnalytics from '../../lib/billFetchAnalytics.js'
import KafkaConsumerChecks from '../../lib/kafkaConsumerChecks';
import KafkaConsumer from '../../lib/KafkaConsumer';
import NOTIFIER from '../../services/notify'

class smsParsingBillPayment {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.dwhRealTimeSmsParsing = _.get(options,'telcoSmsParsingBillsDwhRealtime',false);
        if(this.dwhRealTimeSmsParsing) _.set(options,'smsParsingBillsDwhRealtime',true);
        this.processCreditCardStrategy = new processCreditCardStrategy(options, this);
        this.prepaidSmsparsing = new prepaidSmsparsing(options, this);
        this.postpaidSmsParsing = new postpaidSmsParsing(options, this);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;

        let { MONGO_DB, MYSQL, KAFKA } = this.config.VIL_SYNC_DB;
        this.mongoDbInstance = new INFRAUTILS.mongo(this.config.MONGO.HIDDEN_SLAVE);
        this.mongoCollection = 'users';
        this.retryCountForMongo = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'RETRY_COUNT'], MONGO_DB.RETRY_COUNT);
        this.commonLib = new utility.commonLib(options);
        this.mongoDbTps = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
        this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);  //this.mongoDbTps = 3 or 4;
        this.lastMongoFetchTime = this.getTimeInMs() - this.timeThresholdForMongo;
        this.mongoDbFetchRecordsFailureRetryInterval = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FAILURE_RETRY_INTERVAL'], MONGO_DB.FAILURE_RETRY_INTERVAL);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.saveForAnalyticsInCassandraAndKafka = options.saveForAnalyticsInCassandraAndKafka ? true : false;
        this.notify = new NOTIFIER(options);
    }

    async start() {
        let self = this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SMS_PARSING_BILL_PAYMENT :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SMS_PARSING_BILL_PAYMENT :: start', 'Kafka Configured successfully !!');
            }
        });
        this.L.log("start", "Mongo DB connectivity!!")

        // await promisify(this.mongoDbInstance.connect.bind(this.mongoDbInstance))();

        this.L.log("Mongo DB Connected!!")
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.dwhRealTimeSmsParsing) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return "SMS_PARSING_DWH";
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next =>{
                self.notify.configureKafkaPublisher((err) => {
                    return next(err);
                })
            },
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:AUTOMATIC_SYNC_PRODUCER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:CT_EVENTS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:NON_PAYTM_RECORDS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.dataExhaustKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NONRU_NOTIFICATION_REALTIME.HOSTS
                });
                this.dataExhaustKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchRealTimeKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchRealTimeKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH_REALTIME", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH_REALTIME', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.planValidityKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_SYNC_DB.HOSTS
                });
                this.planValidityKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:PLAN_VALIDITY_SYNC_DB_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.commonPvPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_NOTIFICATION.HOSTS
                    //_.get(this.config.PLAN_VALIDITY_NOTIFICATION, ["CONSUMER_SCHEDULER", 3], null)
                });
                this.commonPvPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : SMS_PARSING_BILL_PAYMENT');

                let kafkaConsumerObj;

                if(self.dwhRealTimeSmsParsing) {
                    kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_BILL_PAYMENT_DWH_REALTIME.HOSTS'),
                        "groupId": "smsParsingBillPaymentDwhRealTime-consumer-v2",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_BILL_PAYMENT_DWH_REALTIME.TOPIC'),
                        "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                    };
                } else {
                    kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_BILL_PAYMENT.HOSTS'),
                        "groupId": "smsParsingBillPayment-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_BILL_PAYMENT.TOPIC'),
                        "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                    };
                }
                self.kafkasmsParsingBillPaymentConsumer = new KafkaConsumer(kafkaConsumerObj);
                self.kafkasmsParsingBillPaymentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:SMS_PARSING_BILL_PAYMENT_CONSUMER','SOURCE:MAIN_FLOW']);
                    }
                    if (!error) {
                        self.L.log("configureKafka", "consumer of topic : SMS_PARSING_BILL_PAYMENT Configured");
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records,resolveOffset , topic , partition , cb) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
        } else {
            self.L.critical('SMS_PARSING_BILL_PAYMENT:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return cb();
        }


        self.L.log('SMS_PARSING_BILL_PAYMENT:execSteps:: ', `Processing ${records.length} SMS Parsing Bill Payment data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            async (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("smsParsingBillPayment", records, topic , partition);

                    await resolveOffset(lastMessage.offset)
                    self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));


                    // Resume consumer now


                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'debuglog: per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:EMI_DUE_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        return cb();
                    }, self.kafkaBatchDelay);
                }) 
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                }
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        self.L.info("Record received: ",record);
        let published_time = Number(_.get(record, 'timestamp', null));
        try {
            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.critical('SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }

        let record_data = _.get(record, 'data', null);

        if (record_data.length < 1) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
            self.L.log(`smsParsingBillPayment :: Empty sms Data found`);
            return done();
        }

        let level_2_category = record_data[0].level_2_category;

        if(!level_2_category || level_2_category == 12 || level_2_category == 4 || level_2_category == 10){
            self.L.log(`smsParsingBillPayment :: rejecting flow as per data level_2_category : ${level_2_category}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:CLASS_12', 'SOURCE:MAIN_FLOW']);
            if (self.saveForAnalyticsInCassandraAndKafka && self.allowedClassIdsForAnalytics(level_2_category)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record_data[0], null, null),`rejecting flow as per data level_2_category : ${level_2_category}`,done,false);
            return done();
        }

        let telecom_model_version = _.get(record_data[0], ['telecom_details','telecom_model_version'], null);
        if (level_2_category == 11 || (!telecom_model_version && level_2_category == 5)) {
            self.L.log(`smsParsingBillPayment :: executing postpaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    _.set(smsData, 'published_time', published_time);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:TRAFFIC','PAYTYPE:POSTPAID', `SERVICE:MOBILE`, `LEVEL_2_CATEGORY:${level_2_category}`, `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(smsData)}`]);
                    if(self.dwhRealTimeSmsParsing)smsData.isDwhSmsParsingRealtime = true;
                    self.postpaidSmsParsing.executeStrategy(() => {
                        return next();
                    }, smsData, self);
                },
                err => {
                    if (err) {
                        self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    }
                    return done();
                }
            )
        }
        else {
            //return self.prepaidSmsparsing.executeStrategy(record.data[0], self);
            self.L.log(`smsParsingBillPayment :: executing prepaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    _.set(smsData, 'published_time', published_time);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:TRAFFIC', `SERVICE:MOBILE`,'PAYTYPE:PREPAID', `LEVEL_2_CATEGORY:${level_2_category}`, `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(smsData)}`]);
                    self.prepaidSmsparsing.executeStrategy(() => {
                        return next();
                    }, smsData, self);
                },
                err => {
                    if (err) {
                        self.L.critical('SMS_PARSING_PREPAID:processData', `Invalid Kafka record received`, err);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    }
                    return done();
                }
            )
        }
    }

     


    /**
    * run mongoDb query on recents after specified time, 
    *  retry if query faied after specified time
    */
    async mongoThrottleWapper(queryObj, retryCount = 0) {
        try {
            let sleep = (this.getTimeInMs() - this.lastMongoFetchTime);

            if (sleep < this.timeThresholdForMongo) {
                await this.commonLib.calledAfterTime(this.timeThresholdForMongo - sleep);
            }

            let data = await this.runMongoQuery(queryObj);

            this.L.verbose("mongoThrottleWapper: data from mongo", data);

            this.lastMongoFetchTime = this.getTimeInMs();

            return data;
        } catch (error) {
            this.L.error(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
            this.lastMongoFetchTime = this.getTimeInMs();

            /**
             * you can apply checks on error message, what ever here we are fetching data, so there should be no issue on fetching
             * if there is possible some other issue 
             */

            if (retryCount < this.retryCountForMongo) {
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            } else {
                this.L.critical(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
                await this.commonLib.calledAfterTime(this.mongoDbFetchRecordsFailureRetryInterval);
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            }
        }
    }

    runMongoQuery(queryObj) {
        return new Promise((resolve, reject) => {

            this.L.verbose("runMongoQuery: ", queryObj);

            this.mongoDbInstance.fetchDataFromCollection((err, results) => {
                if (err) {
                    let stats = { type: "MONGO_QUERY_FAILED", STATE: "ERROR", count: 1 }
                    this.publishStats(stats);
                    reject(err);
                } else {
                    resolve(results);
                }
            }, this.mongoCollection, queryObj);
        });
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    suspendOperations() {

        var self = this,
            deferred = Q.defer();
        self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkasmsParsingBillPaymentConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`smsParsingBillPayment::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`smsParsingBillPayment::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`smsParsingBillPayment::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source = self.dwhRealTimeSmsParsing ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'cId', null);
        recordForAnalytics.service = null;
        recordForAnalytics.recharge_number = _.get(record, 'smsReceiver', null);
        recordForAnalytics.operator = _.get(record, 'smsOperator', null);
        recordForAnalytics.due_amount = _.get(record, 'dueAmt', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id=_.get(record, 'smsUUID', null);
        recordForAnalytics.paytype=_.get(record, 'payType', null);
        recordForAnalytics.updated_at=_.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id=_.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time=self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null));
        recordForAnalytics.dwh_class_id=_.get(record, 'level_2_category', null);
        return recordForAnalytics;
    }

    getEpochminiToTimestampString(time) {
        if(typeof time == "number" || (Number(time) != NaN && Number(time) > 0)){
            return MOMENT(Number(time)).format('YYYY-MM-DD HH:mm:ss');
        }
        return MOMENT().format('YYYY-MM-DD HH:mm:ss') 
    }

    allowedClassIdsForAnalytics(classId) {
        let self = this;
        let allowedClassIdsForAnalytics = _.get(self.config, ['DYNAMIC_CONFIG', 'SMSPARSING', 'AllowedDwhClassIdsForAnalytics','classIds'],["1","5","6","8","11"]);
        if(allowedClassIdsForAnalytics.includes(classId)) return true;
        return false;
    }
}


export default smsParsingBillPayment;
