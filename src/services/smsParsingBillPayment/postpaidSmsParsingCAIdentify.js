import postpaidSmsParsing from "./postpaidSmsParsing";
import utility from '../../lib';
import _ from 'lodash';
import MOMENT from 'moment';


class PostpaidSmsParsingCAIdentify extends postpaidSmsParsing{
    constructor(options) {
        super(options)
    }

    async getForwardActionFlow(done, processedRecord){
        let self = this;
        self.L.log('4. getForwardActionFlow:: starting getForwardActionFlow');
        self.getRecordsFromDb((err, recordsFound) =>{
            if(err){
                self.L.error(`SMS_PARSING_POSTPAID: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    'STATUS:ERROR', 
                    'TYPE:ERROR_GETTING_RECORD_FROM_DB', 
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'SOURCE:getForwardActionFlow',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null),`ERROR in getRecordsFromDb with error: ${err}`,done);
                return done(err);
            }else{
                if(recordsFound){
                    self.L.log(`processRecord:: Record found in Mysql for CA Identify case, so skipping this record ${processedRecord.debugKey}`);    
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        'TYPE:CA_IDENTIFY_IGNORE', 
                        'SOURCE:getForwardActionFlow',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                    ]);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"),`DueDate or Amount found as null`,done);
                    return done('Skipping as record found for CA Identify case');
                }else{
                    self.L.log(`processRecord:: No recordsFound in Mysql DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndUpdateToCassandra' , processedRecord);
                }
            }
        }, processedRecord);

    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME_CA_IDENTIFY"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME_CA_IDENTIFY':'SMS_PARSING_DWH_CA_IDENTIFY';
    }

    async updateCassandra(done, processedRecord) {
        let self = this;
        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let extra = {};
            extra.eventState = "bill_gen";
            extra.billSource = "sms_parsed";
            extra.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.created_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);

            if(_.get(processedRecord,'isRuSmsParsing', false)){
                extra.isRuSmsParsing = true;
            }
            if(_.get(processedRecord,'isDwhSmsParsing',false)){
                extra.isDwhSmsParsing = true;
            } else if(_.get(processedRecord,'isDwhSmsParsingRealtime',false)) {
                extra.isDwhSmsParsingRealtime = true;
            }

            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.amount,
                dueDate : MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss') : null,
                billDate :  MOMENT().format('YYYY-MM-DD HH:mm:ss'),   
                billFetchDate : MOMENT(processedRecord.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                paytype: processedRecord.paytype,
                service: processedRecord.service,
                circle: processedRecord.circle,
                categoryId: _.get(self.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
                customer_mobile:  null,
                customer_email: null,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
                customerOtherInfo: _.get(processedRecord,['billsData','customerOtherInfo'],'{}'),   
                extra : JSON.stringify(extra),                        
                dbEvent: "upsert",
                dwhClassId: _.get(processedRecord, 'dwhClassId', null),
                rtspClassId: _.get(processedRecord, 'rtspClassId', null),
                source : self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord),
                toBeNotified: false
            }

            if(processedRecord.partialRecordFound){
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                    `SERVICE:${processedRecord.service}`, 
                    "STATUS:SUCCESS",
                    'TYPE:PARTIAL_RECORD', 
                    'OPERATOR:' + processedRecord.operator,
                    `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                ]);
                _.set(dataToBeInsertedInDB, 'partialSmsFound',true);
            }

            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: JSON.stringify(dataToBeInsertedInDB)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_POSTPAID :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(dataToBeInsertedInDB), error);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"),'Error while publishing message in Kafka',done);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('SMS_PARSING_POSTPAID :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(dataToBeInsertedInDB));
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"),`updateCassandra error: ${error}`,done);
            done(error);
        }
    }

}
export default PostpaidSmsParsingCAIdentify;