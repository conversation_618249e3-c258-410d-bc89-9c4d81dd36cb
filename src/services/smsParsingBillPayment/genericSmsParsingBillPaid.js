import CATALOGVERTICALRECHARGE from '../../models/catalogVerticalRecharge';
import BILLS from '../../models/bills';
import recentBillLibrary from '../../lib/recentBills';
import utility from '../../lib';
import MOMENT from 'moment';
import <PERSON><PERSON><PERSON> from 'async';
import RecentsLayerLib from '../../lib/recentsLayer';
import _ from 'lodash';
import VALIDATOR from 'validator';
import BILLS_SUBSCRIBER from '../billSubscriber';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import genericSmsParsing from './genericSmsParsing';
import DynamicSmsParsingRegexExecutor from '../smsParsingBillPayment/dynamicSmsParsingRegexExecutor'

class genericSmsParsingBillPaid {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.categoryId = options.categoryId;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], ['credit card']);
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.rechargeNumberAlreadySeen = []
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime = new Date().getTime();       // Time at which RU reads from the KAFKA
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.postpaidSmsParsing = new genericSmsParsing(options, this);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    getOriginOfPayloadCurrentlyBeingProcessed() {
        let self = this;
        if (self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return 'SMS_PARSING_DWH';
    }

    initializeVariable() {
        this.L.verbose("Reinitializing variables")
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
    }

    executeStrategy(done, record, kafkaTopic, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime = new Date().getTime();       // Time at which RU reads from the KAFKA
        let category = kafkaTopic;
        self.L.log('1. executeStrategy:: start executing');




        try {
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecord(record, kafkaTopic, function (err) {
                if (err) {
                    self.L.error(`GENERIC_SMS_PARSING_BILL_PAID :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                        `SERVICE:${category}`,
                        'STATUS:PROCESSING_ERROR',
                        'SOURCE:POSTPAID_SMS_BILL_PAID',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`GENERIC_SMS_PARSING_BILL_PAID :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                `SERVICE:${category}`,
                'STATUS:PROCESSING_ERROR',
                'SOURCE:POSTPAID_SMS_BILL_PAID',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                `APP_VERSION:${_.get(record, 'appVersion', null)}`
            ]);
            return done();
        }
    }

    async processRecord(record, kafkaTopic, done) {
        let self = this;
        let category = kafkaTopic;
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));

        let configuration = null;
        if (!self.smsParsingBillsDwhRealtime) {
            configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', kafkaTopic], null);
            if (!configuration) {
                configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', _.get(record, 'category', null)], null);
            }
        }
        else {
            configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', kafkaTopic], null)
            if (!config) {
                configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', _.get(record, 'category', null)], null);
            }
        }

        try {
            ASYNC.waterfall([
                (next) => {
                    self.validateAndProcessRecord(async (errorResponse, processedRecord) => {
                        let operator = processedRecord.operator || "NoOpertor";
                        if (errorResponse) {
                            self.L.error(`GENERIC_SMS_PARSING_BILL_PAID :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:ERROR',
                                'TYPE:VALIDATION_FAILURE',
                                'OPERATOR:' + operator,
                                'REASON:' + errorResponse,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            next(errorResponse, processedRecord);
                        } else {
                            self.L.log(`GENERIC_SMS_PARSING_BILL_PAID :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS',
                                'OPERATOR:' + operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            next(null, processedRecord);
                        }
                    }, record, kafkaTopic, configuration);
                },
                (processedRecord, next) => {
                    self.getForwardActionFlow((err, action) => {
                        if (err) {
                            self.L.error(`GENERIC_SMS_PARSING_BILL_PAID :: getForwardActionFlow`, `invalid action found for: ${processedRecord.debugKey} with error ${err}`);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', `SERVICE:${category}`, 'STATUS:NO_ACTION', `ORIGIN:${self.smsParsingBillsDwhRealtime == true ? "SMS_PARSING_DWH_REALTIME" : (_.get(record, 'isRuSmsParsing', false) == true ? 'SMS_PARSING_REALTIME' : 'SMS_PARSING_DWH')}`]);
                            next(err, processedRecord)
                        } else {
                            self.L.log(`6. GENERIC_SMS_PARSING_POSTPAID_BILL_PAID :: getForwardActionFlow`, `action: ${action}`);
                            next(null, action, processedRecord);
                        }
                    }, processedRecord)
                },
                (action, processedRecord, next) => {
                    if (action == 'findAndCreateToCassandra') {
                        if (processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId) {
                            self.L.log(`GENERIC_SMS_PARSING_BILL_PAID :: updateCassandra | Record found for same RN,but with new custId`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:RECORD_NOT_FOUND_OF_SAME_CID',
                                'TYPE:NON_PAYTM_EVENTS',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                        }
                        self.updateCassandra((err) => {
                            if (err) {
                                self.L.error(`GENERIC_SMS_PARSING_BILL_PAID :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err, processedRecord);
                            } else {
                                self.L.log(`GENERIC_SMS_PARSING_BILL_PAID ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, processedRecord);
                            }
                        }, processedRecord, kafkaTopic, configuration);
                    }
                    else {
                        next(null, processedRecord);
                    }
                },
                (processedRecord, next) => {
                    let service = _.get(processedRecord, 'service', null);
                    let source = `SMS_${service}_POSTPAID`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err) => {
                        next(null, processedRecord);
                    }, source, self.timestamps, processedRecord.operator, processedRecord);
                },
            ], async function (error, processedRecord) {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:PROCESS_RECORD_FAILURE',
                        'SOURCE:POSTPAID_SMS',
                        'TYPE:' + error,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ]);
                    self.L.verbose(`GENERIC_SMS_PARSING_BILL_PAID :: processRecords`, `Exception occured Error Msg:: ${error}`);
                } else {
                    self.L.log(`GENERIC_SMS_PARSING_BILL_PAID :: processRecords`, `Record processed `);
                }
                return done();
            });
        } catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }
    }

    async updateCassandra(done, processedRecord, kafkaTopic, configurations) {
        let self = this;
        let extra = {};
        extra.eventState = "bill_paid";
        extra.billSource = "sms_parsed";
        extra.updated_data_source = "SMS_PARSING_DWH_PAYMENT";
        extra.created_source = "SMS_PARSING_DWH_PAYMENT";
        if (self.smsParsingBillsDwhRealtime) {
            extra.updated_data_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
            extra.created_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
        }

        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let dataToBeInsertedInDB = {
                customerId: _.get(processedRecord, 'customerId', null),
                rechargeNumber: _.get(processedRecord, 'rechargeNumber', null),
                productId: _.get(processedRecord, 'productId', null),
                operator: _.get(processedRecord, 'operator', null),
                amount: _.get(processedRecord, 'currentPaidAmount', 0),
                dueDate: _.get(processedRecord, 'dueDate', null) ? MOMENT(processedRecord.dueDate).format() : null,
                billDate: _.get(processedRecord, 'billDate', null) ? (MOMENT(processedRecord.billDate).format()) : null,
                billFetchDate: _.get(processedRecord, 'billFetchDate', null) ? MOMENT(processedRecord.billFetchDate).format() : null,
                paytype: _.get(processedRecord, 'paytype', null),
                service: _.get(processedRecord, 'service', null),
                circle: _.get(processedRecord, 'circle', null),
                customer_mobile: _.get(processedRecord, 'customerMobile', null),
                customer_email: _.get(processedRecord, 'customerEmail', null),
                status: _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14),
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
                customerOtherInfo: JSON.stringify(_.clone(processedRecord)),
                extra: JSON.stringify(extra),
                paymentDate: _.get(processedRecord, 'paymentDate', null),
                dbEvent: "findAndCreate",
                source: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)
            }
            if (configurations.KAFKA_NON_PAYTM_ENABLE && configurations.KAFKA_NON_PAYTM_ENABLE == 1) {
                return self.postpaidSmsParsing.pushToNonPaytm(done, dataToBeInsertedInDB, processedRecord, "SMS_PARSING_POSTPAID_BILL_PAID", self.parent);
            }
            done(null);

        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            done(error);
        }
    }

    async getForwardActionFlow(done, processedRecord) {
        let self = this;
        self.L.log('5. getForwardActionFlow:: starting getForwardActionFlow');
        self.postpaidSmsParsing.getRecordsFromDb((err, recordsFound) => {
            if (err) {
                self.L.error(`GENERIC_SMS_PARSING_BILL_PAID: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                return done(err);
            } else {
                if (processedRecord.recordFoundOfSameCustId) {
                    self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :`, `for the processedRecord: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`,
                        'STATUS:SUCCESS',
                        'TYPE:RECORD_FOUND_IN_DB',
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                    ]);
                    return done('record found in table');
                }
                else {
                    self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndCreateToCassandra', processedRecord);
                }
            }
        }, processedRecord);
    }

    validateAndProcessRecord(done, record, kafkaTopic, configuration) {
        let self = this;
        let dateFormat = 'YYYY-MM-DD';

        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        if (!record) return done('invalid_record', record);
        if (self.smsParsingBillsDwhRealtime == true && (typeof _.get(record, 'smsDateTime', null) == "string") && _.get(record, 'smsDateTime', null) != null) {
            _.set(record, 'smsDateTime', parseInt(_.get(record, 'smsDateTime', null)));
        }
        if (_.get(record, 'smsDateTime', null)) {
            if (Number(record.smsDateTime) && record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000
            }
        }




        let smsDateTime_fromPayload = Number(record.smsDateTime);
        const timestamp = new Date(record.timestamp).getTime(),
            smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
            smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
            smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
            deviceDateTime = new Date(record.deviceDateTime).getTime(),
            uploadTime = new Date(record.uploadTime).getTime(),
            collector_timestamp = new Date(record.collector_timestamp).getTime();
        _.set(self.timestamps, 'data_smsDateTime', smsDateTime);
        _.set(self.timestamps, 'data_timestamp', timestamp);
        _.set(self.timestamps, 'data_deviceDateTime', deviceDateTime);
        _.set(self.timestamps, 'data_uploadTime', uploadTime);
        _.set(self.timestamps, 'collector_timestamp', collector_timestamp);
        _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
        _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
        _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);

        if (_.get(record, 'smsDateTime', null) == null || _.get(record, 'smsDateTime', null).toString().length == 0) {
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }
        let customerId = typeof _.get(record, 'cid') === 'number'
            ? _.get(record, 'cId')
            : (typeof _.get(record, 'cId') === 'string' && VALIDATOR.isNumeric(_.get(record, 'cId')))
                ? VALIDATOR.toInt(_.get(record, 'cId'))
                : null,
            amount = _.get(record, 'amount', null) ? utility.getFilteredAmount(_.get(record, 'amount', null)) : null,
            dueDate = utility.getFilteredDate(_.get(record, 'dueDate', null)).value,
            billDate = utility.getFilteredDate(_.get(record, 'billDate', null)).value || MOMENT(record.smsDateTime),

            operator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'GENERIC_DWH_OPERATOR_MAPPING', operator], _.get(record, 'operator', null)));

        let paymentDate = record.smsDateTime ? new Date(record.smsDateTime) : null;
        paymentDate = MOMENT(paymentDate, dateFormat, true).isValid() ? MOMENT(paymentDate, dateFormat) : null;
        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber": record.rechargeNumber,
            "gateway": null,
            "billFetchDate": null,
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null,
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "currentPaidAmount": -amount,
            "status": _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14),
            "paytype": 'postpaid',
            "customerMobile": null,
            "customerEmail": _.get(record, 'smsReceiverEmail', null),
            "extra": null,
            "paymentDate": paymentDate,
            "msgId": _.get(record, 'msg_id', ''),
            "senderId": _.get(record, 'smsSenderID', null),
            "category": _.get(record, 'category', null),
            "appVersion": _.get(record, 'appVersion', null),
            "smsSenderID": _.get(record, 'smsSenderID', '')
        };


        if (operator) {
            _.set(processedRecord, 'productId', _.get(self.config, ['DYNAMIC_CONFIG', `GENERIC_${self.categoryId}_SMS_PARSING`, operator, 'PRODUCT_ID'], null));
        }

        let rechargeNumber2 = _.toLower(_.get(record, 'rechargeNumber2', null));
        let pidMapKey = (operator + (rechargeNumber2 != null && rechargeNumber2 != '' ? `_${rechargeNumber2}` : '')).replace(/ /g, '_');
        self.postpaidSmsParsing.setPidAttributes(processedRecord, record, pidMapKey, "GENERIC_SMS_PARSING_BILL_PAID");

        self.postpaidSmsParsing.checkCommonValidationForRecord(processedRecord, record, configuration).
            then((processedRecord) => {
                let activePid = self.activePidLib.getActivePID(processedRecord.productId);
                self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
                processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
                processedRecord.productId = activePid;    // Replacing active PID

                let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null);

                if (!tableName) {
                    self.L.error(`processRecord:: ${processedRecord.operator} not migrated`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID',
                        `SERVICE:${kafkaTopic}`,
                        'STATUS:ERROR',
                        'TYPE:TABLE_NOT_FOUND',
                        'OPERATOR:' + processedRecord.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ]);
                    return done(`Table not found for ${processedRecord.operator}`, processedRecord);
                }
                self.L.log(`4. processRecord:: table_name found for operator: ${processedRecord.operator}:${tableName}`);
                _.set(processedRecord, 'tableName', tableName);

                return done(null, processedRecord);
            }).catch((err) => {
                done(err, processedRecord);
            });

    }


    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'category', null) ? _.get(record, 'category', null).toLowerCase() : null;

        return key;
    }


}

export default genericSmsParsingBillPaid;