

import utility from '../../lib';
import MOMENT from 'moment';
import _ from "lodash";
import OAuth from '../../lib/oauth';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import <PERSON>YNC from 'async';
import BILLS from '../../models/bills';
//import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills';
import RecentsLayerLib from '../../lib/recentsLayer';
import digitalUtility from 'digital-in-util';
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard';
import BillFetchAnalytics from '../../lib/billFetchAnalytics';
import Logger from '../../lib/logger';
import EncryptionDecryptioinHelper from '../../lib/EncryptionDecryptioinHelper';

class processCreditCard {
    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
        this.tableName = 'bills_creditcard';
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        //this.smsParsingSyncCCBillLib = new ref.SmsParsingSyncCCBillLibrary(options);
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        // this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.service = 'financial services';
        this.logger = new Logger(options);
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
    }
     executeStrategy(done,record,ref) { 
        let self = this;
        this.parent = ref; 
        self.timestamps = {};
        try {
            if(!record) {
                return done();
            }
            self.processRecords(record, function(err){
                if(err){
                    self.logger.error('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:: Error for record :', {record, err}, self.service);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'SOURCE:CC_STRATEGY']);
                }
                return done();
            });
        }
        catch (err) {
            self.logger.error('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:: Error for record :', {record, err}, self.service);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'SOURCE:CC_STRATEGY']);
            return done();
        }

    }

    initializeAnalyticsPayload(){
        return  {
            source: "SMS_PARSING_DWH_PAYMENT",
            source_subtype_2: "FULL_BILL",
            user_type: null,
            customer_id: null,
            service: "financial services",
            recharge_number: null,
            operator: null,
            due_amount: null,
            sender_id: null,
            updated_at: null,
            sms_date_time: null,
            sms_id: null,
            sms_class_id: null,
            paytype: "credit card"
        }
    }

    processRecords(record, done) {   
        let self = this;
        let analyticsPayload = this.initializeAnalyticsPayload();

        try {
            let [error, processedRecord] = self.validateAndProcessRecord(record, analyticsPayload);
            
            if (error) {
                self.logger.error("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY::processRecords Invalid record received", {record, error}, self.service);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:VALIDATION_FAILURE', 'SOURCE:CC_STRATEGY']);
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload, error)
                    .then(() => {
                        return done(error);
                    });
            }
    
            self.logger.log(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY::Processing with debug key ${processedRecord.debugKey}`, record, self.service);
            
            ASYNC.waterfall([
                next => {
                    self.getActionforCCBills(function (error, action, dbRecordResp , message) {
                        if(error) { 
                            self.L.error('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:processRecords::getActionforCCBills', `Unable to get valid SingleMatchingCardByCustomer for ${processedRecord.debugKey} with reason:${error}`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_ACTION', 'SOURCE:CC_STRATEGY']);
                            return next(error);
                        } else {
                            if(dbRecordResp) {
                                analyticsPayload.operator = _.get(dbRecordResp, "operator", null);
                                analyticsPayload.paytype = _.get(dbRecordResp, "paytype", null);
                                processedRecord.debugKey = `${processedRecord.debugKey}_Id:${dbRecordResp.id}_MCN:${self.encryptionDecryptionHelper.encryptData(dbRecordResp.recharge_number)}_operator:${dbRecordResp.operator}`;
                            }
                            self.L.log('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:processRecords::getActionforCCBills', `action:${action}_debugkey:${processedRecord.debugKey}${message ? '_msg:'+message : ''}`);
                            return next(null, action, dbRecordResp)
                        }
                    }, processedRecord, analyticsPayload)
                },
                (action, dbRecordResp, next) => {
                    if(_.get(dbRecordResp,'is_automatic',0) === 0  && action == 'update') {
                        self.updateCCBill(function (error) {
                            if(error) {
                                return next(error);
                            } else {
                                return next(null, dbRecordResp, action);
                            }
                        }, processedRecord, dbRecordResp, analyticsPayload);
                    }
                    else if (action == 'findAndUpdateToCassandra'){
                        self.updateCassandra(function(error){
                                if(error){
                                    next(error);
                                }else{
                                    _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
                                    return next(null, dbRecordResp , action);
                                }
                        }, processedRecord);
                    } else {
                        self.logger.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY::processRecords No action found for record with debugKey:: ${processedRecord.debugKey}`, record, self.service);
                        return next(null, dbRecordResp , action);
                    }   
                },
                (dbRecordResp, action, next) => {
                    if(action == 'update'){
                        dbRecordResp = self.getDbRecordToUpdate(processedRecord, dbRecordResp);
                        if(dbRecordResp){
                            self.publishCtAndPFCCEvents((error) => {
                                if(error){
                                    return next(error);
                                } else {
                                    return next(null)
                                }
                            }, dbRecordResp);
                        }
                        else {
                            return next(null);
                        }
                    }else{
                        return next(null);
                    }
                },
                next => {
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        return next(err);
                    },'SMS_CC_STRATEGY', self.timestamps, processedRecord.bankName,processedRecord);
                }
            ], function (error) {
                if(error) {
                    self.logger.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY::processRecords Exception occured Error Msg:: ${error} debugKey:: ${processedRecord.debugKey} for record::`, record, self.service);
                    return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload, error)
                        .then(() => {
                            return done(error);
                        })
                } else {
                    self.L.log(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:processRecords`,`Record processed having debug key`, processedRecord.debugKey);
                }
                if(error)
                return done(error);
                else return done();
            });
        } catch(err) {
            self.logger.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY::processRecords Exception occured for record with error ${err}::`, record, self.service);
            return done(err);
        }
    }
    validateAndProcessRecord(record, analyticsPayload = {}) { 
        let self = this;
        if (!record) return ['Invalid record', record];
        let dateFormat = 'YYYY-MM-DD';
        if(_.get(record, 'smsDateTime', null)){
            if(record.smsDateTime.toString().length===10){
                record.smsDateTime=record.smsDateTime*1000
            }}

        // set timestamps to checkk preformance delay on dashboard
        const timestamp = new Date(record.timestamp).getTime(),
               smsDateTime = new Date(record.smsDateTime).getTime(),
               deviceDateTime = new Date(record.deviceDateTime).getTime(),
               uploadTime = new Date(record.uploadTime).getTime(),
               collector_timestamp = new Date(record.collector_timestamp).getTime(),
               dwhKafkaPublishedTime = new Date(record.published_time).getTime();
        _.set(self.timestamps,'data_smsDateTime',smsDateTime);
        _.set(self.timestamps,'data_timestamp',timestamp);
        _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
        _.set(self.timestamps,'data_uploadTime',uploadTime);
        _.set(self.timestamps,'collector_timestamp',collector_timestamp);
        _.set(self.timestamps, 'RUreadsKafkaTime', self.parent.RUreadsKafkaTime);
        _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);

        let paymentDate = record.smsDateTime? new Date(record.smsDateTime) : null;
        let processedRecord = {
            customerId: (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            lastCC: _.get(record, 'card_no', '') + '',
            rawLastCC : _.get(record, 'card_no', null) ,
            currentPaidAmount : self.parseAmount(_.get(record, 'amount', null)),
            paymentDate:  MOMENT(paymentDate, dateFormat,true).isValid() ? MOMENT(paymentDate, dateFormat) : null,
            sender_id: _.get(record, 'smsSenderID', ''), 
            bankName: _.get(record,'bankName', ''),
            msgId : _.get(record, 'msg_id', ''),
            sms_id : _.get(record, 'sms_id', ''),
            sms_date_time : _.get(record, 'smsDateTime', ''),
            dwh_classId : _.get(record, 'dwh_classId', ''),
            dwhKafkaPublishedTime : dwhKafkaPublishedTime,
        };
        analyticsPayload.due_amount = _.get(processedRecord, 'currentPaidAmount', 0);
        analyticsPayload.customer_id = (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null;
        analyticsPayload.recharge_number = _.get(record, 'card_no', '') + '';
        analyticsPayload.rawlastcc = _.get(record, 'card_no', '') + '';

        analyticsPayload.sms_date_time = MOMENT(_.get(record, 'smsDateTime', '')).format('YYYY-MM-DD HH:mm:ss');
        analyticsPayload.sender_id = _.get(record, 'smsSenderID', '');
        analyticsPayload.sms_id = _.get(record, 'msg_id', '');
        analyticsPayload.sms_class_id = _.get(record, "level_2_category");
        analyticsPayload.operator = _.get(record,'bankName', '');
        let mandatoryParams = ['customerId', 'lastCC', 'currentPaidAmount', 'paymentDate', 'bankName'];
        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });
        if(processedRecord['currentPaidAmount'] < 0) invalidParams.push('currentPaidAmount');
        if(!_.get(self.config, ['DYNAMIC_CONFIG','BILL_PAYMENT_CONFIG','ALLOWED_BANKS','BANK_LIST'], null).includes(processedRecord['bankName'])){
            invalidParams.push('bankName');
        }
        if(processedRecord.lastCC) {
            // Lets send lastCC for analytics purpose
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT', `LASTCCLEN:${processedRecord.lastCC.length}`, 'SOURCE:CC_STRATEGY']);
            if(processedRecord.lastCC.length < 2) invalidParams.push(`lastCC-length-${processedRecord.lastCC.length}`);// We  expecting at least last 2 digits
            else if (processedRecord.lastCC.length >= 5) { // greater then equal to 5 digits
                processedRecord.lastCC = processedRecord.lastCC.slice(processedRecord.lastCC.length-4);
            }
        }
        processedRecord.debugKey = `smsSenderID:${processedRecord.smsSenderID}_custId:${processedRecord.customerId}_lastCC:${this.encryptionDecryptionHelper.encryptData(processedRecord.lastCC)}`;

        if (invalidParams.length > 0) return [`Mandatory Params ${invalidParams} is Missing / Invalid`, record];
        else return [null, processedRecord];
    }

    parseAmount(amountStr) {  
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
        // case of "Rs.x.y" i.e. "Rs.101.54"
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        if(parsedAmount) return parsedAmount;
        //case of "x.y" i.e. "101.54"
        let foundMatch2 = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount2 = (foundMatch2 && foundMatch2[1]) ? VALIDATOR.toFloat(foundMatch2[1]) : null;
        return parsedAmount2;
    }

    getActionforCCBills(done, processedRecord, analyticsPayload = {}) {  
        let self = this;
        self.bills.getBillByCustomer(function (error, records) {
            if (error) {
                self.L.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:checkOneCardForCustomer`, `failed with error for ${processedRecord.debugKey} and error:${error}`);
                return done(error);
            } else if (records && _.isArray(records) && records.length > 0) {
                let last4DigitsMatchingRecords = [];
                for (let index = 0; index < records.length; index++) {
                    let rechargeNumber = records[index].recharge_number.replace(/\s+/g, ''); // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                    if (rechargeNumber && rechargeNumber.substr(rechargeNumber.length - processedRecord.lastCC.length) == processedRecord.lastCC) {
                        last4DigitsMatchingRecords.push(records[index]);
                    }
                }

                if (last4DigitsMatchingRecords.length === 0) {
                    analyticsPayload.user_type = "NON_RU";
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_MATCHING_MCN(findAndUpdateToCassandra)', 'SOURCE:CC_STRATEGY']);
                    return done(null, 'findAndUpdateToCassandra' , null ,  "No records found with given last4Digits");
                } else if (last4DigitsMatchingRecords.length === 1) {
                    analyticsPayload.user_type = "RU";
                    let skipRecord = self.shouldRecordBeSkipped(last4DigitsMatchingRecords[0], processedRecord);
                    if(skipRecord) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:SKIPPING_MCN', 'SOURCE:CC_STRATEGY']);
                        return done("Record cant be updated due to either record already updated or bill has not generated yet !!");
                    }

                    if(self.parent.smsParsingSyncCCBillLib.isPaytmFirstCCFromRechargeNumber(last4DigitsMatchingRecords[0])){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:PAYTM_FIRST_CC_MCN', 'SOURCE:CC_STRATEGY']);
                        return done('Skipping Paytm First CC update');
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:MATCHING_MCN', 'SOURCE:CC_STRATEGY']);
                        return done(null, 'update', last4DigitsMatchingRecords[0] , null );
                    }
                } else {
                    analyticsPayload.user_type = "RU";
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:MULTIPLE_MCN', 'SOURCE:CC_STRATEGY']);
                    self.parent.smsParsingSyncCCBillLib.checkEncryptedCards((err,data)=>{
                        if(err){
                            return done(err);
                        }else{
                            return done(null, 'update', data , null );
                        }
                    },last4DigitsMatchingRecords)
                }
            } else {
                analyticsPayload.user_type = "NON_RU";
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_RECORD(findAndUpdateToCassandra)', 'SOURCE:CC_STRATEGY']);
                return done(null, 'findAndUpdateToCassandra' , null ,  "No records found");
            }
        }, self.tableName, processedRecord.customerId);
    }
    shouldRecordBeSkipped(dbRecord, processedRecord){   
        let self = this;
        try {
            const graceDays = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_DAYS'], 5)
            const graceAmount = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_AMOUNT'], 5)
            let extra = _.get(dbRecord, 'extra', '{}');
            if(typeof extra=='string') extra = JSON.parse(extra);
            const paidAmount = extra.last_paid_amount ? Math.round(extra.last_paid_amount): null;
            let smsAmount = Math.round(_.get(processedRecord, 'currentPaidAmount', 0));
            let dbpaymentDate = _.get(dbRecord, 'payment_date', null) ? MOMENT(_.get(dbRecord, 'payment_date', null)).utc(): null;
            let dbDueDate = _.get(dbRecord, 'due_date', null) ? MOMENT(_.get(dbRecord, 'due_date', null)).utc() : null;
            let paymentDate = (_.get(processedRecord, 'paymentDate', null))? MOMENT(_.get(processedRecord, 'paymentDate', null)).utc() : null;
            let billFetchDate = _.get(dbRecord, 'bill_fetch_date', null);

            var loggingKey = `customerId:${processedRecord.customerId}_lastCC:${this.encryptionDecryptionHelper.encryptData(processedRecord.lastCC)}`;

            if(dbpaymentDate==null || paidAmount==null || dbDueDate==null  || paymentDate.diff(dbDueDate, 'days')-30 > 0){
                self.L.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:shouldRecordBeSkipped :: Bill already paid in last x days or Bill has not generated yet", loggingKey);
                return true
            }
            if(paymentDate.diff(dbpaymentDate, 'days') < graceDays && Math.abs(paidAmount - smsAmount) < graceAmount){
                self.L.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:shouldRecordBeSkipped :: Bill already paid in last x days or Bill has not generated yet", loggingKey);
                return true;
            }
            if(MOMENT(paymentDate).utc().diff(MOMENT(dbpaymentDate).utc()) < 0){
                self.L.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:shouldRecordBeSkipped ::Payment date in DB > payment date recieved", loggingKey);
                return true;
            }
            if(billFetchDate && MOMENT(paymentDate).utc().diff(MOMENT(billFetchDate).utc()) < 0){
                self.L.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:shouldRecordBeSkipped ::Bill Fetch date in DB > payment date recieved", loggingKey);
                return true;
            }
            return false
        } catch (error) {
            self.L.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:shouldRecordBeSkipped :: unable to get previous amount paid ", loggingKey);
            return true
        }
    }
    updateCCBill(done, processedRecord, dbRecord, analyticsPayload = {}) {
        let self = this;
        try {
            dbRecord = self.getDbRecordToUpdate(processedRecord, dbRecord);
            if(dbRecord) return self.updateCCBillInSystem(done, processedRecord, dbRecord, analyticsPayload);
            else return done(null);
        } catch(err) {
            self.L.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:updateCCBill`,`Exception occured for record having debugkey:${processedRecord.debugKey}`,err)
            return done(err);
        }
    }
    checkUniquenessInRecents(done, params, processedRecord) {  
        let self = this;
        return done(null, null); // As of now we are not using recents for CC

        // let recentUniqueRecord;
        // self.recentsLayerLib.getCCDetailsByMcnCustId(function (error, data) {
        //     if (error) {
        //         self.L.error('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:checkUniquenessInRecents', `Error updating recents for ${processedRecord.debugKey}_error:${error}`);
        //         return done(error);
        //     } else {
        //         let cinFromReminderDb =  _.get(params, 'par_id', null) ? null : _.get(params,'reference_id',null);
        //         let panUniqueReference = _.get(params, 'par_id', null);
        //         let lastFourMCN = _.get(params,'recharge_number').substr(-4);
        //         data = data.filter(row => row.recharge_number.substr(-4) == lastFourMCN); // removing other CC of customer
        //         for (let index = 0; index < data.length; index++) {
        //             //need to check PAR as well as reference ID
        //             if(panUniqueReference && panUniqueReference != data[index].panUniqueReference) return done(`different PAN:${data[index].panUniqueReference} exists`);
        //             if(panUniqueReference && panUniqueReference == data[index].panUniqueReference){
        //                 recentUniqueRecord =  data[index]
        //             }

        //             if(cinFromReminderDb && cinFromReminderDb != data[index].cin) return done(`different CIN:${data[index].cin} exists`);
        //             if(cinFromReminderDb && cinFromReminderDb == data[index].cin && !recentUniqueRecord){ // cant overwrte PAN record
        //                 recentUniqueRecord =  data[index]
        //             }
        //         }
        //         return done(null, recentUniqueRecord);
        //     }
        // }, params, "smsParsingBillPayment");
    }
    getDbRecordToUpdate(processedRecord, dbRecord) {     
        let self = this;
        let updatedDbRecord = _.clone(dbRecord), dateFormat = 'YYYY-MM-DD HH:mm:ss';
        try{
            if(_.get(updatedDbRecord, 'extra', null)==null || _.get(updatedDbRecord, 'extra', null)==''){
                updatedDbRecord.extra = {};
            }
            if(_.get(updatedDbRecord, 'customerOtherInfo', null)==null || _.get(updatedDbRecord, 'customerOtherInfo', null)==''){
                updatedDbRecord.customerOtherInfo = {};
            }
            if(typeof _.get(updatedDbRecord, 'extra', null)=='string'){
                updatedDbRecord.extra = JSON.parse(updatedDbRecord.extra);
            }
            if(typeof _.get(updatedDbRecord, 'customerOtherInfo', null)=='string'){
                updatedDbRecord.customerOtherInfo = JSON.parse(updatedDbRecord.customerOtherInfo);
            }
        }
        catch(error){
            self.L.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:getDbRecordToUpdate`, `Error in parsing for ${processedRecord.debugKey}_error: ${error} `);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'SOURCE:CC_STRATEGY']);
            return null;
        }
        updatedDbRecord.amount = updatedDbRecord.amount - _.get(processedRecord, 'currentPaidAmount', 0);   
        updatedDbRecord.payment_date = _.get(processedRecord, 'paymentDate', null).format(dateFormat);
        updatedDbRecord.due_date = MOMENT(_.get(updatedDbRecord, 'due_date', '')).isValid() ? MOMENT.utc(_.get(updatedDbRecord, 'due_date', '')).format(dateFormat) : null;
        updatedDbRecord.status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14);
        updatedDbRecord.extra.last_paid_amount = processedRecord.currentPaidAmount;
        updatedDbRecord.extra.updated_data_source = "SMS_PARSING_DWH";
        updatedDbRecord.extra.source_subtype_2 = 'FULL_BILL';
        updatedDbRecord.extra = JSON.stringify(updatedDbRecord.extra);
        try{
            updatedDbRecord.customerOtherInfo.paymentDate = processedRecord.paymentDate.format(dateFormat);
        }catch(err){
            self.L.error("getDbRecordToUpdate:",err)
            _.set(updatedDbRecord,'customerOtherInfo',{})
            utility._sendMetricsToDD(1,["REQUEST_TYPE:SMS_PARSING_BILLPAYMENT","STATUS:ERROR","TYPE:PAYMENT_DATE_SETTING"]);
            updatedDbRecord.customerOtherInfo.paymentDate = MOMENT().format(dateFormat);
        }
        try{
            // updatedDbRecord.customerOtherInfo.paymentDate = processedRecord.paymentDate.format(dateFormat);
            if(_.get(updatedDbRecord, ['customerOtherInfo','currentBillAmount'], null)){
                updatedDbRecord.customerOtherInfo.currentBillAmount = updatedDbRecord.customerOtherInfo.currentBillAmount - _.get(processedRecord, 'currentPaidAmount', 0);}
            if(_.get(updatedDbRecord, ['customerOtherInfo','currentMinBillAmount'], null)){
                updatedDbRecord.customerOtherInfo.currentMinBillAmount = (updatedDbRecord.customerOtherInfo.currentMinBillAmount - _.get(processedRecord, 'currentPaidAmount', 0)) < 0 ? 0 : updatedDbRecord.customerOtherInfo.currentMinBillAmount - _.get(processedRecord, 'currentPaidAmount', 0);}
            updatedDbRecord.customerOtherInfo.sender_id = processedRecord.sender_id;
            updatedDbRecord.customerOtherInfo.debugKey = processedRecord.debugKey;
            updatedDbRecord.customerOtherInfo.msgId = processedRecord.msgId;
            updatedDbRecord.customerOtherInfo.sms_id = processedRecord.sms_id;
            updatedDbRecord.customerOtherInfo.sms_date_time = processedRecord.sms_date_time;
            updatedDbRecord.customerOtherInfo.dwh_classId = processedRecord.dwh_classId;
            updatedDbRecord.customerOtherInfo.rawLastCC = processedRecord.rawLastCC;
            updatedDbRecord.customerOtherInfo = JSON.stringify(updatedDbRecord.customerOtherInfo);
        }catch(err){
            self.L.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:getDbRecordToUpdate`, `ERROR_WHILE_SETTING_CUSTOTHERINFO`);
            utility._sendMetricsToDD(1,["REQUEST_TYPE:SMS_PARSING_BILLPAYMENT","STATUS:ERROR","TYPE:ERROR_WHILE_SETTING_CUSTOTHERINFO"]);
        }

        return updatedDbRecord;
    }
    updateCCBillInSystem(done, processedRecord, dbRecord, analyticsPayload = {}) {   
        let self = this;
        self.bills.updateCCBillPaidByCustomerId(function (error) {
            _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
            if (error) {
                self.L.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:updateCCBillInSystem::updateBillByCustomerId`, `Error updating table for for ${processedRecord.debugKey}_error:${error}`);
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload, error)
                .then(()=>{
                    return done(null);
                })
            } else {
                self.L.log(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:updateCCBillInSystem::updateCCBillByCustomerId`, `Table Updation successful for ${processedRecord.debugKey}`);
            }
            
            return done(null);
        }, self.tableName, dbRecord);
    }
    updateCCBillInRecentSystem(done, processedRecord, dbRecord, recentRecord) {  
        /**
                 * After https://bitbucket.org/paytmteam/digital-reminder/pull-requests/1012/#chg-src/models/users.js PR merge we can directly call users.updateBillsInRecents method.
                 * Not doing as of now to reduce sanity scope
                 */
        let self = this;
        let queryParam = {
                recharge_number: _.get(dbRecord, 'recharge_number', null),
                operator: _.get(dbRecord, 'operator', null),
                paytype: _.get(dbRecord, 'paytype', null),
                service: _.get(dbRecord, 'service', null),
                reference_id: _.get(dbRecord, 'reference_id', null),
                customer_id: _.get(dbRecord, 'customer_id', null),
            }, fieldValue = {
                txnAmount: _.get(processedRecord, 'currentPaidAmount', null),
                txnTime: _.get(processedRecord, 'paymentDate', null),
                txnDetailSource: "paidOutside"
            };
        if(recentRecord && recentRecord.panUniqueReference && recentRecord.panUniqueReference !== '') {
            queryParam.panUniqueReference = recentRecord.panUniqueReference;
        }
        self.recentsLayerLib.update(function (error) {
            self.L.log('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:updateCCBillInSystem::recentsLayerLib.update', `update recents request completed for ${processedRecord.debugKey},error if any is:${error}`);
            return done(null);
        }, queryParam, "txnDetails", [fieldValue], "smsParsingBillPayment");

    }
    publishCtAndPFCCEvents(done, dbRecordResp) {
        let self = this;

        const customerId = _.get(dbRecordResp, 'customer_id', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
        const referenceId = _.get(dbRecordResp, 'reference_id', '')
        const dbEvent = _.get(dbRecordResp, 'dbEvent', null)
        const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'SMS_PARSING_CC_BILL_PAYMENT'], 'ccbillpaidoutsidepaytm')
        const dbDebugKey = `rech:${self.encryptionDecryptionHelper.encryptData(rechargeNumber)}::cust:${customerId}::op:${operator}::ref_id:${self.encryptionDecryptionHelper.encryptData(referenceId)}`;

        if(dbEvent){
            return done(null)
        }

        let productId = _.get(dbRecordResp, 'product_id', '');
        productId = self.activePidLib.getActivePID(productId);
        _.set(dbRecordResp, 'product_id', productId);

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
               
                ASYNC.parallel([
                    function (cb) {

                        if(self.commonLib.isCTEventBlocked(eventName)){
                            self.L.info(`Blocking CT event ${eventName}`)
                            return cb()
                        }
                        
                        if (_.get(dbRecordResp, 'notification_status', 1)) {
                            self.parent.ctKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(mappedData)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', "TYPE:CT_EVENTS","SOURCE:CC_STRATEGY", "OPERATOR:" + operator]);
                                    self.logger.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:publishInKafka :: publishCtEvents Error while publishing message in Kafka ${error} - MSG:-`, clonedData, self.service);  
                                } else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS","SOURCE:CC_STRATEGY", "OPERATOR:" + operator]);
                                    self.logger.log('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:prepareKafkaResponse :: publishCtEvents Message published successfully in Kafka on topic REMINDER_CT_EVENTS', clonedData, self.service);
                                }
                                cb(error);
                            }, [200, 800]);
                        } else {
                            self.logger.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record with debugKey:: ${dbDebugKey}`, dbRecordResp, self.service);
                            cb();
                        }
                    },
                    function (cb) {
                        self.parent.paytmFirstKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.PAYTM_FIRST_CC_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], (error) => {
                            if (error) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', "TYPE:PFCC_EVENTS", "OPERATOR:" + operator]);
                                self.logger.critical(`publishInKafka :: publishPFCCEvents Error while publishing message in Kafka ${error} - MSG:- `, clonedData, self.service);
                            } else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:PUBLISHED', "TYPE:PFCC_EVENTS", "OPERATOR:" + operator]);
                                self.logger.log("prepareKafkaResponse :: publishPFCCEvents Message published successfully in Kafka on topic PAYTM_FIRST_CC", clonedData, self.service);
                            }
                            cb(error);
                        }, [200, 800]);
                    },
                ], function done(err) {
                    return next(err);
                });
            }
        ], error => {
            if(error) {
                self.logger.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:publishCtEvents Exception occured Error Msg:: ${error} for record::`, dbRecordResp, self.service);
            } else {
                self.L.log(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:publishCtEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }
    updateCassandra(done, processedRecord){
        let self = this;
        let newRecordUniqueKey = self.parent.smsParsingSyncCCBillLib.getUniqueKeyForSavedCardsData({
            isPaytmFirstCard: 0,
            bankName: processedRecord.bankName,
            cardScheme: _.get(self.config, 'COMMON.NON_PAYTM_CC.cardScheme', 'dummyNetwork')
        })
        self.parent.smsParsingSyncCCBillLib.getFinancialServicesPID(function (error, productId) {
            if(error) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT' , `STATUS:ERROR` , `TYPE:PRODUCT_NOT_EXISTS_IN_DCAT` ,`SOURCE:CC_STRATEGY`, `DEBUG_KEY_NAME:BANK_CARD_TYPE_UNIQ_KEY` ]);
                self.L.error(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:getFinancialServicesPID`, `Error Msg: ${error} for newRecordUniqueKey ${newRecordUniqueKey} having debugkey:${processedRecord.debugKey}`);    
                return done(error);
            } else {
                self.L.log(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:getFinancialServicesPID`, `productId: ${productId} for newRecordUniqueKey ${newRecordUniqueKey} having debugkey:${processedRecord.debugKey}`); 
                if(!_.has(self.config, ['CVR_DATA', productId, 'operator'])) {
                    self.L.error('SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:', `CVR data not exists for productId:${productId} having debugKey: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT', `DEBUG_KEY_NAME:PRODUCT_ID` , `DEBUG_KEY_VALUE:${productId}` , `STATUS:ERROR` ,`SOURCE:CC_STRATEGY`, `TYPE:CVR_DATA_NOT_EXISTS_FOR_PID` ]);
                    return done(`CVR data not exists for productId:${productId}`);
                }
                let dataToBeInsertedInDB = self.parent.smsParsingSyncCCBillLib.formatDataForUpdateNonPaytmCards({
                    productId : productId,
                    source: "SMSParsingDWH",
                    processedRecord : processedRecord
                });
                dataToBeInsertedInDB.dbEvent = 'findAndUpdateData';
                let extra = {};
                extra.updated_data_source = 'SMS_PARSING_DWH_PAYMENT';
                extra.created_source = 'SMS_PARSING_DWH';
                dataToBeInsertedInDB.extra = JSON.stringify(extra);

                let loggerData = self.parent.smsParsingSyncCCBillLib.formatDataForCustomLogger(dataToBeInsertedInDB); // had to make a separate payload because we are stringifying custotherinfo and extra which will cause problem in encryption

                self.parent.nonPaytmKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
                    messages: JSON.stringify(dataToBeInsertedInDB)
                }], (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR',`SOURCE:CC_STRATEGY`, "TYPE:NON_PAYTM_EVENTS"]);
                        self.logger.critical(`SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:nonPaytmKafkaPublisher Error while publishing message in Kafka ${error} - MSG:- `, loggerData, self.service);
                        return done('Error while publishing message in Kafka');
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:PUBLISHED', "TYPE:NON_PAYTM_EVENTS",`SOURCE:CC_STRATEGY`,"OPERATOR:" + dataToBeInsertedInDB.operator]);
                        self.logger.log("SMS_PARSING_BILL_PAYMENT:CC_STRATEGY:nonPaytmKafkaPublisher Message published successfully in Kafka  on topic NON_PAYTM_EVENTS", loggerData, self.service);
                    }
                })
                return done(null);    
            }
        }, newRecordUniqueKey);
    }

}
export default processCreditCard;



    /*
    Example Payload of Kafka- 
    {
        "data": [
        {
            "data": [
            {
                "appCount": 1,
                "appVersion": "9.6.2",
                "batteryPercentage": 100,
                "brand": "Nokia",
                "cId": "227316905",
                "clientId": "androidapp",
                "collector_timestamp": null,
                "db_name": null,
                "deviceDateTime": 1620903472035,                   ----
                "eventType": "smsEvent",
                "event_name": "sms",
                "msg_id": null,
                "latitude": 0,
                "longitude": 0,
                "mId": "",
                "model": "TA-1021",
                "netWorkType": "WIFI",
                "osType": "android",
                "osVersion": "28",
                "uploadTime": 1620903472035,                       ----
                "timestamp": null,
                "user_agent": null,
                "true_client_ip": null,
                "preference": [
                {
                    "prefCat": "permission",
                    "prefKeys": "ocl.permission.creditcard.sms_read_consent",
                    "prefSubCat": "sms consent"
                }
                ],
                "newUser": null,
                "realTime": null,
                "smsDateTime": *************,
                "smsSenderID": "VM-KOTAKB",
                "smsBody": "Dear Customer, Payment of INR 100 has been received towards your ICICI Bank Credit Card XX2003 on 13-OCT-21 through UPI. Thank you",
                "smsOperator": "JIO",
                "smsReceiver": "xxxxxxxxx6365",
                "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641",
                "predicted_category": "2",
                "level_2_category": "2.0",
                "fastag_class": "-1",
                "fastag_features": {},
                "mode_of_transaction": {
                "mode": "credit_card",
                "vendor": "KOTAK",
                "transaction_reference_no": null
                },
                "ccbp_category": "1",
                "account_no": null,
                "card_no": "2003",
                "amount": 100,
                "available_Balance": null,
                "date": "2021-03-15",                              ----
                "bankName": "KOTAK"
            }
            ],
            "kafka_topic": [
            "SMS_PARSING_CC_BILLS_PAYMENT"
            ]
        }
        ]
    }

    */

    /*
        recent api to be used - 
        URL- /v1/recentupdate/admin
        method- PUT
        Request Body-
        {
                    recharge_number : "1234 XXXX XXXX 1234",
                    operator:"neft hdfc",
                    customer_id : 12973,
                    paytype:"credit card",
                    service:"financial services",
                    "reference_id":"123132",
                    "panUniqueReference":"********",
                    data:[
                        {
                            fieldName:"txnDetails",
                            fieldValue:[{
                                "txnTime":"2020-10-08 12:59:59",
                                "txnAmount":1,
                                "txnDetailSource":"sms"
                            }]
                        }
                    ]
                }

        Success response- 

        Http Code- 200
        Response-
        {"status_code" :00,"data":{"rows_updated" : 1,"fields" : []}}

        Http Code- 422 
        Response Format- {"status_code" :"ERR03","data":{"rows_updated" : 0,"fields" : ["txnAmount"]}
        */