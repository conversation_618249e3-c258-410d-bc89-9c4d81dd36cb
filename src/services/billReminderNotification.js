/*jshint esversion: 8 */
import NOTIFICATION from '../models/notification'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import BILLS from '../models/bills'
import nonPaytmBills from "../models/nonPaytmBills";
import utility from '../lib'
import REQUEST from 'request'
import MOMENT from 'moment'
import ASYNC from 'async'
import _ from 'lodash'
import RemindableUsersLibrary from '../lib/remindableUser'
import OS from 'os'
import NOTIFIER from '../services/notify'
import NotificationLibrary from '../lib/notification'
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills'
import Q from 'q'
import cassandraBills from '../models/cassandraBills'
import billsLib from '../lib/bills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import Logger from '../lib/logger';
import NonPaytmBillsConsumer from './nonPaytmBillsConsumer';

import EncryptorDecryptor from 'encrypt_decrypt';
const env = (process.env.NODE_ENV || 'development').toLowerCase();

const LOAN_SERVICE = "LOAN";

class BillReminderNotification {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.notification = new NOTIFICATION(options);
        this.notificationLibrary = new NotificationLibrary(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.nonPaytmBillsModel  = new nonPaytmBills(options);
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.operatorNotificationConfig = _.get(this.config, 'OPERATOR_NOTIFICATION_CONFIG', {});
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        this.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        this.cvrData = {};
        this.offsetIdMap = {};
        this.authToken = null;
        this.blackListOperators = null;
        this.blackListCustomers = null;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.infraUtils = options.INFRAUTILS;
        this.notify = new NOTIFIER(options);
        this.commonLib = new utility.commonLib(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.categoryId = 1; // Bill Reminder Category which is used in notification table to segregate data
        this.greyScaleEnv = options.greyScaleEnv;
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'BILL_REMINDER_NOTIFICATION', 'BATCHSIZE'],2) : 100;
        this.billReminderNotificationRealtime = _.get(options, 'billReminderNotificationRealtime', false);
        this.billReminderNotificationNonRuRealtime = _.get(options, 'billReminderNotificationNonRuRealtime', false);
        this.billReminderNotificationNonRu = _.get(options, 'billReminderNotificationNonRu', false);
        this.allowedTemplatesForPromoCodes = new Set(_.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','PROMO_CODES_CONFIG','ALLOWED_TEMPLATE_IDS_FOR_PROMOCODE'],[]));
        this.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'BillReminder', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        this.prepaidEnabledOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.blackListOperatorsPrepaidLowBalance = _.get(this.notificationConfig, 'BlackListOperatorPrepaidLowBalance', null);
        this.minPrepaidBalanceCheck = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);
        this.ctPromoCodeUtil = new utility.CtPromoCodes();
        this.disabledSourcesList = []
        this.cassandraBills = new cassandraBills(options);
        this.billsLib = new billsLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
        this.redisTTL = null;
        this.cryptr = new EncryptorDecryptor();
        this.nonPaytmBillsConsumer = new NonPaytmBillsConsumer(options);
        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(this.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_NOTIFY'],{})

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                this.disabledSourcesList.push(source)
            }
        })

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: billReminderNotification", "Re-initializing variable after interval");
        self.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        self.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.operatorNotificationConfig = _.get(this.config, 'OPERATOR_NOTIFICATION_CONFIG', {});
        self.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        self.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        self.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'BillReminder', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListOperatorsPrepaidLowBalance = _.get(self.notificationConfig, 'BlackListOperatorPrepaidLowBalance', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_EXCEPTIONS','BLOCK_CUSTOMER_NOTIFICATIONS','CUSTOMER_ID'],null);
        self.redisTTL = _.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_REDIS','REDIS_TTL'], 129600000);
        self.prepaidEnabledOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], null);
        self.minPrepaidBalanceCheck = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);

        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(self.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_NOTIFY'],{})

        self.disabledSourcesList = []

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                self.disabledSourcesList.push(source)
            }
        })
    }

    start() {
        let self = this;
        self.L.log("start", "bill reminder notification service started");

        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_EXCEPTIONS','BLOCK_CUSTOMER_NOTIFICATIONS','CUSTOMER_ID'],null);
        self.operatorsSendingNotificationToRegisteredUser = _.get(self.notificationConfig, 'registeredUserNotificationOperator', null);
        if(self.billReminderNotificationNonRuRealtime==true || self.billReminderNotificationNonRu==true){
            self.notify.notificationBillSource='NONRU';
        }
        // set cvr data
        self.cvrData = _.get(self.config, 'CVR_DATA', {});

        if (_.isEmpty(self.cvrData)) {
            self.L.critical('BillReminderNotification service: CVR data is empty');
            process.exit(0);
        }
        ASYNC.waterfall([
            (callback) => {
                // initialize kafka consumer
                self.L.log('start', 'Going to configure Kafka');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('billReminderNotification :: start', 'unable to configure kafka', error);
                        callback(error);
                    }
                    else {
                        self.L.log('billReminderNotification :: start', 'Kafka Configured successfully !!');
                        callback(null);
                    }
                });
            }
        ], (error, result) => {
            if(error){
                this.L.critical('billReminderNotification :: start', 'Error:', error);
                // exiting in case of error
                process.exit(0);
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return self.notify.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REMINDER_BILL_FETCH');
                if(self.billReminderNotificationRealtime==true){
                    self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS'),
                        "groupId": "billFetch-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME'),
                        "id": 'billFetchConsumer_' + OS.hostname(),
                        "fromOffset": "earliest",
                        "autoCommit": false,
                        "batchSize": self.kafkaBatchSize
                    });
    
                    self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                        if (!error)
                            self.L.log("configureKafka", "consumer of topic : REMINDER_BILL_FETCH_REALTIME Configured");
                        return next(error);
                    });
                }else if(self.billReminderNotificationNonRuRealtime==true){
                    self.L.log(_.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''))
                        self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NONRU_NOTIFICATION_REALTIME.HOSTS'),
                            "groupId": "nonru-billFetch-consumer-realtime",
                            "topics": _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''),
                            "id": 'billFetchConsumer_' + OS.hostname(),
                            "fromOffset": "latest",
                            "autoCommit": false,
                            "batchSize": self.kafkaBatchSize
                        });
        
                        self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                            if (!error)
                                self.L.log("configureKafka", "consumer of topic : NONRU_REMINDER_BILL_FETCH_REALTIME Configured");
                           else
                                self.L.error("error in configure kafka", error);
                            return next(error);
                        });
                }else if(self.billReminderNotificationNonRu==true){
                    self.L.log(_.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE.NONRU_NOTIFICATION', ''))
                        self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NONRU_NOTIFICATION_REALTIME.HOSTS'),
                            "groupId": "nonru-billFetch-consumer-v1",
                            "topics": _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE.NONRU_NOTIFICATION', ''),
                            "id": 'billFetchConsumer_' + OS.hostname(),
                            "fromOffset": "earliest",
                            "autoCommit": false,
                            "batchSize": self.kafkaBatchSize
                        });
        
                        self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                            if (!error)
                                self.L.log("configureKafka", "consumer of topic : NONRU_REMINDER_BILL_FETCH Configured");
                            return next(error);
                        });
                } else{
                    // Initialize consumer of topic REMINDER_BILL_FETCH
                self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS'),
                    "groupId": "billFetch-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH'),
                    "id": 'billFetchConsumer_' + OS.hostname(),
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : REMINDER_BILL_FETCH Configured");
                    return next(error);
                });
                }
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }


    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage,processingTimeout;
      
        let startTime = new Date().getTime();
        if (self.notify.notificationBillSource == 'NONRU') {
                processingTimeout = setTimeout(() => {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_TIMEOUT_RESUME',
                    'STATUS:TIMEOUT',
                    'SOURCE:BILL_REMINDER_NOTIFICATION',
                    'TIMEOUT_DURATION:1800000'
                ]);
                self.L.error('execSteps::', 'Consumer processing timeout after 5 minutes - forcing resume');
                self.kafkaBillFetchConsumer._resumeConsumer();
            }, 1800000); // 5 minutes timeout

        }

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaBillFetchConsumer._pauseConsumer();
            utility._sendMetricsToDD(records.length, [
                'STATUS:CONSUMED',
                'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH'),
                'REQUEST_TYPE:BILLREMINDER_CONSUMER'
            ])
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 100);
                });
            },
            (err) => {
                try{
                    if (self.notify.notificationBillSource == 'NONRU') {
                        clearTimeout(processingTimeout); 
                    }
                    self.kafkaConsumerChecks.findOffsetDuplicates("BillReminderNotification", records);
                    
                    self.kafkaBillFetchConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
    
                        // Resume consumer now
                        let endTime = new Date().getTime();
                        let executionTime = (endTime - startTime) / 1000;      //in seconds
                        executionTime = Math.round(executionTime);
                        self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:BILL_REMINDER_NOTIFICATION", "TIME_TAKEN:" + executionTime]);
                      

                        if(self.greyScaleEnv) {
                            setTimeout(function(){
                                self.kafkaBillFetchConsumer._resumeConsumer();
                            },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'BILL_REMINDER_NOTIFICATION', 'BATCH_DELAY'],10));
                        } else {
                            self.kafkaBillFetchConsumer._resumeConsumer();
                        }
                    });
                } catch (err){
                    self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    self.kafkaBillFetchConsumer._resumeConsumer();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processNotification(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processNotification(done, data) {
        let self = this,
            record=null, tableName,
            debugKey = data;

        utility.sendNotificationMetricsFromCreate({},{},"RECVD")
        ASYNC.waterfall([
            (callback) => {
                self.validateDataToProcessForNotification(function (error, result) {
                    record = result;
                    if (error) {
                        return callback(`Unable to validate data, error - ${error}`);
                    } else {
                        return callback();
                    }
                }, data);
            },
            (callback) => {
                self.L.log('2. processNotification', `Processing notification for ${_.get(record, 'debugKey', null)}`);

                if((self.notify.notificationBillSource !== 'NONRU') && !_.get(record, 'customer_mobile', null)) {
                    self.remindableUsersLibrary._getUserDetails(callback, record);
                } else {
                    callback(null, false);
                }
            },
            (updateData, callback) => {
                tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', _.get(record, 'product_id', null)], null) ||
                    _.get(self.config, ['OPERATOR_TABLE_REGISTRY', _.get(record, 'operator', null)], null);

                    if(_.toLower(_.get(record, 'service', ''))=='paytm postpaid'){
                        tableName='bills_paytmpostpaid'
                    }

                    if(_.get(record, 'notificationType', null) == 'PREPAID_LOW_BALANCE'){
                        tableName = tableName + '_prepaid'
                    }

                ASYNC.parallel([
                    (cb) => {
                        if (updateData) {
                            self.L.log('updating user data for the bill id ' + _.get(record, 'id', null));
                            self.bills.updateUserData(() => {
                                cb(null);
                            }, tableName, record['id'], _.get(record, 'customer_mobile', null), _.get(record, 'customer_email', null));
                        } else {
                            cb(null);
                        }
                    },
                    (cb) => {
                        self.prepareNotification((error) => {
                            if (error) {
                                return cb(error);
                            } else {
                                return cb(null);
                            }
                            
                        }, record, tableName);
                    }
                ], (err) => {
                    if (err) {
                        return callback(err);
                    } else {
                        return callback(null);
                    }
                }
                );
            }], (error) => {
                if (error) {
                    //have to remove this log as can't differentiate between the error and the debugKey
                    //self.L.error('processNotification', 'debugKey', debugKey, 'error', error);
                    return self.notify.insertRejectedNotifications(done, self.billsLib.createErrorMessage(error), record, null);
                } else {
                    return done();
                }
            }
        );
    }

    isCustomerIdPresentInPayload(record){
        if(_.get(record, 'customer_id', null) == null || 
            _.get(record, 'customer_id', null) == '' || 
            _.get(record, 'customer_id', null) == undefined || 
            _.get(record, 'customer_id', null) == 'null' || 
            _.toString(_.get(record, 'customer_id', null)) == '0'){
            return false;
        }
        return true;
    }

    validateDataToProcessForNotification(callback, kafkaPayload) {
        let self = this;
        self.L.log('1. validateDataToProcessForNotification :: convert payload to record and validate');
        let record = self.convertKafkaPayloadToRecord(kafkaPayload);
        if(self.notify.notificationBillSource == 'NONRU'){
            _.set(record, 'bill_source', 'NONRU');
        }
        if (record == null) {
            utility.sendNotificationMetricsFromCreate(record,{},"INVALID_PAYLOAD")
            return callback('unable to get valid data from kafka payload', record);
        }
        record.amount = utility.getFilteredAmount(record.amount);
        let reconId = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'service',null)=='financial services' ? _.get(record, 'bank_name', null):_.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null), _.get(record, 'bill_date', null));
        if(_.get(record, 'recon_id', null) && _.get(record, 'recon_id', null) != reconId){
            // utility.sendNotificationMetricsFromCreate(record,{},"INVALID_RECON_ID")
            self.logger.error(`Invalid recon id ${_.get(record, 'recon_id', null)} != ${reconId}`, record, _.get(record, 'service', null));
        }
        if(!_.get(record, 'recon_id', null)){
            self.logger.error("validateDataToProcessForNotification :: recon_id not found for : ", record, _.get(record, 'service', null));
            utility._sendMetricsToDD(1, ['TYPE:RECON_ID_NOT_FOUND', `REQUEST_TYPE:NOTIFICATION_CREATE`,`SOURCE:${_.get(record, 'source', 'UNKNOWN')}`]);
            _.set(record, 'recon_id', reconId);
        }
        if(self.isCustomerIdPresentInPayload(record) == false){
            return callback(`Customer id not found in payload`, record);
        }
        if(self.notify.notificationBillSource == 'NONRU'){
            if(_.get(record, 'source', null)=='notifyRejectedBills'){
                self.L.log(`validateDataToProcessForNotification:: record is from notifyRejectedBills source, bypassing all validations`);
                return callback(null, record);
            }
            try{
                let extra = _.get(record, 'extra', null);
                if(typeof extra == 'string'){
                    extra = JSON.parse(extra);
                }
                let updatedSource = _.get(extra, 'updated_source', null);
                if(updatedSource == 'validationSync'){
                    utility.sendNotificationMetricsFromCreate(record,{},"VALIDATION_SYNC")
                    return callback('validation sync record- not eligible for notification');
                }
            }catch(e){
                self.L.error('validateDataToProcessForNotification', `updated_source not found in extra`, kafkaPayload);
            }
        }

        let notificationType = _.get(record, 'notificationType');
        let remindLaterDate = _.get(record, 'remind_later_date', null);
        const fastagError = self.validateFastagPrepaidRecharge(record);
        if (fastagError) {
            return callback(fastagError, record);
        }


        let productInfo = self.cvrData[_.get(record, 'product_id', null)];    
        
        if (self.blackListOperators && _.isArray(self.blackListOperators) && self.blackListOperators.indexOf(_.get(record, 'operator')) != -1) {
            utility.sendNotificationMetricsFromCreate(record,{},"BLACKLISTED_OP")
            return callback(`Blacklisted operator ${_.get(record, 'operator')}`, record);
        } else if (self.blackListOperatorsPrepaidLowBalance && _.isArray(self.blackListOperatorsPrepaidLowBalance) && self.blackListOperatorsPrepaidLowBalance.indexOf(_.get(record, 'operator')) != -1) {
            utility.sendNotificationMetricsFromCreate(record,{},"BLACKLISTED_OP_PREPAID_LOW_BALANCE")
            return callback(`Blacklisted operator ${_.get(record, 'operator')}`, record);
        }
        else if(self.blackListCustomers!==null && self.blackListCustomers.includes(_.toString(_.get(record, 'customer_id', null)))){
            utility.sendNotificationMetricsFromCreate(record,{},"BLACKLISTED_CUST")
            return callback(`Blacklisted customer_id ${_.get(record, 'customer_id')}`, record);
        }
        else if ([1,2].indexOf(_.get(record, 'is_automatic', 0)) != -1) {// send notification only for 0,3,4,5
            utility.sendNotificationMetricsFromCreate(record,{},"AUTOMATIC")
            return callback('Subscription exists', record);
        } else if(self.notify.notificationBillSource == 'NONRU'&& _.get(record, 'is_automatic', 0) != 0){
            utility.sendNotificationMetricsFromCreate(record,{},"AUTOMATIC")
            return callback('Subscription exists', record);
        }
        else if (_.get(record, 'status', null) == _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
            utility.sendNotificationMetricsFromCreate(record,{},"NOT_IN_USE")
            return callback('Not In use record', record);
        }
        else if (productInfo && !_.get(productInfo, 'status', null)) {
            utility.sendNotificationMetricsFromCreate(record,{},"INACTIVE_PID")
            self.L.log('validateDataToProcessForNotification :: PID is marked inactive for : ',`${_.get(record, 'debugKey')} ProductStatus:${_.get(productInfo, 'status', null)}`);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_PID_INACTIVE', 'STATUS:ERROR']);
            return callback('PID Is marked inactive', record);
        }else if(self.disabledSourcesList.indexOf(_.get(record , 'data_source',null)) > -1){
            utility.sendNotificationMetricsFromCreate(record,{},"DISABLED_SOURCE")
            return callback(`record data source is ${_.get(record , 'data_source',null)} and is disabled`, record);
        }else if(remindLaterDate &&  MOMENT.utc(remindLaterDate).startOf('day').isAfter(MOMENT.utc().startOf('day'))){
            utility.sendNotificationMetricsFromCreate(record,{},"REMIND_LATER_DATE")
            return callback(`remind later date ${remindLaterDate} is greater than current date`, record);
        }
        else if (notificationType == 'BILLGEN') {
            // check for notification eligibility
            let amount = _.get(record, 'amount', null);
            if (_.get(record, 'noAmountFlag', false)==false && amount !== null && amount <= _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)) {
                utility.sendNotificationMetricsFromCreate(record,{},"MIN_NOTIFY_AMOUNT")
                return callback(`amount <= defined amount ${_.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)}`, record);
            }
            
            else if (_.get(record, 'notification_status', null) != _.get(self.config, 'COMMON.notification_status.ENABLED', 1)) {
                utility.sendNotificationMetricsFromCreate(record,{},"NOTIFICATION_STATUS_DISABLED")
                return callback(`notification status:${_.get(record, 'notification_status', null)} disabled`, record);
            }
            else if (_.get(record, 'service_id', null) == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
                utility.sendNotificationMetricsFromCreate(record,{},"PREPAID")
                return callback(`prepaid service id ${_.get(record, 'service_id', null)}`, record);
            }
            else if (_.get(record, 'noDueDateFlag',false)==true){
                return callback(null, record);
            }
            else {
                let
                    currDate = MOMENT().startOf('day'),
                    dueDate = MOMENT(_.get(record, 'due_date')).utc().startOf('day'),
                    dueDate_currDate_diff = dueDate.diff(currDate, 'days'),
                    setDueDateNull = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'ALLOW_NULL_DUE_DATE'],false);

                // setting due date format to avoid further conversion
                _.set(record, 'dueDate', dueDate.format('YYYY-MM-DD'));

                if ((setDueDateNull && !dueDate.isValid()) || _.get(record, 'due_date', null) === null) {
                    return callback(null, record);
                } else if (dueDate_currDate_diff > -1) {
                    return callback(null, record);
                } else {
                    utility.sendNotificationMetricsFromCreate(record,{},"INVALID_DUE_DATE")
                    return callback(`Invalid diff dueDate_currDate_diff : ${dueDate_currDate_diff}`, record);
                }
            }

        } else if (notificationType == 'DUEDATE' || notificationType == 'BILLDUE') {
            if (self.notify.notificationBillSource == 'NONRU' && _.get(record, 'noAmountFlag', false)==false && _.get(record, 'amount', null) <= _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 9)) {
                return callback(`amount <= defined amount ${_.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 9)}`);
            }
            // Data already filtered by DB query - filtering more records as per business logic

            if (_.get(record, 'notification_status', null) != _.get(self.config, 'COMMON.notification_status.ENABLED', 1)) {
                utility.sendNotificationMetricsFromCreate(record,{},"NOTIFI_STATUS_DISABLED")
                return callback(`notification status:${_.get(record, 'notification_status', null)} disabled`, record);
            }
            _.set(record, 'notificationType', 'DUEDATE');

            return callback(null, record);
        } else if (notificationType == 'OLD_BILL_NOTIFICATION') {
            let amount = _.get(record, 'amount', null);
            if (_.get(record, 'notification_status', null) != _.get(self.config, 'COMMON.notification_status.ENABLED', 1)) {
                utility.sendNotificationMetricsFromCreate(record, {}, "NOTIFI_STATUS_DISABLED")
                return callback(`notification status:${_.get(record, 'notification_status', null)} disabled`, record);
            }
            else if (amount !== null && amount <= _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)) {
                utility.sendNotificationMetricsFromCreate(record, {}, "MIN_NOTIFY_AMOUNT")
                return callback(`amount <= defined amount ${_.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)}`, record);
            }
            else {
                let
                    dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').utc().startOf('day') : null,
                    oldBillFetchDate = MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD') : null,
                    oldBillFetchDate_dueDate_diff = 0,
                    billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;
                if (dueDate == null) {
                    return callback(`dueDate is null`, record);
                }
                if (oldBillFetchDate && dueDate)
                    oldBillFetchDate_dueDate_diff = oldBillFetchDate.diff(dueDate, 'days');

                self.L.log("validateDataToProcessForNotification ~ oldBillFetchDate_dueDate_diff:", oldBillFetchDate_dueDate_diff)
                self.L.log("validateDataToProcessForNotification ~ oldBillFetchDate_billFetchDate_diff:", MOMENT(oldBillFetchDate).diff(billFetchDate, 'days'))
                if (oldBillFetchDate && dueDate && oldBillFetchDate_dueDate_diff > _.get(self.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'DUE_DATE_DIFF_THRESHOLD'], 45)) {
                    utility.sendNotificationMetricsFromCreate(record, {}, "INVALID_DUE_DATE")
                    return callback(`dueDate_currDate_diff is more than threshold: ${oldBillFetchDate_dueDate_diff}`, record);
                } else if (oldBillFetchDate && billFetchDate && MOMENT(billFetchDate).diff(oldBillFetchDate, 'days') >= 0) {
                    utility.sendNotificationMetricsFromCreate(record, {}, "INVALID_DUE_DATE")
                    return callback(`billFetchDate is greater than equal to oldBillFetchDate`, record);
                }
                else {
                    return callback(null, record);
                }
            }
        } else if (notificationType == 'PREPAID_LOW_BALANCE') {
            // check for notification eligibility
            let amount = _.get(record, 'amount', null);
            if (amount !== null && amount > self.minPrepaidBalanceCheck) {
                utility.sendNotificationMetricsFromCreate(record,{},"AMOUNT_GREATOR_THAN_MIN_BALANCE")
                return callback(`amount >= defined amount ${self.minPrepaidBalanceCheck}`, record);
            }
            else if (_.get(record, 'notification_status', null) != _.get(self.config, 'COMMON.notification_status.ENABLED', 1)) {
                utility.sendNotificationMetricsFromCreate(record,{},"NOTIFICATION_STATUS_DISABLED")
                return callback(`notification status:${_.get(record, 'notification_status', null)} disabled`, record);
            }
            else if (_.get(record, 'service_id', null) == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
                utility.sendNotificationMetricsFromCreate(record,{},"PREPAID")
                return callback(`prepaid service id ${_.get(record, 'service_id', null)}`, record);
            }
            else {
                let
                    currDate = MOMENT().startOf('day'),
                    dueDate = MOMENT(_.get(record, 'due_date')).utc().startOf('day'),
                    dueDate_currDate_diff = dueDate.diff(currDate, 'days'),
                    setDueDateNull = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'PREPAID_ALLOW_NULL_DUE_DATE'],false);

                // setting due date format to avoid further conversion
                _.set(record, 'dueDate', dueDate.format('YYYY-MM-DD'));

                if ((setDueDateNull && !dueDate.isValid()) || _.get(record, 'due_date', null) === null) {
                    return callback(null, record);
                } else if (dueDate_currDate_diff > -1) {
                    return callback(null, record);
                } else {
                    utility.sendNotificationMetricsFromCreate(record,{},"PREPAID_LOW_BALANCE_INVALID_DUE_DATE")
                    return callback(`Invalid diff dueDate_currDate_diff : ${dueDate_currDate_diff}`, record);
                }
            }

        }
        else {
            utility.sendNotificationMetricsFromCreate(record,{},"NOTIFI_TYPE_NOT_SUPPORTED")
            return callback('Notification type not supported/passed', record);
        }
    }


    validateFastagPrepaidRecharge(record) {
        // Return null if validation passes, otherwise return error message
        try {
            if (_.get(record, 'paytype', '').toLowerCase() === 'prepaid' && 
                _.get(record, 'service', '').toLowerCase() === 'fastag recharge') {

                // Check for low balance in customer_other_info
                let customerOtherInfo = _.get(record, 'customerOtherInfo');
                if (typeof customerOtherInfo === 'string') {
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                }
                if (_.get(customerOtherInfo, 'isLowBalance', false)) {
             
                    // Check for smsDateTime in extra
                    let extra = _.get(record, 'extra', {});
                    if (typeof extra === 'string') {
                        extra = JSON.parse(extra);
                    }
                    let smsDateTime = _.get(extra, 'smsDateTime');
                    if (smsDateTime) {
                        let smsDate = MOMENT(parseInt(smsDateTime));
                        if (smsDate.isBefore(MOMENT().startOf('day'))) {
                            utility.sendNotificationMetricsFromCreate(record,{},"FASTAG_OLD_SMS_DATE")
                            return 'SMS date is older than current date';
                        }
                    }
                }
            }
            return null;
        } catch (err) {
            this.L.error('validateFastagPrepaidRecharge :: Error in validation', err);
            utility.sendNotificationMetricsFromCreate(record,{},"VALIDATION_ERROR")
            return 'Error in Fastag prepaid validation checks';
        }
    }


    getLatePaymentSurchargePercentage(record, month) {
        let self = this;
        let operator = _.get(record, 'operator', null),
            service = _.get(record, 'service', null);
        let surchargePercentage = _.get(this.config, ['DYNAMIC_CONFIG', 'LATE_PAYMENT_CONFIG', operator, `LATE_PAYMENT_SURCHARGE_PERCENTAGE_${month}_MONTH`],
            _.get(this.config, ['DYNAMIC_CONFIG', 'LATE_PAYMENT_CONFIG', 'LATE_PAYMENT_PERCENTAGE_BY_SERVICE', `LATE_PAYMENT_SURCHARGE_PERCENTAGE_${month}_${service}_MONTH`], 0)
        );
        return surchargePercentage;
    }

    getLatePaymentSurcharge(record) {
        try {
            let self = this,
                currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
                dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD') : null,
                dueDateCurrentDateDiff = MOMENT(currentDate).diff(dueDate, 'days'),
                months = ['FIRST', 'SECOND', 'THIRD'],
                amount = _.get(record, 'amount', 0),
                penaltyAmounts = {},
                totalAmount = amount, penaltyAmount = 0, latePaymentSurchargePercentage = 0;

            for (let i = 0; i < months.length; i++) {
                let latePaymentPercentage = self.getLatePaymentSurchargePercentage(record, months[i]);
                let penaltyAmount = (totalAmount * latePaymentPercentage) / 100;
                penaltyAmounts[months[i]] = penaltyAmount;
                totalAmount += penaltyAmount;
            }


            if (dueDateCurrentDateDiff <= 30) {
                latePaymentSurchargePercentage = self.getLatePaymentSurchargePercentage(record, months[0]);
                penaltyAmount = penaltyAmounts[months[0]];
            }
            if (dueDateCurrentDateDiff > 30 && dueDateCurrentDateDiff <= 60) { // 2nd month
                latePaymentSurchargePercentage = self.getLatePaymentSurchargePercentage(record, months[1]);
                penaltyAmount = penaltyAmounts[months[1]];
            }
            if (dueDateCurrentDateDiff > 60 && dueDateCurrentDateDiff <= 90) { // 3rd month
                latePaymentSurchargePercentage = self.getLatePaymentSurchargePercentage(record, months[2]);
                penaltyAmount = penaltyAmounts[months[2]];
            }
            self.L.log('getLatePaymentSurcharge', `Penalty for due date ${dueDate}:: amount ${_.get(record, 'amount', null)} with latePaymentSurchargePercentage ${latePaymentSurchargePercentage} is ${penaltyAmount}`);
            return penaltyAmount;
        }
        catch (error) {
            self.L.error("getLatePaymentSurcharge::Error while gettingLatePaymentSurcharge");
            return 0;
        }
    }

    getValue(val){
        if(val == '' || val == null || val == undefined){
            return null;
        }
        else{
            return val;
        }
    }

    findMax(sms_date_time, bill_fetch_date, payment_date){
        if(!sms_date_time && !bill_fetch_date && !payment_date){
            return null;
        }
        else if(!sms_date_time && !payment_date){
            return MOMENT(bill_fetch_date).format('YYYY-MM-DD HH:mm:ss');
        }
        else if(!bill_fetch_date && !payment_date){
            return MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
        }
        else if(!sms_date_time && !bill_fetch_date){
            return MOMENT(payment_date).format('YYYY-MM-DD HH:mm:ss');
        }
        else{
            return MOMENT(sms_date_time).isAfter(MOMENT(payment_date)) ?
            (MOMENT(sms_date_time).isAfter(MOMENT(bill_fetch_date)) ? MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss') : MOMENT(bill_fetch_date).format('YYYY-MM-DD HH:mm:ss')) : 
            (MOMENT(payment_date).isAfter(MOMENT(bill_fetch_date)) ? MOMENT(payment_date).format('YYYY-MM-DD HH:mm:ss') : MOMENT(bill_fetch_date).format('YYYY-MM-DD HH:mm:ss'));

        }
    }

    findMaxPrepaid(content, latest_recharge_date, sms_date_time) {
        content.operator_validated_at = null;
        if (sms_date_time == null && latest_recharge_date == null) {
            content.operator_validated_at = null;
        }
        if (sms_date_time == null) {
            content.operator_validated_at = MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
        }
        else if (latest_recharge_date == null) {
            content.operator_validated_at = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
        }
        else {
            content.operator_validated_at = MOMENT(sms_date_time).isAfter(MOMENT(latest_recharge_date)) ? MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss') : MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
        }
    }

    addOperatorValidatedAtPrepaid(extra, content){
        let self = this;
        let sms_date_time = _.get(extra, 'sms_date_time', null);
        let latest_recharge_date = _.get(content, "latest_recharge_date", null);
        let updated_source = _.get(extra, "updated_data_source", null);

        self.L.log('billReminderNotification:: prepaid paytype,  latest_recharge_date:', latest_recharge_date, 'updated_source:', updated_source, 'sms_date_time:', sms_date_time, "cust_id and RN", _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));

        try{
            if(sms_date_time){
                sms_date_time = parseInt(sms_date_time);
                sms_date_time = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }
        catch(err){
            this.L.log("billReminderNotification :: process Batch :: sms_date_time parsing issue", err);
            sms_date_time = null;
        }

        if(updated_source){
            if (['SMS_PARSING_DWH_REALTIME', 'SMS_PARSING_DWH_MANUAL', 'SMS_PARSING_DWH', 'SMS_PARSING_REALTIME'].includes(updated_source)) {
                if (sms_date_time) {
                    content.operator_validated_at = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
                }
                else{
                    self.findMaxPrepaid(content, latest_recharge_date, sms_date_time);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                    self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
                }
            }
            else if (updated_source == "transaction") {
                if (latest_recharge_date) {
                    content.operator_validated_at = MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
                }
                else{
                    self.findMaxPrepaid(content, latest_recharge_date, sms_date_time);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                    self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
                }
            }
            else{
                self.findMaxPrepaid(content, latest_recharge_date, sms_date_time);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
            }
        }
        else{
            self.findMaxPrepaid(content, latest_recharge_date, sms_date_time);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:UPDATED_DATA_SOURCE_NOT_PRESENT`]);
            self.L.log('convertKafkaPayloadToRecord', 'updated_data_source not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
        }
    }

    getOperatorValidatedAtValue(data, extra, source){
        let self= this;
        if(source =='airtelBillFetchRealtime' || source == 'airtelBillFetch'){
            if(!_.isEmpty(_.get(data, 'published_date')) ){
                self.L.log("BillReminderNotification:: airtelBillFetch with publishedDate:", _.get(data, 'published_date'));
                return MOMENT(_.get(data, 'published_date') ).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        if(_.get(data, 'paytype') && _.get(data, 'paytype').toLowerCase() == 'prepaid'){
            return self.addOperatorValidatedAtPrepaid(extra, data);
        }
        

        let customerOtherInfo, sms_date_time, operator_validated_at=null;
        try{
            
            let bill_fetch_date = self.getValue(_.get(data, 'billFetchDate', _.get(data, 'bill_fetch_date')));
            let payment_date = self.getValue(_.get(data, "paymentDate"));

            try{
                customerOtherInfo = _.get(data, "customerOtherInfo", {});
            if(typeof customerOtherInfo == 'string'){
                customerOtherInfo = JSON.parse(customerOtherInfo);
            }
                sms_date_time = self.getValue(_.get(customerOtherInfo, "sms_date_time"));
                if(sms_date_time!=null){
                    sms_date_time = parseInt(sms_date_time);
                    sms_date_time = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
                }
            }
            catch(err){
                self.L.log("BillReminderNotification :: process Batch :: sms_date_time parsing issue", err);
                sms_date_time = null;
            }

            let updated_data_source = self.getValue(_.get(extra, "updated_data_source"));
            self.L.log('BillReminderNotification::', 'bill_fetch_date:', bill_fetch_date, 'updated_data_source:', updated_data_source, 'sms_date_time:', sms_date_time, 'payment_date:', payment_date, "cust_id and RN", _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));

            if (updated_data_source) {
                if ((updated_data_source == 'ffr' || updated_data_source == 'ValidationSync')) {
                    if(bill_fetch_date)
                        operator_validated_at = MOMENT(bill_fetch_date).format('YYYY-MM-DD HH:mm:ss');
                    else{
                        operator_validated_at = self.findMax(sms_date_time, bill_fetch_date, payment_date);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_data_source}`]);
                        self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_data_source, _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));
                    }
                }
                else if (['SMS_PARSING_DWH_REALTIME', 'SMS_PARSING_DWH_MANUAL', 'SMS_PARSING_DWH', 'SMS_PARSING_REALTIME'].includes(updated_data_source) ) {
                    if(sms_date_time)
                        operator_validated_at = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
                    else{
                        operator_validated_at = self.findMax(sms_date_time, bill_fetch_date, payment_date);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_data_source}`]);
                        self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_data_source, _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));
                    }
                }
                else if (updated_data_source == "transaction" ) {
                    if(payment_date)
                        operator_validated_at = MOMENT(payment_date).format('YYYY-MM-DD HH:mm:ss');
                    else{
                        operator_validated_at = self.findMax(sms_date_time, bill_fetch_date, payment_date);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_data_source}`]);
                        self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_data_source, _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));
                    }
                }
                else{
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR', `UPDATED_DATA_SOURCE:${updated_data_source}`]);
                    self.L.log('convertKafkaPayloadToRecord', 'updated_data_source other value', updated_data_source, _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));
                    operator_validated_at = self.findMax(sms_date_time, bill_fetch_date, payment_date);
                }
            }
            else{
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR', 'UPDATED_DATA_SOURCE:UPDATED_DATA_SOURCE_NOT_PRESENT']);
                self.L.log('convertKafkaPayloadToRecord', 'updated_data_source not present', _.get(data, 'customerId', null), _.get(data, 'rechargeNumber', null));
                operator_validated_at = self.findMax(sms_date_time, bill_fetch_date, payment_date);
            }
        }
        catch(err){
            if(err){
                self.L.critical('convertKafkaPayloadToRecord', 'customerOtherInfo is invalid', err);
            }
        }

        return operator_validated_at;
    }

    convertKafkaPayloadToRecord(kafkaPayload) {
        let
            self = this,
            kafkaPayloadData, data, extra, operator_validated_at, source;

        try {
            kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
            data = _.get(kafkaPayloadData, 'data', {});
            source = _.get(kafkaPayloadData, 'source', null);
            extra = _.get(data, 'extra', {});
            if(typeof extra == "string"){
                extra = JSON.parse(extra)
            }
            if(_.get(kafkaPayloadData, 'correlationId', null)){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_CORRELATION_ID', `ID:${_.get(kafkaPayloadData, 'correlationId', null)}`]);
            }
        } catch (error) {
            if (error) {
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, kafkaPayload);
                return null;
            }
        }
        
        if(_.get(data, 'service', null) && _.get(data, 'service', null).toLowerCase() == 'mobile')
            operator_validated_at = self.getOperatorValidatedAtValue(data, extra, source);
        else{
            operator_validated_at = null;
        }
        
        
        let dataExhaustExtra = {
            "data_exhaust_value": null,
            "data_exhaust_date": null
        }
        if(self.notify.notificationBillSource == 'NONRU'){
            if( _.get(data, 'service', '')=='mobile' && _.get(data, 'isRnDecrypted', false)==false){
                try{
                    let decryptedRechargeNumber = this.cryptr.decrypt(data.recharge_number);
                data.recharge_number = decryptedRechargeNumber;
                }catch(err){
                    self.L.error('convertKafkaPayloadToRecord', `Could not decrypt recharge number`, kafkaPayload)
                    return null;
                }
            }
            if(_.get(data, 'partialBillState', null)){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_NOTIFICATION', 'TYPE:COUNT', `OPERATOR:${_.get(data, 'operator', null)}`, `PARTIAL_BILL:${_.get(data, 'partialBillState', 'NO_STATE')}`]);
            }
            let isRealTimeDataExhausted = _.get(data, 'isRealTimeDataExhausted', false);
            if(isRealTimeDataExhausted ){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_NOTIFICATION', 'TYPE:COUNT', `OPERATOR:${_.get(data, 'operator', null)}`, `IS_REALTIME_DATA_EXHAUST:${isRealTimeDataExhausted}`]);
                dataExhaustExtra.data_exhaust_value = _.get(extra, 'data_exhaust_value', null);
                dataExhaustExtra.data_exhaust_date = _.get(extra, 'data_exhaust_date', null);
          
            }
            if(_.get(data, 'paytype', null)=='credit card'){
                if(self.notify.notificationBillSource == 'NONRU'){
                    data.recharge_number =  self.smsParsingSyncCCBillLib.createMaskedCCBasedOnCCLen(data.recharge_number, data.recharge_number.slice(-4))
                } // for credit card send only last 4 digit in  payload
                else {
                    data.rechargeNumber =  self.smsParsingSyncCCBillLib.createMaskedCCBasedOnCCLen(data.rechargeNumber, data.rechargeNumber.slice(-4))
                }
            }
            return this.transformDataNonRu(data, extra, kafkaPayloadData, dataExhaustExtra,operator_validated_at);
            
        }
        return this.transformDataRu(data, extra, kafkaPayloadData, operator_validated_at);
    }

    transformDataRu(data, extra, kafkaPayloadData, operator_validated_at) {
        let self = this;
        return {
            id: _.get(data, 'id', null),
            customer_id: _.get(data, 'customerId', null),
            recharge_number: (_.get(data, 'rechargeNumber', null) ? _.get(data, 'rechargeNumber', null).toString() : null),
            old_product_id: _.get(data, 'productId', null),
            product_id: self.activePidLib.getActivePID(_.get(data, 'productId', null)),
            operator: _.get(data, 'operator', null),
            amount: _.get(data, 'amount', null),
            bill_date: _.get(data, 'billDate', null),
            recon_id: _.get(extra, 'recon_id', null),
            dataConsumed: _.get(data, 'dataConsumed', null),
            due_date: _.get(data, 'dueDate', null),
            bill_fetch_date: _.get(data, 'billFetchDate', _.get(data, 'bill_fetch_date', null)),
            next_bill_fetch_date: _.get(data, 'nextBillFetchDate', null),
            gateway: _.get(data, 'gateway', null),
            paytype: _.get(data, 'paytype', null),
            service: _.get(data, 'service', null),
            circle: _.get(data, 'circle', null),
            customer_mobile: _.get(data, 'customerMobile', null),
            customer_email: _.get(data, 'customerEmail', null),
            status: _.get(data, 'status', null),
            user_data: _.get(data, 'userData', null),
            notification_status: _.get(data, 'notification_status', null),
            payment_date: _.get(data, 'paymentDate', null),
            service_id: _.get(data, 'service_id', null),
            customerOtherInfo: _.get(data, 'customerOtherInfo', null),
            is_automatic: _.get(data, 'is_automatic', 0),
            source: _.get(kafkaPayloadData, 'source', null),
            notificationType: _.get(kafkaPayloadData, 'notificationType', null),
            templates: _.get(data, 'templates', null),
            skipNotification: _.get(data, 'skipNotification', null),
            time_interval: _.get(data, 'time_interval', null),
            extra: _.get(data, 'extra', null),
            bank_name: _.get(data, 'bank_name', null),
            card_network: _.get(data, 'card_network', null),
            timestamps: {
                billFetchReminder_acknowledgeTime: new Date().getTime(),
                billFetchReminder_onBoardTime: _.get(data, 'billFetchReminder_onBoardTime', null),
                dwhKafkaPublishedTime : _.get(data,'dwhKafkaPublishedTime',null)
            },
            debugKey: `id-${_.get(data, 'id')},customerId-${_.get(data, 'customerId')},rechargeNumber-${_.get(data, 'service') == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(_.get(data, 'rechargeNumber')) : _.get(data, 'rechargeNumber')},operator-${_.get(data, 'operator')}`,
            refId: _.get(data, 'refId', null),
            rtspId: _.get(data, 'rtspId', null),
            data_source: _.get(data, 'data_source', null),
            old_bill_fetch_date: _.get(data, 'oldBillFetchDate', _.get(data, 'old_bill_fetch_date', null)),
            remind_later_date: _.get(data, 'remindLaterDate', _.get(data, 'remind_later_date', null)),
            operator_validated_at: operator_validated_at
        };
    }

    transformDataNonRu(data, extra, kafkaPayloadData,dataExhaustExtra,operator_validated_at) {
        let self = this;
        return {
            customer_id: _.get(data, 'customer_id', null),
            recharge_number: (_.get(data, 'recharge_number', null) ? _.get(data, 'recharge_number', null).toString() : null) ,
            old_product_id: _.get(data, 'product_id', null),
            product_id: self.activePidLib.getActivePID(_.get(data, 'product_id', null)),
            operator: _.get(data, 'operator', null),
            amount: _.get(data, 'due_amount', null),
            bill_date: _.get(data,'bill_date',null),
            dataConsumed: _.get(data, 'data_consumed',null),
            due_date: _.get(data, 'due_date', null) ? new Date(_.get(data, 'due_date')).toISOString() : null,
            bill_fetch_date: _.get(data, 'bill_fetch_date', null),
            next_bill_fetch_date: _.get(data, 'next_bill_fetch_date', null),
            gateway: _.get(data, 'gateway', null),
            paytype: _.get(data, 'paytype', null),
            service: _.get(data, 'service', null),
            circle: _.get(data, 'circle', null),
            customer_mobile: _.get(data, 'customer_mobile', null),
            customer_email: _.get(data, 'customer_email', null),
            status: _.get(data, 'status', null),
            user_data: _.get(data, 'user_data', null),
            notification_status: _.get(data, 'notification_status', null),
            payment_date: _.get(data, 'payment_date', null) ? new Date(_.get(data, 'payment_date')).toISOString() : null,
            service_id: _.get(data, 'service_id', null),
            customerOtherInfo : _.get(data, 'customer_other_info', null),
            is_automatic: _.get(data, 'is_automatic', 0),
            source: _.get(kafkaPayloadData, 'source', null),
            notificationType: _.get(kafkaPayloadData, 'notificationType', null),
            templates: _.get(data, 'templates', null),
            skipNotification : _.get(data, 'skip_notification', null),
            time_interval: _.get(data, 'time_interval', null),
            extra : _.get(data, 'extra', null),
            bank_name : _.get(data, 'bank_name', null),
            card_network : _.get(data, 'card_network', null),
            timestamps:{
                billFetchReminder_acknowledgeTime : new Date().getTime(),
                billFetchReminder_onBoardTime :  _.get(data,'billFetchReminder_onBoardTime',null),
                dwhKafkaPublishedTime : _.get(data,'dwhKafkaPublishedTime',null),
            },
            debugKey: `id-${_.get(data, 'id')},customerId-${_.get(data, 'customer_id')},rechargeNumber-${_.get(data, 'service') == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(_.get(data, 'recharge_number')) : _.get(data, 'recharge_number')},operator-${_.get(data, 'operator')}`,
            refId : _.get(data, "refId", null),
            rtspId : _.get(data, "rtspId", null),
            noAmountFlag : _.get(data, 'due_amount', null) || _.get(data, 'due_amount', null)==0 ? false: true,
            noDueDateFlag : _.get(data, 'due_date', null) ? false: true,
            correlationId : _.get(kafkaPayloadData, 'correlationId', null),
            recon_id : _.get(data, 'recon_id', null),
            isRealTimeDataExhausted : _.get(data, "isRealTimeDataExhausted", false),
            category_id : _.get(data, 'category_id', null),
            rawlastcc : _.get(data, 'rawlastcc', null),
            partialBillState: _.get(data, 'partialBillState', null),
            dataExhaustExtra : dataExhaustExtra,
            operator_validated_at : operator_validated_at,
            remind_later_date: _.get(data, 'remindLaterDate', _.get(data, 'remind_later_date', null))
        };
    }

    appendLandlordName(payLoad, record) {
        let self = this;
        try {
            let userData = _.get(record, 'user_data', null);
            userData = JSON.parse(userData);
            let landlordName = _.get(userData, 'recharge_number_3', null);
            if (landlordName) {
                _.set(payLoad, 'landlord_name', landlordName);
                self.L.log('appendLandlordName::', `Landlord name ${landlordName} - appended for record rech_number ${_.get(payLoad, 'recharge_number', null)}, customer_id ${_.get(payLoad, 'customer_id', null)}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:APPEND_LANDLORD_NAME',
                    'STATUS:SUCCESS',
                    'SOURCE:BILL_REMINDER_NOTIFICATION',
                    `OPERATOR:${_.get(record, 'operator', 'unknown')}`
                ]);
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:APPEND_LANDLORD_NAME',
                    'STATUS:FAILURE',
                    'SOURCE:BILL_REMINDER_NOTIFICATION',
                    `OPERATOR:${_.get(record, 'operator', 'unknown')}`
                ]);
                self.L.log('appendLandlordName::', `Landlord name not found for record rech_number ${_.get(payLoad, 'recharge_number', null)}, customer_id ${_.get(payLoad, 'customer_id', null)}`);
            }
        } catch (error) {
            self.L.error('appendLandlordName::', `Error in appending landlord name for record ${JSON.stringify(payLoad)}`, error);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:APPEND_LANDLORD_NAME',
                'STATUS:ERROR',
                'SOURCE:BILL_REMINDER_NOTIFICATION',
                `OPERATOR:${_.get(record, 'operator', 'unknown')}`
            ]);
        }
    }

    async prepareNotification(callback, record, tableName) {
        let self = this,
            productInfo = self.cvrData[_.get(record, 'product_id', null)];
        self.L.log('3. prepareNotification :: fetch templates and prepare notification records');

        if (productInfo) {
            let dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').format('Do MMM YYYY') : null;
            let payLoad = {
                amount: _.get(record, 'amount', null),
                dataConsumed: _.get(record, 'dataConsumed',null),
                recharge_number: _.get(record, 'recharge_number', null),
                operator:   self.notify.notificationBillSource == 'NONRU' && _.get(record,'operator') ? _.get(record,'operator') : _.get(productInfo, 'operator'),
                operator_label: _.get(productInfo, 'operator_label'),
                brand: _.get(productInfo, 'brand'),
                thumbnail: _.get(productInfo, 'thumbnail'),
                category_id: _.get(productInfo, 'category_id'),
                service: _.get(productInfo, 'service'),
                customer_id: _.get(record, 'customer_id', null),
                bank_name: _.get(record, 'bank_name', null),
                card_network: _.get(record, 'card_network', null),
                paytype: _.get(productInfo, 'paytype'),
                late_payment_surcharge: self.getLatePaymentSurcharge(record),
                time_interval:  _.get(record, 'time_interval', null),
                refId : _.get(record, "refId", null),
                rtspId : _.get(record, "rtspId", null),
                
            },
                notificationType;
            dueDate ? _.set(payLoad, 'due_date', dueDate) : '';
            if (_.toLower(_.get(payLoad, 'service', null)) == 'rent payment') {
                self.appendLandlordName(payLoad, record);
            }
            let emojiData = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES'], null);
            _.extend(payLoad, emojiData);
            if (_.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SYSTEM_FETCH_BILL_SOURCES', 'ALLOWED_SERVICES'], []).indexOf(_.toLower(_.get(productInfo, 'service'))) > -1) {
                _.set(payLoad, 'bill_source', self.getBillSource(record));
            }
            if (_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []).indexOf(_.toLower(_.get(productInfo, 'operator'))) > -1) {
                notificationType = 'PREPAID';
                let dueDateIntervals = self.notificationLibrary.getPossibleDueDates(_.get(record, 'operator', null), tableName);

                if (_.get(productInfo, 'operator', null) != _.get(record, 'operator', null)) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_DIFF_OPERATOR', `OPERATOR_RECEIVED_FROM_CVR:${_.get(productInfo, 'operator', null)}`, `OPERATOR_RECEIVED_FROM_KAFKA:${_.get(record, 'operator', null)}`]);
                }

                let defaultDueDateDiff = _.get(self.config, 'COMMON.DEFAULT_DUE_DATE_DIFF', 1);
                let dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD');
                let currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
                    oldBillFetchDate = MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD');
                // if(_.get(record, 'source', null) == "reminderBillGenPublisher"){
                //     notificationType = 'BILLGEN';
                //     _.set(record, 'notificationType', 'BILLGEN');
                // }
                if (_.get(record, 'templates', null) != null) {
                    notificationType = _.get(record, 'notificationType', null);
                }
                else if ((dueDateIntervals.indexOf(dueDate.diff(currentDate, 'days')) > -1) || (dueDate.diff(currentDate, 'days') <= defaultDueDateDiff)) {
                    notificationType = 'DUEDATE';
                    _.set(record, 'notificationType', 'DUEDATE');
                }
                else { // Bill Fetch from operator Prepaid Flow
                    // Process only T-1 Notifications, Skipping all bill generation notifications
                    if (_.get(record, 'notificationType') != 'DUEDATE') {
                        return callback(`drop bill gen notifications from operator prepaid flow`);
                    }
                }
            } else if (_.get(record, 'service_id', 0) == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
                notificationType = 'PREPAID';

                var daysToNextDueDate = _.get(self.operatorsBillsConfig, [_.get(productInfo, 'operator'), 'daysToNextDueDate'], 0);
                if (daysToNextDueDate) { // Normal Config based Prepaid Flow
                    self.decideNextDueDate(record, _.get(productInfo, 'operator'), tableName);
                } else { // Bill Fetch from operator Prepaid Flow
                    // Process only T-1 Notifications, Skipping all bill generation notifications
                    if (_.get(record, 'notificationType') != 'DUEDATE') {
                        return callback(`drop bill gen notifications from operator prepaid flow`);
                    }
                }
            }else {
                
                let dueDateIntervals = self.notificationLibrary.getPossibleDueDates(_.get(record, 'operator', null), tableName),
                    oldBillDueDateIntervals = self.notificationLibrary.getIntervalForOldDueDates(_.get(record, 'operator', null), tableName);
                
                if ( _.get(productInfo, 'operator', null) != _.get(record, 'operator', null) ) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_DIFF_OPERATOR', `OPERATOR_RECEIVED_FROM_CVR:${_.get(productInfo, 'operator', null)}` , `OPERATOR_RECEIVED_FROM_KAFKA:${_.get(record, 'operator', null)}`]);
                }

                let defaultDueDateDiff = _.get(self.config, 'COMMON.DEFAULT_DUE_DATE_DIFF', 1);
                let dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD');
                let currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
                    oldBillFetchDate = MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD');
                // if(_.get(record, 'source', null) == "reminderBillGenPublisher"){
                //     notificationType = 'BILLGEN';
                //     _.set(record, 'notificationType', 'BILLGEN');
                // }
                if(_.get(record, 'templates', null)!=null){
                    notificationType = _.get(record, 'notificationType', null);
                }
                else if (_.get(record, 'notificationType', null) == 'OLD_BILL_NOTIFICATION') {
                    notificationType = 'OLD_BILL_NOTIFICATION';
                    _.set(record, 'notificationType', 'OLD_BILL_NOTIFICATION');
                }
                else if((_.get(record, 'notificationType', null) != 'PREPAID_LOW_BALANCE') && (( dueDateIntervals.indexOf( dueDate.diff(currentDate, 'days') ) > -1) || (dueDate.diff(currentDate, 'days') <= defaultDueDateDiff)) ) {
                    let existingNotificationType = _.get(record, 'notificationType', null);
                    notificationType = 'DUEDATE';
                    _.set(record, 'notificationType', 'DUEDATE');
                    if (existingNotificationType == 'BILLGEN' && _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CONVERT_BG_TO_DUEDATE_FLAG', 'ALLOWED_SERVICES'], []).indexOf(_.toLower(payLoad.service)) > -1) {
                        _.set(payLoad, 'convertBGtoDueDate', true);
                    }
                } else if (_.get(record, 'notificationType', null) == 'PREPAID_LOW_BALANCE') {
                    notificationType = 'PREPAID_LOW_BALANCE';
                    _.set(record, 'notificationType', 'PREPAID_LOW_BALANCE');
                } else {
                    notificationType = 'BILLGEN';
                    _.set(record, 'notificationType', 'BILLGEN');
                }
            }

            if ( notificationType === 'BILLGEN' && _.has(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'BLACKLIST_BILLGEN_NOTIFICATION_OPERATOR', _.get(record, 'operator') ] ) ) {
                return callback(`Operator:${_.get(record, 'operator')} blacklisted for ${notificationType} NOTIFICATION`);
            } else if ( notificationType === 'DUEDATE' && _.has(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'BLACKLIST_DUEDATE_NOTIFICATION_OPERATOR', _.get(record, 'operator') ] ) ) {
                return callback(`Operator:${_.get(record, 'operator')} blacklisted for ${notificationType} NOTIFICATION`);
            } else if ( notificationType === 'PREPAID_LOW_BALANCE' && _.has(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'BLACKLIST_PREPAID_LOW_BALANCE_NOTIFICATION_OPERATOR', _.get(record, 'operator') ] ) ) {
                return callback(`Operator:${_.get(record, 'operator')} blacklisted for ${notificationType} NOTIFICATION`);
            }

            if ((_.toLower(_.get(record, 'service', null)) == 'financial services' || _.toLower(_.get(payLoad, 'service', null)) == 'financial services') ||  (_.toLower(_.get(record, 'service',null))=='financial services' && self.notify.notificationBillSource == 'NONRU')) {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'last_four_digits', self.getLast4digitsOfCC(_.get(record, 'recharge_number', null)));
                _.set(payLoad, 'minimum_due', self.getMinDueAmount(record));
            } else if ( _.get(this.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG', env , _.get(record, 'operator', null) , 'notificationCreateSendMinDueAmount' ], null )) {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'minimum_due', self.getMinDueAmount(record));
            }
            _.set(payLoad, "dataExhaustExtra",  _.get(record, 'dataExhaustExtra', null));

            if (_.toLower(_.get(payLoad, 'service', null)) == 'rent payment') {
                if (self.isWhitelistedForRentReminder(payLoad)) {
                    self.L.log('prepareNotification::', `record whitelisted for rent reminder, rech_number ${_.get(payLoad, 'recharge_number', null)}, customer_id ${_.get(payLoad, 'customer_id', null)}`);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:NOTIFICATION_SERVICE', 'NOTIFICATION_TYPE:RENT_REMINDER', 'STATUS:WHITELISTED', `OPERATOR:${_.get(payLoad, 'operator', 'UNKNOWN')}`]);
                } else {
                    self.L.log('prepareNotification::', `record not whitelisted for rent reminder, rech_number ${_.get(payLoad, 'recharge_number', null)}, customer_id ${_.get(payLoad, 'customer_id', null)}`);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:NOTIFICATION_SERVICE', 'NOTIFICATION_TYPE:RENT_REMINDER', 'STATUS:NOT_WHITELISTED', `OPERATOR:${_.get(payLoad, 'operator', 'UNKNOWN')}`]);
                    return callback(`record not whitelisted for rent reminders`);
                }
            }

            let templates = self.getTemplates(record, notificationType, payLoad, tableName);
            let notificationRecords;
            if (self.notify.notificationBillSource == 'NONRU') {
                notificationRecords = [
                    {
                        type: 'PUSH',
                        recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                        notificationType: notificationType,
                        template_id: _.get(templates, 'PUSH', null),
                    }
                ];
            } else {
                notificationRecords = [
                    {
                        type: 'SMS',
                        recipients: _.get(record, 'customer_mobile', null),
                        notificationType: notificationType,
                        template_id: _.get(templates, 'SMS', null),
                    },
                    {
                        type: 'EMAIL',
                        recipients: _.get(record, 'customer_email', null),
                        notificationType: notificationType,
                        template_id: _.get(templates, 'EMAIL', null),
                    },
                    {
                        type: 'PUSH',
                        recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                        notificationType: notificationType,
                        template_id: _.get(templates, 'PUSH', null),
                    },
                    {
                        type: 'CHAT',
                        recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                        notificationType: notificationType,
                        template_id: _.get(templates, 'CHAT', null),
                    }
                ];
            }
                
            let short_operator_name = null;
            

            // if (_.get(record, 'customer_mobile', null)) {
            //     try {
            //         let notificationRecordForWhatsapp = {
            //             type: 'WHATSAPP',
            //             recipients: _.get(record, 'customer_mobile', null),
            //             notificationType: notificationType,
            //             template_id: _.get(templates, 'WHATSAPP', null),
            //         };
            //         let existingFallbackCustomerId = await self.nonPaytmBillsModel.readFallbackCustomerId({"customerId": payLoad.customer_id});
            //         if (existingFallbackCustomerId.length) {
            //             self.L.log(`prepareNotification::  existing records found for fallback customerId:${payLoad.customer_id} in Cassandra db`);
            //             notificationRecords.push(notificationRecordForWhatsapp);
            //         } else {
            //             try{
            //                 await self.notify.insertRejectedNotificationsViaPromise( 'no records found for fallback customerId', record, notificationRecordForWhatsapp);
            //             }catch(e){
            //                 self.L.error('Error while inserting rejected notification for customerId:', payLoad.customer_id, e);
            //             }
            //             self.L.log(`prepareNotification::  no records found for fallback customerId:${payLoad.customer_id} in Cassandra db`);
            //         }
            //     } catch (error) {
            //         self.L.error('Error while fetching FallbackCustomerId record for customerId:', payLoad.customer_id, error);
            //     }   
            // }

            try {
                let notificationRecordWhatsapp = await self.notificationLibrary.checkForWhatsappNotification(record, payLoad, notificationType);
                if (!_.isNull(notificationRecordWhatsapp) && !notificationRecordWhatsapp.error) {
                    if (_.toLower(_.get(payLoad, 'service', null)) == 'rent payment' && self.isWhitelistedForRentWhatsapp(_.get(payLoad, 'customer_id', null))) {
                        self.L.log(`record eligible for WA notification for rent payment, customer id ${_.get(payLoad, 'customer_id', null)}`);
                        _.set(notificationRecordWhatsapp, 'template_id', _.get(templates, 'WHATSAPP', null));
                    }
                    notificationRecords.push(notificationRecordWhatsapp);
                } else {
                    let rejectedRecord = _.cloneDeep(record);
                    _.set(rejectedRecord, 'type', 'WHATSAPP');
                    if (_.get(rejectedRecord, 'type', null) == 'WHATSAPP' && (_.get(payLoad, 'convertBGtoDueDate', false)) ) {
                        self.L.log(`getDataForMeasurementAndTracking:: setting timepoint and notif_type for whatsapp when conversion is true`);
                        let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;
                        if(billFetchDate){
                            _.set(rejectedRecord, 'timepoint', MOMENT().diff(billFetchDate, 'days'));

                        }
                        _.set(rejectedRecord, 'notificationType', 'BILLGEN');
            
                    }
                    await self.notify.insertRejectedNotificationsViaPromise(notificationRecordWhatsapp.error?notificationRecordWhatsapp.error:'checks for whatsapp notification failed, dropping notification', rejectedRecord);
                }
            } catch (error) {
                self.L.error('prepareNotification::', `error occurred while checking for whatsapp notification for cust id ${_.get(payLoad, 'customer_id', null)}, rech number ${_.get(payLoad, 'service', null) == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(_.get(payLoad, 'recharge_number', null)) : _.get(payLoad, 'recharge_number', null)}`, error);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                    `STATUS: ERROR_OCCURRED`,
                    `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                    `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
                ]);
            }
            
            try {
                short_operator_name = _.get(record, 'source',null)!='notifyRejectedBills'?_.get(JSON.parse(productInfo.attributes), 'short_operator_name', null):null;
            }
            catch (error) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_PID_PARSE', 'STATUS:ERROR',`PARTIAL_BILL:${_.get(record,'partialBillState', 'NO_STATE')}`]);
                self.L.error('Error while parsing attributes for PID', _.get(record, 'product_id', null), error);
            }

            short_operator_name = short_operator_name || _.get(productInfo, 'brand');
            _.set(payLoad, 'short_operator_name', short_operator_name);

            self.createShortUnsubscribeUrl(function (err, short_url) {
                if (err) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_SHORT_UNSUBURL', 'STATUS:ERROR',`PARTIAL_BILL:${_.get(record,'partialBillState', 'NO_STATE')}`]);
                    self.L.error('Error creating unsubscribe url ::', err);
                } else {
                    _.set(payLoad, 'unsubscribe_url', short_url);
                }
                ASYNC.map(
                    notificationRecords,
                    (notificationRecord, next) => {
                        self.sendNotification(record, notificationRecord, payLoad, () => {
                            next();
                        });
                    },
                    err => {
                        callback();
                    }
                )
            }, _.get(productInfo, 'operator'), _.get(record, 'recharge_number',null), record);


        } else {
            let errorMsg = `Product info does not exist for the product id: ${_.get(record, 'product_id', null)}`;
            self.L.error(errorMsg);
            callback(errorMsg);
        }
    }

    isWhitelistedForRentWhatsapp(customerId) {
        let self = this;
        let isRentWhatsAppEnabledForAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'WHATSAPP_NOTIFICATION', 'RENT_WHATSAPP_ENABLED_FOR_ALL'], null);
        if (isRentWhatsAppEnabledForAll) {
            return true;
        }
        let whitelistedCustomerIds = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'WHATSAPP_NOTIFICATION', 'RENT_WHATSAPP_ENABLED_FOR_CUSTOMER_IDS'], null);
        customerId = customerId ? customerId.toString() : null;
        if (whitelistedCustomerIds) {
            return whitelistedCustomerIds.includes(customerId);
        }
        return false;
    }

    getLast4digitsOfCC(rechargeNumber = "") {
        let self = this;
        try {
            let last4Digits = rechargeNumber.replace(/ /g, "");
            last4Digits = last4Digits.substring(last4Digits.length - 4);
            return last4Digits;
        }
        catch (err) {
            self.L.error('billReminder::getLast4digitsOfCC', 'Error while finding last 4 digits of CC', err);
            return null;
        }
    }

    getMinDueAmount(record) {
        let self = this;
        try {
            let customerOtherInfo = JSON.parse(_.get(record, 'customerOtherInfo', null));
            let currentMinBillAmount = _.get(customerOtherInfo, 'currentMinBillAmount', null)
            return currentMinBillAmount;
        }
        catch (err) {
            self.L.error('billReminder::getMinDueAmount', 'Error while parsing customerOtherInfo', err);
            return null;
        }
    }

    createShortUnsubscribeUrl(cb, operator, recharge_number, record) {
        let self = this,
            unsubscribe_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null);
        if(self.notify.notificationBillSource == 'NONRU'){
            operator = _.get(record, 'soruce',null)=='notifyRejectedBills'? _.get(record, 'operator', null) : operator;
        }
        unsubscribe_url = unsubscribe_url + `/unsubscribe?operator=${operator}&recharge_number=${recharge_number}`;
        let object = new utility.TinyUrl();
        object.createShortUrl(cb, _.get(self.config, ['TINYURL_CONFIG'], null), unsubscribe_url);
    }

    createUrl(paramsForUrl) {
        let self = this,
            tinyurl = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'TINY_URL_API'], null),
            product_id = _.get(paramsForUrl, 'product_id', null),
            category_id = _.get(paramsForUrl, 'category_id', null),
            recharge_number = _.get(paramsForUrl, 'recharge_number', null),
            user_data = _.get(paramsForUrl, 'user_data', '{}'),
            amount = _.get(paramsForUrl, 'amount', null),
            product_service = _.get(paramsForUrl, 'product_service', null),
            notificationRecord = _.get(paramsForUrl, 'notificationRecord', null),
            operator = _.get(paramsForUrl, 'operator', null),
            short_operator_name = _.get(paramsForUrl, 'short_operator_name', null),
            notificationType = _.get(notificationRecord, 'notificationType', null),
            type = _.get(notificationRecord, 'type', null),
            paytype = _.get(paramsForUrl, 'paytype', null),
            template_id = _.get(notificationRecord, 'template_id', null),
            smart_url =
                _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_PRODUCT_ID', `${product_id}`],
                    _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_CATEGORY_ID', `${category_id}`],
                        _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE', `${product_service}::${paytype}`], null))),
            sms_url = '',
            rech_num_count = -1;

        if (product_id == null || category_id == null || recharge_number == null || product_service == null || notificationRecord == null) {
            return ["paramsForUrl cannot have null values", null];
        }

        if (smart_url && smart_url.length) {
            if(product_service=='mobile'){
                sms_url = smart_url[0] + `?$category_id=${category_id}&`;
            }else if(_.toLower(product_service) == 'paytm postpaid'){
                sms_url = smart_url[0] + `?`;
            }else{
                sms_url = smart_url[0] + `?category_id=${category_id}&`;
            }
            if (smart_url.length > 1) {
                rech_num_count = Number(smart_url[1]);
            }
        } else {
            self.L.error('createUrl:: Smart URL is not picked for operator: ', operator, ' service: ', product_service, ' paytype: ', paytype);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_SMART_URL', 'STATUS:ERROR']);
            let product_service_sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'PRODUCT_SERVICE_SMS_URL'], null);
            if(product_service=='mobile'){
                sms_url = _.get(product_service_sms_url, product_service, null) + '?$';
            }
            else{
                sms_url = _.get(product_service_sms_url, product_service, null) + '?';
            }
        }

        if (sms_url == null) {
            sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null) + "?";
        } else {
            sms_url = sms_url + `${self.getQueryParams(paramsForUrl, '&')}`;
            if (user_data) {
                try {
                    user_data = JSON.parse(user_data) || {};
                    if (smart_url && smart_url.length) {
                        for (let i = 2; i <= rech_num_count; i++) {
                            let key = 'recharge_number_' + i;
                            let value = user_data[key] || '';
                            sms_url = sms_url + `&${key}=${value}`;
                        }
                    } else {
                        for (let i = 2; i < 10; i++) {
                            let key = 'recharge_number_' + i,
                                value = '';

                            if (!_.has(user_data, [key], null)) break;

                            value = _.get(user_data, [key], '');
                            sms_url = sms_url + `&${key}=${value}`;
                        }
                    }
                } catch (error) {
                    self.L.error("Error in parsing of data fetched: ", error);
                }
            }

        }
        sms_url = sms_url + self._utmByTemplateId(template_id, short_operator_name, '&', product_service, operator);
        return [null, sms_url];
    }

    createTinyUrl(url, callback) {
        let
            self = this,
            object = new utility.TinyUrl();
            self.L.log("url to be shortened:: ", url);
        object.createShortUrl(callback, _.get(self.config, ['TINYURL_CONFIG'], null), url);
    }

    sendProcessedNotification(record, notificationData, notificationRecord, callback) {
        let self = this;
        try {
            let
                source = _.get(record, 'source', null),
                sourceIdMapping = _.invert(_.get(self.notificationConfig, 'source'), {});
            if(self.notify.notificationBillSource == 'NONRU'){
                sourceIdMapping = _.invert(_.get(self.notificationConfig, 'nonRuSource'), {});
            }
            let source_id = _.get(sourceIdMapping, source, 1);
            
            let additionalData = self.getDataForMeasurementAndTracking(record, notificationRecord, notificationData);
            _.set(notificationData, 'additional_data', additionalData);
            let send_at = self.getSendAtForNotification(record, notificationRecord, source_id);
            if (!send_at) {
                return callback();
            }

            if (_.get(record, 'recharge_number', null)) {
                record.recharge_number = _.get(record, 'recharge_number').toString().replace(/'/g, '');
            }
            let body = {
                "source_id": source_id,
                "category_id": 1,
                "recharge_number": _.get(record, 'recharge_number', null),
                "product_id": _.get(record, 'product_id', null),
                "max_retry_count": 2,
                "retry_interval": 30,
                "type": _.get(notificationRecord, 'type', null),
                "template_id": _.get(notificationRecord, 'template_id', null),
                "recipient": _.get(notificationRecord, 'recipients', null),
                "send_at": send_at,
                "data": notificationData,
                "correlationId": _.get(record, 'correlationId', null),
                "dayValue":_.get(record, 'dayValue',null),
                "recon_id": _.get(record, 'recon_id', null),
                "partialBillState": _.get(record, 'partialBillState', null),
                "dataExhaustExtra": _.get(record, 'dataExhaustExtra', null),
                "remind_later_date": _.get(record, 'remind_later_date', null),
                "rules": {
                    "condition": `category_id=1 and source_id=${source_id} and recharge_number='${_.get(record, 'recharge_number', null)}' and product_id=${_.get(record, 'product_id', null)} 
                                    and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)}`,
                    "actions": [
                        {
                            "status": "pending",
                            "action": "drop"
                        },
                        {
                            "status": "sent",
                            "action": "drop"
                        }
                    ]
                },
                "operator_validated_at": _.get(record, "operator_validated_at", null)
            };

            if(_.get(record, 'time_interval', null)){
                body['time_interval'] = _.get(record, 'time_interval', null);
            }

            _.set(body, 'timestamps', _.get(record, 'timestamps', {}));

            if(_.get(record,'skipNotification',null) == 1) {
                self.logger.log(`sendProcessedNotification ${_.get(notificationRecord, 'type', null)} skipped because of skipNotification=1 for ${_.get(record, 'debugKey')} body:`, body, _.get(record, 'service', ''));
                return self.notify.insertRejectedNotifications(callback, 'skipped due to skipping_notification=1', record, notificationRecord);
            }

            // Calling notify operation internally instead of API hit
            self.notify.__createNotification(function (error, data) {
                if (error) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION', 'STATUS:ERROR']);
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_CREATE")
                    self.L.error('sendProcessedNotification', 'createNotification', 'Error :', error, `for ${_.get(notificationRecord, 'type', null)}-${_.get(record, 'debugKey')}`);
                    return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
                } else {
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"CREATED")
                    self.L.log('sendProcessedNotification', `${_.get(notificationRecord, 'type', null)} successful for ${_.get(record, 'debugKey')}`);
                    return callback();
                }
                
            }, body);

        } catch (error) {
            self.L.error('sendProcessedNotification::Error sending notification::Err:Msg', error);
            return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
        }
    }

    isWhitelistedForRentReminder(payLoad) {
        let self = this;
        let customer_id = _.get(payLoad, 'customer_id', null) ? _.get(payLoad, 'customer_id', null).toString() : null;
        let cugList = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'RENT_REMINDERS', 'WHITELISTED_CUST_IDS'], []);
        let configEnabledForAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'RENT_REMINDERS', 'ENABLED_FOR_ALL'], false);
        if(configEnabledForAll) {
            return true;
        }
        return cugList.includes(customer_id);
    }

    getSendAtForNotification(record, notificationRecord, source_id) {
        let self = this;
        let send_at = MOMENT(_.get(record,['timestamps', 'billFetchReminder_onBoardTime'],null)).isValid()? MOMENT(_.get(record,['timestamps', 'billFetchReminder_onBoardTime'],null)).format('YYYY-MM-DD HH:mm:ss.SSS'):MOMENT().format('YYYY-MM-DD HH:mm:ss.SSS');

        if (_.get(notificationRecord, 'type', null) == 'WHATSAPP') {
            return self.getSendAtForWhatsappNotification(record, notificationRecord);
        }

        if (self.notify.notificationBillSource == 'NONRU') {
            return self.getSendAtForNONRU(source_id);
        }
        try{
            let topicToPublish = self.notify.getTopicToPublish(1, source_id, _.get(notificationRecord, 'type', null),_.get(notificationRecord, 'template_id', null))
            let hourTosendNotification = topicToPublish.includes('_NOTIFICATION')? null : topicToPublish.split('_')[1];
            
            let 
            currentDate = MOMENT().format('YYYY-MM-DD'),
            startDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_START_TIME'], '09:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            endDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            dueDateExpiryTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'DUE_DATE_NOTIFICATOION_EXPIRY_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            startOfDay = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            startOfHour =hourTosendNotification? MOMENT().startOf('day').add(hourTosendNotification, 'hours').format('YYYY-MM-DD HH:mm:ss'):null
            
            if(!hourTosendNotification && topicToPublish!='RT_NOTIFICATION'){
                if(!MOMENT(send_at).isBetween(startDateTime, endDateTime)){
                    if (MOMENT(send_at).isBetween(startOfDay, startDateTime)) {
                        send_at = startDateTime;
                    } else {
                        send_at = MOMENT(startDateTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
                    }
                }
            }else if (hourTosendNotification){
                if(!MOMENT(send_at).isBetween(startOfHour, endDateTime)){ //3pm-10pm
                    if (MOMENT(send_at).isBetween(startOfDay, startOfHour)) {
                        send_at = startOfHour;
                    } else {
                        send_at = MOMENT(startOfHour).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
                    }
                }
            }
            if(MOMENT().isAfter(dueDateExpiryTime) && _.get(record, 'notificationType', null)=='DUEDATE' && topicToPublish!='RT_NOTIFICATION'){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION', 'STATUS:ERROR', 'TYPE:DUEDATE_NOTIFICATOION_EXPIRY_TIME']);
                self.L.log(`sendProcessedNotification:: DUEDATE notification for ${_.get(record, 'debugKey')} is not sent because of DUEDATE_NOTIFICATOION_EXPIRY_TIME`);
                return null;
            }
        }catch(e){
            self.L.error(`Error in calculating send at:: setting default send_at as now()`, e);
        }
        return send_at;
    }

    //whatsapp -> realtme -> 7AM
    //whatsapp -> normal ->

    getSendAtForWhatsappNotification(record, notificationRecord) {
        /**
         * 1. if notificationType = BILLGEN and day_value = 0 -> realtime whatsapp
         *      a. non-DND hours - set send_at = current time
         *      b. DND hours - set send_at = next day morning 7 am
         * 2. else -> set send_at = 9 am (whatsapp pipeline start time)
         */
        let self = this;
        let send_at = null;
        let currentDate = MOMENT().format('YYYY-MM-DD');
        
        let rtStartDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'BR_CONSUMER', 'START_TIME'], '07:00:00')).format('YYYY-MM-DD HH:mm:ss');
        let rtEndDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'BR_CONSUMER', 'END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss');


        if (_.get(record, 'notificationType', null) == 'BILLGEN' && Number(_.get(record, 'timepoint', null)) == 0) {
            rtStartDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'BR_RT_CONSUMER', 'START_TIME'], '07:00:00')).format('YYYY-MM-DD HH:mm:ss');
            rtEndDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'BR_RT_CONSUMER', 'END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss');
        }

            
        if (MOMENT().isBetween(MOMENT(rtStartDateTime), MOMENT(rtEndDateTime))) {
            send_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        } else if(MOMENT().isBetween(MOMENT(rtEndDateTime), MOMENT().endOf('day'))){  //11PM notification
            send_at = MOMENT(rtStartDateTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
        }else{
            send_at = MOMENT(rtStartDateTime).format('YYYY-MM-DD HH:mm:ss');
        }

        self.L.log('getSendAtForWhatsappNotification::', `send_at set to ${send_at} for record ${_.get(record, 'debugKey', null)}`);
        return send_at;
    }

    getSendAtForNONRU(source_id) {
        var self = this;
        let send_at = MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        currentDate = MOMENT().format('YYYY-MM-DD'),
        startDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig,  ['scheduletimeinterval', 'BR_NOTIFICATION_START_TIME'], '13:00:00')).format('YYYY-MM-DD HH:mm:ss'),
        endDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
        startOfDay = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
       
        let nonRuRealTimeNotificationSourceIds = _.get(self.notificationConfig, 'nonRurealTimeNotificationSourceIds',['4','6','7']);

        if (!nonRuRealTimeNotificationSourceIds.includes(source_id+'') && !MOMENT(send_at).isBetween(startDateTime, endDateTime)) {
            if (MOMENT(send_at).isBetween(startOfDay, startDateTime)) {
                send_at = startDateTime;
            } else {
                send_at = MOMENT(startDateTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        return send_at;
    }

    sendNotification(record, notificationRecord, data, callback) {

        if (_.get(notificationRecord, 'recipients', null) && _.get(notificationRecord, 'template_id', null)) {
            let self = this,
                paramsForUrl,
                payLoad = _.clone(data);

                let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
                if (product_service == 'electricity') {
                    try {
                        let extra = _.get(record, 'extra', null),
                        productInfo = self.cvrData[_.get(record, 'product_id', null)], 
                        attributes = JSON.parse(_.get(productInfo, 'attributes', '{}'));
                        extra = JSON.parse(extra);
                        _.set(record, 'ambiguous', extra != null ? _.get(extra, 'ambiguous', null) : null);
                        const board = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'UPPCL_CONFIG', 'BOARD'], 'Uttar Pradesh Power (UPPCL)');
                        _.set(record, 'board', board);
                        _.set(record, 'state', attributes != null ? _.get(attributes, 'state', null) : null);
                    } catch(error) {
                        self.L.log("Error in parsing the ambiguous information");
                    }
                }

            if (_.get(notificationRecord, 'type', null) == 'SMS') {
                self.notificationLibrary.disableSmsForUser(record, (error, res) => {
                    if(error) {
                        utility.sendNotificationMetricsFromCreate(record,notificationRecord,"SMS_DISABLED")
                        return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
                    }
                    let isSmsBlockedForUser = res;
                    if(isSmsBlockedForUser) {
                        utility.sendNotificationMetricsFromCreate(record,notificationRecord,"SMS_DISABLED")
                        return self.notify.insertRejectedNotifications(callback, 'SMS is blocked for this customertype', record, notificationRecord);
                    }
                    paramsForUrl = self.getParamsForUrl(record, notificationRecord, payLoad);
                    let [err, url] = self.createUrl(paramsForUrl);
                    if (!err && url) {
                        self.appendPromoCodeInNotification(payLoad, notificationRecord,(promoCampaignErr, promoCampaign)=>{
                            if(promoCampaignErr == null) {
                                if(_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                                    if(url.includes("utm_campaign"))url = url + `_P-${_.get(promoCampaign, "promocode")}`;                      // appending  _P-<promocode> to utm_campaign in deeplink
                                    _.set(payLoad,"promo_content", _.get(promoCampaign, "promo_text", null));   // appending promo_content in dynamicParams
                                    _.set(payLoad,"promo_code", _.get(promoCampaign, "promocode", null));   // appending promo_code in dynamicParams
                                    self.L.log(`appendPromoCodeInNotification :: value of url: ${url} and value of promo_content: ${_.get(payLoad,'promo_content')}`);
                                }
                            }
                            self.createTinyUrl(url, function (err, tiny_url) {
                                if (!err && tiny_url) {
                                    self.L.log("shortened url:: ", tiny_url);
                                    let tiny_url_data = { 'sms_short_link': tiny_url };
                                    _.extend(payLoad, tiny_url_data);
                                    let smsNotificationData = self.notificationLibrary.getSmsNotiData(payLoad, notificationRecord);
                                    self.sendProcessedNotification(record, smsNotificationData, notificationRecord, callback);
                                }
                                else {
                                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_URLCREATE")
                                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_SMSSHORTLINK', 'STATUS:ERROR']);
                                    self.L.error(err);
                                    return self.notify.insertRejectedNotifications(callback, 'Error in creating url', record, notificationRecord);
                                }
                            });
                        });
                    } else {
                        utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_URLCREATE")
                        self.L.error(err);
                        return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                    }
                });
            } else if (_.get(notificationRecord, 'type', null) == 'PUSH') {
                let deepLinkData = {};
                let url_type = "external";
                let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
                let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
                let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
                let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
                let paytype = _.get(payLoad, 'paytype', null) != null ? _.get(payLoad, 'paytype', null).toLowerCase() : null;
                let short_operator_name = _.get(payLoad, "short_operator_name", null);
                let template_id = _.get(notificationRecord, "template_id", null);
                let operator = _.get(payLoad, "operator", null);
                let utm = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);
                if (!landing_path) {
                    if(self.notify.notificationBillSource == 'NONRU'){
                        if (product_service === 'mobile' && paytype === 'prepaid') {
                            landing_path = "mobile_prepaid";
                        } else if (product_service === 'mobile' && paytype === 'postpaid') {
                            landing_path = "datacard_postpaid";
                        } else if (product_service === 'datacard' && paytype === 'prepaid' ) {
                            landing_path = "datacard_prepaid";
                        } else if (product_service === 'datacard' && paytype === 'postpaid') {
                            landing_path = "datacard_postpaid";
                        } else if (product_service === 'dth') {
                            landing_path = "dth";
                        } else {
                            landing_path = 'utility';
                        }
                    } else{  
                        if (product_service === 'mobile') {
                            landing_path = "mobile_postpaid";
                        } else if (product_service === 'datacard') {
                            landing_path = "datacard_postpaid";
                        } else if (product_service === 'dth') {
                            landing_path = "dth";
                        } else {
                            landing_path = 'utility';
                        }
                    }
                }

                let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
                if(_.toLower(product_service)=='paytm postpaid'){
                    let deeplinkForPaytmPostpaid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE',`${product_service}::${paytype}`],[]);
                    if(deeplinkForPaytmPostpaid && deeplinkForPaytmPostpaid.length){
                        url = deeplinkForPaytmPostpaid[0];
                    }
                }
                if(product_service.toLowerCase()=='fastag recharge'){
                    let deeplinkForFastagPrepaid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE',`${product_service.toLowerCase()}::${paytype}`],[]);
                    if(deeplinkForFastagPrepaid && deeplinkForFastagPrepaid.length){
                        url = deeplinkForFastagPrepaid[0];
                    }
                }
                paramsForUrl = self.getParamsForChatAndPush(record, payLoad);
                _.set(paramsForUrl, "isRealTimeDataExhausted", _.get(record, "isRealTimeDataExhausted", false));
                let completeUrl = null;
                if(product_service=='mobile'){
                    completeUrl = url + "?$" + self.getQueryParams(paramsForUrl, '$')+ self.getExtraRechargeNum(record, '$') + utm;
                }else if (_.toLower(product_service) == 'rent payment') {
                    let aId = '15b13a1757fa446180e6f5d2f18ec435';
                    let data = 'eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL3JlbnRhbHNlcnZpY2VzL3YxL2luZGV4Lmh0bWwifQ==';
                    completeUrl = `paytmmp://mini-app?aId=${aId}&data=${data}&category_id=${_.get(paramsForUrl, 'category_id', null)}&product_id=${_.get(paramsForUrl, 'product_id', null)}&recharge_number=${_.get(paramsForUrl, 'recharge_number', null)}${self.getExtraRechargeNum(record, '&')}${utm}`;
                }else {
                    completeUrl = url + "?" + self.getQueryParams(paramsForUrl, '$')+ self.getExtraRechargeNum(record, '$') + utm;
                }
                if(_.get(record, "isRealTimeDataExhausted", false)){
                    if (completeUrl.includes('$')) {
                        completeUrl += '$dataRecharge=true';
                    } else {
                        completeUrl += '?$dataRecharge=true';
                    }
                }
                self.appendPromoCodeInNotification(payLoad, notificationRecord,(promoCampaignErr, promoCampaign)=>{
                    if(promoCampaignErr == null) {
                        if(_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                            if(completeUrl.includes("utm_campaign"))completeUrl = completeUrl + `_P-${_.get(promoCampaign, "promocode")}`;                      // appending  _P-<promocode> to utm_campaign in deeplink
                            _.set(payLoad,"promo_content", _.get(promoCampaign, "promo_text", null));   // appending promo_content in dynamicParams
                            _.set(payLoad,"promo_code", _.get(promoCampaign, "promocode", null));   // appending promo_code in dynamicParams
                            self.L.log(`appendPromoCodeInNotification :: value of url: ${completeUrl} and value of promo_content: ${_.get(payLoad,'promo_content')}`);
                        }
                    }
                    deepLinkData = {
                        "payLoad": payLoad,
                        "extra": {
                            "url": completeUrl,
                            "url_type": url_type
                        },
                        "paramsForUrl": paramsForUrl
                    }

                    let pushNotificationData = self.notificationLibrary.getPushNotiData(deepLinkData, notificationRecord, 1);
                    self.sendProcessedNotification(record, pushNotificationData, notificationRecord, callback);
                });
            } else if (_.get(notificationRecord, 'type', null) == 'CHAT') {
                let deepLinkUrl = self.getDeepLinkUrl(record, notificationRecord, payLoad);
                _.set(payLoad, 'deeplink', deepLinkUrl);
                self.appendPromoCodeInNotification(payLoad, notificationRecord,(promoCampaignErr, promoCampaign)=>{
                    if(promoCampaignErr == null) {
                        if(_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                            let completeUrl = _.get(payLoad, 'deeplink');
                            if(completeUrl.includes("utm_campaign")) completeUrl = completeUrl + `_P-${_.get(promoCampaign, "promocode")}`;                     // appending  _P-<promocode> to utm_campaign in deeplink
                            _.set(payLoad,"promo_content", _.get(promoCampaign, "promo_text", null));   // appending promo_content in dynamicParams
                            _.set(payLoad,"promo_code", _.get(promoCampaign, "promocode", null));   // appending promo_code in dynamicParams
                            _.set(payLoad, 'deeplink', completeUrl);
                            self.L.log(`appendPromoCodeInNotification :: value of url: ${completeUrl} and value of promo_content: ${_.get(payLoad,'promo_content')}`);
                        }
                    }
                    let [error, chatNotificationData] = self.notificationLibrary.getChatNotiData(payLoad, notificationRecord);
                    if (error) {
                        self.L.error('sendNotification', `Error generating chat payload for ${_.get(record, 'debugKey', null)}, templateId:${_.get(notificationRecord, 'template_id', null)} with error - ${error}`);
                        utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_PAYLOADGEN")
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTI_CHAT_V3PAYLOAD', 'STATUS:ERROR']);
                        return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
                    } else {
                        self.sendProcessedNotification(record, chatNotificationData, notificationRecord, callback);
                    }
                });
            } else if (_.get(notificationRecord, 'type', null) == 'WHATSAPP') {
                paramsForUrl = self.getParamsForUrl(record, notificationRecord, payLoad);
                let wpValue = '';
                let service = _.get(record, 'service', null);
                if(service && _.toUpper(service) == 'LOAN'){
                    wpValue = `1&product_id=${_.get(paramsForUrl, 'product_id', null)}`;
                } else if (_.toLower(service) == 'rent payment'){
                    let short_operator_name = _.get(payLoad, "short_operator_name", null);
                    let template_id = _.get(notificationRecord, "template_id", null);
                    let operator = _.get(payLoad, "operator", null);
                    let utm = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);
                    wpValue = `1$category_id=${_.get(paramsForUrl, 'category_id', null)}$product_id=${_.get(paramsForUrl, 'product_id', null)}$recharge_number=${_.get(paramsForUrl, 'recharge_number', null)}${self.getExtraRechargeNumForRentWA(record, '$')}${utm}`;
                } else if(_.toLower(service) == 'fastag recharge'){
                    wpValue = `1`;
                    let recharge_number = _.get(paramsForUrl, 'recharge_number', null);
                    if(recharge_number && !_.toLower(_.toString(recharge_number)).includes('default')){
                        wpValue += `&recharge_number=${recharge_number}&product_id=${_.get(paramsForUrl, 'product_id', null)}`;
                    }
                    wpValue += `&toAmount=true` + self.getExtraRechargeNum(record) + self.notificationLibrary.getWpPriceSuffix(record);
                }else{
                    wpValue = `1&recharge_number=${_.get(paramsForUrl, 'recharge_number', null)}&product_id=${_.get(paramsForUrl, 'product_id', null)}&toAmount=true` + self.getExtraRechargeNum(record) + self.notificationLibrary.getWpPriceSuffix(record);
                }
                _.set(payLoad, 'wp', wpValue);
                let [err,whatsappNotificationData] = self.notificationLibrary.getWhatsappNotiData(payLoad, notificationRecord);
                if (err) {
                    self.L.error('sendNotification', `Error generating whatsapp payload for ${_.get(record, 'debugKey', null)}, templateId:${_.get(notificationRecord, 'template_id', null)} with error - ${err}`);
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_PAYLOADGEN")
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTI_WHATSAPP_V3PAYLOAD', 'STATUS:ERROR']);
                    return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                } else {
                    self.sendProcessedNotification(record, whatsappNotificationData, notificationRecord, callback);
                }
                // let [err, url] = self.createUrl(paramsForUrl);
                // if (!err && url) {
                //     self.appendPromoCodeInNotification(payLoad, notificationRecord,(promoCampaignErr, promoCampaign)=>{
                //         if(promoCampaignErr == null) {
                //             if(_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                //                 if(url.includes("utm_campaign"))url = url + `_P-${_.get(promoCampaign, "promocode")}`;                      // appending  _P-<promocode> to utm_campaign in deeplink
                //                 _.set(payLoad,"promo_content", _.get(promoCampaign, "promo_text", null));   // appending promo_content in dynamicParams
                //                 _.set(payLoad,"promo_code", _.get(promoCampaign, "promocode", null));   // appending promo_code in dynamicParams
                //                 self.L.log(`appendPromoCodeInNotification :: value of url: ${url} and value of promo_content: ${_.get(payLoad,'promo_content')}`);
                //             }
                //         }
                //         self.createTinyUrl(url, function (err, tiny_url) {
                //             if (!err && tiny_url) {
                //                 self.L.log("shortened url:: ", tiny_url);
                //                 let tiny_url_data = { 'whatsapp_short_link': tiny_url };
                //                 _.extend(payLoad, tiny_url_data);
                                // let [err,whatsappNotificationData] = self.notificationLibrary.getWhatsappNotiData(payLoad, notificationRecord);
                                // if(err){
                                //     self.L.error('sendNotification', `Error generating whatsapp payload for ${_.get(record, 'debugKey', null)}, templateId:${_.get(notificationRecord, 'template_id', null)} with error - ${err}`);
                                //     utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_PAYLOADGEN")
                                //     utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTI_WHATSAPP_V3PAYLOAD', 'STATUS:ERROR']);
                                //     return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                                // }else{
                                //     self.sendProcessedNotification(record, whatsappNotificationData, notificationRecord, callback);
                                // }
                //             }
                //             else {
                //                 utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_URLCREATE")
                //                 utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION_WHATSAPP_SHORTLINK', 'STATUS:ERROR']);
                //                 self.L.error(err);
                //                 return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                //             }
                //         });
                //     });
                // } else {
                //     self.L.error(err);
                //     return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record);
                // }
            } else {
                paramsForUrl = self.getParamsForUrl(record, notificationRecord, payLoad);
                let [err, url] = self.createUrl(paramsForUrl);
                if (!err && url) {
                    self.appendPromoCodeInNotification(payLoad, notificationRecord,(promoCampaignErr, promoCampaign)=>{
                        if(promoCampaignErr == null) {
                            if(_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                                if(url.includes("utm_campaign")) url = url + `_P-${_.get(promoCampaign, "promocode")}`;                      // appending  _P-<promocode> to utm_campaign in deeplink
                                _.set(payLoad,"promo_content", _.get(promoCampaign, "promo_text", null));   // appending promo_content in dynamicParams
                                _.set(payLoad,"promo_code", _.get(promoCampaign, "promocode", null));   // appending promo_code in dynamicParams
                                self.L.log(`appendPromoCodeInNotification :: value of url: ${url} and value of promo_content: ${_.get(payLoad,'promo_content')}`);
                            }
                        }
                        let url_data = { 'url': url };
                        _.extend(payLoad, url_data);
                        let emailNotificationData = self.notificationLibrary.getEmailNotiData(payLoad, notificationRecord);
                        self.sendProcessedNotification(record, emailNotificationData, notificationRecord, callback);
                    });
                } else {
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_URLCREATE")
                    self.L.error(err);
                    return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                }
            }
        } else {
            let self = this;
            utility.sendNotificationMetricsFromCreate(record,notificationRecord , "INVALID_RECP_OR_TEMPLATE_ID")
            self.L.error('sendNotification', `Invalid Recipient:${_.get(notificationRecord, 'recipients', null)} or templateId:${_.get(notificationRecord, 'template_id', null)} for ${_.get(record, 'debugKey', null)}`);
            return self.notify.insertRejectedNotifications(callback, 'Invalid recepient or template', record, notificationRecord);
        }
    }

    /**
     * Generate deepLinkUrl based on input params
     * Sample deeplink url : paytmmp://utility?url=https://catalog.paytm.com/v1/mobile/getproductlist/64739?product_id=150344895$price=2090$recharge_number=0396987144$recharge_number_2=10-03-1986$recharge_number_3=9935006051$utm_source=billGenerationReminder$utm_medium=chat$utm_campaign=Bajaj%20Allianz%20Life%20Insurance
     * @param {*} payLoad 
     * @param {*} notificationRecord 
     */
    getDeepLinkUrl(record, notificationRecord, payLoad) {
        let self = this;
        let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
        let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
        let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
        let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
        let short_operator_name = _.get(payLoad, "short_operator_name", null);
        let template_id = _.get(notificationRecord, "template_id", null);
        let operator = _.get(payLoad, "operator", null);
        let paytype = _.get(payLoad, 'paytype', null) != null ? _.get(payLoad, 'paytype', null).toLowerCase() : null;
        let utm = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);

        if (!landing_path) {
            if (product_service === 'mobile') {
                landing_path = "mobile_postpaid"; 
            } else if (product_service === 'datacard') {
                landing_path = "datacard_postpaid";
            } else if (product_service === 'dth') {
                landing_path = "dth";
            } else {
                landing_path = 'utility';
            }
        }
 
        let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

        if(_.toLower(product_service)=='paytm postpaid'){
            let deeplinkForPaytmPostpaid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE',`${product_service}::${paytype}`],[]);
            if(deeplinkForPaytmPostpaid && deeplinkForPaytmPostpaid.length){
                url = deeplinkForPaytmPostpaid[0];
            }
        }

        let paramsForUrl = self.getParamsForChatAndPush(record, payLoad);
        let deepLinkUrl = null;
        if(product_service=='mobile'){
            deepLinkUrl = url + "?$" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNum(record, '$') + utm;
        }else{
            deepLinkUrl = url + "?" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNum(record, '$') + utm;
        }
        return deepLinkUrl;
    }


    getDataForMeasurementAndTracking(record, notificationRecord, notificationData) {
        let self = this;
        let raw_expiry_date = _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.due_date', null):_.get(notificationData, 'options.data.due_date', null);
        if(raw_expiry_date && MOMENT(raw_expiry_date, 'Do MMM YYYY').isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date, 'Do MMM YYYY').format('YYYY-MM-DD');
        }
        let timepointForDataForMeasurementAndTracking= _.get(record, 'timepoint', null);
        let notifTypeForDataForMeasurementAndTracking= _.get(record, 'notificationType', null);
        if (_.get(notificationRecord, 'type', null) == 'WHATSAPP' &&  (_.get(notificationData, 'dynamicParams.convertBGtoDueDate', false) || _.get(notificationData, 'options.data.convertBGtoDueDate', false))) {
            self.L.log(`getDataForMeasurementAndTracking:: setting timepoint for whatsapp when conversion is true`);
            let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;
            if(billFetchDate){
                timepointForDataForMeasurementAndTracking=MOMENT().diff(billFetchDate, 'days');

            }
            
            notifTypeForDataForMeasurementAndTracking='BILLGEN';

        }
        return {
            recon_id: _.get(record, 'recon_id', null),
            type:_.get(notificationRecord, 'type', null),
            notif_type: notifTypeForDataForMeasurementAndTracking,
            promocode: _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.promo_code', null):_.get(notificationData, 'options.data.promo_code', null),
            msg_type: null,
            timepoint: timepointForDataForMeasurementAndTracking,
            templateName:_.get(notificationData, 'templateName', null),
            operator: _.toLower(_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.operator', null):_.get(notificationData, 'options.data.operator', null)),
            amount: _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.amount', null):_.get(notificationData, 'options.data.amount', null),
            service: _.toLower(_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.service', null):_.get(notificationData, 'options.data.service', null)),
            due_date: raw_expiry_date,
            customer_id:_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.customer_id', null):_.get(notificationData, 'options.data.customer_id', null),
            bill_source : self.notify.notificationBillSource == 'NONRU' ? 'NONRU' : 'RU' // Add your condition here

        }
    }

    getQueryParams(data, delimiter = '&') {
        let self = this;
        try{
            let
            operator = _.toLower(_.get(data, 'operator', null)),
            amount = _.get(data, 'amount', null),
            product_id = _.get(data, 'product_id', null),
            recharge_number = _.get(data, 'recharge_number', ''),
            bank_name = _.get(data, 'bank_name', ''),
            card_network = _.get(data, 'card_network', ''),
            category_id = _.get(data, 'category_id' , null),
            notificationRecord = _.get(data, 'notificationRecord', null),
            product_service = _.get(data, 'product_service',null),
            paytype = _.get(data, 'paytype',null),
            type = _.get(notificationRecord, 'type', null),
            operator_validated_at = _.get(data, 'operator_validated_at', null),
            operatorKey = _.get(self.config,['CVR_DATA',product_id,'operator']),
            circleKey = _.get(self.config,['CVR_DATA',product_id,'circle']),
            billSourceKey=_.get(data, 'bill_source', null);

        let ccEnabledServices = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CC_ENABLED', 'SERVICE_LIST'], []);

        if(product_service.toLowerCase() == 'fastag recharge'){
            let aid = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'AID_BY_SERVICE_PAYTYPE',`${product_service.toLowerCase()}::${paytype}`], '');
            let dynamicObject = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'DYNAMIC_PARAMS_BY_SERVICE_PAYTYPE',`${product_service.toLowerCase()}::${paytype}`], {});	
            _.set(dynamicObject, ['sparams', 'product_id'], product_id);
            if(recharge_number && !_.toLower(_.toString(recharge_number)).includes('default')){
                _.set(dynamicObject, ['sparams', 'recharge_number'], recharge_number);
            }
            return `aId=${aid}&data=${new Buffer.from(JSON.stringify(dynamicObject),'utf-8').toString('base64')}`;
        }
        else if (product_service && ccEnabledServices.includes(product_service.toLowerCase())) {
            self.L.log("deep_link creation to be done for credit card  :: ccEnabledServices: ", ccEnabledServices);
            let enableNewFormatMCN = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'DEEP_LINK'], 0)
            if(enableNewFormatMCN){
                let lastIndex = recharge_number.lastIndexOf('X');
                recharge_number = recharge_number.substring(0, lastIndex).replace(/[0-9]/g, "X") + recharge_number.substring(lastIndex) // ensuring only last 4 digits remain in MCN
            }
            return `recharge_number=${recharge_number.replace(/ /g, '')}${delimiter}bank_name=${bank_name}${delimiter}card_network=${card_network}`;
        } else if(_.toLower(product_service) == 'paytm postpaid'){
            let aid = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'AID_BY_SERVICE_PAYTYPE',`${_.toLower(product_service)}::${_.toLower(paytype)}`], '');
            let dynamicObject = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'DYNAMIC_PARAMS_BY_SERVICE_PAYTYPE',`${_.toLower(product_service)}::${_.toLower(paytype)}`], {});
            _.set(dynamicObject,['sparams','product_id'],product_id);
            _.set(dynamicObject,['sparams','recharge_number'],recharge_number);
            _.set(dynamicObject,['sparams', 'price'], amount);
            return `aId=${aid}&data=${new Buffer.from(JSON.stringify(dynamicObject),'utf-8').toString('base64')}`;
        } else if (product_service == 'electricity' && self.notify.notificationBillSource == 'NONRU') {
            let url = `product_id=${product_id}`, 
            saveRechargeNumber = false,
            ambiguous = _.get(data, 'ambiguous', null),
            board = _.get(data, 'board', null),
            state = _.get(data, 'state', null);
            if (ambiguous != null) {
                let boardFromAmbiguous = _.get(ambiguous, 'board', false);
                if (_.get(ambiguous, 'state', false) == true && state != null) {
                    url = `state=${state}`;
                    saveRechargeNumber = true;
                    if (boardFromAmbiguous == true && board != null) {
                        url += `${delimiter}board=${board}`;
                    } else if (typeof boardFromAmbiguous == 'string') {
                        url += `${delimiter}board=${boardFromAmbiguous}`;
                    }
                }
            }
            if (data.ignoreAmountInDeeplink || type == 'EMAIL') {
                url += `${delimiter}price=${delimiter}recharge_number=${recharge_number}`;
            } else {
                url += `${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}`;
            }
            if (saveRechargeNumber) {
                url += `${delimiter}saveRechargeNumber=true`;
            }

            if(billSourceKey && billSourceKey != null){
                url += `${delimiter}bill_source=${billSourceKey}`;
            }

            return url;  
            
        } else {
            if (operator_validated_at) {
                if (data.ignoreAmountInDeeplink || type == 'EMAIL') {
                    return `product_id=${product_id}${delimiter}price=${delimiter}recharge_number=${recharge_number}${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                } else if (category_id === 17 && _.get(self.ignoreAmountInURLForMobilePrepaidOperators, operator, null)) {
                    // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
                    if (_.get(data, "isRealTimeDataExhausted")) {
                        return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}${delimiter}`;
                    }
                    else {
                        return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}expandBrowsePlan=true${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                    }
                } else if (_.get(data, 'is_automatic', null) == 5) {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}toAmount=true${delimiter}isRenewSubscription=true${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                }
                else {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}operator_validated_at=${operator_validated_at}${delimiter}circleKey=${circleKey}${delimiter}operatorKey=${operatorKey}`;
                }
            } else {
                if (data.ignoreAmountInDeeplink || type == 'EMAIL') {
                    return `product_id=${product_id}${delimiter}price=${delimiter}recharge_number=${recharge_number}`;
                } else if (category_id === 17 && _.get(self.ignoreAmountInURLForMobilePrepaidOperators, operator, null)) {
                    // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
                    if (_.get(data, "isRealTimeDataExhausted")) {
                        return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}`;
                    }
                    else {
                        return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}expandBrowsePlan=true`;
                    }
                } else if (_.get(data, 'is_automatic', null) == 5) {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}toAmount=true${delimiter}isRenewSubscription=true`;
                }
                else if(billSourceKey && billSourceKey != null){
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}bill_source=${billSourceKey}`;
                }
                else {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}`;
                }
            }
        }
    }catch(error){
        self.L.error("Error in getQueryParams of data fetched: ", error, JSON.stringify(data));
        return null;
    }
    }

    getExtraRechargeNum(record, operator = '&') {
        let self = this;
        let user_data = _.get(record, "user_data", null);
        let extra_recharge_num_url = "";
        if (user_data) {
            try {
                user_data = JSON.parse(user_data);
                for (var i = 2; i < 10; i++) {
                    var key = 'recharge_number_' + i,
                        value = '';

                    if (!_.has(user_data, [key], null)) continue;

                    value = _.get(user_data, [key], '');
                    extra_recharge_num_url = extra_recharge_num_url + `${operator}${key}=${value}`;
                }
            } catch (error) {
                self.L.error("Error in parsing of data fetched: ", error);
                return extra_recharge_num_url;
            }
        }

        return extra_recharge_num_url;
    }

    getExtraRechargeNumForRentWA(record, operator = '&') {
        let self = this;
        let user_data = _.get(record, "user_data", null);
        let extra_recharge_num_url = "";
        if (user_data) {
            try {
                user_data = JSON.parse(user_data);
                for (var i = 2; i < 10; i++) {
                    var key = 'recharge_number_' + i,
                        value = '';

                    if (!_.has(user_data, [key], null)) continue;

                    value = _.get(user_data, [key], '');
                    extra_recharge_num_url = extra_recharge_num_url + `${operator}${key}=${encodeURIComponent(value)}`;
                }
            } catch (error) {
                self.L.error("Error in parsing of data fetched: ", error);
                return extra_recharge_num_url;
            }
        }

        return extra_recharge_num_url;
    }

    getParamsForUrl(record, notificationRecord, payLoad) {
        let
            recharge_number = _.get(payLoad, 'recharge_number', null) != null ? _.get(payLoad, 'recharge_number', null) : null,
            amount = _.get(payLoad, 'amount', null) != null ? _.get(payLoad, 'amount', null) : null,
            product_id = _.get(record, 'product_id', null) != null ? _.get(record, 'product_id', null) : null,
            product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null,
            user_data = _.get(record, 'user_data', null) != null ? _.get(record, 'user_data', null) : null,
            paytype = _.get(record, 'paytype', null),
            category_id = _.get(payLoad, 'category_id', null),
            bank_name = _.get(record, 'bank_name', null),
            card_network = _.get(record, 'card_network', null),
            bill_source=_.get(payLoad, 'bill_source', null),
            paramsForUrl = {
                "product_id": product_id,
                "recharge_number": recharge_number,
                "user_data": user_data,
                "amount": amount,
                "product_service": product_service,
                "notificationRecord": notificationRecord,
                "paytype": paytype,
                "category_id": category_id,
                "bank_name": bank_name,
                "card_network": card_network,
                "short_operator_name": _.get(payLoad, "short_operator_name", null),
                "operator": _.get(payLoad, "operator", null),
                "ignoreAmountInDeeplink": _.get(record, "ignoreAmountInDeeplink", false),
                "operator_validated_at": _.get(record, "operator_validated_at", null)
            };
        if (_.get(record, 'is_automatic', null) == 5) {
            _.set(paramsForUrl, 'is_automatic', _.get(record, 'is_automatic', 5));
        }

        if (bill_source && bill_source != null) {
            _.set(paramsForUrl, 'bill_source', bill_source);
        }

        return paramsForUrl;
    }

    getParamsForChatAndPush(record, payLoad) {
        let paramsForUrl = {
                operator : _.toLower(_.get(record, 'operator', null)),
                amount : _.get(record, 'amount', null),
                product_id : _.get(record, 'product_id', null),
                recharge_number : _.get(record, 'recharge_number', ''),
                category_id : _.get(payLoad, 'category_id' , null),
                bank_name : _.get(record, 'bank_name', null),
                card_network : _.get(record, 'card_network', null),
                paytype : _.get(record, 'paytype', null),
                product_service : _.get(record, 'service', null),
                ambiguous : _.get(record, 'ambiguous', null),
                state : _.get(record, 'state', null),
                board : _.get(record, 'board', null),
                operator_validated_at: _.get(record, 'operator_validated_at', null)
        };
        let bill_source= _.get(payLoad, 'bill_source', null);
        if (_.get(record, 'is_automatic', null) == 5) {
            _.set(paramsForUrl, 'is_automatic', _.get(record, 'is_automatic', 5));
        }
        if (bill_source && bill_source != null) {
            _.set(paramsForUrl, 'bill_source',bill_source) ;
        }


        return paramsForUrl;
    }
    /**
     * 
     * @param {*} record 
     * @param {*} notificationType 
     * @param {*} payload 
     * @param {*} tableName
     * @returns {object} {
     *      SMS: 1233, // 1233 is template id correspondent notificationType for record and payload.
     *      PUSH: 4562,
     *      EMAIL: 6788,
     *      CHAT: 7676,
     *      WHATSAPP: 8787, 
     * }
     */
    getTemplates(record, notificationType, payload, tableName) {
        let self = this;
        let operator = _.get(record, 'operator', null),
            rechargeNumber = _.get(record, 'recharge_number', null),
            numberRechargeBy = _.get(record, 'customer_mobile', null),
            operatorAlowedOnlyToRN = this.operatorsSendingNotificationToRegisteredUser,
            dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD') : null;
            let partialBillState = _.get(record, 'partialBillState', null);
            let preDefinedTemplates = _.get(record, 'templates', null);
            let extra = null;
            try{
                if(typeof record.extra == "string"){
                    extra = JSON.parse(record.extra);
                }else{
                    extra = record.extra;
                }
                if(!partialBillState){
                    partialBillState = _.get(extra, 'partialBillState', null);
                }
            }catch(error){
                self.L.error("Error in parsing of extra data fetched: ", error);
            }

        let templates = {};

        for (let key in this.notificationConfig.type) {
           
            if (this.notificationConfig.type[key]) {
                
                /**
                 * set generic template 
                 *  if RN is not recharged by customerId of RN 
                 *    and operator does not allowed notification to other customer
                 */
                // removed above comment code in IN-33220 |  VI Mobile Postpaid: Send common template for Recharged and Recharge Numbers
                if(preDefinedTemplates || partialBillState == null){
                    templates[key] = this.getTemplateId(key, record, notificationType, payload, dueDate, tableName);
                }else{
                    templates[key] = this.nonPaytmBillsConsumer.getTemplateId(key, record, 'NODUEDATE', dueDate, partialBillState);
                }
            }
        }

        this.L.verbose("getTemplates, templates: ", templates, " for record: ", record, " for payload: ", payload, " and for notificationType: ", notificationType);
        return templates;
    }
    
    checkForDummyRN(extra) {
        if (_.get(extra, 'isDummyRN', "0") == "1") {
            return true;
        } else
            return false;
    }

    processForOldBillTemplate(record, payload, tableName, notificationType, type) {
        try {
            let self = this,
                dayValue = '',
                oldBillFetchDate = MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'old_bill_fetch_date', null), 'YYYY-MM-DD') : null,
                oldBillDueDateIntervals = self.notificationLibrary.getIntervalForOldDueDates(_.get(record, 'operator', null), tableName),
                currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
                templateId = null;
            if (oldBillFetchDate) {
                dayValue = oldBillFetchDate.diff(currentDate, 'days');
                dayValue = `${dayValue}_`
                _.set(record, 'timepoint', oldBillFetchDate.diff(currentDate, 'days'));
            }
            self.L.log("processForOldBillTemplate :: getTemplateId :: dayValue: ", dayValue, "operator : ", _.get(record, 'operator', null));

            let serviceBasedKey = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`;
            self.L.log("processForOldBillTemplate :: getTemplateId :: serviceBasedKey: ", serviceBasedKey);
            templateId = _.get(record, ['templates', type],
                _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `${dayValue}${notificationType}_${type}`],
                    _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey],
                        _.get(this.notificationConfig, ['templateid', `BR_${dayValue}${notificationType}_${type}`],
                            _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `${notificationType}_${type}`],
                                _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_GENERIC_${type}`], null)
                            )
                        )
                    )
                )
            );
            return templateId;
        }
        catch (error) {
            self.L.error(`processForOldBillTemplate::Error while fetching templateId for old bill notification: ${error}`);
            return null;
        }
    }

    /**
     * 
     * @param {string} type 
     * @param {object} record 
     * @param {string} notificationType 
     * @param {object} payload 
     * @param {object} dueDate 
     * @param {string} tableName
     * @returns {number} template_id of type(sms || push || chat|| email), notificationType(BILLGEN,DUEDATE) for record and payload
     */
    getTemplateId(type, record, notificationType, payload = {}, dueDate, tableName) {
        // console.log("🚀 ~ BillReminderNotification ~ getTemplateId ~ record:", record)
        let self = this,
            dayValue = '',
            noDueDate = '';
        let updatedDataSource = null, dummyRN = '', isDummyRN = false;
        let prepaidHeuristic = '';
        if((_.get(record,"isRealTimeDataExhausted",false) || _.get(record, 'source', null)=='notifyRejectedBills' || (record && record.templates && _.get(record, ['templates', type],null)))){
            return _.get(record, ['templates', type],null);
            }
        if (_.get(record, 'notificationType', null) === 'OLD_BILL_NOTIFICATION') {
            return self.processForOldBillTemplate(record, payload, tableName, notificationType, type)
        }
            let
                source = _.get(record, 'source', null),
                sourceIdMapping = _.invert(_.get(self.notificationConfig, 'source'), {}),
                source_id = _.get(sourceIdMapping, source, 1);
        try {
            let extra = _.get(record, 'extra', {});
            if(typeof extra == "string"){
                extra = JSON.parse(extra)
            }
            updatedDataSource = _.get(extra, 'updated_data_source', null);
            if (self.checkForDummyRN(extra)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", `NOTIFICATION_TYPE:${type}`, `OPERATOR:${_.get(record, 'operator', null)}`, `SERVICE:${_.get(record, 'service', null)},"TYPE:DUMMY_RN_FOUND_IN_EXTRA"`]);
                dummyRN = '_DUMMYRN';
                isDummyRN = true;
            }            
        } catch (error) {
            self.L.error("getTemplates :: Error in parsing of extra data fetched: ", error);
        }
        /**
         * If notification type is "DUEDATE" and we want to send different notification for each respective dueDates
         * Old logic : irrespective of dueDate or dueDate == D-1 same template_id will be fetched
         * New logic : for each different dueDate, different template_id will be fetched
         * Please ensure for old logic to also work, difference b/w now() & dueDate should be 1. i.e dueDate == D-1
         */
        let billDate = MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD') : null;
        let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;

        let dueDateIntervals = self.notificationLibrary.getPossibleDueDates(_.get(record, 'operator', null), tableName);
        let currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'); 

        if((notificationType === 'DUEDATE' || (notificationType === 'BILLDUE')) && dueDate){
            _.set(record, 'timepoint', dueDate.diff(currentDate, 'days'));
        }else if(notificationType === 'BILLGEN' && billFetchDate){
            _.set(record, 'timepoint', MOMENT().diff(billFetchDate, 'days'));
        }
        
        if(_.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','TEMPLATES_FROM_PAYLOAD','SOURCE_IDS'],[]).indexOf(_.toString(source_id))> -1){
            return _.get(record, ['templates', type], null);
        }
        
        if (notificationType === 'DUEDATE' && dueDate && ( dueDateIntervals.indexOf( dueDate.diff(currentDate, 'days') ) > -1 ) ) {
            dayValue = dueDate.diff(currentDate, 'days');
            dayValue = dayValue !== 1 ? `${dayValue}_` : '';
            self.L.log("getTemplateId:: dayValue: ", dayValue, "operator : " , _.get(record, 'operator', null));
        }
        else if(notificationType === 'BILLGEN' && billFetchDate && currentDate){
            dayValue = MOMENT().diff(billFetchDate, 'days');
            dayValue = `${dayValue}_`;
            self.L.log("getTemplateId:: dayValue: ", dayValue, "operator : " , _.get(record, 'operator', null),"notificationType : ",notificationType);
        } else if(notificationType === 'PREPAID_LOW_BALANCE') {
            if(dueDate && ( dueDateIntervals.indexOf( dueDate.diff(currentDate, 'days') ) > -1 )) {
                dayValue = dueDate.diff(currentDate, 'days');
                dayValue = dayValue !== 1 ? `${dayValue}_` : '';
            }

            if(updatedDataSource === 'ffrSkip'){
                prepaidHeuristic = '_HEURISTIC';
            }

            noDueDate = dueDate === null ? '_NODUEDATE' : '';

            self.L.log("getTemplateId:: PREPAID_LOW_BALANCE :: dayValue: ", dayValue, "prepaidHeuristic:", prepaidHeuristic, "operator : " , _.get(record, 'operator', null));
        }

        let noAmount = _.get(record, 'amount', null) === null ? '_NOAMOUNT' : '';
        if (notificationType === 'BILLGEN') {
            noDueDate = dueDate === null ? '_NODUEDATE' : '';
        }
       
         /** This extra param is for sending different notifs based on Credit card source */
         let ccDataSource = _.get(record, 'data_source', null);
         ccDataSource = _.isEmpty(ccDataSource) ?  '' : '_'+ccDataSource;
        let serviceBasedKey = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}${prepaidHeuristic}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`;

        if ( (_.get(record, 'source', null) === 'nonRUbillDuePublisher' || 
            _.get(record, 'source', null) === 'nonRUbillDuePublisherRealtime') &&
             _.get(payload, 'service', '').toLowerCase() === 'mobile' &&
            _.get(payload, 'paytype', '').toLowerCase() === 'prepaid') {
        
            serviceBasedKey = `BR_${_.toUpper(_.get(payload, 'service'))}_${_.toUpper(_.get(payload, 'paytype'))}_${dayValue}${notificationType}${prepaidHeuristic}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`;
        }
        
        self.L.log("getTemplateId, serviceBasedKey: ", serviceBasedKey);
        let templateId=null;

        if (_.get(record, 'is_automatic', 0) == 5) {
            let serviceBasedKeyForAutomatic = `BR_AUTOMATIC_APPLICABLE_FOR_RENEW_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`;
            self.L.log(" BillReminderNotification ~ getTemplateId ~ serviceBasedKeyForAutomatic:", serviceBasedKeyForAutomatic)
            templateId = _.get(record, ['templates', type],
                _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `${dayValue}${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`],
                    _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKeyForAutomatic],
                        _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `AUTOMATIC_${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`],
                            _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `BR_AUTOMATIC_APPLICABLE_FOR_RENEW_${_.toUpper(_.get(payload, 'service'))}_${notificationType}${dummyRN}_GENERIC_${type}${noAmount}${noDueDate}${ccDataSource}`],
                            )
                        )
                    )
                )
            )
        }
        else{
            templateId =  _.get(record, ['templates', type],
                _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `${dayValue}${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`],
                                _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey],
                                    _.get(this.notificationConfig, ['templateid', `BR_${dayValue}${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`],
                                        _.get(this.operatorTemplateMappingConfig, [_.get(record, 'operator', null), `${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`],
                                            _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}${dummyRN}_GENERIC_${type}${noAmount}${noDueDate}${ccDataSource}`],
                                                _.get(this.notificationConfig, ['templateid', `BR_${notificationType}${dummyRN}_${type}${noAmount}${noDueDate}${ccDataSource}`], null)
                                                )
                                            )
                                        )
                                    )
                                )
                            );

        }

        if(_.get(record, 'source', null) == "reminderBillGenPublisher"){
            self.L.log("getTemplateId::",`operator:${_.get(record, 'operator', null)}_notificationType:${notificationType}_billDate:${billDate}_dayValue:${dayValue}_type:${type}_templateId:${templateId}`);
        }
        else{
            self.L.log("getTemplateId::", `operator:${_.get(record, 'operator', null)}_notificationType:${notificationType}_dueDateIntervals:${dueDateIntervals}_dueDate:${dueDate}_dayValue:${dayValue}_type:${type}_templateId:${templateId}`);
        }
        if (isDummyRN) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", `NOTIFICATION_TYPE:${type}`, `OPERATOR:${_.get(record, 'operator', null)}`, `SERVICE:${_.get(record, 'service', null)}`, `TEMPLATE:${templateId}`, "TYPE:DUMMY_RN"]);
        }

        return templateId;
    }

    decideNextDueDate(record, operator, tableName) {
        var
            self = this,
            service_id = _.get(record, 'service_id', null),
            paymentDueDate = _.get(record, 'payment_date', null);
        if (operator && service_id == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
            var
                daysToExpireReminder = _.get(self.notificationExpiryPeriod, operator, null),
                daysToNextDueDate = _.get(self.operatorsBillsConfig, [operator, 'daysToNextDueDate'], null);

            if (daysToExpireReminder && paymentDueDate && MOMENT().diff(MOMENT(paymentDueDate, 'YYYY-MM-DD 00:00:00'), 'days') >= daysToExpireReminder) {
                let params = {
                    customerId: _.get(record, 'customer_id', null),
                    rechargeNumber: _.get(record, 'recharge_number', null),
                    notificationStatus: 0
                };
                self.bills.updateNotificationStatus(function (error, data) {
                    if (error) {
                        self.L.critical('decideNextDueDate :: Unable to update Notification status for  rechargeNumber :: ' + params.rechargeNumber + " customerId :: " + params.customerId);
                    }
                }, tableName, params);
            } else if (daysToNextDueDate) {
                let params = {
                    customerId: _.get(record, 'customer_id', null),
                    rechargeNumber: _.get(record, 'recharge_number', null),
                    productId: _.get(record, 'product_id', null),
                    operator: _.get(record, 'operator', null),
                    service: _.get(record, 'service', null),
                    dueDate: MOMENT().add(daysToNextDueDate, 'days').format('YYYY-MM-DD 00:00:00')
                };
                self.bills.updateDueDateInReminder(function (error, data) {
                    if (error) {
                        self.L.critical('decideNextDueDate :: Unable to update due date for  rechargeNumber :: ' + params.rechargeNumber + " customerId :: " + params.customerId);
                    }
                }, tableName, params);
            }
        }
    }

    _utmByTemplateId(template_id, short_operator_name, operator = '&', product_service, operator_name) {
        let self = this;
        //fetch utm details by notificationType
        //utm_source, utm_medium, utm_campaign
        let utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', template_id]);
        if (!utm) {
            self.L.critical(`UTM config not found for template: ${template_id}`);
            utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', 'notFound'], {});
        }

        if (short_operator_name) {
            short_operator_name = short_operator_name.toLowerCase();
        } else if (operator_name) {
            short_operator_name = operator_name.toLowerCase();
        } else {
            short_operator_name = "default";
        }

        let utmCampaign =  (product_service ? product_service.toString().toLowerCase() : "default") + "_" + short_operator_name + "_" + (template_id ? template_id : "default");
        return `${operator}utm_source=${self.getUtmParam(utm.utm_source)}${operator}utm_medium=${self.getUtmParam(utm.utm_medium)}${operator}utm_campaign=${self.commonLib.getUtmParam(utmCampaign, "_")}`;
    }

    getUtmParam(utmparam) {
        if (!utmparam) return '';
        let utmProcessed = encodeURIComponent(utmparam.replace(/[^a-zA-Z0-9 ]/g, ''));
        return utmProcessed;
    }

    appendPromoCodeInNotification(payLoad, notificationRecord, callback ) {
        let self = this;
        if(self.allowedTemplatesForPromoCodes.size == 0) self.allowedTemplatesForPromoCodes = new Set(_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','PROMO_CODES_CONFIG','ALLOWED_TEMPLATE_IDS_FOR_PROMOCODE'],[]));
        let template_id = _.get(notificationRecord, 'template_id', null);
        if(template_id == null) {
            // error case
            self.L.error(`appendPromoCodeInNotification :: template_id is null for customerId : ${_.get(payLoad, 'customer_id', null)}`)
            return callback("appendPromoCodeInNotification :: template_id is null");
        } else if(!self.allowedTemplatesForPromoCodes.has(template_id)) {
            return callback("appendPromoCodeInNotification :: template_id not in allowed template_id");
        }

        _.set(payLoad,"promo_content", ""); //setting value of promocode initially to empty string to handle case where no promocode is found and template is expecting some value of promo_content else for null it will curropt the notification

        try {
            // let product_info = self.cvrData[_.get(payLoad, 'product_id', null)];
            let service     = _.get(payLoad, 'service', null);
            let paytype     = _.get(payLoad, 'paytype', null);
            let customer_id = _.get(payLoad, 'customer_id', null);
            let amount      = _.get(payLoad, 'amount', null);
            if(service == null || paytype == null || customer_id == null) throw `product_info or customer_id not found for customer_id ${customer_id}`;
            service = service.replace(" ", "_").toLowerCase();
            let params = {
                customer_id: customer_id,
                service: service,
                paytype: paytype,
                template_id: template_id,
                amount: amount
            }
            self.ctPromoCodeUtil.getPromoCodeFromCT((error, promoCampaign) => {
                if(error) {
                    self.L.error(`appendPromoCodeInNotification :: Error in getting PromoCode from CT for customerId : ${customer_id}. Error: ${error}`);
                } else {
                    self.L.log(`appendPromoCodeInNotification :: PromoCampaign from CT for customerId ${customer_id}. promoCampaign: ${promoCampaign}`);
                }
                callback(error, promoCampaign);
            },_.get(self.config, ['CT_PROMOCODE_CONFFIG'], null), params);
        } catch (e) {
            self.L.error(`appendPromoCodeInNotification :: Error in getting PromoCode from CT for customerId ${customer_id}. Error: ${e}`);
            callback(e);
        }
    }

    getBillSource(record) {
        let self = this;
        let extra = _.get(record, 'extra', {});

        try {

            if (typeof extra == "string") {
                extra = JSON.parse(extra)
            }
        } catch (error) {
            if (error) {
                return null;
            }
        }
        if (self.checkForDummyRN(extra) && _.toUpper(_.get(record, 'service', null)) == LOAN_SERVICE) {
            return "smsParsing";
        }
        let updated_data_source = self.getValue(_.get(extra, "updated_data_source"));
        self.L.log('BillReminderNotification::getBillSource', 'updated_data_source:', updated_data_source);
        if (updated_data_source) {
            if (_.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SYSTEM_FETCH_BILL_SOURCES', 'SOURCES_LIST'], []).indexOf(_.toLower(_.toString(updated_data_source))) > -1) {
                return "systemFetch";
            }
            else if(_.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMS_PARSING_BILL_SOURCES', 'SOURCES_LIST'], []).indexOf(_.toLower(_.toString(updated_data_source))) > -1){
                return "smsParsing";
            }
            else {
                return "unknown";
            }
        }
        return null;
    }

    

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`billReminderNotification::suspendOperations kafka consumer shutdown initiated`);
        Q()
        .then(function(){
            self.kafkaBillFetchConsumer.close(function(error, res){
                if(error){
                    self.L.error(`billReminderNotification::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`billReminderNotification::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`billReminderNotification::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`billReminderNotification::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default BillReminderNotification;


// paytmmp://mobile_postpaid?url=https://digitalcatalog.paytm.com/v1/mobile/getproductlist/21?$product_id=192$recharge_number=9094807879$price=219.53$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Airtel
// curl -X POST -H 'content-type: application/json' "http://notifypanel-mum.paytm.com/v1/admin/notification/async/send" -d '{"template_type":"push","template_id":4753,"options":{"notificationOpts":{"recipients":"23716219","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://digitalcatalog.paytm.com/v1/mobile/getproductlist/21?$product_id=192$recharge_number=9094807879$price=219.53$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Airtel","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":219.53,"recharge_number":"9094807879","operator":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/1555311132380.png","category_id":21,"service":"Mobile","due_date":"5th Mar 2020","short_operator_name":"Airtel","unsubscribe_url":"https://p-y.tm/BH-pUIk"}}}' 
