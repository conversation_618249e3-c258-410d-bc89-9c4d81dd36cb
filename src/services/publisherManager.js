import _         from 'lodash'
import Publisher from './publisher'
import ccPublisher from './ccPublisher'
import LatencyProvider from './latencyProvider'
import MOMENT from 'moment'

let L = null

/*
    This class prvoides the functionality to manage multiple publishers 
 */
class PublisherManager {

    constructor(options) {
        L               = options.L
        L.info('PublisherManager', 'initialising PublisherManager...')
        this.L          = options.L
        this.config     = options.config
        this.options    = options
        this.batchId    = options.batchId
        //This is set of publishers, where one publisher fetches bills from a single table only
        this.publishers = {}
        this.prepaidPublisher = {}
        this.cc = false;
        this.latencyProvider = new LatencyProvider(options)
        this.latencyProvider.keepUpdating()
         //start setInterval to check disabled operators
        this.disableOperator();
    }

    createPublisherForOperator(tableName, operator, cc = false, prepaid = false) { 
        this.cc = cc;
        if(!this.publishers[operator]) { //Create one if not present
            if(cc==true){
                this.publishers[operator] = {
                    state           : 'INACTIVE', //Creating a publisher in INACTIVE STATE 
                    serviceInstance : new ccPublisher({
                    ...this.options,
                    tableName        : tableName,
                    latencyProvider  : this.latencyProvider,
                    operator         : operator
                    }) 
                    //assign a new publisher service instance for each publisher
                }
            } else {
                this.publishers[operator] = {
                    state           : 'INACTIVE', //Creating a publisher in INACTIVE STATE 
                    serviceInstance : new Publisher({
                    ...this.options,
                    tableName        : tableName,
                    latencyProvider  : this.latencyProvider,
                    operator         : operator
                    }) 
                    //assign a new publisher service instance for each publisher
                }
            }
        }
        else if (prepaid && !this.prepaidPublisher[operator]) {
            this.prepaidPublisher[operator] = {
                state: 'INACTIVE', //Creating a publisher in INACTIVE STATE 
                serviceInstance: new Publisher({
                    ...this.options,
                    tableName: tableName,
                    latencyProvider: this.latencyProvider,
                    operator: operator,
                    prepaidPublisherInstance: true
                })
            //assign a new publisher service instance for each publisher
            }
        }
        return true
    }

    startPublisherForOperator(operator, prepaid = false) {
        let self = this;
        let publisher = _.get(this.publishers, operator, null);
        let prepaidPublisher = _.get(this.prepaidPublisher, operator, null);
        if(!publisher){
            L.info('No Publisher registered for the operator:',operator)
            return false
        } else if (self.isDisabled(operator) && !prepaid) {
            L.info('Publisher disabled for the operator:',operator);
            return false;
        }
        else if(publisher.state==='INACTIVE') {
            _.set(publisher, 'state', 'ACTIVE')
            publisher.serviceInstance.start() //starting the publisher for the table
            return true
        }
        else if (prepaidPublisher.state === 'INACTIVE') {
            _.set(prepaidPublisher, 'state', 'ACTIVE')
            prepaidPublisher.serviceInstance.start() //starting the publisher for the table
            return true
        }
        else {
            L.log('Publisher is already running for this operator', operator)
            return true
        }
    }

    stopPublisherForOperator(operator) {
        let publisher = _.get(this.publishers, operator, null)
        if(!publisher){
            L.info('No Publisher registered for the operator: ', operator);
            return false
        }
        else if(publisher.state==='ACTIVE') {
            _.set(publisher, 'state', 'INACTIVE')
            publisher.serviceInstance.stop() //stopping the publisher for the table
            L.log('Publisher is stopped for this operator', operator)
            return true
        }
        else {
            L.log('Publisher is already stopped for this operator', operator)
            return true
        }
    }

    isDisabled(operator) {
        let self = this;
        let isDisabled = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator,'DISABLED_PERMANENT'], false);
        if (isDisabled) {
            isDisabled = true;
        } else {
            let disableConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator,'DISABLED_TEMPORARY'], false);
            if (disableConfig && disableConfig.length && disableConfig.length > 1) {
                let from = disableConfig[0]; // In HH:mm:ss
                let to = disableConfig[1]; // In HH:mm:ss
                let now = MOMENT().format('HH:mm:ss');
                if (now >= from && now < to) {
                    isDisabled = true;
                }
            }
        }
        return isDisabled;
    }

    startAllPublishers() {
        for(var operator in this.publishers) { //do it individually for each table
            this.startPublisherForOperator(operator)
        }
    }

    stopAllPublishers() {
        for(var operator in this.publishers) { //do it individually for each table
            this.stopPublisherForOperator(operator)
        }
    }

    disableOperator() {
        let self = this;
        setInterval(function () {
            let operatorsInConfig = _.get(self.options.config, ['PUBLISHER_CONFIG', 'PUBLISHER', self.batchId], []);
            let operatorsInPublisher = Object.keys(self.publishers);
            let freshlyAddedOperators = operatorsInConfig.filter(operator => !operatorsInPublisher.includes(operator));
            let freshlyDisabledOperators = operatorsInPublisher.filter(operator => !operatorsInConfig.includes(operator));

            freshlyAddedOperators.forEach(operator => {
                let tableName = _.get(self.options.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
                if (tableName && self.createPublisherForOperator(tableName, operator, self.cc)) {
                    self.startPublisherForOperator(operator);
                } else {
                    self.L.error('publisherManager::disableOperator::', `no table configured for the operator: ${operator}`);
                }
            });

            freshlyDisabledOperators.forEach(operator => {
                self.stopPublisherForOperator(operator);
                delete self.publishers[operator];
            });

            Object.keys(self.publishers).forEach(operator => {
                if (self.isDisabled(operator)) {
                    self.stopPublisherForOperator(operator);
                } else {
                    self.startPublisherForOperator(operator);
                }
            });
        }, 15 * 60 * 1000);
    }
}

export default PublisherManager

