import billSubscriber    from './billSubscriber'
import poller            from './poller'
import latencyProvider   from './latencyProvider'
import publisher         from './publisher'
import publisherManager  from './publisherManager'
import notify 			 from './notify'
import notificationStatus from './notificationStatus'
import billReminderNotification from './billReminderNotification'
import planValidityNotification from './planValidityNotification'
import ruleEngine        from './ruleEngine'
import recentBills      from './recentBills'
import notificationService from './notificationService' 
import cvrDataLoader from './cvrDataLoader' 
import reminderSync from './reminderSync' 
import airtelPublisher from './airtelPublisher'
import ValidationSync from './validationSync';
import PrepaidValidationSync from './prepaidValidationSync';
import SmsParsingCCBills from  './smsParsingCCBills'
import SmsParsingFastag from './fasTagSmsParsing'
import EmiDueConsumer from './emiDueConsumer'
import EmiDueCommonConsumer from './emiDueCommonConsumer'
import BillReminderCylinderConsumer from './billReminderCylinderConsumer'
import planValidityNotificationSubscriber from './planValidityNotificationSubscriber'
import planValiditySyncDbPublisher from './planValiditySyncDbPublisher'
import historicalRecords from './historicalRecords';
import planValidityRMQConsumer from './planValidityRMQConsumer'
import allTransactionsConsumer from './allTransactionsConsumer'
import updateRecentsConsumer from './updateRecentsConsumer'
import updateUserConsentConsumer from './updateUserConsentConsumer'
import syncAutomaticRecent from './syncAutomaticRecent'
import nonPaytmBills from './nonPaytmBillsConsumer'
import smsParsingBillPayment from './smsParsingBillPayment'
import ccsmsParsingBillPayment from './smsParsingBillPayment/ccindex'
import ElectricitySMSParsingBillPayment from './smsParsingBillPayment/electricityIndex'
import RentSmsParsing from './smsParsingBillPayment/rentSmsParsingIndex'
import SmsParsingLoanEmi from './smsParsingLoanEmi'
import prepaidHistoricRecords from './prepaidHistoricRecords'
import OperatorUpNotification from './operatorUpNotification';
import paytmPostpaid from './paytmPostpaid';
import realtimeSmsParsingPrepaid from './realtimeSmsParsing/prepaid'
import realtimeSmsParsingPostpaid from './realtimeSmsParsing/postpaid'
import notificationFallBackConsumer from './notificationFallbackConsumer'
import airtelBillFetchConsumer from './airtelBillFetchConsumer'
import personalLoan from './personalLoan'
import pgTokenDeletion from './pgTokenDeletion'
import customNotificationCreateConsumer from './customNotificationCreate'
import ccIngestionNonRu from './cc_ingestion_nonru'
import activePaytmUsersConsumer from './activePaytmUsersConsumer'
import checkActiveUsersConsumer from './checkActiveUsersConsumer'
import notifyRejectedBills from './notifyRejectedBills'
import normalCustomNotificationCreateConsumer from './normalCustomNotificationCreate'
import heuristicCustomNotificationCreateConsumer from './heuristicCustomNotificationCreate'
import DthSmsParsingBills from './smsParsingBillPayment/dthSmsParsingIndex'
// import generalSmsParser from './smsParsingBillPayment/generalSmsParser'
import cronUserScoreIngestionConsumer from './cronUserScoreIngestionConsumer'
import userScoreIngestionConsumer from './user_score_ingestion_consumer'
import generalSmsParser from './smsParsingBillPayment/generalSmsParser'

import cronWhatsAppWhitelistCustomerConsumer from './cronWhatsAppWhitelistCustomerConsumer'
import ReminderConsentConsumer from './reminderConsentConsumer'

import expiredCAPublisher from './expiredCAPublisher'
import ffrValidatorConsumer from './ffrValidatorConsumer'
import whatsappNotificationFallbackConsumer from './whatsappNotificationFallbackConsumer'
import smsParsingValidator from './smsParsingValidator'

export default {
   billSubscriber,
   poller,
   latencyProvider,
   publisher,
   publisherManager,
   notify,
   notificationStatus,
   billReminderNotification,
   planValidityNotification,
   ruleEngine,
   recentBills,
   notificationService,
   cvrDataLoader,
   reminderSync,
   airtelPublisher,
   ValidationSync,
   PrepaidValidationSync,
   SmsParsingCCBills,
   SmsParsingFastag,
   EmiDueConsumer,
   EmiDueCommonConsumer,
   BillReminderCylinderConsumer,
   planValidityNotificationSubscriber,
   planValiditySyncDbPublisher,
   historicalRecords,
   planValidityRMQConsumer,
   allTransactionsConsumer,
   updateRecentsConsumer,
   updateUserConsentConsumer,
   syncAutomaticRecent,
   nonPaytmBills,
   smsParsingBillPayment,
   ccsmsParsingBillPayment,
   ElectricitySMSParsingBillPayment,
   RentSmsParsing,
   SmsParsingLoanEmi,
   prepaidHistoricRecords,
   OperatorUpNotification,
   paytmPostpaid,
   personalLoan,
   realtimeSmsParsingPrepaid,
   realtimeSmsParsingPostpaid,
   notificationFallBackConsumer,
   airtelBillFetchConsumer,
   pgTokenDeletion,
   notifyRejectedBills,
   normalCustomNotificationCreateConsumer,
   heuristicCustomNotificationCreateConsumer,
   customNotificationCreateConsumer,
   ccIngestionNonRu,
   activePaytmUsersConsumer,
   checkActiveUsersConsumer,
   DthSmsParsingBills,
   // generalSmsParser,
   cronUserScoreIngestionConsumer,
   userScoreIngestionConsumer,
   generalSmsParser,
   cronWhatsAppWhitelistCustomerConsumer,
   ReminderConsentConsumer,
   whatsappNotificationFallbackConsumer,
   expiredCAPublisher,
   whatsappNotificationFallbackConsumer,
   smsParsingValidator,
   ffrValidatorConsumer
}
