'use strict';

import PlanValidity from '../models/planValidity'
import MOMENT from 'moment'
import L from 'lgr'
import ASYNC from 'async'
import _ from 'lodash'

import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import startup from '../lib/startup'
import OAuth from '../lib/oauth'
import PublishStats from '../lib/publishStats'
import utility from '../lib'
import cassandraBills from '../models/cassandraBills'
import NOTIFIER from '../services/notify'
import billsLib from '../lib/bills'

const clauses = {
    1: {
        //operator: ['airtel'],
        TOPIC: "COMMON_PV_NOTIFICATION",
        CLAUSE : "FIRST",
        //PVREMINDER_DAYVALUES:[-5, -2, -1, 0, 1, 2, 5]
    },
    2: {
        //operator: ['jio'],
        TOPIC: "COMMON_PV_NOTIFICATION",
        CLAUSE : "SECOND",
        //PVREMINDER_DAYVALUES:[-5, -2, -1, 0, 1, 2, 5] /*{
            /* '-1': {
                'where': function () {
                    let threshold = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'jio', `THRESHOLD_ORDER_DATE`], null);
                    if (threshold) {
                        return ` AND order_date >= '${threshold}'`
                    }
                    return '';
                }
            },
            '0': {
                'where': function () {
                    let threshold = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'jio', `THRESHOLD_ORDER_DATE`], null);
                    if (threshold) {
                        return ` AND order_date < '${threshold}'`
                    }
                    return '';
                }
            },
            '1': {
                'where': function () {
                    let threshold = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'jio', `THRESHOLD_ORDER_DATE`], null);
                    if (threshold) {
                        return ` AND order_date >= '${threshold}'`
                    }
                    return '';
                }
            }, 
            '2': {
                'where': function () {
                    let threshold = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'jio', `THRESHOLD_ORDER_DATE`], null);
                    if (threshold) {
                        return ` AND order_date < '${threshold}'`
                    }
                    return '';
                }
            }
        }*/
        },
    3: {
        //operator: ['idea', 'vodafone', 'vodafone idea'],
        TOPIC: "VIL_PV_NOTIFICATION",
        CLAUSE : "THIRD",
        //PVREMINDER_DAYVALUES:[-5, -2, -1, 0, 1, 2, 5],
    },
    4: {
        //operator: ['bsnl', 'mtnl'],
        TOPIC: "COMMON_PV_NOTIFICATION",
        CLAUSE : "FOURTH",
    },
    5: {
        //operator: ['dishtv', 'd2h (formerly videocon d2h)', 'suntv'],
        dthService: true,
        TOPIC: "COMMON_PV_NOTIFICATION",
        CLAUSE : "FIFTH",
        //PVREMINDER_DAYVALUES: [-4, -1, 0, 1, 2, 3]
    }
};

class planValidityNotification {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.infraUtils = options.INFRAUTILS;

        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.planValidity = new PlanValidity(options);

        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.timeSplice = 30; //in mins
        this.iteratorIndex = 0;
        this.clause = (options.clause && clauses[options.clause]) || {};
        this.baseTime = MOMENT();
        this.fromIndex = options.fromIndex || 0;
        this.requestType = "PLAN_VALIDITY_NOTIFICATION" + (options.clause ? "_" + options.clause : "");
        this.planValidityNotificationTopic = this.clause.TOPIC;

        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });

        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.cassandraBills = new cassandraBills(options);
        this.notify = new NOTIFIER(options);
        this.billsLib = new billsLib(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: planValidityNotification", "Re-initializing variable after interval");
        self.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
    }

    async start() {
        if (!this.planValidityNotificationTopic) {
            throw new Error("Please start with correct clause!!");
        }

        this.L.log("going for start kafka publisher!!");
        await this._configureKafkaPublisher();
        this._execSteps();
    }

    async _configureKafkaPublisher() {
        return new Promise((resolve, reject) => {
            this.notify.configureKafkaPublisher((err) => {
                if (err) {
                    this.L.critical("error in configuring kafka publisher: ", err);
                    reject(err);
                } else {
                    this.L.log("kafka publisher configured successfully");
                    resolve();
                }
            })
        })
    }

    _execSteps() {
        var self = this;
        self.L.log("starting plan validity expiry reminder");

        // let pvReminderDayList = _.get(self.clause,'PVREMINDER_TEMPLATE_DAYVALUES',Object.keys(_.get(self.notificationConfig, 'PVREMINDER_TEMPLATE_CONFIG', []))),

        let operatorClause = _.get(self.clause, 'CLAUSE', 1);

        _.set(self.clause,['operator'],_.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CUSTOM_PREPAID_OPERATOR', operatorClause], null));

        _.set(self.clause,['PVREMINDER_DAYVALUES'],_.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CUSTOM_SCHEDULE', operatorClause], null));

        let dayValues = _.get(self.clause, 'PVREMINDER_DAYVALUES', {}),
            pvReminderDayList;

        if (Array.isArray(dayValues)) {
            pvReminderDayList = dayValues
        } else {
            pvReminderDayList = Object.keys(dayValues);
        }

        let currentIndex = 0;
        if (_.isEmpty(pvReminderDayList)) {
            pvReminderDayList = _.get(self.notificationConfig, 'DEFAULT_PVREMINDER_DAYVALUES', []);
        }
        ASYNC.whilst(
            () => {
                return currentIndex < pvReminderDayList.length;
            },
            (callback) => {
                self.totalNoOfBatch = 0;
                self.NoOfBatchProcessed = 0;
                self.lastprocessedId = 0;
                let currentDayValue = pvReminderDayList[currentIndex];
                self.L.log("Processing start for day expiry D" + currentDayValue);
                currentIndex += 1;
                var startTime = MOMENT(self.baseTime).add(-currentDayValue, 'days').startOf('d');
                self.getPlanValidityRecords(currentDayValue, startTime, () => {
                    self.L.log("Total number of batch processed:", self.NoOfBatchProcessed + " for day expiry D" + currentDayValue);
                    self.L.log("Total number of batch fetched:", self.totalNoOfBatch + " for day expiry D" + currentDayValue);
                    setTimeout(() => {
                        callback();
                    }, 200);
                    // process.exit(0);
                });
            },
            (err) => {
                if (err) {
                    self.L.critical("process failure due to error: ", err);
                } else {
                    self.L.log("process completed");
                }
                process.exit(0);
            }
        );
    }

    getPlanValidityRecords(dayValue, startTime, done) {
        let self = this,
            endTime = startTime.clone(),
            batchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PLAN_VALIDTY_NOTIFICATION_PUBLISHER', 'MYSQL_FETCH_LIMIT'], 5) :
                _.get(this.config, ['PLAN_VALIDITY_NOTIFICATION', 'PUBLISHER', 'MYSQL_FETCH_LIMIT'], 100);

        endTime = endTime.add(self.timeSplice, 'minutes');
        if (endTime.diff(MOMENT(self.baseTime).add(-dayValue, 'days').endOf('d')) > 0) {
            endTime = MOMENT(self.baseTime).add(-dayValue + 1, 'days').startOf('d');
        }

        let startTimeString = startTime.format('YYYY-MM-DD HH:mm:ss'),
            endTimeString = endTime.format('YYYY-MM-DD HH:mm:ss');

        if (self.iteratorIndex < self.fromIndex) {
            self.L.log(`Skipping: ${startTimeString} - ${endTimeString} - ${self.iteratorIndex}`);
            self.iteratorIndex++;
            return self.restartPlanValidityWithNextTime(startTime, endTime, dayValue, done);
        } else {
            self.L.log(`Processing: ${startTimeString} - ${endTimeString} - ${self.iteratorIndex}`);
            self.iteratorIndex++;
        }

        ASYNC.waterfall([
            function(next){
                return self.planValidity.getAllElgibleIDs(function(error , data){
                    if(error){
                        self.L.error("getAllElgibleIDs:",`Error:${error}`)
                        return next(error);
                    }
                    if(_.isArray(data) && data.length >= 0){
                        self.L.info("getAllElgibleIDs:",`fetched ${data.length} records`)
                        return next(null , _.map(data , 'id'))
                    }else{
                        self.L.error("getAllElgibleIDs:","error",error)
                        return next(error || "Error")
                    }
                } , startTimeString , endTimeString , self.clause , dayValue)
            },
            function( IDs , next){
                let currentIndex = 0;
                if(_.get(IDs,'length',0) == 0)return self.restartPlanValidityWithNextTime(startTime, endTime, dayValue, next);
                self.planValidity.getRecordsToNotifyByID(function _doUntilNoMoreRecords(error, data) {
                   
                    currentIndex = currentIndex + batchSize;
                    if (!error && data && data.length > 0) {
                        ++self.totalNoOfBatch;
                        self.execSteps(dayValue, data, () => {
                                ++self.NoOfBatchProcessed;
                                setTimeout(() => {
        
                                    if (self.greyScaleEnv && data.length) {
                                        self.L.log('getPlanValidityRecords:: processing next batch has byPassed,  greater than id: ' + self.lastprocessedId, " endTime: ", endTime, " dayValue: ", dayValue);
                                        return done();
                                    }
                                    if(currentIndex>=(IDs.length)){
                                        IDs = null;
                                        return self.restartPlanValidityWithNextTime(startTime, endTime, dayValue, next);
                                    }
                                    self.L.log('processing next batch greater than id: ' + self.lastprocessedId);
                                    self.planValidity.getRecordsToNotifyByID(_doUntilNoMoreRecords, IDs.slice(currentIndex , currentIndex+batchSize));
                                   
                                }, 100);
                            })
                    }
                    else {
                        if (!error) {
                            self.restartPlanValidityWithNextTime(startTime, endTime, dayValue, done);
                        } else {
                            self.L.critical('error in getPlanValidityRecords: ', error);
                            done();
                        }
                    }
                }, IDs.slice(currentIndex , currentIndex+batchSize));
            }
        ],function(error){
            if(error){
                self.L.error("getPlanValidityRecords:",`Error:${error}`)
            }
            return done()
        })
    }

    restartPlanValidityWithNextTime(startTime, endTime, dayValue, done) {
        let self = this,
            nextStartTime = endTime.clone();

        if (nextStartTime.diff(MOMENT(self.baseTime).add(-dayValue, 'days').endOf('d')) > 0) {
            self.L.log('Finishing');
            done();
        } else {
            self.lastprocessedId = 0;
            self.getPlanValidityRecords(dayValue, nextStartTime, done);
        }
    }

    execSteps(dayValue, records, done) {
        let self = this,
            chunkSize = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50),
            currentPointer = 0;

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(dayValue, nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 100);
                });
            },
            (err) => {
                if (err) {
                    this.L.error("error in execSteps: ", err);
                }
                done();
            }
        );
    }

    /**
     * Processing plan validity record in batch of 1000
     * @param {*} dayValue contains the day on which plan expired i.e 1 => d+1
     * @param {*} records contains all records
     * @param {*} done
     */
    processBatch(dayValue, records, done) {
        ASYNC.map(
            records,
            async (record, next) => {
                if (record && record.customer_id) {
                    record.dayValue = dayValue;
                    record.requestType = this.requestType;

                    if (this.checkMinimumPlanValidity(record, dayValue)) {
                        let { recharge_number, operator, service } = record;
                        let debugKey = `${recharge_number}_${operator}_${service}`;

                        let isCustomerInfoUpdate = await this.checkAndSetCustomerDetails(record, debugKey);
                        this.updatePlanAmount(record, debugKey);

                        if (isCustomerInfoUpdate) {
                            await this.updateCustomerDetailInDb(record).catch(err => {
                                this.L.critical(`[processBatch] error in updating customer details in the db for key:${debugKey}, error= `, err);
                            });
                        }

                        if (this.greyScaleEnv) {
                            record.skipNotification = true;
                        }

                        await this.publishInKafka(record, debugKey);
                        next();

                    } else {
                        this.notify.insertRejectedNotifications(next, 'minimum plan validity check failed', {...record, dataFrom: 'pvCron'});
                    }
                    
                } else {
                    next();
                }
            },
            err => {
                if (err) {
                    this.L.error("processBatch:: error: ", err);
                }
                done();
            }
        )
    }

    updatePlanAmount(record, debugKey) {
        let operator = _.get(record , ['operator'] , null);
        let service = _.get(record , ['service'] , null);
        let amount = _.get(record , ['amount'] , null);
        if (operator === 'airtel' && service === 'mobile' && amount === 49) {
            let updatedAmount = 79;
            _.set(record , ['amount'] , updatedAmount);
            this.L.log(`updatePlanAmount :: operator:${operator}_service:${service}_amount:${amount}_updatedAmount:${updatedAmount}_debugKey:${debugKey}`);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_NOTIFICATION_PUBLISHER', `OPERATOR:${operator}`]);
        }
    }

    checkMinimumPlanValidity(record, dayValue) {
        let planExpiryDate = _.get(record, 'validity_expiry_date', null),
            planCreatedDate = _.get(record, 'created_at', null);

        let self = this;

        if (MOMENT(planExpiryDate).isValid() && MOMENT(planCreatedDate).isValid()) {
            let operatorKeyForTemplate = this.getOperatorKeyForTemplate(record);

            let planValidityDays = MOMENT(planExpiryDate).diff(MOMENT(planCreatedDate), 'days');
            let minDuration = _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_MIN_PLAN_DURATION`],
                _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, 'MIN_PLAN_DURATION'], null));

            let d2PlanDurationValidity = minDuration && (planValidityDays < minDuration);
            // if plan duration criteria for d+2 do not match then do not process notification

            if (d2PlanDurationValidity) {
                this.L.log("processNotification :: Ignore sending notification for d2 plan expiry because plan duration less than:", minDuration);
                return false;
            }
        }
        return true;
    }

    async checkAndSetCustomerDetails(record, debugKey) {
        let queryString, response, isCustomerInfoUpdate = false;

        try {
            if (!_.get(record, 'cust_mobile', null)) {
                try {
                    queryString = '?fetch_strategy=BASIC&user_id=' + _.get(record, 'customer_id', null)
                    response = await this.OAuth.fetchCustomerDetail(queryString);

                    let phone = _.get(response, ['basicInfo', 'phone'], null);

                    if (phone) {
                        _.set(record, 'cust_mobile', phone);
                        _.set(record, 'cust_email', _.get(response, ['basicInfo', 'email'], null));
                        isCustomerInfoUpdate = true;
                    } else {
                        this.L.log(`[checkAndSetCustomerDetails] we have not gotten mobile_no of customer_id from oauth, ${debugKey} and response from oauth:`, response);
                    }

                } catch (error) {
                    this.L.error(`[checkAndSetCustomerDetails] error for ${debugKey} for queryString: ${queryString}`, error);
                }
            }

            if (!_.get(record, 'rn_customer_id', null) && (_.get(record, 'cust_mobile', null) != record.recharge_number) 
                            && (record.service !== "dth") && (record.service !== "fastag recharge")) { // dth, fastag recharge no. is not a phone no.

                queryString = '?fetch_strategy=USERID,BASIC&phone=' + _.get(record, 'recharge_number', null);
                response = await this.OAuth.fetchCustomerDetail(queryString);

                let userId = _.get(response, ['userId'], null);

                if (userId) {
                    _.set(record, 'rn_customer_id', userId);
                    isCustomerInfoUpdate = true;
                } else {
                    this.L.log(`[checkAndSetCustomerDetails] we have not gotten userId of rn_customer from oauth, ${debugKey} and response from oauth:`, response);
                }
            }

        } catch (error) {
            this.L.error(`[checkAndSetCustomerDetails] error for ${debugKey} for queryString: ${queryString}`, error);
        }

        return isCustomerInfoUpdate;
    }

    updateCustomerDetailInDb(record) {
        this.L.log('updating user data for the id ' + _.get(record, 'id', null) + ' mobile: ' +
            _.get(record, 'cust_mobile', null) + ' and email: ' + _.get(record, 'cust_email', null) +
            ", rn_customer_id :" + _.get(record, 'rn_customer_id', null));

        return new Promise((resolve, reject) => {
            this.planValidity.updateUserData(() => {
                resolve();
            }, record.id, _.get(record, 'cust_mobile', null), _.get(record, 'cust_email', null), _.get(record, 'rn_customer_id', null));
        })
    }

    async publishInKafka(payload, debugKey) {
        utility.sendNotificationMetricsFromSource(payload,"INITIATED","PV")
        try {
            await this.notify.kafkaPVPublisher.publishData([{
                topic: this.planValidityNotificationTopic,
                messages: JSON.stringify(payload)
            }], null, [200, 800]);

            PublishStats.publishCounter(1, {
                REQUEST_TYPE: this.requestType + "_" + "PUBLISHER",
                TYPE: "PUBLISHED",
            });

            this.L.log(`Message published successfully in Kafka on topic ${this.planValidityNotificationTopic} for key: ${debugKey} payLoad:`, JSON.stringify(payload));
        } catch (error) {
            utility.sendNotificationMetricsFromCreate(payload,"ERROR","PV")
            PublishStats.publishCounter(1, {
                REQUEST_TYPE: this.requestType + "_" + "PUBLISHER",
                TYPE: "NOT_PUBLISHED",
            });
            this.L.critical(`Error while publishing message in Kafka for key: ${debugKey} - MSG:- ` + JSON.stringify(payload), error)
            await this.notify.insertRejectedNotificationsViaPromise(this.billsLib.createErrorMessage(error), {...payload, dataFrom: 'pvCron'});
        }
    }

    getOperatorKeyForTemplate(record) {

        if (this.config.COMMON.VI_GROUP.includes(record.operator)) {
            let paidStatus = "paid_at_paytm";
            if (record.status == this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM) {
                paidStatus = "paid_at_other_platform";
            }

            return "vi" + "_" + paidStatus;
        } else {
            return _.get(record, 'operator', null);
        }
    }
}

export default planValidityNotification;

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-c, --clause <value>', 'clause', String)
            .option('-f, --fromIndex <value>', 'fromIndex', Number)
            .parse(process.argv);

        if (commander.verbose) {
            L.setLevel('verbose');
        }

        startup.init({ planValidityNotification: true }, function (err, options) {
            try {
                if (err) {
                    L.critical("Service has crashed!! Please check on priority", err)
                    process.exit(1);
                }
                options.clause = commander.clause;
                options.fromIndex = commander.fromIndex;

                let serviceInstance = new planValidityNotification(options);
                serviceInstance.start().catch(error => {
                    L.critical("Service has crashed!! Please check on priority", error)
                    process.exit(1);
                });
            } catch (error) {
                L.critical("Service has crashed!! Please check on priority", error)
                process.exit(1);
            }
        });
    }
})();