import nonPaytmBills from "../models/nonPaytmBills";
import ASYNC from 'async'
import _ from 'lodash'
import OS from 'os'
import utility from '../lib'
import digitalUtility from 'digital-in-util'
import MOMENT from 'moment';
import EncryptorDecryptor from 'encrypt_decrypt';
import BILLS from '../models/bills'
import CRYPTO from 'crypto';
import BillFetchAnalytics from '../lib/billFetchAnalytics.js'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import BillPush from '../lib/billPush';
import Logger from "../lib/logger";
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import NOTIFIER from '../services/notify'

import Q from 'q'
import service from "recharge-config/service";
const OPERATOR_LIST = ['vodafone idea', 'jio', 'airtel'];
class NonPaytmBills {
    constructor(options) {
        let self = this;
        self.config = options.config;
        self.L = options.L;
        self.nonPaytmBillsModel  = new nonPaytmBills(options)
        self.infraUtils = options.INFRAUTILS;
        self.greyScaleEnv = options.greyScaleEnv;
        self.cryptr = new EncryptorDecryptor();
        self.commonLib = new utility.commonLib(options);
        self.bills = new BILLS(options);
        self.reminderUtils = new digitalUtility.ReminderUtils();
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCHSIZE'], 2) : 500;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;    
        self.allowed_operators = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATORS'], null);
        self.partialRecordAllowedServices = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_RECORD_ALLOWED', 'SERVICES'], null);
        self.partialAllowedServicesComparison = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_COMPARE_ALLOWED', 'SERVICES'], ["electricity", "mobile"]);
        self.allowedOperatorForBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'BILL_FETCH_CONFIG', 'OPERATORS'], null);
        self.allowedOperatorForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], null);
        self.multiplePidOperators = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'MULTIPLE_PID_OPERATORS', 'OPERATORS'], null);
        self.allowedServiceForBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'BILL_FETCH_CONFIG', 'SERVICES'], ["electricity","loan"]);
        self.allowedServiceForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'ALLOWED_SERVICES'], ["electricity"]);
        self.blockedServiceForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'BLOCKED_SERVICES'], ["credit card"]);
        self.allowedServicesForUPMS = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPMS_CONFIG', 'SERVICES'], ["electricity"]);
        self.notificationTimeOut = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "PARTIAL_BILL_NOTIFICATION", "TIMEOUT"], 20*60);
        self.fastTagTimeOut = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "FASTAG_NOTIFICATION", "TIMEOUT"], 30);
        self.greyPercentage = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "PARTIAL_BILL_NOTIFICATION", "GREY_PERCENTAGE"], null);
        self.validityExpiryIds = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','VALIDITY_EXPIRY_IDS'],[5,6]);
        self.billGenNotUpdated = _.get(self.config, ['DYNAMIC_CONFIG', "NON_PAYTM_CONFIG", "BILL_GEN_NOT_UPDATED"], null);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.logger = new Logger(options);
        self.encryptHelper = new EncryptionDecryptioinHelper(options);
        this.OPERATOR_DEFAULT_DIFF_DAYS = {};
        this.initializeOperatorWisePartialToFullConfig();
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.DUEDATE_DIFF = _.get(this.config, ['DYNAMIC_CONFIG','PARTIAL_BILL_THRESHOLD', 'MOBILE_PARTIAL_BILL', 'IGNORE_DAYS_THRESHOLD'], 7);
        this.BillPush = new BillPush(options);
        this.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        this.billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION'], ['electricity']);
        self.DUE_DATE_RANGE_VALIDITY_EXPIRED_LOWER_BOUND = _.get(this.config, ['SAGA_SERVICE_CONFIG', 'smartReminderPrepaidStartDays'], 3);
        self.DUE_DATE_RANGE_VALIDITY_EXPIRED_UPPER_BOUND = _.get(this.config, ['SAGA_SERVICE_CONFIG', 'smartReminderPrepaidEndDays'], 3);
        this.allowedServicessForUserInitiatedDelete = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_INITIATED_DELETE_CONFIG', 'ALLOWED_SERVICES', 'SERVICES'], ['electricity']);
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        self.allowedRolloutPercentageBaja = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'BAJAJ_LOANS', 'ROLLOUT_PERCENTAGE'], null);
        this.blockedOperatorForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'BLOCKED_OPERATOR'], []);
        this.notify = new NOTIFIER(options);
        this.activePidLib = options.activePidLib;
        self.setVarFromDynamicConfig();
    }

    initializeOperatorWisePartialToFullConfig() {
        OPERATOR_LIST.forEach((operator_name) => {
            this.OPERATOR_DEFAULT_DIFF_DAYS[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_DIFF_DAYS'], 5);
        });
    }

    start(){
        const self = this;
        self.L.log("NonPaytmBills:: Starting service");

        ASYNC.waterfall([
            next =>{
                self.notify.configureKafkaPublisher((err) => {
                    return next(err);
                })
            },
            next => {
                
                self.startProducer(next);
            },
            next => {
                self.startConsumer(next);
            }
        ],(error) => {
            if (error) {
                self.L.critical('NonPaytmBills :: start', 'Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('NonPaytmBills :: start', 'Service started....');
            }
        })
    }

    startProducer(cb){
        let self = this;
        ASYNC.parallel([
            next => {
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                self.ctKafkaPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising ctKafkaPublisher Producer :: ', error);
                    self.L.log("NON_PAYTM_BILLS :: ctKafkaPublisher KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.nonRuPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.PUBLISHER_NON_RU.HOSTS
                });
                self.nonRuPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising nonRu Producer :: ', error);
                    self.L.log("NON_PAYTM_BILLS :: nonRuPublisher KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.nonRubillFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.NONRU_BILL_FETCH.HOSTS
                });
                self.nonRubillFetchKafkaPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising nonRubillFetchKafkaPublisher Producer :: ', error);
                    self.L.log("BILL_FETCH_REMINDER_REALTIME :: nonRubillFetchKafkaPublisher KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.paytmFirstKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.PAYTM_FIRST_CC_EVENTS_PUBLISHER.HOSTS
                });
                self.paytmFirstKafkaPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising paytmFirstKafkaPublisher Producer :: ', error);
                    self.L.log("NON_PAYTM_BILLS :: paytmFirstKafkaPublisher KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.nonRubillFetchKafkaPublisherRealtime = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.NONRU_NOTIFICATION_REALTIME.HOSTS
                });
                self.nonRubillFetchKafkaPublisherRealtime.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising nonRubillFetchKafkaPublisherRealtime Producer :: ', error);
                    self.L.log("BILL_FETCH_REMINDER_REALTIME :: nonRubillFetchKafkaPublisherRealtime KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.cassandraCdcPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.CASSANDRA_CDC.HOSTS
                });
                self.cassandraCdcPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising cassandraCdcPublisher Producer :: ', error);
                    self.L.log("NON_PAYTM_BILLS :: cassandraCdcPublisher KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events of UMPS_SUBCRIPTION
                 */
                self.upmsPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.UPMS_PUBLISHER.HOSTS
                });
                this.upmsPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYRM_BILLS", 'STATUS:ERROR', 'TYPE:UPMS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            }
        ], (error) => {
            return cb(error);
        })
    }

    startConsumer(cb){
        const self = this;
        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                "groupId": 'nonPaytmBillsConsumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_CONSUMER.TOPIC'),
                "id": "non-paytm-bills-consumer-" + OS.hostname(),
                "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.NONPAYTM_CONSUMER.BATCHSIZE',1000000),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.NONPAYTM_TIMEOUT',30*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.critical("nonPaytmBillsConsumer : consumer Configured cannot start.", error);
                else if (!error)
                    self.L.log("nonPaytmBillsConsumer : consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error)
        }
    }

    setVarFromDynamicConfig() {
        let self = this;
        self.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCHSIZE'], 2) : 500;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;    
        self.allowed_operators = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATORS'], null);
        self.partialRecordAllowedServices = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_RECORD_ALLOWED', 'SERVICES'], null);
        self.partialAllowedServicesComparison = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_COMPARE_ALLOWED', 'SERVICES'], ["electricity", "mobile"]);
        self.allowedOperatorForBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'BILL_FETCH_CONFIG', 'OPERATORS'], null);
        self.allowedOperatorForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], null);
        self.DUEDATE_DIFF = _.get(self.config, ['DYNAMIC_CONFIG', 'PARTIAL_BILL_THRESHOLD', 'MOBILE_PARTIAL_BILL', 'IGNORE_DAYS_THRESHOLD'], 7);
        self.notificationTimeOut = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "PARTIAL_BILL_NOTIFICATION", "TIMEOUT"], 20*60);
        self.validityExpiryIds = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','VALIDITY_EXPIRY_IDS'],[5,6]);
        self.greyPercentage = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "PARTIAL_BILL_NOTIFICATION", "GREY_PERCENTAGE"], null);
        self.fastTagTimeOut = _.get(self.config, ["DYNAMIC_CONFIG", "NON_PAYTM_CONFIG", "FASTAG_NOTIFICATION", "TIMEOUT"], 30);
        self.billGenNotUpdated = _.get(self.config, ['DYNAMIC_CONFIG', "NON_PAYTM_CONFIG", "BILL_GEN_NOT_UPDATED"], null);
        self.multiplePidOperators = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'MULTIPLE_PID_OPERATORS', 'OPERATORS'], null);
        self.allowedServiceForBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'BILL_FETCH_CONFIG', 'SERVICES'], "electricity");
        self.allowedServiceForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'ALLOWED_SERVICES'], ["electricity"]);
        self.blockedServiceForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'BLOCKED_SERVICES'], ["credit card"]);
        self.allowedServicesForUPMS = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPMS_CONFIG', 'SERVICES'], ["electricity"]);
        self.fastagOperatorList =  _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_LIST_MAPPING', 'OPERATOR_LIST'], []);  //update operator list here from config
        self.billPushServiceRegistration = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_SERVICE_REGISTRATION'], null);
        self.billPushOperatorRegistrationAllowed = _.get(this.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'COMMON', 'BILL_PUSH_OPERATOR_REGISTRATION'], ['electricity']);
        this.fastagOperatorPidMap = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP'], {});
        this.allowedServicessForUserInitiatedDelete = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_INITIATED_DELETE_CONFIG', 'ALLOWED_SERVICES', 'SERVICES'], ['electricity']);
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        self.fastagOperatorList = [];
        self.blockedOperatorForUpdateAll = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'UPDATE_ALL_CONFIG', 'BLOCKED_OPERATOR'], []);


        Object.entries(self.fastagOperatorPidMap).forEach(([key, value]) => {
            let activePid = self.activePidLib.getActivePID(value)
            self.fastagOperatorList.push(_.get(self.config, ['CVR_DATA', activePid, 'operator'] , ''));
        });

        OPERATOR_LIST.forEach((operator_name) => {
            this.OPERATOR_DEFAULT_DIFF_DAYS[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_DIFF_DAYS'], 5);
        });

        self.DUEDATE_DIFF = _.get(self.config, ['DYNAMIC_CONFIG', 'PARTIAL_BILL_THRESHOLD', 'MOBILE_PARTIAL_BILL', 'IGNORE_DAYS_THRESHOLD'], 7);
        setTimeout(() => {
            self.L.log(`setVarFromDynamicConfig`, `Updating service params from dynamic config..`)
            self.setVarFromDynamicConfig()
        }, 15 * 60 * 1000);
    }

    _initializeKafkaConsumer(cb) {
        const self = this;
        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                "groupId": 'nonPaytmBillsConsumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_CONSUMER.TOPIC'),
                "id": "non-paytm-bills-consumer-" + OS.hostname(),
                "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.NONPAYTM_CONSUMER.BATCHSIZE',1000000),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.NONPAYTM_TIMEOUT',30*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.critical("nonPaytmBillsConsumer : consumer Configured cannot start.", error);
                else if (!error)
                    self.L.log("nonPaytmBillsConsumer : consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error)
        }
    }

    _processKafkaData(records,resolveOffset , topic , partition , cb) {
        let startTime = new Date().getTime();
        const self = this;
        const chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_RECORDS', 'COMMON', 'CHUNKSIZE'], 1);
        let lastMessage,
        rechargeData = null,
        recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('nonPaytmBillsConsumer::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('nonPaytmBillsConsumer::_processKafkaData error while reading kafka');
            return cb();
        }

        records.forEach(row => {
            if(row && row.value) {
                try {
                    let nonPaytmPublishedTime = Number(_.get(row, 'timestamp', null));
                    rechargeData = JSON.parse(row.value);
                    let nonpaytm_onBoardTime = _.get(rechargeData, 'nonpaytm_onBoardTime', null);
                    if(!nonpaytm_onBoardTime){
                        _.set(rechargeData, 'nonpaytm_onBoardTime', nonPaytmPublishedTime);
                    }
                    _.set(rechargeData, 'nonPaytmAcknowledgeTime', new Date().getTime());
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INVALID_JSON_PAYLOAD']);
                    self.L.error("nonPaytmBillsConsumer::_processKafkaData", "Failed to parse recents data topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("nonPaytmBillsConsumer::_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp );
            }
        });

        self.L.log('nonPaytmBillsConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, chunkSize, self._processBillsData.bind(self), async (err) => {

            self.kafkaConsumerChecks.findOffsetDuplicates("NonPaytmBills", records,topic , partition);

            if(err) {
                self.L.error("nonPaytmBillsConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }else{

                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                
                    recordsToProcess = [];
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:NON_PAYTM_BILLS", "TIME_TAKEN:" + executionTime]);
    
                    setTimeout(() => {
                        // Resume consumer now
                        return cb();
                    }, self.kafkaResumeTimeout);
                
            }
        });
    }

    _processBillsData(billsKafkaRow, done) {
        const self = this;
        const dbEvent = _.get(billsKafkaRow, 'dbEvent', '');
        const isPrepaid = self.checkIfPrepaid(billsKafkaRow);
        let billsKafkaRowCloneForDataExhaust = null;
        if(!dbEvent || dbEvent === ''){
            let error='db event is missing'
            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), error,done,false);
            //why non-RU?
        }

        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:TRAFFIC', 'SOURCE:PROCESS_BILL_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`, `PARTIAL_BILL:${_.get(billsKafkaRow, 'partialBillState','NO_STATE')}`]);
        
        let errorResponse = self.validateKafkaRecord(billsKafkaRow);
        if (errorResponse) {
            let error = `_processBillsData:: Invalid Record: ${JSON.stringify(billsKafkaRow)} error:${errorResponse}`;

            if(billsKafkaRow.service == 'financial services'){
                self.logger.error(`_processBillsData:: Invalid Record: error:${errorResponse} `, billsKafkaRow, "financial services");
            } else {
                self.L.error(error);
            }
            

            return self.insertAnalyticsRecordBasedOnValidation(error, billsKafkaRow)
                .then(() => {
                    self.L.log(`_processBillsData : Successfully Processed Records for Analytics`);   
                    return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), errorResponse,done,false);
                })
                .catch((err) => {
                    self.L.log(`_processBillsData : Failed to Process Records for Analytics `, err);
                    return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), errorResponse,done,false);
                })
        }

        if(billsKafkaRow.paytype == 'credit card'){
            try {
                let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', billsKafkaRow.productId, 'attributes'] , '{}'))
                let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));

                if(!bankName || bankName == ''){ 
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:BANK_CODE_MISSING','SOURCE:MAIN_FLOW_EXECUTION']);
                    let err = `_processBillsData:: bank code unavailable ${JSON.stringify(billsKafkaRow)} `;
                    self.logger.error("_processBillsData:: bank code unavailable", billsKafkaRow, "financial services");

                    

                    return self.insertAnalyticsRecordBasedOnValidation(err, billsKafkaRow)
                        .then(() => {
                            self.L.log(`_processBillsData : Successfully Processed Records for Analytics`);
                            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), 'bank code unavailable',done,false);
                        })
                        .catch((err) => {
                            self.L.log(`_processBillsData : Failed to Process Records for Analytics `, err);
                            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), 'bank code unavailable',done,false);
                        })
                }
                billsKafkaRow.operator = bankName;
                billsKafkaRow.bankName = bankName;
            } 
            catch(error) {
                let err = `Error while parsing attributes for PID + ${_.get(self.config, ['CVR_DATA', billsKafkaRow.productId, 'attributes'], '{}')}, ${error}`;
                self.L.error('Error while parsing attributes for PID', _.get(self.config, ['CVR_DATA', billsKafkaRow.productId, 'attributes'], '{}'), error);
                
                return self.insertAnalyticsRecordBasedOnValidation(err, billsKafkaRow)
                    .then(() => {
                        self.L.log(`_processBillsData : Successfully Processed Records for Analytics`);
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), err,done,false);
                    })
                    .catch((err) => {
                        self.L.log(`_processBillsData : Failed to Process Records for Analytics `, err);
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), err,done,false);
                    })
            }

        }
        if(_.get(billsKafkaRow, "isRealTimeDataExhausted", null)){
            billsKafkaRowCloneForDataExhaust = _.cloneDeep(billsKafkaRow);
        }

        ASYNC.waterfall([
            next => {
                if(dbEvent == "upsert"){
                    return self.upsertData(next, billsKafkaRow);
                }
                if(dbEvent == "upsertWithoutRead"){
                    return self.upsertWithoutRead(next, billsKafkaRow);
                }
                if(dbEvent == "upsertWithRead"){
                    return self.upsertWithRead(next, billsKafkaRow);
                }

                if(dbEvent == "merge"){
                    return self.mergeData(next, billsKafkaRow);
                }

                if(dbEvent == 'delete'){
                    return self.deleteData(next, billsKafkaRow);
                }

                if(dbEvent == "findAndUpdateData"){
                    return self.findAndUpdateData(function(err,result){
                        if(err) return next(err); 
                        else{
                            billsKafkaRow=result;
                            return next();
                        }
                    }, billsKafkaRow)
                }
                if(dbEvent == "findAndCreate"){
                    return self.findAndCreateData(function(err,result){
                        if(err) return next(err);
                        else{
                            
                            billsKafkaRow = result;
                            return next();
                        }
                    }, billsKafkaRow)
                }
                
                if (dbEvent == "UpdateOrDeleteData") {
                    return self.UpdateOrDeleteData(next, billsKafkaRow)
                }

                if(dbEvent == "updateMultipleRecordsWithSameRN"){
                    return self.updateMultipleRecordsWithSameRN(done, billsKafkaRow, billsKafkaRowCloneForDataExhaust);
                }

                let error='No valid dbevent selected'
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), error,next);
                
                //return next('No valid dbevent selected');
            },

            next => {
                if(!billsKafkaRow){
                    self.L.log("nonPaytmBillsConsumer::_processBillsData", `Skipping all processing as billsKafkaRow is null`);
                    return done();
                }else{
                    return next();
                }
            },

            next => {
                if (_.toLower(_.get(billsKafkaRow, 'service')) == 'mobile') {
                    return self.fetchDataTemplates(function (err, result) {
                        if (err) {
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:NON_PAYTM_BILLS',
                                'STATUS:NOTIFICATION_TEMPLATE_NOT_FOUND',
                                "OPERATOR:" + billsKafkaRow.operator,
                                `SERVICE:${_.get(billsKafkaRow, 'service', 'NO_SERVICE')}`,
                                `SOURCE:${_.get(billsKafkaRow, 'source', 'NO_SOURCE')}`
                            ]);
                            self.L.error("fetchDataTemplates:: error in fetching templates ", err);
                        } else {
                            _.set(billsKafkaRow, 'templates', result);
                        }
                        return next();
                    }, billsKafkaRow)
                }
                else {
                    return next();
                }
            },
            next => {
                if(self.isArchivalCronsExpiredUser(billsKafkaRow) || (billsKafkaRow.isValaidationSync && !billsKafkaRow.isValidityExpired) || _.get(billsKafkaRow, 'toBeNotified', true)==false || (billsKafkaRow.action && billsKafkaRow.action == 'noAction' || dbEvent!='upsert') || billsKafkaRow.service == "fastag recharge" || _.get(billsKafkaRow, 'isRealTimeDataExhausted', false)){
                    self.L.log("nonPaytmBillsConsumer::_processBillsData", `Skipping notification for record with dbEvent:${dbEvent} and isValaidationSync:${_.get(billsKafkaRow,'isValaidationSync',null)} and toBeNotified:${_.get(billsKafkaRow,'toBeNotified',null)}`);
                    return next();
                }
                
                if(billsKafkaRow.isValaidationSync && billsKafkaRow.isValidityExpired){
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: VALIDATION_SYNC_NOTIFICATION_TOPIC", 
                        `SERVICE:${_.get(billsKafkaRow,['service'],null)}`, 
                        'STATUS:COUNT',
                        'TYPE:KAFKA_PUBLISH',
                        "OPERATOR:" + _.get(billsKafkaRow,['operator'],null),
                        "PARTIAL_BILL:" + _.get(billsKafkaRow, ["partialBillState"], null),
                        "IS_VALIDITY_EXPIRED:" + _.get(billsKafkaRow, ["isValidityExpired"], false)
                    ]);
                }

                // else if (_.toLower(_.get(billsKafkaRow, 'paytype'))=='prepaid' &&  _.toLower(_.get(billsKafkaRow, 'service'))=='mobile' && !billsKafkaRow.partialBillState) {
                //     self.L.log("nonPaytmBillsConsumer::_processBillsData", `Skipping BG notification for prepaid mobile`);
                //     return next();
                // }
                
                let billFetchKafkaPayload = self.formatBillFetchKafkaPayload(billsKafkaRow);
                if(billFetchKafkaPayload){
                    self.publishInBillFetchKafka(function(err){//
                        if(err){
                            //do i need to push here
                            self.L.error("Error while publishing publishBillFetchKafka with error :", err);
                        }
                        return next(null);
                    },billFetchKafkaPayload)
                }else return next(null);

            },
            next => {
                if(_.isEmpty(billsKafkaRow.otherCustomerNotificationPayloads) || self.isArchivalCronsExpiredUser(billsKafkaRow) || billsKafkaRow.isValaidationSync || _.get(billsKafkaRow, 'toBeNotified', true)==false || (billsKafkaRow.action && billsKafkaRow.action == 'noAction' || dbEvent!='upsert') || billsKafkaRow.service == "fastag recharge" || _.get(billsKafkaRow, 'isRealTimeDataExhausted', false)){
                    self.L.log("nonPaytmBillsConsumer::_processBillsData", `Skipping other customer notification for record with dbEvent:${dbEvent} and isValaidationSync:${_.get(billsKafkaRow,'isValaidationSync',null)} and toBeNotified:${_.get(billsKafkaRow,'toBeNotified',null)}`);
                    return next();
                }
                let billsKafkaRowClone = _.clone(billsKafkaRow);

                ASYNC.each(billsKafkaRow.otherCustomerNotificationPayloads, function (otherCustDetail, innerCb) {
                    _.set(billsKafkaRowClone, 'customerId', otherCustDetail.customerId);
                    _.set(billsKafkaRowClone, 'customerOtherInfo', otherCustDetail.customerOtherInfo);
                    let billFetchKafkaPayload = self.formatBillFetchKafkaPayload(billsKafkaRowClone);
                    if(billFetchKafkaPayload){
                        self.publishInBillFetchKafka(function(err){//
                            if(err){
                                //do i need to push here
                                self.L.error("Error while publishing publishBillFetchKafka with error :", error);
                            }
                            return innerCb();
                        },billFetchKafkaPayload)
                    }else return innerCb();
                }, function (err) {
                    return next(null);
                })
            },
            next => {
                if(_.get(billsKafkaRow, "partialBillState", null) || _.get(billsKafkaRow, "service", null) == "fastag recharge" || _.get(billsKafkaRow, "isRealTimeDataExhausted", false)){
                    return next(null);
                }
                // if ((_.get(billsKafkaRow, 'partialSmsFound', null) || (_.toLower(_.get(billsKafkaRow, 'service', null)) == "financial services" && ((billsKafkaRow.nextBillFetchDate != null && MOMENT().diff(billsKafkaRow.nextBillFetchDate, 'days') >= 0) || billsKafkaRow.nextBillFetchDate == null))) && self.allowedOperatorForBillFetch && self.allowedOperatorForBillFetch.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase()) && _.get(billsKafkaRow, 'source', null) != "reminderNonRuBillFetch") {
                //     self.publishNonRuBillFetch(function(err){
                //         if(err){
                //             //do i need to push here
                //             self.L.error("Error while publishing publishNonRuBillFetch with error :", err);
                //         }
                //         return next(null);
                //     },billsKafkaRow)
                // }
                if (self.isArchivalCronsExpiredUser(billsKafkaRow) && self.isOperatorAllowedForSmartFetch(billsKafkaRow) && !isPrepaid){
                    self.publishNonRuBillFetch(function (err) {
                        if (err) {
                            self.L.error("Error while publishing publishNonRuBillFetch with error :", err);
                        }
                    }, billsKafkaRow);
                    return next(null);
                } else if (self.isPublishableBill(billsKafkaRow)) {
                    if (self.multiplePidOperators && self.multiplePidOperators.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase()) && !isPrepaid) {
                        //Publishing both partial and full bills
                        self.publishNonRuBillFetchMultiplePid(function (err) {
                            if (err) {
                                self.L.error("Error while publishing publishNonRuBillFetchMultiplePid for multiPidOperators with error :", err);
                            }
                            return next(null);
                        }, billsKafkaRow)
                    }
                    else if (_.get(billsKafkaRow, 'demergerOperatorsList', null) != null && !isPrepaid && self.isBajajFinanceLoanAllowed(billsKafkaRow)) {
                        self.publishNonRuBillFetchMultiplePid(function (err) {
                            if (err) {
                                self.L.error("Error while publishing publishNonRuBillFetchMultiplePid for deMergerOperators with error :", err);
                            }
                            return next(null);
                        }, billsKafkaRow)
                    }
                    else if (_.get(billsKafkaRow, 'partialRecordFound', false) && !isPrepaid) {
                        self.publishNonRuBillFetch(function (err) {
                            if (err) {
                                self.L.error("Error while publishing publishNonRuBillFetch with error :", err);
                            }
                            return next(null);
                        }, billsKafkaRow)
                    }
                    else {
                        return next(null);
                    }
                }
                else{
                    return next(null)
                }
            },
            next => {
                let isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);

                if(isRealTimeDataExhausted){
                    self.publishInBillFetchKafkaForDataExhaust(function(err){
                        if(err){
                            self.L.error("Error while publishing publishInBillFetchKafkaForDataExhaust with error :", err);
                        }
                        return next(null);

                    },billsKafkaRow, billsKafkaRowCloneForDataExhaust)
                }else if (!self.isArchivalCronsExpiredUser(billsKafkaRow) && _.get(billsKafkaRow, 'service') == 'fastag recharge' && (billsKafkaRow.dbEvent == 'upsertWithoutRead' || (billsKafkaRow.dbEvent == 'upsertWithRead' && _.get(billsKafkaRow, 'isLowBalance', false))) ) {
                    let realtimeNotificationKafkaPayload = self.formatBillFetchKafkaPayload(billsKafkaRow);
                    self.publishInRealTimeNotification(function (err) {
                        if (err) {
                            return next(err)
                        }
                        else return next(null);
                    }, realtimeNotificationKafkaPayload, billsKafkaRow)
                }
                else {
                    return next(null);
                }
            },
            next => {
                if(self.isArchivalCronsExpiredUser(billsKafkaRow) || _.get(billsKafkaRow,'isValaidationSync',null) || (_.get(billsKafkaRow,'action',null) && billsKafkaRow.action == 'noAction') || billsKafkaRow.service == 'fastag recharge' ||  _.get(billsKafkaRow, 'isRealTimeDataExhausted', false) || isPrepaid) return next();
                return self.publishCtAndPFCCEvents(next, billsKafkaRow);
            }
        ], (error) => {
            if (error) {
                self.logger.error(`_prepareDataToInsert Failed with error ${error} for data with customer_id ${_.get(billsKafkaRow, 'customerId', null)}`, billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            } 
            let errorMsg=error;
            return self.insertAnalyticsRecordBasedOnValidation(error, billsKafkaRow)
                .then(() => {
                    
                    self.L.log(`_processBillsData : Successfully Processed Records for Analytics`);
                    if(error){
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), errorMsg,done,false);

                    }
                    else{
                        return done()

                    }
                    
                })
                .catch((err) => {
                    self.L.log(`_processBillsData : Failed to Process Records for Analytics `, err);
                    if(error){
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), errorMsg,done);

                    }
                    else{
                        return done(err);
                    }
                    
                })
        })
    }

    publishNonRuBillFetchMultiplePid(cb, billsKafkaRow) {
        let self = this;
        let payload = {
            customerId: billsKafkaRow.customerId,
            rechargeNumber: billsKafkaRow.rechargeNumber,
            productId: billsKafkaRow.productId,
            operator: billsKafkaRow.operator,
            amount: billsKafkaRow.amount,
            dueDate: billsKafkaRow.dueDate,
            billDate: billsKafkaRow.billDate,
            billFetchDate: billsKafkaRow.billFetchDate,
            next_bill_fetch_date: billsKafkaRow.nextBillFetchDate,
            paytype: billsKafkaRow.paytype,
            service: billsKafkaRow.service,
            circle: billsKafkaRow.circle,
            categoryId: billsKafkaRow.categoryId,
            customer_mobile: billsKafkaRow.customer_mobile,
            customer_email: billsKafkaRow.customer_email,
            status: billsKafkaRow.status,
            notification_status: 1,
            customerOtherInfo: billsKafkaRow.customerOtherInfo,
            extra: _.get(billsKafkaRow, 'extra', null),
            dwhClassId: billsKafkaRow.dwhClassId,
            rtspClassId: billsKafkaRow.rtspClassId,
            source: billsKafkaRow.source
        }
        self.nonRuPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.PUBLISHER_NON_RU_MULTIPLE_PID.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS",
                    `SERVICE:${_.get(payload, 'service', null)}`,
                    'STATUS:ERROR',
                    "TYPE:NON_RU_PUBLISHER",
                    "TOPIC:MULTIPLE_PID_TOPIC",
                    "OPERATOR:" + payload.operator
                ]);
                self.L.critical('nonPaytmKafkaPublisher :: publishNonRuBillFetchMultiplePid', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return cb('Error while publishing message in Kafka');
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS",
                    `SERVICE:${_.get(payload, 'service', null)}`,
                    'STATUS:PUBLISHED',
                    "TYPE:NON_RU_PUBLISHER",
                    "TOPIC:MULTIPLE_PID_TOPIC",
                    "OPERATOR:" + payload.operator
                ]);
                self.L.log('nonPaytmKafkaPublisher :: publishNonRuBillFetchMultiplePid', 'Message published successfully in Kafka', ' on topic MULTIPLE_PID_TOPIC', JSON.stringify(payload));
                return cb(null);
            }
        })
    }

    async updateProductIdAndAmbiguous(billsKafkaRow, pidMappingData) {
        let self = this;
        let extra = self.getExtra(billsKafkaRow);
        if (pidMappingData.length == 1) {
            pidMappingData = pidMappingData[0];
            let oldProductId = _.get(billsKafkaRow, 'productId', null);
            let newProductId = _.get(pidMappingData, 'product_id', oldProductId);
            if (newProductId == null) {
                return billsKafkaRow;
            }
            newProductId = Number(newProductId);
            extra.ambiguous = null;
            _.set(billsKafkaRow, 'productId', newProductId);
            if (_.get(billsKafkaRow, 'demergerOperatorsList', null) != null) {
                let oldOperator = _.get(billsKafkaRow, 'operator', null);
                let newOperatorName = _.get(pidMappingData, 'newoperator', oldOperator);
                if (newOperatorName == null) {
                    return billsKafkaRow;
                } else {
                    _.set(billsKafkaRow, 'oldOperatorName', oldOperator);
                    _.set(billsKafkaRow, 'operator', newOperatorName);
                }
            }
        }
        else if (self.multiplePidOperators && self.multiplePidOperators.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase())) {
            extra.ambiguous = { 'state': true, 'board': true };
        }
        else if (_.get(billsKafkaRow, 'demergerOperatorsList', null) != null) {
            let oldOperator = _.get(billsKafkaRow, 'operator', null);
            const board = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'UPPCL_CONFIG', 'BOARD'], 'Uttar Pradesh Power (UPPCL)');
            _.set(billsKafkaRow, 'operator', _.get(pidMappingData, 'newoperator', oldOperator));
            extra.ambiguous = { 'state': true, 'board': board };
        }
        else {
            return billsKafkaRow;
        }
        billsKafkaRow.extra = JSON.stringify(extra);
        return billsKafkaRow;
    }

    publishInRealTimeNotification(done, payload, processedRecord){
        let self = this;
            utility.sendNotificationMetricsFromSource(payload)
            self.nonRubillFetchKafkaPublisherRealtime.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''),
                messages: JSON.stringify(payload),
                key :  _.get(payload, 'data.customer_id', '')
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_PREPAID", 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME', 
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'service', null)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME', 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'service', null)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic NONRU_REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
                }
                return done(null);
            }, [200, 800]);
        
    }

    publishNonRuBillFetch(cb,billsKafkaRow){
        let self = this;
        let payload = {
            customerId: billsKafkaRow.customerId,
            rechargeNumber: billsKafkaRow.rechargeNumber,
            productId: billsKafkaRow.productId,
            operator: billsKafkaRow.operator,
            amount: billsKafkaRow.amount,
            dueDate: billsKafkaRow.dueDate,
            billDate: billsKafkaRow.billDate,
            billFetchDate: billsKafkaRow.billFetchDate,
            next_bill_fetch_date: billsKafkaRow.nextBillFetchDate,
            paytype: billsKafkaRow.paytype,
            service: billsKafkaRow.service,
            circle: billsKafkaRow.circle,
            categoryId: billsKafkaRow.categoryId,
            is_active_expired_user: billsKafkaRow.is_active_expired_user,
            service_payment_date_list: billsKafkaRow.service_payment_date_list,
            latest_payment_date: billsKafkaRow.latest_payment_date,
            customer_mobile: billsKafkaRow.customer_mobile,
            customer_email: billsKafkaRow.customer_email,
            status: billsKafkaRow.status,
            notification_status: 1,
            customerOtherInfo: billsKafkaRow.customerOtherInfo,
            extra: _.get(billsKafkaRow, 'extra', null),
            dwhClassId: billsKafkaRow.dwhClassId,
            rtspClassId: billsKafkaRow.rtspClassId,
            source: billsKafkaRow.source
        }
        self.nonRuPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.PUBLISHER_NON_RU.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS", 
                    `SERVICE:${_.get(payload, 'service', null)}`, 
                    'STATUS:ERROR', 
                    "TYPE:NON_RU_PUBLISHER",
                    "TOPIC:PUBLISHER_BILL_FETCH",
                    "OPERATOR:" + payload.operator
                ]);
                self.L.critical('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return cb('Error while publishing message in Kafka');
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS", 
                    `SERVICE:${_.get(payload, 'service', null)}`, 
                    'STATUS:PUBLISHED', 
                    "TYPE:NON_RU_PUBLISHER",
                    "TOPIC:PUBLISHER_BILL_FETCH",
                    "OPERATOR:" + payload.operator
                ]);
                self.L.log('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Message published successfully in Kafka', ' on topic PUBLISHER_BILL_FETCH', JSON.stringify(payload));
                return cb(null);
            }
        })
    }

    isPublishableBill(billsKafkaRow) {
        const self = this;
        return self.allowedOperatorForBillFetch &&
            self.allowedOperatorForBillFetch.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase()) &&
            _.get(billsKafkaRow, 'source', null) != "reminderNonRuBillFetch"
            && (_.get(billsKafkaRow, 'dbEvent', '') == "upsert");
    }

    fetchDataTemplates(cb, record){
        let self = this,
        notificationType = 'NODUEDATE',
        partialBillState= _.get(record, 'partialBillState',null)
        if(partialBillState==null){
            return cb('Missing partialBillState field');
        }
        

        let templates = {};

        for (let key in this.notificationConfig.type) {
           
            if (this.notificationConfig.type[key]) {
                templates[key] = this.getTemplateId(key, record, notificationType, _.get(record,'dueDate',null),partialBillState);
            }
        }
        return cb(null,templates);
    }

    getTemplateId(type, record, notificationType, dueDate, partialBillState){
        let self=this;
        
        let serviceBasedKey = `PARTIALBILL_${_.toUpper(_.get(record, 'service'))}_${partialBillState}_${notificationType}_${type}`;
        
        self.L.log("getTemplateId, serviceBasedKey: ", serviceBasedKey);
        
        let templateId = _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey], null);
                                
        self.L.log("getTemplateId::", `operator:${_.get(record, 'operator', null)}_notificationType:${notificationType}_dueDate:${dueDate}_partialBillState:${partialBillState}_type:${type}_templateId:${templateId}`);
        return templateId;
    }

    publishInBillFetchKafka(done,processedRecord){
        let self = this,
        toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen(),
        publisher = self.nonRubillFetchKafkaPublisher,
        topic = _.get(self.config, ['KAFKA', 'SERVICES', 'NONRU_BILL_FETCH', 'TOPIC'], '');
        utility.sendNotificationMetricsFromSource(processedRecord)
        if(toBeNotifiedRealtime){
            _.set(processedRecord, 'source', 'nonRUbillDuePublisherRealtime');
            publisher = self.nonRubillFetchKafkaPublisherRealtime;
            topic = _.get(self.config, ['KAFKA', 'SERVICES', 'NONRU_NOTIFICATION_PIPELINE_REALTIME', 'NONRU_NOTIFICATION_REALTIME'], '');
        }

        publisher.publishData([{
            topic: topic,
            messages: JSON.stringify(processedRecord),
            key :  _.get(processedRecord, 'data.customer_id', '')
        }], (error) => {
            if (error) {
                utility.sendNotificationMetricsFromSource(processedRecord,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: NON_PAYTM_BILLS", 
                        `SERVICE:${_.get(processedRecord,[ "data", 'service'],null)}`, 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        `TOPIC:${topic}`, 
                        "OPERATOR:" + _.get(processedRecord,["data", 'operator'],null),
                        "PARTIAL_BILL:" + _.get(processedRecord, ["data","partialBillState"], null)
                    ]);
                self.L.critical(`publishInKafka :: ${topic} Error while publishing message in Kafka - MSG:- ${JSON.stringify(processedRecord)} error: ${error}`);
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS", 
                    `SERVICE:${_.get(processedRecord,[ "data", 'service'],null)}`, 
                    'STATUS:PUBLISHED',
                    'TYPE:KAFKA_PUBLISH',
                    `TOPIC:${topic}`, 
                    "OPERATOR:" + _.get(processedRecord,["data", 'operator'],null),
                    "PARTIAL_BILL:" + _.get(processedRecord, ["data","partialBillState"], null)
                ]);
                self.logger.log(`prepareKafkaResponse :: ${topic} Message published successfully in Kafka on topic ${topic}`, processedRecord, _.get(processedRecord,[ "data", 'service'],null));
            }
            return done(null);
        }, [200, 800]);
    }

    preparePayloadForNotificationReject(processedRecord){
        let self = this;
        try{
            let customerOtherInfo = _.get(processedRecord, 'customerOtherInfo', '{}');
            if(typeof customerOtherInfo == 'string'){
                customerOtherInfo = JSON.parse(customerOtherInfo);
            }
            let payload = {
                "recharge_number" : _.get(processedRecord, 'rechargeNumber', null),
                "customer_id" : _.get(processedRecord, 'customerId', null),
                "operator" : _.get(processedRecord, 'operator', null),
                "product_id" : _.get(processedRecord, 'productId', null),
                "service" : _.get(processedRecord, 'service', null),
                "paytype" : _.get(processedRecord, 'paytype', null),
                "bill_source": "NONRU",
                "message_id": _.get(customerOtherInfo, 'msgId', null)
            }
            return payload;
        }catch(err){
            self.L.error("preparePayloadForNotificationReject: ", "Error in preparing payload for notification reject", err);
            return null;
        }
    }

    async publishInBillFetchKafkaForDataExhaust(done,processedRecord, billsKafkaRowData){
        let self = this;
        let extra = _.get(billsKafkaRowData, "extra", '{}');
        let is_notified_at_fe = _.get(billsKafkaRowData, "is_notified_at_fe", false);

        if(is_notified_at_fe){
            self.L.log("publishInBillFetchKafkaForDataExhaust :: ", "Notification already sent to user, hence not publishing in bill fetch kafka for CID and RN: ",processedRecord.customerId, processedRecord.rechargeNumber);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:NON_PAYTM_BILLS",
                'STATUS:NOT_NOTIFIED',
                'TYPE:KAFKA_PUBLISH',
                'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME',
                `CLASSID:${_.get(billsKafkaRowData, 'rtspClassId', null)}`,
                "OPERATOR:" + _.get(billsKafkaRowData,'operator',null),
                `ORIGIN:${_.get(billsKafkaRowData,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                `APP_VERSION:${_.get(billsKafkaRowData,'appVersion', null)}`
            ]);

            let error = "NOTIFIED_AT_FE_AND_NOT_PUBLISHED_IN_BILL_FETCH_KAFKA";
            let payloadToSendNotificationReject = self.preparePayloadForNotificationReject(processedRecord);
            if(payloadToSendNotificationReject){
                await self.notify.insertRejectedNotificationsViaPromise(error,payloadToSendNotificationReject);
            }
            return done(null);
        }else{
            try{
                if(!extra || extra == 'null'){
                    extra = {};
                }
                else if(typeof extra == 'string'){
                    extra = JSON.parse(extra);
                }
            }
            catch(err){
                self.L.error("publishInBillFetchKafkaForDataExhaust:: Error in parsing record extra", err);
            }

            let existingExtra = _.get(processedRecord, "extra", '{}');

            try{
                if(!existingExtra || existingExtra == 'null'){
                    existingExtra = {};
                }
                else if(typeof existingExtra == 'string'){
                    existingExtra = JSON.parse(existingExtra);
                }
            }
            catch(err){
                self.L.error("publishInBillFetchKafkaForDataExhaust:: Error in parsing existing record extra", err);
            }
            extra.is_data_exhaust = true;
            extra.recon_id = _.get(existingExtra, 'recon_id', null);

            let payload = {
                source: "dataExhaust",
                notificationType: "BILLDUE",
                data: {
                    customer_id: processedRecord.customerId,
                    recharge_number: processedRecord.rechargeNumber,
                    product_id: _.get(processedRecord, 'productId'),
                    operator: processedRecord.operator,
                    due_amount: _.get(billsKafkaRowData, "amount"),
                    data_consumed: _.get(billsKafkaRowData,"dataConsumed"),
                    bill_fetch_date: MOMENT(),
                    paytype: "prepaid",
                    service: _.get(billsKafkaRowData,"service"),
                    circle: _.get(processedRecord, "circle"),
                    customer_mobile:  null,
                    customer_email: null,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    user_data: null,
                    bill_date: null,
                    notification_status: 1,
                    due_date: _.get(billsKafkaRowData,"dueDate"),
                    customer_other_info: _.get(billsKafkaRowData, "customerOtherInfo", '{}'),
                    plan_bucket: _.get(billsKafkaRowData, "planBucket"),
                    templates: _.get(billsKafkaRowData, "templates"),
                    isRealTimeDataExhausted: _.get(billsKafkaRowData, "isRealTimeDataExhausted"),
                    time_interval: parseInt(_.get(billsKafkaRowData, "time_interval")),
                    extra: extra,
                    dwhKafkaPublishedTime : _.get(billsKafkaRowData, 'dwhKafkaPublishedTime', null),
                    nonpaytm_onBoardTime : _.get(billsKafkaRowData, 'nonpaytm_onBoardTime', null),
                    nonPaytmAcknowledgeTime : _.get(billsKafkaRowData, 'nonPaytmAcknowledgeTime', null),
                    billFetchReminder_onBoardTime :new Date().getTime(),
                }
            }
            _.set(payload, ['data', 'billFetchReminder_onBoardTime_dummy'],_.get(billsKafkaRowData, "nonpaytm_onBoardTime"));
            if(_.get(billsKafkaRowData,"isRealTimeDataExhausted",true))_.set(payload, ['data', 'isRnDecrypted'],true);
            utility.sendNotificationMetricsFromSource(payload)
            self.nonRubillFetchKafkaPublisherRealtime.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''),
                messages: JSON.stringify(payload),
                key :  _.get(payload, 'data.customer_id', '')
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: NON_PAYTM_BILLS",
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME',
                        `CLASSID:${_.get(billsKafkaRowData, 'rtspClassId', null)}`,
                        "OPERATOR:" + _.get(billsKafkaRowData,'operator',null),
                        `ORIGIN:${_.get(billsKafkaRowData,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(billsKafkaRowData,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:NON_PAYTM_BILLS",
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        `CLASSID:${_.get(billsKafkaRowData, 'rtspClassId', null)}`,
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME',
                        "OPERATOR:" + _.get(billsKafkaRowData,'operator',null),
                        `ORIGIN:${_.get(billsKafkaRowData,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(billsKafkaRowData,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic NONRU_REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
                }
                return done(null);
            }, [200, 800]);
        }
    }

    formatBillFetchKafkaPayload(record){
        let self = this;
        try{
            let payload = {
                source: "nonRUbillDuePublisher",
                notificationType: "BILLGEN",
                data: {
                    customer_id: record.customerId,
                    recharge_number: record.rechargeNumber,
                    product_id: record.productId,
                    operator: record.operator,
                    due_amount: record.amount,
                    bill_fetch_date: MOMENT(),
                    paytype: record.paytype,
                    service: record.service,
                    circle: record.circle,
                    customer_mobile:  null,
                    customer_email: null,
                    bank_name: record.operator,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    user_data: null,
                    bill_date:record.billDate,
                    notification_status: 1,
                    due_date: record.dueDate? MOMENT(record.dueDate).utc().valueOf():null,
                    extra: record.extra,
                    customerOtherInfo: JSON.stringify(record),
                    plan_bucket: record.planBucket,
                    partialBillState: _.get(record, "partialBillState", null),
                    templates: _.get(record, "templates", null),
                    customer_other_info: _.get(record, "customerOtherInfo", '{}'),
                    dwhKafkaPublishedTime : _.get(record, 'dwhKafkaPublishedTime', null),
                    nonpaytm_onBoardTime : _.get(record, 'nonpaytm_onBoardTime', null),
                    nonPaytmAcknowledgeTime : _.get(record, 'nonPaytmAcknowledgeTime', null),
                }
            }

            if (_.toLower(_.get(record, 'paytype'))=='prepaid' &&  _.toLower(_.get(record, 'service'))=='mobile') {                    
                let dueDate= MOMENT(_.get(record, 'dueDate', null)).isValid() ? MOMENT(_.get(record, 'dueDate', null)).format('YYYY-MM-DD') : null;
                let currentDate = MOMENT().format('YYYY-MM-DD');
                
                if(MOMENT(dueDate).diff(currentDate, 'days') < _.get(self.config, ['REALTIME_SMS_PARSING','DUE_DATE_NOTIFICATION','EARLIEST_DAY_VALUE'],10) &&
                MOMENT(dueDate).diff(currentDate, 'days') > _.get(self.config, ['REALTIME_SMS_PARSING','DUE_DATE_NOTIFICATION','LATEST_DAY_VALUE'],-7)){
                    self.L.log("publishToPvKafka:: ", 'going to publish on NONRU_BILL_FETCH kafka');
                    _.set(payload, 'notificationType', 'BILLDUE');
                }
                else if(_.get(record, "partialBillState", null)){
                    _.set(payload, ["data", "time_interval"], self.notificationTimeOut);
                }
                else if(!_.get(record, "partialBillState", null)){
                    self.L.log("publishToPvKafka:: ", 'Not publishing on NONRU_BILL_FETCH kafka due to failing eligibility')
                    return null;
                }
            }
            if( _.toLower(_.get(record, 'paytype')) == 'prepaid' && _.toLower(_.get(record, 'service')) == 'fastag recharge'){
                _.set(payload, "source", "nonRUbillGenPublisherRealtime");
                _.set(payload, ["data", "time_interval"], self.fastTagTimeOut);
            }
            if(record.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)){
                _.set(payload, "notificationType", "OLD_BILL_NOTIFICATION");
                _.set(payload, ["data", "status"], _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5));
            }
            _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());
            return payload;
        }catch(err){
            self.L.error("formatBillFetchKafkaPayload:: Error while creating payload ", err);
            return null;
        }
    }
    insertAnalyticsRecordBasedOnValidation(error, kafkaPayload = {}) {
        let self = this;
        const dbEvent = _.get(kafkaPayload, 'dbEvent', '');
        if(dbEvent == "upsert" && _.has(kafkaPayload, "refId")) {   // capture record only for sms parsing cc which are updated in db
            return new Promise((resolve, reject) => {
                return self.commonLib.validateAndCreatePayload(error, kafkaPayload)
                    .then((params) => {
                        return self.bills.insertAnalyticsRecordInDB(params, "sms_parsing_analytics")
                            .then((data) => {
                                self.logger.log('insertAnalyticsRecordBasedOnValidation: Successfully inserted records for data in Analytics', kafkaPayload, _.get(kafkaPayload, 'service', null));
                                return resolve();
                            })
                            .catch((err) => {
                                self.logger.error(`insertAnalyticsRecordBasedOnValidation: Failed to insert record ${err} for data `, kafkaPayload, _.get(kafkaPayload, 'service', null));
                                return reject(err);
                            })
                    })
                    .catch((err) => {
                        self.L.error(`insertAnalyticsRecordBasedOnValidation: Failed to validate Analytics record - Error: `, err);
                        return reject(err);
                    })
    
            })
        }
        else{
            return Promise.resolve();
        }
        
    }

    publishCtAndPFCCEvents(done, billsKafkaRowTemp) {
        const self = this;        
        let billsKafkaRow = _.clone(billsKafkaRowTemp);
        const productId = billsKafkaRow.productId;
        if(billsKafkaRow.is_encrypted_done){
            billsKafkaRow.rechargeNumber = this.cryptr.decrypt(billsKafkaRow.rechargeNumber);
        }
        let dbDebugKey = `rech:${billsKafkaRow.rechargeNumber}::cust:${billsKafkaRow.customerId}::op:${billsKafkaRow.operator}`;
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_BILLS'], 'smsParsedBillPayment');
        if(_.get(billsKafkaRow, 'dbEvent', '') == 'delete'){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_BILLS_DELETED'], 'smsParsedBillPaymentDeleted');
        }
        if(_.get(billsKafkaRow, 'service', '') == 'mobile'){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_PREPAID'], 'smsParsedPrepaidRecharge');
             if(_.get(billsKafkaRow, 'dbEvent', '') == 'delete'){
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_PREPAID_DELETED'], 'smsParsedPrepaidRechargeDel');
            }
        }

        if(_.get(billsKafkaRow, 'service', '') == 'loan'){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_LOAN'], 'smsparsedloanpayment');
            if(_.get(billsKafkaRow, 'dbEvent', '') == 'delete') {
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_LOAN_DELETED'], 'smsparsedloanpaymentdeleted');
            }
        }
        
        if(_.get(billsKafkaRow, 'service', '') == 'mobile' && _.get(billsKafkaRow, 'paytype', '') == 'postpaid'){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_MOBILE_POSTPAID'], 'smsParsedMobilePostpaid');
            if(_.get(billsKafkaRow, 'dbEvent', '') == 'delete') {
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_MOBILE_POSTPAID_DELETED'], 'smsParsedMobilePostpaidDeleted');
            }
        }

        if(_.get(billsKafkaRow, 'smsparsedPlanRecharged', false)){
            eventName = "smsparsedPlanRecharged"
        }

        if (_.get(billsKafkaRow, 'service', '') == 'electricity' && _.get(billsKafkaRow, 'paytype', '') == 'postpaid') {
            if(_.get(billsKafkaRow, 'source', '') == 'postpaidSmsParsingBillPaid'){
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_ELECTRICITY_POSTPAID_BILL_PAID'], 'smsParsedBillPaidElectricityPostpaid')
            }
            else{
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_ELECTRICITY_POSTPAID'], 'smsParsedElectricityPostpaid');
            }
        }

        if (_.get(billsKafkaRow, 'service', '') == 'rent payment' && _.get(billsKafkaRow, 'paytype', '') == 'postpaid') {
            let extra;
            try{
               extra = _.get(billsKafkaRow, 'extra','{}');
            }catch(jsonErr){
                self.L.error('error parsing json data', jsonErr);
            }
            extra = JSON.parse(extra);
            let level = _.get(extra, 'level_2_category','');

           if( level == 1 ){
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_SMS_RENT_LANDLORD'], 'smsParsedRentLandlord'); 
           }else if(level == 2 ){
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_SMS_RENT_TENANT'], 'smsParsedRentTenant');
           }
           if(_.get(billsKafkaRow, 'dbEvent', '') == 'delete') {
                eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_SMS_RENT_DELETE'], 'smsParsedRentDeleted');
           }
        }
        if(_.get(billsKafkaRow,'source',null) == "reminderNonRuBillFetch"){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_PUBLISHER'], 'SMSParsedPartialElectrcityPostpaid');
        }

        if(_.get(billsKafkaRow,'source',null) == "reminderNonRuBillFetch" && _.get(billsKafkaRow, 'service', null) == 'financial services'){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NON_PAYTM_PUBLISHER_CC'],'bbpsBillFetchCreditCard');
        }

        let rechargeNumber = _.get(billsKafkaRow, "rechargeNumber", null);
        let customerId = _.get(billsKafkaRow, "customerId", null);
        if(_.get(billsKafkaRow, 'service', '') == 'mobile' && _.get(billsKafkaRow, "dwhClassId", null)){
            if(billsKafkaRow.dwhClassId == 7){
                eventName = "smsparsedIncomingStopped";
            }
            if(self.validityExpiryIds.includes(billsKafkaRow.dwhClassId)){
                if (_.get(billsKafkaRow, "dueDate", null) == null) eventName = 'PrepaidRechargedWODate';
            }
        }

        console.log("publishCtEvents", eventName);

        ASYNC.waterfall([
            next => {
                if(_.get(self.config ,['DYNAMIC_CONFIG','CT_CONFIG','CT_EVENT_FILTER',eventName],null) == 1 ){
                    self.commonLib.validateCTEventCondition((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, customerId, rechargeNumber,eventName);
                }else{
                    next(null)
                }
            },
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, billsKafkaRow.customerId, billsKafkaRow);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, billsKafkaRow);
            },
            next => {                  
                let mappedData = self.reminderUtils.createCTPipelinePayload(billsKafkaRow, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                ASYNC.parallel([
                    function (cb) {
                        if(self.commonLib.isCTEventBlocked(eventName)){
                            self.L.info(`Blocking CT event ${eventName}`)
                            return cb()
                        }
                        if (_.get(billsKafkaRow, 'notificationStatus', 1)) {
                            let dataToPush = mappedData;
                            let clonedDataToPush = clonedData;
                            let customerOtherInfo = JSON.parse(_.get(clonedData,'customerOtherInfo',"{}"));
                            if(_.get(customerOtherInfo,'partialBillfound', false) == true) {
                                dataToPush = _.cloneDeep(dataToPush);
                                clonedDataToPush = _.cloneDeep(clonedDataToPush);
                                _.set(dataToPush, "eventName", "partialBillfound");
                                _.set(clonedDataToPush, "eventName", "partialBillfound");
                            }
                            self.ctKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(dataToPush)
                            }], (error) => {
                                if (error) {
                                    if(_.get(customerOtherInfo,'partialBillfound', false) == true) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "PARTIAL_BILL_FOUND:TRUE", "TYPE:CT_EVENTS", "OPERATOR:" + billsKafkaRow.operator,`SERVICE:${_.get(billsKafkaRow,'service','NO_SERVICE')}`,`SOURCE:${_.get(billsKafkaRow,'source','NO_SOURCE')}`,`EVENT_NAME:${eventName}`]);
                                    } else{
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + billsKafkaRow.operator,`SERVICE:${_.get(billsKafkaRow,'service','NO_SERVICE')}`,`SOURCE:${_.get(billsKafkaRow,'source','NO_SOURCE')}`,`EVENT_NAME:${eventName}`]);
                                    }
                                    self.logger.critical(`publishInKafka :: publishCtEvents Error while publishing message in Kafka ${error} - MSG:- `, clonedDataToPush, _.get(clonedDataToPush, 'service', null));
                                } else {
                                    if(_.get(customerOtherInfo,'partialBillfound', false) == true) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:PUBLISHED', "PARTIAL_BILL_FOUND:TRUE", "TYPE:CT_EVENTS", "OPERATOR:" + billsKafkaRow.operator,`SERVICE:${_.get(billsKafkaRow,'service','NO_SERVICE')}`,`SOURCE:${_.get(billsKafkaRow,'source','NO_SOURCE')}`,`EVENT_NAME:${eventName}`]);
                                    } else {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + billsKafkaRow.operator,`SERVICE:${_.get(billsKafkaRow,'service','NO_SERVICE')}`,`SOURCE:${_.get(billsKafkaRow,'source','NO_SOURCE')}`,`EVENT_NAME:${eventName}`]);
                                    }
                                    self.logger.log('prepareKafkaResponse :: publishCtEvents Message published successfully in Kafka on topic REMINDER_CT_EVENTS', clonedDataToPush, _.get(clonedDataToPush, 'service', null));
                                }
                                cb(error);
                            }, [200, 800]);
                        } else {
                            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(billsKafkaRow, 'notificationStatus', 0)}`);
                            cb();
                        }
                    },
                    function (cb) {
                        if(_.get(mappedData, 'service',null)=='financial services'){
                            self.paytmFirstKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.PAYTM_FIRST_CC_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(mappedData)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:PFCC_EVENTS", "OPERATOR:" + billsKafkaRow.operator]);
                                    self.logger.critical(`publishInKafka :: publishPFCCEvents Error while publishing message in Kafka ${error} - MSG:- `, clonedData, _.get(clonedData, 'service', null));
                                } else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:PUBLISHED', "TYPE:PFCC_EVENTS", "OPERATOR:" + billsKafkaRow.operator]);
                                    self.logger.log('prepareKafkaResponse :: publishPFCCEvents Message published successfully in Kafka on topic PAYTM_FIRST_CC', clonedData, _.get(clonedData, 'service', null));
                                }
                                cb(error);
                            }, [200, 800]);
                        } else cb();
                    },
                ], function done(err) {
                    return next(err);
                });
            }
        ], (err) => {
            if(err)
                self.L.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' , err);
            return done(err)
        })
    }

    // publishCassandraCDCvents(done, billsKafkaRow) {
    //     const self = this; 
    //     if(_.get(billsKafkaRow, 'convertPayload',null) == 'y'){
    //         billsKafkaRow = self.convertCdcRecovery(billsKafkaRow);
    //     }
    //     if(typeof _.get(billsKafkaRow, 'extra', {}) != 'string'){    
    //         billsKafkaRow.extra = JSON.stringify(_.get(billsKafkaRow, 'extra',{}));
    //     }
    //      self.cassandraCdcPublisher.publishData([{
    //         topic: _.get(self.config.KAFKA, 'SERVICES.CASSANDRA_CDC.TOPIC', ''),
    //         messages: JSON.stringify(billsKafkaRow)
    //     }], (error) => {
    //         if (error) {
    //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_CDC", 'STATUS:ERROR', "TYPE:CCASSANDRA_CDC", "OPERATOR:" + billsKafkaRow.operator]);
    //             self.L.critical('publishInKafka :: CASSANDRA_CDC', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(billsKafkaRow), error);
    //         } else {
    //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_CDC", 'STATUS:PUBLISHED', "TYPE:CASSANDRA_CDC", "OPERATOR:" + billsKafkaRow.operator]);
    //             self.L.log('prepareKafkaResponse :: CASSANDRA_CDC', 'Message published successfully in Kafka', ' on topic CDC_RECOVERY', JSON.stringify(billsKafkaRow));
    //         }
    //         done(error);
    //     }, [200, 800]);
            
    // }

    async upsertWithoutRead(cb, billsKafkaRow){
        const self = this;
        let updateDueTable= false, updateGenTable = false, existingRecordDueTable=[], existingRecordGenTable=[];
        self.L.log("upsertWithoutRead :: Received payload for non paytm");
        billsKafkaRow.billGenNotUpdated = _.get(self.billGenNotUpdated, `${billsKafkaRow.service}::${billsKafkaRow.paytype}`, null);
        self.nonPaytmBillsModel.readRecentBills(billsKafkaRow)
        .then((recentRecords) => {
            
           
            self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, recentRecords, updateDueTable, updateGenTable, existingRecordDueTable, existingRecordGenTable,self.cassandraCdcPublisher).then((data)=>{
                return cb();
                
            })
                .catch(error => {
                    let err = `Failed with error ${error}`;
                    self.L.error(`upsertWithoutRead`, `Failed with error ${error}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_WITHOUT_DATA', 'TYPE:DB_EXCEPTION', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
                    return cb(error);
                    // return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
                })
        })
        .catch(error => {
            self.L.error(`upsertWithoutRead`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPSERT_WITHOUT_DATA','TYPE:UPSERT_FAILED',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`]);
            return cb(error)
            // return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
        })
    }

    async upsertWithRead(cb, billsKafkaRow){
        const self = this;
        let isFastagLowBalance = false;
        self.L.log("upsertWithRead :: Received payload for non paytm");
        
        if (_.get(JSON.parse(billsKafkaRow.customerOtherInfo || '{}'), 'isLowBalance', false) &&  _.get(billsKafkaRow, 'service', '').toLowerCase() == 'fastag recharge') {
            isFastagLowBalance = true;
        }

        if(!isFastagLowBalance && _.get(billsKafkaRow, 'operator', null)=='dummy bank'){ // this is to handle dummy bank, as we dont' want to process oprator as dummy bank
            self.L.info(`upsertWithRead`, `Dummy bank record found, ignoring it  ${billsKafkaRow}`);
            cb("dummy_operator_found")
        }
        let updateFlag = false;
    
        let updateDueDateTable = false, updateDueTable= false, updateGenTable = false, existingRecordDueTable=[], existingRecordGenTable=[];
        billsKafkaRow.billGenNotUpdated = _.get(self.billGenNotUpdated, `${billsKafkaRow.service}::${billsKafkaRow.paytype}`, null);
        self.nonPaytmBillsModel.readRecentBills(billsKafkaRow)
        .then((recentRecords) => {

            if(recentRecords.length){
                updateFlag = true;
            }
 
            
            let operators = !_.isEmpty(self.fastagOperatorList)? self.fastagOperatorList : _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_LIST_MAPPING', 'OPERATOR_LIST'], []);  //update operator list here from config
          
            if (isFastagLowBalance)
                operators = [];
          
            self.nonPaytmBillsModel.readBillsWithOperators(billsKafkaRow,operators)
            .then((oldBillsData)=>{
                let isDuplicate = false;
                
                if(oldBillsData && oldBillsData.length){
                    if (isFastagLowBalance) {
                        isDuplicate = self.handleFastagLowBalance(oldBillsData, billsKafkaRow);
                    }
                    else {
                        oldBillsData.every(row =>{
                            if(_.get(row, 'operator', null)=='dummy bank'){
                                self.nonPaytmBillsModel.deleteRecentAndBillsRecords(row,(error =>{
                                    if(error){
                                        self.L.error(`upsertWithRead`, `error while deleting data for dummy bank with error ${error}`);
                                    }
                                }));

                                return true; // will continue the execution
                            }else{ // if data exist with any other bank we will not proceed with current data so marking duplicate.
                                isDuplicate = true;
                                self.L.error(`upsertWithRead`, `Duplicate entry with other bank for paylaod  ${billsKafkaRow} with db data ${row}`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_WITHOUT_DATA', 'TYPE:DUPLICATE', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
                            return false; // break the loop
                            }
                        })
                    }
                }

                if(!isDuplicate){
                self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, recentRecords, updateDueTable, updateGenTable, existingRecordDueTable, existingRecordGenTable,self.cassandraCdcPublisher).then((data)=>{
                    return cb();

                })
                    .catch(error => {
                        let err = `Failed with error ${error}`;
                        self.L.error(`upsertWithoutRead`, `Failed with error ${error}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_WITHOUT_DATA', 'TYPE:DB_EXCEPTION', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
                        return cb(error);
                        //return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
                    })
                }else{
                    cb();
                }
            })
            .catch(error => {
                self.L.error(`upsertWithoutRead`, `Failed with error ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPSERT_WITHOUT_DATA','TYPE:UPSERT_FAILED',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`]);
                return cb(error)
            })

        })
        .catch(error => {
            self.L.error(`upsertWithoutRead`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPSERT_WITHOUT_DATA','TYPE:UPSERT_FAILED',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`]);
            return cb(error)
        })
    }

    async preProcessData(billsKafkaRow){
        const self = this;
        try {
            if(billsKafkaRow.service == 'mobile'){
                billsKafkaRow.rechargeNumber = self.cryptr.encrypt(billsKafkaRow.rechargeNumber);
                billsKafkaRow.is_encrypted_done = true;

                if( billsKafkaRow.service == 'mobile' && billsKafkaRow.isRealTimeDataExhausted){
                    billsKafkaRow.dueDate = null;
                    // billsKafkaRow.amount = 0;
                }

                let extra = _.get(billsKafkaRow, 'extra', null);
                
                if(!extra || extra == 'null'){
                    extra = {};
                }else if (typeof extra == 'string') extra = JSON.parse(extra);
                
                if (billsKafkaRow.isValaidationSync) {
                    extra.created_source = 'validationSync';
                    extra.updated_source = 'validationSync';
                    extra.updated_data_source = 'validationSync';
                } else {
                    extra.created_source = 'sms';
                    extra.updated_source = 'sms';
                    extra.updated_data_source = _.get(extra, 'updated_data_source', 'sms');
                }
                billsKafkaRow.extra = JSON.stringify(extra);

                if (billsKafkaRow.dueDate && MOMENT(billsKafkaRow.dueDate).isValid() && MOMENT(billsKafkaRow.dueDate).diff(MOMENT().endOf('day'), 'days') < 0) {
                    self.L.log("upsertData::  skipping inserting new record in DB as Old Due Date Bill Found", billsKafkaRow);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_OLD_BILL', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:OLD_DUE_DATE_IN_PAYLOAD']);
                    throw new Error("skipping inserting new record in DB as Old Due Date Bill Found");
                }
            }
            return billsKafkaRow;
        } catch (error) {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), error);
            self.L.error(`upsertData`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_DATA','TYPE:PARSING_ERROR_MOBILE_DTH',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`,`PARTIAL_BILL:${_.get(billsKafkaRow, 'partialBillState','NO_STATE')}`]);
            throw error;
        }
    }

    handleFastagLowBalance(oldBillsData, billsKafkaRow) {
        const self = this;
        let isDuplicate = false;
        
        const { existingSmsTime, newSmsTime } = self.extractSmsTimes(oldBillsData[0], billsKafkaRow);
        
        if (newSmsTime) {
            isDuplicate = self.checkSmsTimeDuplication(newSmsTime, existingSmsTime, billsKafkaRow);
        }
        
        return isDuplicate;
    }


    extractSmsTimes(existingRecord, newRecord) {
        const self = this;
        let existingSmsTime = null;
        let newSmsTime = null;

        try {
            let existingExtra = _.get(existingRecord, 'extra', '{}');
            if (typeof existingExtra === 'string') {
                existingExtra = JSON.parse(existingExtra);
            }
            existingSmsTime = _.get(existingExtra, 'smsDateTime', null);
        } catch (err) {
            self.L.error("Error parsing existing record extra", err);
        }

        try {
            let newExtra = _.get(newRecord, 'extra', '{}');
            if (typeof newExtra === 'string') {
                newExtra = JSON.parse(newExtra);
            }
            newSmsTime = _.get(newExtra, 'smsDateTime', null);
        } catch (err) {
            self.L.error("Error parsing new record extra", err);
        }

        return { existingSmsTime, newSmsTime };
    }

    checkSmsTimeDuplication(newSmsTime, existingSmsTime, billsKafkaRow) {
        const self = this;
        let isDuplicate = false;

        let smsDate = MOMENT(Number(newSmsTime)).startOf('day');
        let currentDate = MOMENT().startOf('day');
        
        if (smsDate.isBefore(currentDate)) {
            self.L.log("Ignoring Fastag low balance record - SMS from previous date", billsKafkaRow);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SKIPPED', 'REASON:OLD_DATE', 'TYPE:FASTAG_LOW_BALANCE']);
            isDuplicate = true;
        } 
        else if (existingSmsTime && Number(newSmsTime) <= Number(existingSmsTime)) {
            self.L.log("Ignoring Fastag low balance record - older SMS than in db", billsKafkaRow);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SKIPPED', 'REASON:OLDER_SMS', 'TYPE:FASTAG_LOW_BALANCE']);
            isDuplicate = true;
        }

        return isDuplicate;
    }
    async upsertData(cb, billsKafkaRow) {
        const self = this;
        let updateFlag = false;
        let updateDueDateTable;
        // let insertDataInCassandra = true;
        let partialRecordFound = false;
        let dueGenFlag = 1; // 0 for gen case and 1 for due case
        let updateGenTable = false, updateDueTable = false, existingRecordDueTable = [], existingRecordGenTable = [], existingRecord = [];
        //let non_encrypted_recharge_number=billsKafkaRow.rechargeNumber;

        if(billsKafkaRow.service == 'mobile' || billsKafkaRow.service == 'dth'){
            try {
                if(billsKafkaRow.service == 'mobile'){
                    billsKafkaRow.rechargeNumber = self.cryptr.encrypt(billsKafkaRow.rechargeNumber);
                    billsKafkaRow.is_encrypted_done = true;
                }

                if( billsKafkaRow.service == 'mobile' && billsKafkaRow.isRealTimeDataExhausted){
                    billsKafkaRow.dueDate = null;
                    // billsKafkaRow.amount = 0;
                }

                let extra = _.get(billsKafkaRow, 'extra', null);
                
                if(!extra || extra == 'null'){
                    extra = {};
                }else if (typeof extra == 'string') extra = JSON.parse(extra);
                
                if (billsKafkaRow.isValaidationSync) {
                    extra.created_source = 'validationSync';
                    extra.updated_source = 'validationSync';
                    extra.updated_data_source = 'validationSync';
                } else {
                    extra.created_source = 'sms';
                    extra.updated_source = 'sms';
                    extra.updated_data_source = _.get(extra, 'updated_data_source', 'sms');
                }
                billsKafkaRow.extra = JSON.stringify(extra);

            } catch (error) {
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), error);
                self.L.error(`upsertData`, `Failed with error ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_DATA','TYPE:PARSING_ERROR_MOBILE_DTH',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`,`PARTIAL_BILL:${_.get(billsKafkaRow, 'partialBillState','NO_STATE')}`]);
                return cb();
            }
        }

        if (billsKafkaRow.dueDate && MOMENT(billsKafkaRow.dueDate).isValid() && MOMENT(billsKafkaRow.dueDate).diff(MOMENT().endOf('day'), 'days') < 0) {
            if (self.allowedServiceToSaveOldDueDate.includes(_.toLower(billsKafkaRow.service))) {
                _.set(billsKafkaRow, 'status', _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5))
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:TRAFFIC', 'TYPE:OLD_BILL_FOUND', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:SUCCESS']);
            } else {
                self.L.log("upsertData::  skipping inserting new record in DB as Old Due Date Bill Found", billsKafkaRow);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_OLD_BILL', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:OLD_DUE_DATE_IN_PAYLOAD']);
                return cb("skipping inserting new record in DB as Old Due Date Bill Found");
            }
        }

        if (self.allowedServiceForBillFetch && self.allowedServiceForBillFetch.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase())) {
            try {
                let pidMappingTableData = await self.nonPaytmBillsModel.readPidMappingData(billsKafkaRow);
                billsKafkaRow = await self.updateProductIdAndAmbiguous(billsKafkaRow, pidMappingTableData);
            }
            catch (er) {
                self.L.error(`upsertData:: Error while fetching pid mapping data`, er);
            }
        }

        await self.publishToUPMSRegistration(billsKafkaRow);

        try {
            existingRecord = await self.nonPaytmBillsModel.readBills(billsKafkaRow);
            if((_.get(billsKafkaRow,'dueDate',null) == null || _.get(billsKafkaRow,'amount',null) == null) && self.partialRecordAllowedServices.includes(_.get(billsKafkaRow,'service',"").toLowerCase())){
                partialRecordFound = true;
                billsKafkaRow.partialRecordFound = true;
            }
            if(billsKafkaRow.partialBillState && billsKafkaRow.service == 'mobile'){
                let percentage = Number(self.greyPercentage);
                self.L.info("upsertData: Grey config picked", percentage);
                let custId = Number(_.get(billsKafkaRow, "customerId", null));
                if(self.greyPercentage!=null && custId%100 >= percentage){
                    self.L.log("upsertData :", "Record is partial Bill and falling outside grey percentage and hence skipping", billsKafkaRow);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:OUTSIDE_GREY']);
                    return cb("skipping inserting partial data in DB as falling outside grey");
                }
            }

            if(existingRecord.length == 0 && billsKafkaRow.isDuplicateCANumberOperator && billsKafkaRow.alternateCAExistsInDB){
                self.L.log("upsertData::  skipping inserting new record in DB as alternate CA number record already exists", billsKafkaRow);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_DUPLICATE_CA_DATA', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, 'REASON:DUPLICATE_CA_NUMBER_RECORD_EXISTS']);
                return cb("skipping inserting new record in DB as alternate CA number record already exists");
            }

            if(existingRecord.length == 1){
                self.L.log("upsertData::  existing records found");
                if(_.get(billsKafkaRow,'is_active_expired_user',false)){
                    let dbExtra = _.get(existingRecord[0], 'extra', {});
                    if (typeof dbExtra === 'string' && dbExtra !== '') {
                        dbExtra = JSON.parse(dbExtra);
                    }
                    let dbNBFD = MOMENT(_.get(existingRecord[0], 'next_bill_fetch_date', null));

                    if(_.get(dbExtra,'is_active_expired_user',false) && dbNBFD.isValid() && dbNBFD.isAfter(MOMENT())){
                        return cb("smartFetchFlow :: skipping inserting as is_active_expired_user is set and NBFD is of future");
                    } else if (!_.get(existingRecord[0], 'notification_status', 0)) {
                        self.L.error(`upsertData :: skipping inserting as notification status is 0`);
                        return cb("skipping inserting or publishing to bill fetch topic as notification status is 0");
                    }
                }
                if (!_.get(billsKafkaRow, "isRealTimeDataExhausted", null) && !billsKafkaRow.partialBillState && (partialRecordFound == true && (!self.partialAllowedServicesComparison.includes(_.get(billsKafkaRow, 'service', null).toLowerCase())))) {
                    self.L.log("upsertData::  skipping inserting partial data in DB as record already exists", billsKafkaRow);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
                    return cb("skipping inserting partial data in DB as record already exists");
                }
                
                if(!_.get(billsKafkaRow, "isRealTimeDataExhausted", null) && _.get(billsKafkaRow, 'service', null) == 'mobile' && (_.get(billsKafkaRow, 'source', null) == 'SMS_PARSING_DWH_PREPAID'  || _.get(billsKafkaRow, 'source', null) == 'SMS_PARSING_REALTIME_PREPAID') && _.get(billsKafkaRow,'extra',null).includes('updated_data_source":"SMS_PARSING_DWH_MANUAL') ) {
                    self.L.log("upsertData:: going to check if due date is to updated or not");
                    let dbDueDate = _.get(existingRecord[0], 'due_date') ? MOMENT(_.get(existingRecord[0], 'due_date')).utc().startOf('day') : null;
                    let calculatedDate = MOMENT(Number(_.get(JSON.parse(billsKafkaRow.customerOtherInfo), 'smsDateTime', null))).utc().startOf('day');
                    calculatedDate.subtract(self.OPERATOR_DEFAULT_DIFF_DAYS[billsKafkaRow.operator], 'days');
                    if(dbDueDate && dbDueDate.isAfter(calculatedDate, 'day')) {
                    self.L.log("upsertData::  skipping inserting partial data in DB as record already exists", billsKafkaRow);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow, 'partialBillState','NO_STATE')}`]);
                    return cb("skipping inserting partial data in DB as record already exists");
                    }
                }

                _.set(billsKafkaRow,"remind_later_date",_.get(existingRecord[0],"remind_later_date",null));

                let existingDueDate = _.get(existingRecord[0], 'due_date') ? MOMENT(_.get(existingRecord[0], 'due_date')).utc().startOf('day') : null;
                let newDueDate = _.get(billsKafkaRow, 'dueDate') ? MOMENT(_.get(billsKafkaRow, 'dueDate')).utc().startOf('day') : null;
                newDueDate = newDueDate ? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days') : null;
                if(existingDueDate && newDueDate && newDueDate.isAfter(existingDueDate, 'day')) {
                    this.L.log("[nonPaytmBillsConsumer :: Setting remind later date as null because of new bill");
                    _.set(billsKafkaRow,"resetRemindLaterDate",true);
                }

                self.checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb);

                let state = _.get(billsKafkaRow, 'partialBillState');
                let isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);
                let previousPaytype = _.toLower(_.get(existingRecord[0], "paytype", null));
                if(_.get(billsKafkaRow, "isRealTimeDataExhausted", null) && previousPaytype == "postpaid"){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA_EXHAUST',  'REASON:POSTPAID_BILL_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
                    self.L.log('upsertData: Postpaid bill not updated by data exhaust bill', _.get(billsKafkaRow, "customerId", null),  _.get(billsKafkaRow, 'rechargeNumber', null));
                    return cb('postpaid bill not updated by data exhaust bill');
                }
                if(_.get(billsKafkaRow, "isRealTimeDataExhausted", null)){
                    billsKafkaRow = self.processExistingRecordForDataExhaust(billsKafkaRow, existingRecord[0]);
                } else {
                    billsKafkaRow =  self.processExistingRecord(billsKafkaRow, existingRecord[0]);
                }
                partialRecordFound = _.get(billsKafkaRow, "partialRecordFound", false);
                if(state && billsKafkaRow==null){
                    if(previousPaytype =="postpaid"){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA',  'REASON:POSTPAID_BILL_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
                        return cb('postpaid bill not updated by prepaid partial bill');

                    }
                    else{
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA',  'REASON:FULL_BILL_ENTRY_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
                        return cb('fresh db full bill entry found');
                    }
                }
                if(isRealTimeDataExhausted && billsKafkaRow == null){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:ERROR_OCCURED_IN_DATA_EXHAUST', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `IS_REALTIME_DATA_EXHAUSTED:${_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)}`]);
                    return cb('Error occured in data exhaust parsing');
                }
                if(billsKafkaRow==null){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SMS_DUE_DATE_LESS_THAN_EQUAL_DB_DUE_DATE', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`]);
                    return cb('smsDueDate <= DBDueDate');
                }
            }else if(_.get(billsKafkaRow,'source',null) == "reminderNonRuBillFetch" && _.get(billsKafkaRow, 'service', null) == 'financial services') {
                if(_.get(billsKafkaRow, 'dueDate', null)>self.nonPaytmBillsModel.getDate()) {
                    if(_.get(billsKafkaRow, 'amount', 0)< 0) {
                        _.set(billsKafkaRow, 'amount', 0);
                    }
                    console.log('sms no bill, bbps bill not expired');
                }
                else {
                    if(_.get(billsKafkaRow, 'amount', 0)< 0) {
                        _.set(billsKafkaRow, 'amount', 0);
                    }
                }
            }

            if(existingRecord.length == 1 && _.get(billsKafkaRow,'dueDate',null) != null) {
                dueGenFlag = 1;
                existingRecordDueTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord[0], dueGenFlag);
                if(existingRecordDueTable !=null && existingRecordDueTable.length>0) {
                    let existingDueDate = _.get(billsKafkaRow,'dueDate',null);
                    existingDueDate = JSON.stringify(existingDueDate);
                    let newDueDate = _.get(existingRecordDueTable[0],'due_date',null);
                    newDueDate = JSON.stringify(newDueDate);
                    if(existingRecordDueTable.length == 1 && (existingDueDate.substr(1,10)!=newDueDate.substr(1,10))) {
                        self.L.log("dueTable::  existing records found");
                        updateDueTable = true;
                    }
                    if(updateDueTable == true && existingDueDate.substr(6,2) != newDueDate.substr(6,2)) {
                        updateDueTable = false;
                        await self.nonPaytmBillsModel.processRecordWithDifferentMonthDue(existingRecord[0]);
                    }
                }
                
            }

            if(existingRecord.length == 1 && _.get(billsKafkaRow,'billDate',null) != null) {
                dueGenFlag = 0;
                existingRecordGenTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord[0], dueGenFlag);
                if(existingRecordGenTable !=null && existingRecordGenTable.length>0){
                    let existingBillDate = _.get(billsKafkaRow,'billDate',null);
                    existingBillDate = JSON.stringify(existingBillDate);
                    let newBillDate = _.get(existingRecordGenTable[0],'bill_date',null);
                    newBillDate = JSON.stringify(newBillDate);
                    if(existingRecordGenTable.length == 1 && (existingBillDate.substr(1,10)!=newBillDate.substr(1,10))) {
                        self.L.log("GenTable::  existing records found");
                        updateGenTable = true;
                    }
                    if(updateGenTable == true && existingBillDate.substr(6,2) != newBillDate.substr(6,2)) {
                        updateGenTable = false;
                        await self.nonPaytmBillsModel.processRecordWithDifferentMonthGen(existingRecord[0]);
                    }
                }
                
             }
            await self.nonPaytmBillsModel.processRecordsforOldBillCase(existingRecord.length > 0 ? existingRecord[0] : {}, billsKafkaRow);
            self.L.log(`upsertData:: customerId : ${billsKafkaRow.customerId}, partialRecordFound - ${partialRecordFound}`);
            try{
                let extra = _.get(billsKafkaRow, 'extra', {});
                if(typeof (extra) == 'string' && extra != '') {
                    extra = JSON.parse(extra);
                }
                if(_.get(billsKafkaRow,'is_active_expired_user',false) && _.get(billsKafkaRow,'latest_payment_date',null) && _.get(billsKafkaRow,'service_payment_date_list',null)){
                    _.set(extra,'is_active_expired_user',_.get(billsKafkaRow,'is_active_expired_user',null));
                    _.set(extra,'service_payment_date_list',_.get(billsKafkaRow,'service_payment_date_list',null));
                    _.set(extra,'latest_payment_date',_.get(billsKafkaRow,'latest_payment_date',null));
                }
                if(partialRecordFound == true) {
                    _.set(extra,'source_subtype_2', 'PARTIAL_BILL');
                }else{
                    _.set(extra,'source_subtype_2', 'FULL_BILL');
                }
                extra = JSON.stringify(extra);
                _.set(billsKafkaRow,'extra', extra);
            }catch(e){
                self.L.error(`upsertData:: parsing error`, ` error ${e}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:PARSING_ERROR_EXTRA', `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
            }


            if(partialRecordFound == true) {
                let customerOtherInfo = _.get(billsKafkaRow,"customerOtherInfo",{});
                if(typeof (customerOtherInfo) == 'string' && customerOtherInfo != '') {
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                }
                _.set(customerOtherInfo,'partialBillfound', true);
                customerOtherInfo = JSON.stringify(customerOtherInfo);
                _.set(billsKafkaRow,'customerOtherInfo', customerOtherInfo);
            }
        } catch(e) {
           // await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, partialRecordFound ? 'PARTIAL_BILL' : 'FULL_BILL', 'NON_RU'), e);
            self.L.error(`upsertData`, `Failed with error ${e}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:PARSING_ERROR_EXISTING_RECORD', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`,`DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
            return cb(e);
        }

        self.nonPaytmBillsModel.readRecentBills(billsKafkaRow)
        .then((recentRecords) => {
            if (billsKafkaRow.rejectingCurrentPayload) {
                let err = `record already present by sms, do not update by validation sync`;
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, partialRecordFound ? 'PARTIAL_BILL' : 'FULL_BILL', 'NON_RU'), err,cb,false);
                
                //return cb();
            }
        
            let isUpdateAll = false;
            if(self.allowedServiceForUpdateAll && self.allowedServiceForUpdateAll.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase())) {
                isUpdateAll = true;
            }
            if(self.blockedServiceForUpdateAll && self.blockedServiceForUpdateAll.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase())) {
                isUpdateAll = false;
                self.L.log(`upsertData:: allowedServiceForUpdateAll, isUpdateAll : ${isUpdateAll}`);
            }
            if (self.blockedOperatorForUpdateAll && self.blockedOperatorForUpdateAll.length > 0 && self.blockedOperatorForUpdateAll.includes(_.get(billsKafkaRow, 'operator', 'NO_OPERATOR').toLowerCase())) {
                isUpdateAll = false;
                self.L.log(`upsertData:: blockedOperatorForUpdateAll, isUpdateAll : ${isUpdateAll} for debugKey:: ${_.get(billsKafkaRow, 'debugKey', 'NO_DEBUG_KEY')}`);
            }
            self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, recentRecords, updateDueTable, updateGenTable, existingRecordDueTable, existingRecordGenTable,self.cassandraCdcPublisher, isUpdateAll, existingRecord).then((data)=>{
                return cb();
                
            })
                .catch(error => {
                    let err = `Failed with error ${error}`;
                    self.L.error(`upsertData`, `Failed with error ${error}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPSERT_DATA', 'TYPE:DB_EXCEPTION', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
                    return cb(error);
                    //return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
                })
        })
        .catch(error => {
            self.L.error(`upsertData`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPSERT_DATA','TYPE:UPSERT_FAILED',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
            return cb(error)
        })
    }

    isPartialRecord(billsKafkaRow){
        let self = this;
        if((_.get(billsKafkaRow,'dueDate',null) == null || _.get(billsKafkaRow,'amount',null) == null) && self.partialRecordAllowedServices.includes(_.get(billsKafkaRow,'service',"").toLowerCase())){
            billsKafkaRow.partialRecordFound = true;
            try{
                let customerOtherInfo = _.get(billsKafkaRow,"customerOtherInfo",{});
                if(typeof (customerOtherInfo) == 'string' && customerOtherInfo != '') {
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                }
                _.set(customerOtherInfo,'partialBillfound', true);
                customerOtherInfo = JSON.stringify(customerOtherInfo);
                _.set(billsKafkaRow,'customerOtherInfo', customerOtherInfo);
            }catch(error){
                self.L.error(`isPartialRecord`, `Failed with error ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:IS_PARTIAL_RECORD_FAILED', 'SOURCE:IS_PARTIAL_RECORD', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
                return [false, billsKafkaRow];
            }
            return [true, billsKafkaRow];
        }
        return [false, billsKafkaRow];
    }

    isPartialRecordAllowed(billsKafkaRow, partialRecordFound){
        let self = this;
        if(!_.get(billsKafkaRow, "isRealTimeDataExhausted", null) && !billsKafkaRow.partialBillState && (partialRecordFound == true && (!self.partialAllowedServicesComparison.includes(_.get(billsKafkaRow, 'service', null).toLowerCase())))) {
            self.L.log("upsertData::  skipping inserting partial data in DB as record already exists", billsKafkaRow);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
            throw new Error("skipping inserting partial data in DB as record already exists");
        }else{
            return true;
        }
    }

    setReminderLaterDate(existingRecord, billsKafkaRow){
        let self = this;
        try{
            _.set(billsKafkaRow,"remind_later_date",_.get(existingRecord,"remind_later_date",null));

            let existingDueDate = _.get(existingRecord, 'due_date') ? MOMENT(_.get(existingRecord, 'due_date')).utc().startOf('day') : null;
            let newDueDate = _.get(billsKafkaRow, 'dueDate') ? MOMENT(_.get(billsKafkaRow, 'dueDate')).utc().startOf('day') : null;
            newDueDate = newDueDate ? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days') : null;
            if(existingDueDate && newDueDate && newDueDate.isAfter(existingDueDate, 'day')) {
                this.L.log("[nonPaytmBillsConsumer :: Setting remind later date as null because of new bill");
                _.set(billsKafkaRow,"resetRemindLaterDate",true);
            }
            return;
        }catch(error){
            self.L.error(`setReminderLaterDate`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SET_REMIND_LATER_DATE_FAILED', 'SOURCE:SET_REMIND_LATER_DATE', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
            return;
        }
    }

    checkForPrepaidDataExhaust(billsKafkaRow, existingRecord){
        let self = this;
        let isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);
        let previousPaytype = _.toLower(_.get(existingRecord, "paytype", null));
        if(isRealTimeDataExhausted && previousPaytype == "postpaid"){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA_EXHAUST',  'REASON:POSTPAID_BILL_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
            self.L.log('upsertData: Postpaid bill not updated by data exhaust bill', _.get(billsKafkaRow, "customerId", null),  _.get(billsKafkaRow, 'rechargeNumber', null));
            throw new Error("postpaid bill not updated by data exhaust bill");
        }else{
            return;
        }
    }

    validations(billsKafkaRow, existingRecord){
        let self = this;
        try{
            let state = _.get(billsKafkaRow, 'partialBillState'),
                partialRecordFound = _.get(billsKafkaRow, "partialRecordFound", false),
                isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false),
                previousPaytype = _.toLower(_.get(existingRecord, "paytype", null));

                if(state && billsKafkaRow==null){
                    if(previousPaytype =="postpaid"){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA',  'REASON:POSTPAID_BILL_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
                        throw new Error('postpaid bill not updated by prepaid partial bill');

                    }
                    else{
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA',  'REASON:FULL_BILL_ENTRY_FOUND', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
                        throw new Error('fresh db full bill entry found');
                    }
                }
                if(isRealTimeDataExhausted && billsKafkaRow == null){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:ERROR_OCCURED_IN_DATA_EXHAUST', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `IS_REALTIME_DATA_EXHAUSTED:${_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)}`]);
                    throw new Error('Error occured in data exhaust parsing');
                }
                if(billsKafkaRow==null){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SMS_DUE_DATE_LESS_THAN_EQUAL_DB_DUE_DATE', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`]);
                    throw new Error('smsDueDate <= DBDueDate');
                }
        }catch(error){
            throw error;
        }
    }

    async processForDueDateMappingTable(billsKafkaRow, existingRecord, dueGenFlag, existingRecordDueTable, updateDueTable){
        let self = this;
        try{
            if(_.get(billsKafkaRow,'dueDate',null) != null) {
                dueGenFlag = 1;
                existingRecordDueTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord, dueGenFlag);
                if(existingRecordDueTable !=null && existingRecordDueTable.length>0) {
                    let existingDueDate = _.get(billsKafkaRow,'dueDate',null);
                    existingDueDate = JSON.stringify(existingDueDate);
                    let newDueDate = _.get(existingRecordDueTable[0],'due_date',null);
                    newDueDate = JSON.stringify(newDueDate);
                    if(existingRecordDueTable.length == 1 && (existingDueDate.substr(1,10)!=newDueDate.substr(1,10))) {
                        self.L.log("dueTable::  existing records found");
                        updateDueTable = true;
                    }
                    if(updateDueTable == true && existingDueDate.substr(6,2) != newDueDate.substr(6,2)) {
                        updateDueTable = false;
                        await self.nonPaytmBillsModel.processRecordWithDifferentMonthDue(existingRecord);
                    }
                }
            }else{
                self.L.log("dueTable::  no existing records found");
            }
            return [updateDueTable, existingRecordDueTable];
        }catch(error){
            throw error;
        }
    }

    async processForBillGenMappingTable(billsKafkaRow, existingRecord, dueGenFlag, existingRecordGenTable, updateGenTable){
        let self = this;
        try{
            if(_.get(billsKafkaRow,'billDate',null) != null) {
                dueGenFlag = 0;
                existingRecordGenTable = await self.nonPaytmBillsModel.readGenAndDueBills(existingRecord, dueGenFlag);
                if(existingRecordGenTable !=null && existingRecordGenTable.length>0){
                    let existingBillDate = _.get(billsKafkaRow,'billDate',null);
                    existingBillDate = JSON.stringify(existingBillDate);
                    let newBillDate = _.get(existingRecordGenTable[0],'bill_date',null);
                    newBillDate = JSON.stringify(newBillDate);
                    if(existingRecordGenTable.length == 1 && (existingBillDate.substr(1,10)!=newBillDate.substr(1,10))) {
                        self.L.log("GenTable::  existing records found");
                        updateGenTable = true;
                    }
                    if(updateGenTable == true && existingBillDate.substr(6,2) != newBillDate.substr(6,2)) {
                        updateGenTable = false;
                        await self.nonPaytmBillsModel.processRecordWithDifferentMonthGen(existingRecord[0]);
                    }
                }
                
            }else{
                self.L.log("GenTable::  no existing records found");
            }

            return [updateGenTable, existingRecordGenTable];
        }catch(error){
            throw error;
        }
    }

    processingForPartialBillFound(billsKafkaRow, partialRecordFound){
        let self = this;
        try{
            let extra = _.get(billsKafkaRow, 'extra', {});
            if(typeof (extra) == 'string' && extra != '') {
                extra = JSON.parse(extra);
            }
            if(_.get(billsKafkaRow,'is_active_expired_user',false) && _.get(billsKafkaRow,'latest_payment_date',null) && _.get(billsKafkaRow,'service_payment_date_list',null)){
                _.set(extra,'is_active_expired_user',_.get(billsKafkaRow,'is_active_expired_user',null));
                _.set(extra,'service_payment_date_list',_.get(billsKafkaRow,'service_payment_date_list',null));
                _.set(extra,'latest_payment_date',_.get(billsKafkaRow,'latest_payment_date',null));
            }
            if(partialRecordFound == true) {
                _.set(extra,'source_subtype_2', 'PARTIAL_BILL');
            }else{
                _.set(extra,'source_subtype_2', 'FULL_BILL');
            }
            extra = JSON.stringify(extra);
            _.set(billsKafkaRow,'extra', extra);
        }catch(e){
            self.L.error(`upsertData:: parsing error`, ` error ${e}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:PARSING_ERROR_EXTRA', `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
        }

        try{
            if(partialRecordFound == true) {
                let customerOtherInfo = _.get(billsKafkaRow,"customerOtherInfo",{});
                if(typeof (customerOtherInfo) == 'string' && customerOtherInfo != '') {
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                }
                _.set(customerOtherInfo,'partialBillfound', true);
                customerOtherInfo = JSON.stringify(customerOtherInfo);
                _.set(billsKafkaRow,'customerOtherInfo', customerOtherInfo);
            }
        }catch(error){
            self.L.error(`processingForPartialBillFound`, ` error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:PARSING_ERROR_CUSTOMER_OTHER_INFO', `PARTIAL_BILL:${_.get(billsKafkaRow,'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(billsKafkaRow, "isRealTimeDataExhausted", false)}`]);
        }
        return billsKafkaRow;
    }

    async processUpdateOnEachRecord(existingRecord, billsKafkaRow, billsKafkaRowCloneForDataExhaust){
        const self = this;
        let recordForAnalytics = _.cloneDeep(billsKafkaRow);
        let partialRecordFound = false;
        try{
            let dueGenFlag = 1; // 0 for gen case and 1 for due case
            let updateGenTable = false, updateDueTable = false, existingRecordDueTable = [], existingRecordGenTable = [];

            [partialRecordFound, billsKafkaRow] = self.isPartialRecord(billsKafkaRow);
            
            self.isPartialRecordAllowed(billsKafkaRow, partialRecordFound);

            self.setReminderLaterDate(existingRecord, billsKafkaRow);

            self.checkForPrepaidDataExhaust(billsKafkaRow, existingRecord);
                
            if(_.get(billsKafkaRow, "isRealTimeDataExhausted", null)){
                billsKafkaRow = self.processExistingRecordForDataExhaust(billsKafkaRow, existingRecord);
            } else if(!_.get(existingRecord, "dummy_record", null)){
                billsKafkaRow =  self.processExistingRecord(billsKafkaRow, existingRecord);
            } else if(_.get(existingRecord, "dummy_record", null)){
                self.setCustomerIdInCustomerOtherInfo(billsKafkaRow, existingRecord);
            }
            self.validations(billsKafkaRow, existingRecord);

            [updateDueTable, existingRecordDueTable] = await self.processForDueDateMappingTable(billsKafkaRow, existingRecord, dueGenFlag, existingRecordDueTable, updateDueTable);
            [updateGenTable, existingRecordGenTable] = await self.processForBillGenMappingTable(billsKafkaRow, existingRecord, dueGenFlag, existingRecordGenTable, updateGenTable);


            billsKafkaRow = self.processingForPartialBillFound(billsKafkaRow, partialRecordFound);


            if(billsKafkaRow.rejectingCurrentPayload){
                let err = `record already present by sms, do not update by validation sync`;
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(recordForAnalytics, partialRecordFound ? 'PARTIAL_BILL' : 'FULL_BILL', 'NON_RU'), err,null,false);
            }


            let recentRecords = await self.nonPaytmBillsModel.readRecentBills(billsKafkaRow);

            await self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, recentRecords, updateDueTable, updateGenTable, existingRecordDueTable, existingRecordGenTable,self.cassandraCdcPublisher, false, existingRecord)


            await self.publishNotificationAndCtEvents(billsKafkaRow, billsKafkaRowCloneForDataExhaust);

        }catch(error){
            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(recordForAnalytics, partialRecordFound ? 'PARTIAL_BILL' : 'FULL_BILL', 'NON_RU'), error,null,false);
        }
    }

    async handleRealTimeDataExhausted(billsKafkaRow, billsKafkaRowCloneForDataExhaust){
        const self = this;
        return new Promise(function(resolve, reject){
            try{
                self.publishInBillFetchKafkaForDataExhaust(function(err){
                    if(err){
                        self.L.error("Error while publishing publishInBillFetchKafkaForDataExhaust with error :", err);
                        return reject(err);
                    }
                    return resolve(null);

                },billsKafkaRow, billsKafkaRowCloneForDataExhaust)
            }catch(error){
                self.L.error("Error while handling real time data exhausted with error :", error);
                return reject(error);
            }
        });
    }

    async handleOtherMobileEvents(billsKafkaRow) {
        const self = this;
        try {
            await self.fetchAndSetTemplates(billsKafkaRow);
            await self.handleValidationSyncMetrics(billsKafkaRow);
            await self.handleBillFetchKafkaPublishing(billsKafkaRow);
            await self.handleCtAndPfccEventsPublishing(billsKafkaRow);
        } catch (error) {
            throw error;
        }
    }
    
    async fetchAndSetTemplates(billsKafkaRow) {
        const self = this;
        const templates = await new Promise((resolve, reject) => {
            self.fetchDataTemplates((err, result) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:NON_PAYTM_BILLS',
                        'STATUS:NOTIFICATION_TEMPLATE_NOT_FOUND',
                        `OPERATOR:${billsKafkaRow.operator}`,
                        `SERVICE:${_.get(billsKafkaRow, 'service', 'NO_SERVICE')}`,
                        `SOURCE:${_.get(billsKafkaRow, 'source', 'NO_SOURCE')}`
                    ]);
                    self.L.error("fetchDataTemplates:: error in fetching templates ", err);
                }
                resolve(result);
            }, billsKafkaRow);
        });
    
        _.set(billsKafkaRow, 'templates', templates);
    }
    
    async handleValidationSyncMetrics(billsKafkaRow) {
        if (billsKafkaRow.isValaidationSync && billsKafkaRow.isValidityExpired) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE: VALIDATION_SYNC_NOTIFICATION_TOPIC",
                `SERVICE:${_.get(billsKafkaRow, ['service'], null)}`,
                'STATUS:COUNT',
                'TYPE:KAFKA_PUBLISH',
                `OPERATOR:${_.get(billsKafkaRow, ['operator'], null)}`,
                `PARTIAL_BILL:${_.get(billsKafkaRow, ["partialBillState"], null)}`,
                `IS_VALIDITY_EXPIRED:${_.get(billsKafkaRow, ["isValidityExpired"], false)}`
            ]);
        }
    }
    
    async handleBillFetchKafkaPublishing(billsKafkaRow) {
        const self = this;
        const shouldSkipPublishing = 
            (billsKafkaRow.isValaidationSync && !billsKafkaRow.isValidityExpired) || 
            _.get(billsKafkaRow, 'toBeNotified', true) === false;
    
        if (!shouldSkipPublishing) {
            const billFetchKafkaPayload = self.formatBillFetchKafkaPayload(billsKafkaRow);
            if (billFetchKafkaPayload) {
                await new Promise((resolve) => {
                    self.publishInBillFetchKafka((err) => {
                        if (err) {
                            self.L.error("Error while publishing publishBillFetchKafka with error:", err);
                        }
                        resolve(null);
                    }, billFetchKafkaPayload);
                });
            }
        }
    }
    
    async handleCtAndPfccEventsPublishing(billsKafkaRow) {
        const self = this;
        if (!billsKafkaRow.isValaidationSync) {
            await new Promise((resolve) => {
                self.publishCtAndPFCCEvents((err) => {
                    if (err) {
                        self.L.error("Error while publishing publishCtAndPFCCEvents with error:", err);
                    }
                    resolve(null);
                }, billsKafkaRow);
            });
        }
    }
    
    async publishNotificationAndCtEvents(billsKafkaRow, billsKafkaRowCloneForDataExhaust){
        const self = this;
        try{

            let isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);
            if(isRealTimeDataExhausted){
                return self.handleRealTimeDataExhausted(billsKafkaRow, billsKafkaRowCloneForDataExhaust);
            }else{
                return self.handleOtherMobileEvents(billsKafkaRow);
            }   
        }catch(error){
            throw error;
        }
    }  
    

    /*
    CREATE TABLE reminder.bills_non_paytm ( recharge_number text,
     customer_id int, service text, operator text, amount int, 
     bank_name text, bill_date text, bill_fetch_date text, card_network text, 
     circle text, create_at timestamp, customer_email text, customer_mobile text, 
     customer_other_info text, due_amount double, due_date text, enc_amount text, enc_due_date text, 
     expiry_date_start text, extra text, is_encrypted int, next_bill_fetch_date text,
     next_bill_fetch_start_date text, notification_status int, old_bill_fetch_date text, 
     payment_date text, paytype text, product_id int, 
     published_date text, remind_later_date timestamp, 
     retry int, status int, update_at timestamp, user_data text, 
      PRIMARY KEY (recharge_number, customer_id, service, operator) ) WITH CLUSTERING ORDER BY (customer_id ASC, service ASC, operator ASC)
    */
    makeExistingRecords(cust_id, billsKafkaRow, extra = null){
        const self = this;
        return {
            customer_id: cust_id,
            recharge_number: billsKafkaRow.rechargeNumber,
            operator: billsKafkaRow.operator,
            service: billsKafkaRow.service,
            paytype: billsKafkaRow.paytype,
            product_id: billsKafkaRow.productId,
            notification_status: 1,
            extra: extra,
            dummy_record: true
        }
    }

    async getTotalRecordsToProcess(billsKafkaRow){
        const self = this;
        try{
            let smsParsedCustId = null,
                isSmsParsedCustIdPresentInSql = false,
                existingRecords = [],
                listOfExistingCustIds = [],
                listOfTotalProcessingRecords = [],
                listOfAirtelPrepaidCustIds = [],
                existingRecordsForAirtelPrepaid = [];

            billsKafkaRow = await self.preProcessData(billsKafkaRow);
            isSmsParsedCustIdPresentInSql = _.get(billsKafkaRow, 'isSmsParsedCustIdPresentInSql', false);
            smsParsedCustId = _.get(billsKafkaRow, 'customerId', null);
            

            /**
             * processedRecords -> sms parsed cust_id -> cust_id1
             * list_of_cust_ids -> [cust_id2, cust_id3, cust_id4]
             * 
             * existingRecords -> cust_id5, cust_id6, cust_id7, cust_id8
             * 
             * 
             * Existing records -> cust_id5, cust_id6, cust_id7, cust_id8
             * new creations -> cust_id2, cust_id3, cust_id4
             * 
             * nothing to be done on cust_id1 -> already present in sql
             * 
             */

            existingRecords = await self.nonPaytmBillsModel.readBillsWithoutCustId(billsKafkaRow);
            listOfExistingCustIds = existingRecords.map(record => record.customer_id);
            listOfAirtelPrepaidCustIds = _.get(billsKafkaRow, ['airtelPrepaidPublisherDetails', 'list_of_other_customer_ids'], []);

            if(listOfExistingCustIds.length == 0 && listOfAirtelPrepaidCustIds.length == 0 && isSmsParsedCustIdPresentInSql){
                self.L.log("No existing records found, returning empty array");
                return [];
            }

            listOfAirtelPrepaidCustIds = listOfAirtelPrepaidCustIds.filter(cust_id => 
                !listOfExistingCustIds.includes(cust_id)
            );

            let extraForAirtelPrepaid = JSON.stringify({
                "created_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED",
                "updated_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED",
                "updated_data_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED"
            });
            for(let cust_id of listOfAirtelPrepaidCustIds){
                existingRecordsForAirtelPrepaid.push(self.makeExistingRecords(cust_id, billsKafkaRow, extraForAirtelPrepaid));
            }

            listOfTotalProcessingRecords = [...existingRecords, ...existingRecordsForAirtelPrepaid];

            if(!isSmsParsedCustIdPresentInSql && !listOfExistingCustIds.includes(smsParsedCustId) && !listOfAirtelPrepaidCustIds.includes(smsParsedCustId)){
                listOfTotalProcessingRecords.push(self.makeExistingRecords(smsParsedCustId, billsKafkaRow));
            }

            return listOfTotalProcessingRecords;
        }catch(error){
            self.L.error("Error while getting total records to process with error :", error);
            throw error;
        }
    }

    async updateMultipleRecordsWithSameRN(cb, billsKafkaRow, billsKafkaRowCloneForDataExhaust) {
        const self = this;
        try{
            let listOfTotalProcessingRecords = await self.getTotalRecordsToProcess(billsKafkaRow);

            ASYNC.forEachLimit(listOfTotalProcessingRecords, 1, async (record, callback) => {
                try {
                    await self.processUpdateOnEachRecord(record, {...billsKafkaRow, customerId: record.customer_id}, {...billsKafkaRowCloneForDataExhaust, customerId: record.customer_id});
                    callback();
                } catch (error) {
                    self.L.error("Error while updating multiple records with same RN with error :", error);
                    callback();
                }
            }, (err) => {
                if (err) {
                    return cb(err);
                }
                return cb();
            });
        }catch(error){
            self.L.error("Error while updating multiple records with same RN with error :", error);
            return cb(null);
        }
    }

    async mergeData(cb, billsKafkaRow) {
        const self = this;

        ASYNC.waterfall([
            next => {
                self.nonPaytmBillsModel.checkInRecentAndDelete(billsKafkaRow, self.cassandraCdcPublisher, (err)=>{
                    if(err){
                        self.L.error(`upsertData`, `Failed with error ${err}`);
                        return next(null);
                    }else{
                        return next(null);
                    }
                })
            },
            (next) => {
                self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow,null,null,null,null,null,self.cassandraCdcPublisher).then((data)=>{
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INSERT','SOURCE:MERGE_DATA']);
                    return next(null);
                })
                .catch(error =>{
                    
                    self.L.error(`mergeData`, `Failed with error ${error}`);
                    //return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), error,next);
                    return next(error);
                    
                })
            }
        ], (err) => {
                if(err){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:MERGE_DATA','TYPE:DB_EXCEPTION']);
                    self.L.error('mergeData :: Error while updating Data in DB' , err);
                }
            return cb(err)
        })

    }

    async deleteData(cb, billsKafkaRow) {
        let payloadForProcessing = [billsKafkaRow], self = this;
        if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', billsKafkaRow.operator, 'DEMERGER_MAPPED_OPERATOR'], null)) {
            let clonedData = _.cloneDeep(billsKafkaRow);
            clonedData.operator = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', billsKafkaRow.operator, 'DEMERGER_MAPPED_OPERATOR'], null);
            payloadForProcessing.push(clonedData);
        }
        ASYNC.eachSeries(payloadForProcessing, async (payload, callback) => {
            self.deleteDataExtended((err, data) => {
                if (err) {
                    self.L.error('deleteDataExtended :: Error while deleting Data in DB', err);
                } else {
                    self.L.log('deleteDataExtended :: Data deleted successfully from DB');
                }
                callback(); // Notify that this iteration is complete
            }, payload);
        }, (err) => {
            if (err) {
                self.L.error('deleteDataExtended :: Error in processing payloads', err);
            }
            cb(err); // Call the final callback after all iterations are complete
        });
    }


    async deleteDataExtended(cb, billsKafkaRow) {
        const self = this;

        if(billsKafkaRow.service == 'mobile' && !billsKafkaRow.is_encrypted_done){
            billsKafkaRow.rechargeNumber = this.cryptr.encrypt((billsKafkaRow.rechargeNumber).toString());
            billsKafkaRow.is_encrypted_done = true;
        }
        else if(billsKafkaRow.service == 'rent payment'){
            billsKafkaRow.rechargeNumber = _.get(billsKafkaRow, 'customerId')+'_dummy';
        }

        try {
            self.nonPaytmBillsModel.readBills(billsKafkaRow)
            .then(async existingRecord => {
                if(existingRecord.length == 1){
                    self.L.log("updateNonPaytmRecords::  existing records found");
                    billsKafkaRow.nextBillFetchDate = _.get(existingRecord[0],'next_bill_fetch_date',null);
                    billsKafkaRow.bucketId = await self.generateBucketNameUsingHash(billsKafkaRow,existingRecord[0]);;
                }
                return Promise.resolve()
            })
            .then(()=> {
                self.nonPaytmBillsModel.deleteUsingRecentRecords(billsKafkaRow , self.cassandraCdcPublisher, data=>{
                    return cb();
                })
            })
            .catch(error =>{
                self.L.error(`upsertData`, `Failed with error ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:DELETE_DATA','TYPE:DB_EXCEPTION']);
                return cb(error);
            })
        } catch(error) {
            self.L.error(`upsertData`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:DELETE_DATA']);
            return cb(error);
        }
    }


    async findAndUpdateData(cb, billsKafkaRow){

        const self = this;
        self.nonPaytmBillsModel.readBills(billsKafkaRow)
        .then(async existingRecord => {
            if(existingRecord.length == 1)
            {
                let error = self.validateExistingRecord(billsKafkaRow,existingRecord[0]);
                if(error){
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,self.getBillStatus(billsKafkaRow), 'NON_RU'), error);
                    return Promise.reject()

                } 
                else{
                    let processedRecord = self.getDataToUpdate(billsKafkaRow, existingRecord[0]);
                    if(processedRecord){
                        self.nonPaytmBillsModel.updateAmountOfNonPaytmRecords(function(err, data){
                            if (err) {
                                self.logger.error(`Error while executing: ${err} of record`, billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                                return cb(err);
                                //return   self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), err,cb);
                            }
                            else{
                                let dataToSendCT= self.formatCTData(processedRecord,billsKafkaRow);
                                return cb(null, dataToSendCT);
                            } 
                        },processedRecord,existingRecord[0], self.cassandraCdcPublisher);
                    }
                    else{
                        return cb('Could not format data to update data on DB');
                    } 
                }
            }
            else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:NO_MATCHING_RECORD','SOURCE:FINDANDUPDATE_DATA']);
                return cb('No/multiple matching records found');
            }
        })
        .catch(error => {
            let err;
            if(error){
            self.L.error(`findAndUpdateData`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:FINDANDUPDATE_DATA']);
            err =error;
            }
            else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SKIPPING_MCN','SOURCE:FINDANDUPDATE_DATA']);
                self.L.log(`findAndUpdateData`, `Ignored the record due to either no/multiple records found or failed validation with debug key ${billsKafkaRow.loggingDebugKey}`);
                err = `findAndUpdateData : Ignored the record due to either no/multiple records found or failed validation`;
            }
            return cb(err);
        })
    }

    async findAndCreateData(cb, billsKafkaRow){
        const self = this;
        self.nonPaytmBillsModel.readBills(billsKafkaRow)
        .then(existingRecord => {
            if(existingRecord.length == 1)
            {
                return cb('data is present in the DB');
            }
            else{
                billsKafkaRow = self.formatDataToUpdate(billsKafkaRow);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:NO_MATCHING_RECORD','SOURCE:FINDANDCREATE_DATA']);
                self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, null, null, null, null,null, self.cassandraCdcPublisher).then((data) => {
                    return cb(null, billsKafkaRow);
                })
                    .catch(error => {
                        self.L.error(`findAndCreateData`, `Failed with error ${error}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:FINDANDCREATE_DATA', 'TYPE:DB_EXCEPTION']);
                        return cb(error);
                        //return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow, self.getBillStatus(billsKafkaRow), 'NON_RU'), error,cb);
                        
                    })
            }
        })
        .catch(error => {
            let err;
            if(error){
            self.L.error(`findAndCreateData`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:FINDANDCREATE_DATA']);
            err =error;
            }
            return cb(err);
        })
    }

    formatCTData(dbRecord, kafkaRecord){
        let ctPayload = {
            customerId : _.get(dbRecord, 'customer_id', null),
            rechargeNumber : _.get(dbRecord, 'recharge_number', null),
            productId : _.get(dbRecord, 'product_id', null),
            operator : _.get(dbRecord, 'operator', null),
            amount : _.get(dbRecord, 'amount', null),                // totalAmt
            dueDate : _.get(dbRecord, 'due_date', null),
            billDate : _.get(dbRecord, 'bill_date', null),    
            billFetchDate : _.get(dbRecord, 'bill_fetch_date', null),
            customerOtherInfo : _.get(dbRecord, 'customer_other_info', null),                     
            paytype : _.get(dbRecord, 'paytype', null),
            service : _.get(dbRecord, 'service', null),
            customerMobile : _.get(dbRecord, 'customer_mobile', null),                                                 // Will fill with Auth API otherwise it can be filled in notification create service
            customerEmail : _.get(dbRecord, 'customer_email', null),                                                  // Will fill with Auth API otherwise it can be filled in notification create service
            notificationStatus : _.get(dbRecord, 'notification_status', null),
            bankName : _.get(dbRecord, 'bank_name', null),
            cardNetwork: _.get(dbRecord, 'card_network', null),
            status : _.get(dbRecord, 'status', null),
            dbEvent :  _.get(kafkaRecord, 'dbEvent', null),
            debugKey : _.get(kafkaRecord, 'debugKey', null),
            extra :  _.get(dbRecord, 'extra', null),
            nextBillFetchDate : _.get(dbRecord,'nextBillFetchDate',null),
            nextBillFetchStartDate : _.get(dbRecord,'nextBillFetchStartDate',null),
        }
        return ctPayload;
    }

    validateExistingRecord(billsKafkaRow, existingRecord){
        const self = this;
        try{
            const graceDays = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_DAYS'], 5)
            const graceAmount = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_AMOUNT'], 5)
            let extra; 
            if(typeof _.get(existingRecord, 'extra', null) == 'string')   
            {
                extra = JSON.parse(_.get(existingRecord, 'extra', null));
            }
            let  customerOtherInfo;
            if(typeof _.get(existingRecord, 'customer_other_info', null) == 'string')
            {
                customerOtherInfo = JSON.parse(_.get(existingRecord, 'customer_other_info', null));
            }
            const paidAmount = _.get(extra, 'last_paid_amount', null) ? Math.round(extra.last_paid_amount) : null;
            let smsAmount = Math.round(_.get(billsKafkaRow, 'amount', 0));
            let dbpaymentDate = _.get(existingRecord, 'payment_date', null);
             dbpaymentDate = dbpaymentDate? MOMENT(dbpaymentDate).utc() : null;
            let dbDueDate = _.get(existingRecord, 'due_date', null) ? MOMENT(_.get(existingRecord, 'due_date', 0)).utc() : null;
            let paymentDate = _.get(JSON.parse(billsKafkaRow.customerOtherInfo), 'paymentDate', null);
             paymentDate = paymentDate? MOMENT(paymentDate).utc() : null;
            let billFetchDate = _.get(existingRecord, 'bill_fetch_date', null);

            if((billFetchDate && MOMENT(paymentDate).utc().diff(MOMENT(billFetchDate).utc()) < 0) ||
            (dbpaymentDate && MOMENT(paymentDate).utc().diff(MOMENT(dbpaymentDate).utc()) < 0)){
                self.logger.critical("Ignore record :: Old payment event", billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return ("Old payment event received");
            }
            
        
            if( dbDueDate && paymentDate.diff(dbDueDate, 'days') - 30 < 0){
                if(paidAmount==null || dbpaymentDate == null || (dbpaymentDate && paymentDate.diff(dbpaymentDate, 'days') > graceDays)){
                    return null;
                }
                if((paymentDate.diff(dbpaymentDate, 'days') < graceDays) && (Math.abs(paidAmount - smsAmount) > graceAmount)){
                    return null;
                }
                self.logger.critical("Ignore record :: Bill already paid in last x days or Bill has not generated yet", billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return ("Bill already paid in last x days or Bill has not generated yet");
            } 
            else
            {
                self.logger.critical("Ignore record :: Bill already paid in last x days or Bill has not generated yet", billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return ("Bill already paid in last x days or Bill has not generated yet");
            }

        }
        catch(error){
            self.logger.error("Ignore record ::Error for record", billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return ("Error in parsing data");
        }
    }

    formatDataToUpdate(billsKafkaRow){
        let self = this;
        //let updatedDbRecord = _.clone(existingRecord), dateFormat = 'YYYY-MM-DD HH:mm:ss';
        try{
            if(typeof _.get(billsKafkaRow, 'customerOtherInfo', null) == 'string'){
                billsKafkaRow.customerOtherInfo = JSON.parse(_.get(billsKafkaRow, 'customerOtherInfo', null));
            }
            if(typeof _.get(billsKafkaRow, 'extra', null) == 'string'){
                billsKafkaRow.extra = JSON.parse(_.get(billsKafkaRow, 'extra', null));
            }
            if(typeof  _.get(billsKafkaRow, 'extra', null) == 'string' || _.get(billsKafkaRow, 'extra', null)==null)
            {
                billsKafkaRow.extra = {};  // This is required because some enteries have more than once stringfied extra, now reseting it as no one is using extra column otherthan smsParsingBillPayment
            }
        }
        catch(error){
            self.L.error("Ignore record ::Error for record", JSON.stringify(billsKafkaRow), error);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return null;
        }
        billsKafkaRow.extra.last_paid_amount = Math.abs(billsKafkaRow.amount);
        billsKafkaRow.extra = JSON.stringify(_.get(billsKafkaRow, 'extra', {} ));
        billsKafkaRow.customerOtherInfo = JSON.stringify(_.get(billsKafkaRow, 'customerOtherInfo', null));
        return billsKafkaRow;
    }

    getDataToUpdate(billsKafkaRow, existingRecord){
        let self = this;
        let updatedDbRecord = _.clone(existingRecord), dateFormat = 'YYYY-MM-DD HH:mm:ss';
        try {
            if (typeof _.get(billsKafkaRow, 'extra', null) == 'string') {
                billsKafkaRow.extra = JSON.parse(_.get(billsKafkaRow, 'extra', null));
            }

            if(typeof _.get(billsKafkaRow, 'customerOtherInfo', null) == 'string'){
                billsKafkaRow.customerOtherInfo = JSON.parse(_.get(billsKafkaRow, 'customerOtherInfo', null));
            }
            if(typeof _.get(updatedDbRecord, 'customer_other_info', null) == 'string'){
                updatedDbRecord.customer_other_info = JSON.parse(_.get(updatedDbRecord, 'customer_other_info', null));
            }
            if(typeof _.get(updatedDbRecord, 'extra', null) == 'string'){
                updatedDbRecord.extra = JSON.parse(_.get(updatedDbRecord, 'extra', null));
            }
            if(typeof  _.get(updatedDbRecord, 'extra', null) == 'string' || _.get(updatedDbRecord, 'extra', null)==null)
            {
                updatedDbRecord.extra = {};  // This is required because some enteries have more than once stringfied extra, now reseting it as no one is using extra column otherthan smsParsingBillPayment
            }
        }
        catch(error){
            //self.L.error("Ignore record ::Error for record", JSON.stringify(billsKafkaRow), error);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                return null;
        }
        let amountToConsider=null;
        let originalAmount= _.get(updatedDbRecord, ['customer_other_info','currentBillAmount'], null);
        let intAmount= _.get(updatedDbRecord, 'due_amount', null);

        if(!intAmount) {
            intAmount = _.get(updatedDbRecord, 'amount', null);
        }
        if(originalAmount!=null && intAmount!=null && Math.abs(originalAmount - intAmount)< 1){
            amountToConsider=originalAmount;
        }
        else amountToConsider=intAmount;
        updatedDbRecord.amount = Math.round((amountToConsider - _.get(billsKafkaRow, 'amount', 0))*100)/100; 
        updatedDbRecord.payment_date =_.get(billsKafkaRow, ['customerOtherInfo', 'paymentDate'], null);
        updatedDbRecord.status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14);
        updatedDbRecord.extra.last_paid_amount = billsKafkaRow.amount;
        updatedDbRecord.extra.updated_data_source = _.get(billsKafkaRow.extra, 'updated_data_source', null);


        updatedDbRecord.source_subtype_2 = 'FULL_BILL';
        updatedDbRecord.customer_other_info.paymentDate = updatedDbRecord.payment_date;
        updatedDbRecord.customer_other_info.currentBillAmount = updatedDbRecord.amount;
        updatedDbRecord.customer_other_info.currentMinBillAmount = (updatedDbRecord.customer_other_info.currentMinBillAmount - _.get(billsKafkaRow, 'amount', 0)) < 0 ? 0 : updatedDbRecord.customer_other_info.currentMinBillAmount - _.get(billsKafkaRow, 'amount', 0);
        updatedDbRecord.customer_other_info.smsSenderID = _.get(billsKafkaRow, ['customerOtherInfo', 'smsSenderId'], null)
        updatedDbRecord.customer_other_info.debugKey =_.get(billsKafkaRow, ['customerOtherInfo', 'debugKey'], null);

        updatedDbRecord.extra = JSON.stringify(_.get(updatedDbRecord, 'extra', {} ));
        updatedDbRecord.customer_other_info = JSON.stringify(_.get(updatedDbRecord, 'customer_other_info', {}));
        billsKafkaRow.customerOtherInfo = JSON.stringify(_.get(billsKafkaRow, 'customerOtherInfo', null));
        updatedDbRecord.nextBillFetchDate = _.get(existingRecord,'next_bill_fetch_date',null);
        updatedDbRecord.nextBillFetchStartDate = _.get(existingRecord,'next_bill_fetch_date',null);
        //extra object add ---

        return updatedDbRecord;
    }    

    smsAndBbpsCompare(billsKafkaRow, existingRecord) {
        const self = this;
        if (_.get(existingRecord, 'due_date', null) != null && _.get(existingRecord, 'amount', null) != null) {//full bill case
            let existingDate = _.get(existingRecord, 'due_date', null);
            if (typeof (existingDate) != 'string') {
                existingDate = JSON.stringify(existingDate);
            }
            existingDate = existingDate.substr(0, 10);
            let bbpsDueDate = _.get(billsKafkaRow, 'dueDate', null);
            if (typeof (bbpsDueDate) != 'string') {
                bbpsDueDate = JSON.stringify(bbpsDueDate);
            }
            bbpsDueDate = bbpsDueDate.substr(0, 10);
            if(_.get(billsKafkaRow, 'amount', 0)< 0) {
                _.set(billsKafkaRow, 'amount', 0);
            }
            if (_.get(billsKafkaRow, 'amount', 0) == 0) {
                if (existingDate <= bbpsDueDate) {
                    console.log("case 11, 10");
                    return billsKafkaRow;//show bbps bill
                }
                else if (existingDate > bbpsDueDate) {
                    console.log("case 12");
                    console.log("not updating after comparing with bbps bill");
                    return null; // show sms bill have to define a function to convert parameters like due_date to dueDate
                }
            }
            else {
                if (existingDate <= bbpsDueDate) {
                    console.log("case 14");
                    console.log("print," ,existingDate, bbpsDueDate);
                    return billsKafkaRow; // show bbps bill
                }
                else if (existingDate > bbpsDueDate) {
                    console.log("case 13,15");
                    console.log("not updating after comparing with bbps bill");
                    return null;
                }
            }
        } else if (_.get(existingRecord, 'due_date', null) == null || _.get(existingRecord, 'amount', null) == null) {// partial record case
            if(_.get(billsKafkaRow, 'amount', 0)< 0) {
                _.set(billsKafkaRow, 'amount', 0);
            }
            if (_.get(existingRecord, 'due_date', null) == null) {
                if (_.get(billsKafkaRow, 'dueDate', null) >= self.nonPaytmBillsModel.getDate()) {
                    console.log("case 3,5");
                    return billsKafkaRow; //bbps bill
                } else {
                    let billfetchDate = _.get(existingRecord, 'bill_fetch_date', null);
                    if (typeof (billfetchDate) != 'string') {
                        billfetchDate = JSON.stringify(billfetchDate);
                    }
                    billfetchDate = billfetchDate.substr(0, 10);
                    let bbpsDueDate = _.get(billsKafkaRow, 'dueDate', null);
                    if (typeof (bbpsDueDate) != 'string') {
                        bbpsDueDate = JSON.stringify(bbpsDueDate);
                    }
                    bbpsDueDate = bbpsDueDate.substr(0, 10);
                    if (billfetchDate > bbpsDueDate) {
                        console.log("case4,6");
                        console.log("not updating after comparing with bbps bill");
                        return null;//sms
                    } else {
                        console.log("case4,6");
                        return billsKafkaRow;//bbps
                    }
                }
            } else if (_.get(existingRecord, 'due_date', null) != null) {
                let existingDate = _.get(existingRecord, 'due_date', null);
                if (typeof (existingDate) != 'string') {
                    existingDate = JSON.stringify(existingDate);
                }
                existingDate = existingDate.substr(0, 10);
                let bbpsDueDate = _.get(billsKafkaRow, 'dueDate', null);
                if (typeof (bbpsDueDate) != 'string') {
                    bbpsDueDate = JSON.stringify(bbpsDueDate);
                }
                bbpsDueDate = bbpsDueDate.substr(0, 10);
                if (existingDate < bbpsDueDate) {
                    console.log("case 8");
                    return billsKafkaRow; // show bbps bill
                }
                else if (existingDate >= bbpsDueDate) {
                    console.log("case 7,9");
                    console.log("not updating after comparing with bbps bill");
                    return null;
                }
            }
        }
        
        L.log("all checks bypassed");
        return null;
    }

    processExistingRecordForDataExhaust(billsKafkaRow, existingRecord){
        const self = this;
        try{

            billsKafkaRow.productId = existingRecord.product_id;
            billsKafkaRow.operator = existingRecord.operator;
            billsKafkaRow.amount = existingRecord.amount;
            billsKafkaRow.bill_fetch_date = existingRecord.bill_fetch_date;
            billsKafkaRow.paytype = existingRecord.paytype;
            billsKafkaRow.service = existingRecord.service;
            billsKafkaRow.circle = existingRecord.circle;
            billsKafkaRow.customer_mobile = existingRecord.customer_mobile;
            billsKafkaRow.customer_email = existingRecord.customer_email;
            billsKafkaRow.status = existingRecord.status;
            billsKafkaRow.userData = existingRecord.user_data;
            billsKafkaRow.billDate = existingRecord.bill_date;
            billsKafkaRow.notificationStatus = existingRecord.notification_status;
            billsKafkaRow.dueDate = existingRecord.due_date;

            let existingCustomerOtherInfo = existingRecord.customer_other_info;

            if(!existingCustomerOtherInfo || existingCustomerOtherInfo == 'null'){
                existingCustomerOtherInfo = {};
            }
            else if(typeof existingCustomerOtherInfo == 'string'){
                try{
                    existingCustomerOtherInfo= JSON.parse(existingCustomerOtherInfo);
                }
                catch(err){
                    self.L.info("processExistingRecordForDataExhaust :: customerOtherInfo parse issue");
                }
            }

            let customerOtherInfo = billsKafkaRow.customerOtherInfo;

            if(!customerOtherInfo || customerOtherInfo == 'null'){
                customerOtherInfo = {};
            }
            else if(typeof customerOtherInfo == 'string'){
                try{
                    customerOtherInfo= JSON.parse(customerOtherInfo);
                }
                catch(err){
                    self.L.info("processExistingRecordForDataExhaust :: customerOtherInfo parse issue");
                }
            }

            existingCustomerOtherInfo.sms_id = _.get(customerOtherInfo, "sms_id");
            existingCustomerOtherInfo.sms_date_time = _.get(customerOtherInfo, "sms_date_time");
            existingCustomerOtherInfo.smsDateTime = _.get(customerOtherInfo, "smsDateTime");
            existingCustomerOtherInfo.msgId = _.get(customerOtherInfo, "msgId");

            billsKafkaRow.customerOtherInfo = existingCustomerOtherInfo;

            let extra = _.get(billsKafkaRow, 'extra', '{}');
            if (!extra || extra == 'null') {
                extra = {};
            } else if (typeof extra == 'string'){
                try{
                    extra = JSON.parse(extra)
                }
                catch(err){
                    self.L.info("processExistingRecordForDataExhaust :: Parsing issue in records for incoming data exhaust extra", billsKafkaRow.debugKey, err);
                }

            }
            let existingRecordExtra = {};
            if (!existingRecordExtra || existingRecordExtra == 'null') {
                existingRecordExtra = {};
            }
            else if (typeof existingRecordExtra == 'string') {
                try {
                    existingRecordExtra = JSON.parse(_.get(existingRecord, 'extra', '{}'));
                }
                catch (err) {
                    self.L.info("processExistingRecordForDataExhaust :: Parsing issue in records for existing extra", billsKafkaRow.debugKey, err);
                }
            }
            existingRecordExtra.isRealTimeDataExhausted = true;
            existingRecordExtra.isDataExhausted = true;
            existingRecordExtra.data_exhaust_value = extra.data_exhaust_value;
            existingRecordExtra.data_exhaust_date = extra.data_exhaust_date;
            existingRecordExtra.updated_data_source = _.get(extra, "updated_data_source");

            billsKafkaRow.extra = existingRecordExtra;
            if((_.get(billsKafkaRow,'dueDate',null) == null || _.get(billsKafkaRow,'amount',null) == null) && self.partialRecordAllowedServices.includes(_.get(billsKafkaRow,'service',"").toLowerCase())){
                billsKafkaRow.partialRecordFound = true;

            }

        }catch(error){
            self.L.error(`processExistingRecord`, `Failed with error ${error}`);
            return null;
        }

        return billsKafkaRow;
    }

    setCustomerIdInCustomerOtherInfo(billsKafkaRow, existingRecord){
        let self = this;
        try{
            let customer_id = _.get(existingRecord, 'customer_id', null);
            let customerOtherInfo = _.get(billsKafkaRow, 'customerOtherInfo', null);

            let isParsingDone = false;

            if(typeof customerOtherInfo == 'string'){
                try{
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                    isParsingDone = true;
                }catch(err){
                    self.L.error(`setCustomerIdInCustomerOtherInfo`, `Failed with error ${err}`);
                }
            }

            if(customer_id){
                _.set(customerOtherInfo, 'customerId', customer_id);
            }
            
            if(isParsingDone){
                billsKafkaRow.customerOtherInfo = JSON.stringify(customerOtherInfo);
            }else{
                billsKafkaRow.customerOtherInfo = customerOtherInfo;
            }
            return billsKafkaRow;
            
        }catch(err){
            self.L.error(`setCustomerIdInCustomerOtherInfo`, `Failed with error ${err}`);
            return billsKafkaRow;
        }
    }

    processExistingRecord(billsKafkaRow, existingRecord){
        const self = this;
        try {
            if(_.get(billsKafkaRow,'source',null) != "reminderNonRuBillFetch")
                billsKafkaRow.nextBillFetchDate  = _.get(existingRecord,'next_bill_fetch_date',null);
            self.updateStatusAndNotificationStatus(existingRecord, billsKafkaRow);
            let extra = _.get(billsKafkaRow, 'extra', '{}');
            if (!extra || extra == 'null') {
                extra = {};
            } else if (typeof extra == 'string')
                billsKafkaRow.extra = JSON.parse(extra);
            let customerOtherInfo = _.get(billsKafkaRow, 'customerOtherInfo', null);

            let previousPaytype = _.toLower(_.get(existingRecord, "paytype", null));
    
            let existingRecordExtra = JSON.parse(_.get(existingRecord, 'extra', '{}'));
            
            if (existingRecordExtra == null) existingRecordExtra = {};

            if(billsKafkaRow.isValaidationSync && existingRecordExtra.created_source != 'validationSync' && !billsKafkaRow.isValidityExpired){
                billsKafkaRow.rejectingCurrentPayload = true;    // if record already present by sms, do not update by validation sync
            }

            if(!extra){
                billsKafkaRow.extra =  existingRecordExtra
            } else if (existingRecordExtra.created_source) {
                if (_.get(billsKafkaRow, 'source', null) != 'archivalCronsExpiredUser') {
                    billsKafkaRow.extra.created_source = existingRecordExtra.created_source;
                }
                if(_.get(existingRecordExtra, 'updated_data_source', null) == 'archivalCronsUserInitDeletion'){
                    billsKafkaRow.extra.updated_data_source = existingRecordExtra.updated_data_source;
                }
            }
            if(_.get(existingRecordExtra, 'errorCounters', null)){
                billsKafkaRow.extra.errorCounters = existingRecordExtra.errorCounters;
            }
            let dwhClassId = _.get(customerOtherInfo,'dwh_classId',null);
            if (_.toLower(_.get(billsKafkaRow, 'service'))=='mobile'){ 
                if(_.get(billsKafkaRow, 'partialBillState', null)){
                    _.set(billsKafkaRow, ['extra', 'partialBillState'], _.get(billsKafkaRow, 'partialBillState'));
                    // _.set(billsKafkaRow, ['extra', 'partialBillDate'], MOMENT(new Date()).format("YYYY-MM-DD HH:mm:ss"));
                }
                else{
                // cases with full bill only
                    if((_.get(billsKafkaRow,'dueDate',null) != null && _.get(billsKafkaRow,'amount',null) != null)){
                        if(_.get(billsKafkaRow, ['extra', 'partialBillState'], null)){
                            delete billsKafkaRow.extra.partialBillState;
                        }
                        if(_.get(billsKafkaRow, ['extra', 'partialBillDate'], null)){
                            delete billsKafkaRow.extra.partialBillDate;
                        }
                    }
                }
            }

            if(!customerOtherInfo){
                billsKafkaRow.customerOtherInfo =  _.get(existingRecord, 'customer_other_info', {});
            }else{
                billsKafkaRow = self.setCustomerIdInCustomerOtherInfo(billsKafkaRow, existingRecord);
            }
            if(_.get(billsKafkaRow, "service", 'null').toLowerCase() == "mobile" && _.get(billsKafkaRow, "paytype", 'null').toLowerCase() == previousPaytype && _.get(existingRecord, "product_id") && _.get(existingRecord, "circle") && _.get(billsKafkaRow, "circle", "null").toLowerCase() != _.get(existingRecord, "circle", "null").toLowerCase()){
                if (_.get(billsKafkaRow, "circle", "null").toLowerCase() != _.get(existingRecord, "circle", "null").toLowerCase()) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID_NON_PAYTM',
                        'STATUS:PRODUCT_ID_UPDATE',
                        `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`,
                        `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`,
                        `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`
                    ]);
                }
                self.L.info('Circle is updated from ', _.get(billsKafkaRow, "circle"), ' to ', _.get(existingRecord, "circle"), billsKafkaRow.debugKey);

                billsKafkaRow.productId = existingRecord.product_id;
                billsKafkaRow.circle = existingRecord.circle;

            }

            let createAt = _.get(existingRecord,'create_at',null);
            _.set(billsKafkaRow,'create_at',createAt);

            let dbDueDate = _.get(existingRecord, 'due_date', null) ? MOMENT(_.get(existingRecord, 'due_date', 0)).utc() : null;
            let currentDate = MOMENT().utc();
            let smsDueDate = _.get(billsKafkaRow, 'dueDate', null)? MOMENT(_.get(billsKafkaRow, 'dueDate', 0)).utc() : null;
            let publishedDate = _.get(existingRecord, 'published_date', null) ? MOMENT(_.get(existingRecord, 'published_date', 0)).utc() : null;
            if(_.get(existingRecord, 'notification_status', 1)==0){
                _.set(billsKafkaRow, 'toBeNotified', false);
            }
            if(_.get(billsKafkaRow,'source',null) == "reminderNonRuBillFetch"){
                if(_.get(billsKafkaRow, 'service', null) == 'financial services') {
                    billsKafkaRow = this.smsAndBbpsCompare(billsKafkaRow, existingRecord);
                    if(billsKafkaRow == null) {
                        self.L.info('not updating in db, after comparing with bbps payload');
                        return billsKafkaRow;
                    }
                }
                self.L.info(`processExistingRecord:: Incoming payload has souce ::  ${billsKafkaRow.source}`);
                return billsKafkaRow;
            } else if (_.get(billsKafkaRow, 'source', null) == 'UPMS' && (Math.abs(dbDueDate - smsDueDate) > 0 || _.get(billsKafkaRow, 'amount', null) != _.get(existingRecord, 'amount', null))) {
                self.L.info("processExistingRecord:: Incoming payload has source UPMS and due date or amount is different", JSON.stringify(billsKafkaRow));
                return billsKafkaRow;
            } else if (self.allowedServicesForUPMS && self.allowedServicesForUPMS.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase()) && dbDueDate != null) {
                let curDueDateDiff = MOMENT().startOf('day').diff(dbDueDate.startOf('day'));
                if (curDueDateDiff < 0) {
                    self.L.info("processExistingRecord:: Incoming payload has future duedate in DB", JSON.stringify(billsKafkaRow));
                    return null;
                }
                else if (_.get(billsKafkaRow, 'partialSmsFound', null)) {
                    if (publishedDate != null && (MOMENT().startOf('days').diff(publishedDate, 'days')) < _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_COMPARE_ALLOWED', 'DAYS_TO_COMPARE_DUE'], 25)) {
                        //partial sms again comes back -- partialSmsFound -- we are sending only in case of electricity
                        return null;
                    }
                    else {
                        if (_.get(existingRecord, 'next_bill_fetch_date', null)) {
                            billsKafkaRow.nextBillFetchDate = _.get(existingRecord, 'next_bill_fetch_date', null)
                        }
                        return billsKafkaRow;
                    }
                }
            }
            // if (smsDueDate - dbDueDate < 0){
            //     _.set(billsKafkaRow, 'toBeNotified', false);
            //     self.L.info("processExistingRecord:: Incoming payload has older duedate than DB ", JSON.stringify(billsKafkaRow));
            //     return null;
            // } else
            // this will over write due date in case of mobile prepaid partial bills
            if(_.get(billsKafkaRow, 'partialBillState', null))
                _.set(billsKafkaRow, 'due_date', null);
            
            if(_.get(billsKafkaRow, "partialBillState", null) && previousPaytype == "postpaid") return null;
            if(_.toLower(_.get(billsKafkaRow, 'service'))=='mobile' &&  dwhClassId == 11 && !dbDueDate) return billsKafkaRow;
            else if(_.toLower(_.get(billsKafkaRow, 'service'))=='mobile' &&  billsKafkaRow.partialBillState && dbDueDate && MOMENT(dbDueDate).diff(currentDate,'days') >= -1 * Number(self.DUEDATE_DIFF)) return null;
            else if (smsDueDate - dbDueDate == 0 && !billsKafkaRow.partialBillState && _.get(billsKafkaRow, 'amount', null) == _.get(existingRecord, 'amount', null)) {
                _.set(billsKafkaRow, 'toBeNotified', false);
                self.logger.log('processExistingRecord:: Incoming payload has same duedate and amount as DB', billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                return null;
            }
            else if (self.allowedServiceForBillFetch && self.allowedServiceForBillFetch.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase()) && smsDueDate != null && dbDueDate != null && smsDueDate < dbDueDate) {
                self.logger.log('processExistingRecord:: Incoming payload has future duedate in DB', billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                return null;
            } else if (self.allowedServiceForBillFetch && self.allowedServiceForBillFetch.includes(_.get(billsKafkaRow, 'service', 'NO_SERVICE').toLowerCase()) && smsDueDate == null && dbDueDate != null && dbDueDate.isAfter(MOMENT())) {
                self.logger.log('processExistingRecord:: Incoming payload has future duedate in DB and smsDueDate is null', billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                return null;
            }
            else return billsKafkaRow;
        } catch(error){
            self.L.error(`processExistingRecord`, `Failed with error ${error}`);
            return null;
        }
    }

    updateStatusAndNotificationStatus(existingRecord, billsKafkaRow) {
        let self=this;
        let service = _.get(billsKafkaRow, 'service', '');
        if(self.allowedServicessForUserInitiatedDelete.includes(service)){
            if(self.ifUserInitDeletionConditionIsTrue(existingRecord.status, existingRecord.notification_status, existingRecord.extra)){
                self.L.log(`updateStatusAndNotificationStatus, updated_data_source is archivalCronsUserInitDeletion, updating billsKafkaRow accordingly, debugKey : ${billsKafkaRow.debugKey}`);
                billsKafkaRow.notificationStatus = existingRecord.notification_status;
                billsKafkaRow.status = existingRecord.status;
            } else {
                if(!self.ifUserInitDeletionConditionIsTrue(billsKafkaRow.status, billsKafkaRow.notificationStatus, billsKafkaRow.extra)){
                    billsKafkaRow.notificationStatus = existingRecord.notification_status;
                    billsKafkaRow.status = billsKafkaRow.status || existingRecord.status;
                } else {
                    self.L.log(`updateStatusAndNotificationStatus, not updating status and notification_status in billsKafkaRow as existingRecord has status 13 and notification_status 0 and updated_data_source is archivalCronsUserInitDeletion, debugKey : ${billsKafkaRow.debugKey}`);
                }
            }
        } else {
            billsKafkaRow.notificationStatus = existingRecord.notification_status;
            billsKafkaRow.status = billsKafkaRow.status || existingRecord.status;
        }
        self.L.log(`updateStatusAndNotificationStatus, after update, status, notificationStatus in billsKafkaRow : ${billsKafkaRow.status}, ${billsKafkaRow.notificationStatus}, debugKey : ${billsKafkaRow.loggingDebugKey}`);
    }

    ifUserInitDeletionConditionIsTrue(status, notificationStatus, extra) {
        try {
            if (typeof extra === 'string') {
                extra = JSON.parse(extra);
            } else if (typeof extra === 'object' && extra !== null) {
                extra = extra;
            } else {
                console.error('Invalid extra field type:', typeof extra);
                return false;
            }
            if (status == 13 && notificationStatus == 0 && _.get(extra, 'updated_data_source', null) == 'archivalCronsUserInitDeletion') {
                return true;
            }
        } catch (err) {
            self.L.error('error occurred while checking ifUserInitDeletionConditionIstrue, error : ', err);
        }
        return false;
    }

    processExistingRecentRecord(billsKafkaRow, existingRecord){
        const self = this;
        try {
            billsKafkaRow.rechargeNumber = existingRecord.recharge_number;
    
            return billsKafkaRow;
        } catch(error){
            self.L.error(`processExistingRecentRecord`, `Failed with error ${error}`);
            return error
        }
    }

    validateKafkaRecord(record){
        const self = this;
        let response = '';
        let fieldsNotPresent = [];

        let debugKey = `rech_num:${record.rechargeNumber}::operator:${record.operator}::productId:${record.productId}::custId:${record.customerId}`;
        let loggingDebugKey =debugKey;
        if(record.service == 'financial services'){
            loggingDebugKey = `rech_num:${this.encryptHelper.encryptData(record.rechargeNumber)}::operator:${record.operator}::productId:${record.productId}::custId:${record.customerId}`;
        }
        _.set(record, 'debugKey', debugKey);
        _.set(record, 'loggingDebugKey', loggingDebugKey);
        let mandatoryParams = ['rechargeNumber', 'operator', 'service', 'paytype', 'productId', 'customerId', 'dbEvent'];

        for (let field of mandatoryParams) {
            if (!record[field]) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:MANDATORY_FIELDS_ABSENT', "FIELD:" + field]);
                fieldsNotPresent.push(field);
            }
        }

        // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:FIELDS_MISSING',`ORIGIN:${_.get(record,'source',"NO_SOURCE")}`,`SERVICE:${_.get(record,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(record,'operator','NO_OPERATOR')}`]);
            response = `Mandatory fields not present:: Missing params:: ${fieldsNotPresent.join(',')}`;
            self.L.log(`Error in validateKafkaRecord`,  `Mandatory fields not present:: ${loggingDebugKey} Missing params:: ${fieldsNotPresent.join(',')}`);        
            
        }

        return response;

        
    }

    async generateBucketNameUsingHash(params,currentRecord) {
        const self = this;
        let customer_id = _.get(params,'customerId',_.get(currentRecord,'customer_id',null)),
            recharge_number =  _.get(params,'rechargeNumber',_.get(currentRecord,'recharge_number',null));
        try{
            const inputString = `${customer_id}${recharge_number}`;
            const hash = CRYPTO.createHash('md5').update(inputString).digest('hex');
            let value = 40;
            const hashValue = parseInt(hash, 16) % value;
            const resultString = hashValue.toString();
        
            return resultString;
        } catch(err) {
            self.L.error(`error in generating bucketname`, err);
            return null;
        }
    }

    convertCdcRecovery(record){
        const self = this;
        let debugKey = `rech_num:${record.recharge_number}::operator:${record.operator}::productId:${record.product_id}::custId:${record.customer_id}`;
        _.set(record, 'debugKey', debugKey);
        let cdcPayload= {
           'customerId': _.get(record, 'customer_id', null),
           'rechargeNumber':_.get(record, 'recharge_number', null),
           'productId':_.get(record, 'product_id', null),
           'operator':_.get(record, 'operator', null),
           'amount':_.get(record, 'due_amount', null),
           'dueDate':_.get(record, 'due_date', null),
           'billDate':_.get(record, 'bill_date', null),
           'billFetchDate':_.get(record, 'bill_fetch_date', null),
           'nextBillFetchDate':_.get(record, 'next_bill_fetch_date', null),
           'customerOtherInfo':_.get(record, 'customer_other_info', null),
           'paytype':_.get(record, 'paytype', null),
           'service':_.get(record, 'service', null),
           'categoryId':_.get(record, 'category_id', null),
           'customerMobile':_.get(record, 'customer_mobile', null),
           'customerEmail':_.get(record, 'customer_email', null),
           'notificationStatus':_.get(record, 'notification_status', null),
           'bankName':_.get(record, 'bank_name', null),
           'cardNetwork':_.get(record, 'card_network', null),
           'status':_.get(record, 'status', null),
           'dbEvent':_.get(record, 'dbEvent', null),
           'debugKey':_.get(record, 'debugKey', null),
           'extra':_.get(record, 'extra', null),
           'circle':_.get(record, 'circle', null),
           'cdcEventType':_.get(record, 'cdcEventType', null),
           'paymentDate':_.get(record, 'payment_date', null),
           'userData':_.get(record, 'user_data', null),
           'isAutomatic':_.get(record, 'is_automatic', null),
           'updateAt':_.get(record, 'updateAt', null)
        };
        return cdcPayload;
    }

    async UpdateOrDeleteData(cb, billsKafkaRow) {
        const self = this;

         if (self.allowed_operators && self.allowed_operators.indexOf(billsKafkaRow.operator) < 0) {
            billsKafkaRow.action = 'noAction';
            return cb();
        }   
        if(billsKafkaRow.service == 'mobile'){
            billsKafkaRow.rechargeNumber = this.cryptr.encrypt(billsKafkaRow.rechargeNumber);
            billsKafkaRow.is_encrypted_done = true;
        }
        self.nonPaytmBillsModel.readBillsWithRechargeNumber(billsKafkaRow)
            .then(existingRecord => {
                if (existingRecord.length > 0) {
                    ASYNC.eachLimit(existingRecord, 5, function (recordsToProcess, innerCb) {
                        let action = self.checkUpdateOrDelete(billsKafkaRow, recordsToProcess);
                        billsKafkaRow.action = action;
                        billsKafkaRow.productId = recordsToProcess.product_id;
                        console.log("action    ", action);
                        if (action == 'delete') {
                            return self.deleteData(innerCb, billsKafkaRow);
                        }
                        else if (action == 'update') {
                           // let billsKafkaRowClone = _.clone(billsKafkaRow);
                            billsKafkaRow.customerId = recordsToProcess.customer_id;
                            self.nonPaytmBillsModel.writeBatchRecords(billsKafkaRow, existingRecord, null, null, null, null, self.cassandraCdcPublisher).then((data)=>{
                                return innerCb();
                            })
                            .catch(error => {
                                innerCb();
                            }) 
                        }
                        else {
                           return innerCb();
                        }

                    }, function () {
                        return cb();
                    });
                }
                else {
                    return cb('No/multiple matching records found');
                }
            })
            .catch(error => {
                let err;
                if (error) {
                    
                    self.L.error(`UpdateOrDeleteData`, `Failed with error ${error}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPDATEORDELETE_DATA']);
                    err = error;
                    return  self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
                }
                else {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SKIPPING_MCN', 'SOURCE:UPDATEORDELETE_DATA']);
                    self.L.log(`UpdateOrDeleteData`, `Ignored the record due to either no matching records found or multiple records found with debug key ${billsKafkaRow.loggingDebugKey}`);
                    err = `UpdateOrDeleteData : Ignored the record due to either no matching records found`;
                    return  self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsKafkaRow,partialRecordFound? 'PARTIAL_BILL':'FULL_BILL', 'NON_RU'), err,cb);
                }
                
            })
    }

    checkUpdateOrDelete(billsKafkaRow, existingRecord) {
        if (billsKafkaRow.service != existingRecord.service || billsKafkaRow.operator != existingRecord.operator) return 'noAction';
        if (billsKafkaRow.customerId == existingRecord.customer_id) return 'delete';
        return 'update';
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let self=this;
        let recordForAnalytics = {};
        let extraInfo=self.getExtra(record);
        
        let customerOtherInfo=self.getCustomerOtherInfo(_.get(record, 'customerOtherInfo', null));
        recordForAnalytics.source =extraInfo !=null? _.get(extraInfo, 'updated_data_source', null):null;
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'customerId', null);
        recordForAnalytics.service = _.get(record, 'service', null);
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null);
        try{
            if(record && record.is_encrypted_done){
                recordForAnalytics.recharge_number = this.cryptr.decrypt(recordForAnalytics.recharge_number);
             }

        }
        catch(error){
            self.L.log("Error in encryption recharge number");         
        }
        
        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'amount', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'smsId', null);
        recordForAnalytics.paytype = _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id =  _.get(customerOtherInfo,'sender_id',null) !==null?_.get(customerOtherInfo,'sender_id',null):_.get(customerOtherInfo,'senderId',null);
        recordForAnalytics.sms_date_time = _.get(customerOtherInfo,'sms_date_time',null) !==null?_.get(customerOtherInfo,'sms_date_time',null):_.get(customerOtherInfo,'smsDateTime',null);
        recordForAnalytics.sms_class_id = _.get(customerOtherInfo,'dwh_classId',null) !==null?_.get(customerOtherInfo,'dwh_classId',null):_.get(customerOtherInfo,'dwhClassId',null);
        recordForAnalytics.rawlastcc = _.get(customerOtherInfo,'rawLastCC',null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', null);
        recordForAnalytics.bill_date = _.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate',  MOMENT().format('YYYY-MM-DD HH:mm:ss'));

        return recordForAnalytics;
    }

    getCustomerOtherInfo(customerOtherInfo) {
        var self        = this;
       let customerOtherInfoPayload=null;
        try {
            if (typeof (customerOtherInfo) == 'string' && customerOtherInfo != '') {
                customerOtherInfoPayload = JSON.parse(customerOtherInfo);
            }       
        }
        catch (error) {
            self.L.log("Error in extracting customerOtherInfo");
        }
        return customerOtherInfoPayload;

    }

    getExtra(record) {
        let self = this;
        let extra = _.get(record, 'extra', null);
    
        if (!extra || extra == 'null') {
            return {};
        }
    
        if (typeof extra == 'string') {
            try {
                extra = JSON.parse(extra);
            } catch (error) {
                self.L.error("Failed to parse 'extra' as JSON:", error);
                extra = {};
            }
        }
    
        return extra;
    }


    getBillStatus(record){
        var self=this;
        if(_.get(record,'partialSmsFound',null)){
            return  'PARTIAL_BILL';
        }
        if ((_.get(record, 'dueDate', null) == null || _.get(record, 'amount', null) == null) && self.partialRecordAllowedServices && self.partialRecordAllowedServices.includes(_.toLower(_.get(record, 'service', "")))) {
            return 'PARTIAL_BILL';
        }
        return 'FULL_BILL'
    }



    suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`nonPaytmBillsConsumer::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`nonPaytmBillsConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`nonPaytmBillsConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`nonPaytmBillsConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`nonPaytmBillsConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

    isArchivalCronsExpiredUser(billsKafkaRow){
        return _.get(billsKafkaRow,'source',null) && billsKafkaRow.source == 'archivalCronsExpiredUser';
    }
    isOperatorAllowedForSmartFetch(billsKafkaRow){
        const self = this;
        return self.allowedOperatorForSmartFetch && self.allowedOperatorForSmartFetch.includes(billsKafkaRow.operator.toLowerCase());
    }

    async publishToUPMSRegistration(billsKafkaRow){
        let self = this,
            extra = self.getExtra(billsKafkaRow);

        if (!_.get(extra, 'ambiguous', null) && ((self.billPushServiceRegistration && self.billPushServiceRegistration.includes(_.get(billsKafkaRow, 'service', null))) || (self.billPushOperatorRegistrationAllowed && self.billPushOperatorRegistrationAllowed.includes(_.get(billsKafkaRow,'operator',null))))) {
            self.L.log("Going to push data for billPush Registration as it is confirmed nonRU and non ambiguous");
            self.BillPush.pushToRegistrationProcess(self, billsKafkaRow, billsKafkaRow, 'smsParsed', 'nonru');
        } else {
            self.L.log("Skipping billPush Registration as it is ambiguous or service/operator is not allowed")
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SKIP', 'TYPE:UPMS_REGISTRATION', 'SOURCE:UPSERT_DATA', `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`]);
        }
    }

    checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb) {
        let self = this;
        let currentDateStartOfDay = MOMENT().utc().startOf('day');
        let lowerBound = currentDateStartOfDay.clone().subtract(self.DUE_DATE_RANGE_VALIDITY_EXPIRED_LOWER_BOUND, 'days');
        let upperBound = currentDateStartOfDay.clone().add(self.DUE_DATE_RANGE_VALIDITY_EXPIRED_UPPER_BOUND, 'days');
        if (billsKafkaRow.isValaidationSync && billsKafkaRow.isValidityExpired && existingDueDate && existingDueDate.isBetween(lowerBound, upperBound, null, '[]')) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:SKIPPING_PARTIAL_DATA',  'REASON:EXISTING_DUE_DATE_IN_RANGE', 'SOURCE:UPSERT_DATA',`ORIGIN:${_.get(billsKafkaRow,'source',"NO_SOURCE")}`,`SERVICE:${_.get(billsKafkaRow,'service',"NO_SERVICE")}`,`OPERATOR:${_.get(billsKafkaRow,'operator','NO_OPERATOR')}`]);
            return cb(`The existing due date is within the range of lower_bound - ${self.DUE_DATE_RANGE_VALIDITY_EXPIRED_LOWER_BOUND} and upper_bound - ${self.DUE_DATE_RANGE_VALIDITY_EXPIRED_UPPER_BOUND} days.`);
        }
    }
    checkIfPrepaid(kafkaPayload) {
        let self = this;
        let extraInfo = self.getExtra(kafkaPayload);
        if (extraInfo && _.get(extraInfo, "isPrepaid", "0") === "1") { 
            return true;
        }
        return false;
    }
    isBajajFinanceLoanAllowed(billsKafkaRow) {
        const self = this;
        const custId = _.get(billsKafkaRow, 'customerId', null);
        
        if (billsKafkaRow.operator === "bajaj finance loan") {
            const isAllowed = custId % 100 <= self.allowedRolloutPercentageBaja;
            self.L.log('NonPaytmBills :: isBajajFinanceLoanAllowed', {
                msg: isAllowed ? 'Bajaj Finance loan allowed' : 'Bajaj Finance loan not allowed',
                customerId: custId,
                allowedRolloutPercentage: self.allowedRolloutPercentageBaja
            });
            return isAllowed;
        }
        return true; // Allow all other operators
    }

}


export default NonPaytmBills;
