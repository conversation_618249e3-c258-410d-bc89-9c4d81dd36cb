import CATALOGVERTIC<PERSON>RECHARGE from '../models/catalogVerticalRecharge';
import BILLS from '../models/bills';
import recentBillLibrary from '../lib/recentBills';
import utility from '../lib';
import MOMENT from 'moment';
import <PERSON>Y<PERSON> from 'async';
import RecentsLayerLib from '../lib/recentsLayer';
import reminderFlowManager from '../lib/reminderFlowManager';
import _ from 'lodash';
import VALIDATOR from 'validator';
import OS from 'os';
import BILLS_SUBSCRIBER from './billSubscriber';
import digitalUtility from 'digital-in-util'
import Q from 'q'

class PrepaidValidationSync {
    constructor(options) {
        this.L = options.L;
        this.options = options;
        this.config = options.config;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], ['credit card']);;
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        // this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.rechargeNumberAlreadySeen = []
        this.supportedServices = ['mobile', 'dth'];
        this.options.billsModel = this.bills = new BILLS(options);
        this.reminderFlowManager = new reminderFlowManager(options);
        this.setVarFromDynamicConfig();
    }

    setVarFromDynamicConfig() {
        let self = this;
        this.disableValidationSync = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_VALIDATION_SYNC', 'COMMON', 'DISABLE_FOR_ALL'], false);
        this.skipNotification = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_VALIDATION_SYNC', 'COMMON', 'SKIP_SENDING_NOTIFICATION'], false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'VALIDATION_SYNC', 'BATCHSIZE'], 2) : 500;
        setTimeout(() => {
            self.L.log(`setVarFromDynamicConfig`, `Updating service params from dynamic config..`)
            self.setVarFromDynamicConfig()
        }, 15 * 60 * 1000);
    }

    start() {
        let self = this;
        self.L.log('ValidationSync:: Starting service');
        self.setConsentData((error) => {
            if (_.isEmpty(self.consentData) || error) {
                self.L.error("There was an error in setting Consent Data", error);
            } else {
                self.L.log('setConsentData:: Success');
                self.L.log("start", " Validation Sync service started");

                self.L.log('start', 'Going to configure Kafka');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('ValidationSync :: start', 'unable to configure kafka', error);
                        process.exit(0);
                    }
                    else {
                        self.L.log('ValidationSync :: start', 'Kafka Configured successfully !!');
                        setInterval(self.setConsentData.bind(self), self.cvrReloadInterval, (err) => {
                            if (err) {
                                self.L.critical('_refreshProductData : error while re-loading CVR data', err);
                            } else {
                                self.L.log('_refreshProductData : reload CVR data ');
                            }
                        });
                    }
                });
            }
        });
    }

    setConsentData(callback) {
        let self = this;
        let whereQuery = null;
        if (Object.keys(self.consentData).length) {
            whereQuery = `updated_at > "${MOMENT().subtract(2, 'day').subtract(1, 'hour').format("YYYY-MM-DD HH:mm:ss")}"`;
        }
        self.catalogVerticalRecharge.getCvrData(function (error, data) {
            try {
                if (error || !data) callback(error);
                else {
                    data.forEach(function (row) {
                        if (row && row.attributes) {
                            try {
                                let remindable = JSON.parse(row.attributes).remindable;
                                self.consentData[row.product_id] = remindable == _.get(self.config, 'COMMON.USER_CONSENT_REQUIRED', 2) ? true : false;
                            } catch (error) {
                                self.L.critical('setConsentData', `Error while parsing attributes for product_id:${row && row.product_id},row: ${JSON.stringify(row)}`);
                            }
                        }
                    });
                    callback();
                }
            } catch (Exception) {
                callback(Exception);
            }
        }, whereQuery);
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return self._configureKafkaPublisher(next);
            },
            next => {
                self.L.log('configureKafka', `Going to initialize Kakfa Consumer for FFR Validation topics : ${_.get(self.config.KAFKA, 'SERVICES.VALIDATION_SYNC.VALIDATION_TOPICS', []).join(',')}`);

                // Initialize validation sync consumer.
                self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                    "groupId": "prepaid-validation-sync",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.VALIDATION_SYNC.VALIDATION_TOPICS'),
                    "id": 'prepaidValidationSyncConsumer_' + OS.hostname() + '_' + process.pid,
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : VALIDATION_TOPICS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    _configureKafkaPublisher(cb) {
        let
            self = this;

        ASYNC.parallel([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    self.L.critical('nonPaytmKafkaPublisher:: error in initialising Producer :: ', error);
                    self.L.log("RECENTBILLS :: NON_PAYTM_RECORDS KAFKA PRODUCER STARTED....");
                    return next(error);
                });

            },
        ], function (error) {
            if (error) {
                L.critical('_configureKafkaPublisher', 'error - ', error);
            }
            return cb(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage;

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaBillFetchConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} validation records !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaBillFetchConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.critical('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        process.exit(0);
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds ', records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:VALIDATION_SYNC", "TIME_TAKEN:" + executionTime]);


                    // Resume consumer now
                    if (self.greyScaleEnv) {
                        setTimeout(function () {
                            self.kafkaBillFetchConsumer._resumeConsumer();
                        }, _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'VALIDATION_SYNC', 'BATCH_DELAY'], 100));
                    } else {
                        self.kafkaBillFetchConsumer._resumeConsumer();
                    }
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        // let processedRecords = records.map((record) => { self.processRecord(record); });
        let processedRecords = [];
        for (let itrRecords = 0; itrRecords < records.length; itrRecords++) {
            processedRecords.push(self.processRecord(records[itrRecords]));
        }
        Promise.all(processedRecords)
            .then(() => {
                self.rechargeNumberAlreadySeen = []; // flushing recharge numbers seen array of the batch
                return done();
            })
            .catch(() => {
                return done();
            });
    }

    async processRecord(payLoad) {
        let self = this;
        try {
            let record = self.convertKafkaPayloadToRecord(payLoad);
            if (!record) return await Promise.resolve();

            let errorResponse = self.validateKafkaPayload(record);
            if (errorResponse) {
                // Invalid record count send to promethius
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PREPAID_VALIDATION_SYNC", 'STATUS:VALIDATION_FAILURE']);
                self.L.error(`processRecord:: Invalid Record record: ${JSON.stringify(record)} error:${errorResponse}`);
                return await Promise.resolve();
            }

            console.log("processing ", record);    
            let reminderFlowInstance = this.reminderFlowManager.getFlowInstance(record);
            if (!reminderFlowInstance) {
                self.L.error(`processRecord:: could not identify flow for qParams: ${JSON.stringify(record)}`);
                return await Promise.resolve();
            }

            let tableName = reminderFlowInstance.getTableName(record.operator, record.paytype);

            if (!tableName) {
                self.L.error(`processRecord:: ${record.operator} not migrated`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PREPAID_VALIDATION_SYNC", 'STATUS:TABLE_NOT_FOUND', "OPERATOR:" + record.operator]);
                return await Promise.resolve();
            }
            console.log("table name ", tableName);
            let nonPaytmUserFlag = await self.checkIfNewUser(record, reminderFlowInstance);

            if (nonPaytmUserFlag) {
                await self.publishNonPaytmEvents(record);
            }

        }
        catch (err) {
            self.L.error('processRecord:: ', err);
        }

        return await Promise.resolve();
    }

    convertKafkaPayloadToRecord(payLoad) {
        let self = this;
        try {
            payLoad = JSON.parse(_.get(payLoad, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, payLoad);
                return;
            }
        }

        let validationSuccess = _.get(payLoad, "validationSuccessful", null);

        if (!validationSuccess) return;

        let paytype = _.toLower(_.get(payLoad, 'productInfo_paytype', ''));
        let service = _.toLower(_.get(payLoad, 'productInfo_service', ''));

        if (paytype != 'prepaid' || !self.supportedServices.includes(service)) return;

        let amount = utility.getFilteredAmount(_.get(payLoad, 'userData_totalamount', '0'));
        if (amount <= 0) return;

        let rechargeNumber = _.get(payLoad, 'userData_recharge_number', '');
        //ensuring unique recharge numbers per batch processed
        if (self.rechargeNumberAlreadySeen.includes(rechargeNumber)) {
            self.L.log("ValidationSync :: Skipping duplicate recharge number ", rechargeNumber)
            return
        } else {
            self.rechargeNumberAlreadySeen.push(rechargeNumber);
        }

        let customerId = _.get(payLoad, 'customerInfo_customer_id', 0);
        let productId = _.get(payLoad, 'catalogProductID', 0);


        let operator = _.toLower(_.get(payLoad, 'productInfo_operator', ''));
        productId = typeof (productId) === 'string' ? VALIDATOR.toInt(productId) : productId;
        customerId = typeof (customerId) === 'string' ? VALIDATOR.toInt(customerId) : customerId;

        let record = {
            customerId: customerId,
            rechargeNumber: rechargeNumber,
            productId: productId,
            operator: operator,
            amount: amount,
            bill_fetch_date: MOMENT(),
            paytype: paytype,
            service: service,
            circle: _.get(payLoad, 'productInfo_circle', ''),
            customer_mobile: _.get(payLoad, 'customerInfo_customer_mobile', null),
            customer_email: _.get(payLoad, 'customerInfo_customer_email', null),
            status: self.config.COMMON.bills_status.BILL_FETCHED,
            userData: JSON.stringify(this.recentBillLibrary.getUserData(payLoad)),
            billDate: null,
            notificationStatus: 1,  // ASK by default value 
            dueDate: MOMENT(),
            customerOtherInfo: JSON.stringify(payLoad.metaData),
            planBucket: payLoad.metaData.plan_bucket,
            validationChannel: _.get(payLoad, "customerInfo_channel_id", null)
        }

        return record;
    }

    validateKafkaPayload(record) {

        let self = this;
        let response = '';
        let debugKey = `rech_num:${record.rechargeNumber}::operator:${record.operator}::productId:${record.productId}::custId:${record.customerId}`;
        _.set(record, 'debugKey', debugKey);
        let mandatoryParams = ['rechargeNumber', 'customerId', 'operator', 'service', 'paytype', 'productId', 'amount'];
        let fieldsNotPresent = [];
        let excludedChannelIds = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_CHANNEL_ID'], false)

        // Disable Validation Sync service: Set to true if all records need to be flushed without any processing.
        if (self.disableValidationSync) {
            response = `Validation Sync Service is Diabled ${debugKey}`;
            return response;
        }

        // check is operator is Blocked
        else if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'DISABLE_VALIDATION_SYNC'], false)) {
            response = `Validation Sync disabled for operator: ${record.operator}  ${debugKey}`;
        }

        else if (excludedChannelIds && _.isArray(excludedChannelIds) && excludedChannelIds.indexOf(record.validationChannel) > -1) {
            response = `Channel_id: ${record.validationChannel} is disabled, excludedChannelIds: ${excludedChannelIds}`;
        }

        if (response) return response;

        for (let field of mandatoryParams) {
            if (!record[field]) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PREPAID_VALIDATION_SYNC", 'STATUS:MANDATORY_FIELDS_ABSENT', "OPERATOR:" + record.operator, "FIELD:" + field]);
                fieldsNotPresent.push(field);
            }
        }

        // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            response = `Mandatory fields not present:: ${debugKey} Missing params:: ${fieldsNotPresent.join(',')}`;
        }

        return response;
    }

    async checkIfNewUser(record, reminderFlowInstance) {
        return new Promise((resolve, reject) => {
            let self = this;
            reminderFlowInstance.notificationManager_getBill(function (error, data) {
                if (error) {
                    self.L.error(`notificationManager_getBill:: getting error: ${JSON.stringify(record)} error:${error}`);
                    return reject(error);
                }
                if (data.length > 0) {
                    self.L.log(`getBill`, `Data Already presentrecords for ${record.debugKey}`);
                    return resolve();
                }
                return resolve(true);
            }, record);
        });
    }

    publishNonPaytmEvents(record) {
        return new Promise((resolve, reject) => {
            let self = this;
            record.dbEvent = 'upsert';
            self.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
                messages: JSON.stringify(record)
            }], (error) => {
                if (!error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PREPAID_VALIDATION_SYNC", 'STATUS:PUBLISHED', "TYPE:NON_PAYTM_EVENTS", "OPERATOR:" + record.operator]);
                    self.L.log('nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(record));
                }
                resolve();
            })
        });
    };
    suspendOperations() {
        var self = this,
            deferred = Q.defer();

        self.L.log(`prepaidValidationSync::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.consumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`prepaidValidationSync::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`prepaidValidationSync::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`prepaidValidationSync::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`prepaidValidationSync::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
}


export default PrepaidValidationSync;