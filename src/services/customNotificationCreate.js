/*jshint esversion: 8 */
import NOTIFICATION from '../models/notification'

import utility from '../lib'
import MOMENT from 'moment'
import ASYNC from 'async'
import _ from 'lodash'
import OS from 'os'
import NOTIFIER from './notify'
import NotificationLibrary from '../lib/notification'
import Q from 'q'
import billsLib from '../lib/bills'
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'

const env = (process.env.NODE_ENV || 'development').toLowerCase();
let L = null;

class customNotificationCreateConsumer {
    constructor(options, topic, groupId, categoryId) {
        this.L = options.L;
        L = this.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.notification = new NOTIFICATION(options);
        this.notificationLibrary = new NotificationLibrary(options);
        this.billsLib = new billsLib(options);
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        //this.customNotificationConfig = _.get(this.config, 'CUSTOM_NOTIFICATIONS');
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        this.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        this.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], { 'airtel': 1, 'jio': 1, 'idea': 1, 'vodafone': 1, 'vodafone idea': 1 });
        this.cvrData = {};
        this.offsetIdMap = {};
        this.authToken = null;
        this.blackListOperators = null;
        this.blackListCustomers = null;
        this.infraUtils = options.INFRAUTILS;
        this.notify = new NOTIFIER(options);
        this.categoryId = categoryId;
        this.disabledSourcesList = [];
        this.kafkaBatchSize = 500;
        this.topic = topic;
        this.groupId = groupId;
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);


        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(this.config, ['DYNAMIC_CONFIG', 'CC_PUBLISHER_CONFIG', 'SOURCE_DISABLED_FOR_NOTIFY'], {})

        Object.keys(disabledSources).forEach(source => {
            if (disabledSources[source] == 1) {
                this.disabledSourcesList.push(source)
            }
        })

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: billReminderNotification", "Re-initializing variable after interval");
        self.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        self.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        self.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        self.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], { 'airtel': 1, 'jio': 1, 'idea': 1, 'vodafone': 1, 'vodafone idea': 1 });
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_EXCEPTIONS', 'BLOCK_CUSTOMER_NOTIFICATIONS', 'CUSTOMER_ID'], null);
        self.redisTTL = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'NOTIFICATION_REDIS', 'REDIS_TTL'], 129600000);

        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(self.config, ['DYNAMIC_CONFIG', 'CC_PUBLISHER_CONFIG', 'SOURCE_DISABLED_FOR_NOTIFY'], {})

        self.disabledSourcesList = []

        Object.keys(disabledSources).forEach(source => {
            if (disabledSources[source] == 1) {
                self.disabledSourcesList.push(source)
            }
        })
    }

    start() {
        let self = this;
        self.L.log("start", "custom Notifications service started");

        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_EXCEPTIONS', 'BLOCK_CUSTOMER_NOTIFICATIONS', 'CUSTOMER_ID'], null);
        self.operatorsSendingNotificationToRegisteredUser = _.get(self.notificationConfig, 'registeredUserNotificationOperator', null);
        // set cvr data

        self.cvrData = _.get(self.config, 'CVR_DATA', {});

        if (_.isEmpty(self.cvrData)) {
            self.L.critical('custom Notifications service: CVR data is empty');
            process.exit(0);
        }
        ASYNC.waterfall([
            (callback) => {
                // initialize kafka consumer
                self.L.log('start', 'Going to configure Kafka');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('custom Notifications :: start', 'unable to configure kafka', error);
                        callback(error);
                    }
                    else {
                        self.L.log('custom Notifications :: start', 'Kafka Configured successfully !!');
                        callback(null);
                    }
                });
            }
        ], (error, result) => {
            if (error) {
                this.L.critical('custom Notifications :: start', 'Error:', error);
                // exiting in case of error
                process.exit(0);
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : CUSTOM_NOTIFICATIONS');
                self.customNotificationsKafkaConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CUSTOM_NOTIFICATIONS_PIPELINE.HOSTS,
                    "groupId": this.groupId,
                    "topics": this.topic,
                    "id": 'customNotificationsConsumer_' + OS.hostname(),
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.customNotificationsKafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : CUSTOM_NOTIFICATIONS Configured", this.topic);
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }


    configureKafkaPublisher(done) {
        ASYNC.parallel([
            callback => {
                this.ctKafkaPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                })

                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if (!error)
                        L.log("customNotifications :: ctKafkaPublisher", "publisher Configured");
                    else
                        L.error("customNotifications :: ctKafkaPublisher", "Error while  configuring publisher")
                    callback(error);
                });
            },
            callback => {
                this.notify.configureKafkaPublisher((err) => {
                    return callback(err);
                })
            }
        ], (error, result) => {
            return done(error);
        })
    }


    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage;
        // // what if I do not send messages in array How to commit them
        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.customNotificationsKafkaConsumer._pauseConsumer();
            utility._sendMetricsToDD(records.length, [
                'STATUS:CONSUMED',
                'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.CUSTOM_NOTIFICATIONS_PIPELINE.CUSTOM_NOTIFICATIONS')
            ])
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }



        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 100);
                });
            },
            (err) => {
                try {
                    self.kafkaConsumerChecks.findOffsetDuplicates("BillReminderNotification", records);

                    self.customNotificationsKafkaConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }

                        // Resume consumer now
                        let endTime = new Date().getTime();
                        let executionTime = (endTime - startTime) / 1000;      //in seconds
                        executionTime = Math.round(executionTime);
                        self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :', records.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:CUSTOM_NOTIFICATIONS", "TIME_TAKEN:" + executionTime]);
                        self.customNotificationsKafkaConsumer._resumeConsumer();

                    });
                } catch (err) {
                    self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    self.customNotificationsKafkaConsumer._resumeConsumer();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processNotification(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processNotification(done, data) {
        let self = this,
            record = null,
            debugKey = data;

        utility.sendNotificationMetricsFromCustomNotificationsCreate({}, {}, "RECVD")
        ASYNC.waterfall([
            (callback) => {
                self.validateDataToProcessForNotification(function (error, result) {
                    record = result;
                    if (error) {
                        return callback(`Unable to validate data, error - ${error}`);
                    } else {
                        return callback();
                    }
                }, data);
            },
            (callback) => {
                self.prepareNotification((error) => {
                    if (error) {
                        return callback(error);
                    } else {
                        return callback(null);
                    }

                }, record);
            }], (error) => {
                if (error) {
                    self.L.error('processNotification', 'debugKey', _.get(record, 'debugKey', null), 'error', error);
                    //return done();
                    return self.notify.insertRejectedNotifications(done, self.billsLib.createErrorMessage(error), record, null);
                } else {
                    return done();
                }
            }
        );
    }

    validateDataToProcessForNotification(callback, kafkaPayload) {
        let self = this;
        self.L.log('1. validateDataToProcessForNotification :: convert payload to record and validate');
        let record = self.convertKafkaPayloadToRecord(kafkaPayload);


        if (record == null) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "INVALID_PAYLOAD")
            return callback('unable to get valid data from kafka payload', record);
        }
        //How we will be generating recon id
        let reconId = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'service', null) == 'financial services' ? _.get(record, 'bank_name', null) : _.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null), _.get(record, 'bill_date', null));
        let reconIdFromPayload = _.get(record, 'recon_id', null);

        /*if(!_.get(record, 'recon_id', null)){
            _.set(record, 'recon_id', reconId);
        }*/
        if (!reconIdFromPayload) {
            _.set(record, 'recon_id', reconId);
        } else {
            _.set(record, 'recon_id', reconIdFromPayload);
        }

        let notificationType = _.get(record, 'notificationType');

        let productInfo = self.cvrData[_.get(record, 'product_id', null)];

        if (!productInfo) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "NO_PRODUCT_FOUND")
            return callback('No product Found', record);
        }

        else if (self.blackListOperators && self.blackListOperators.indexOf(_.get(record, 'operator')) != -1) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "BLACKLISTED_OP")
            return callback(`Blacklisted operator ${_.get(record, 'operator')}`, record);
        }
        else if (self.blackListCustomers !== null && self.blackListCustomers.includes(_.get(record, 'customer_id', null).toString())) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "BLACKLISTED_CUST")
            return callback(`Blacklisted customer_id ${_.get(record, 'customer_id')}`, record);
        }

        else if (productInfo && !_.get(productInfo, 'status', null)) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "INACTIVE_PID")
            self.L.log('validateDataToProcessForNotification :: PID is marked inactive for : ', `${_.get(record, 'debugKey')} ProductStatus:${_.get(productInfo, 'status', null)}`);
            return callback('PID Is marked inactive', record);
        }
        else if (self.disabledSourcesList.indexOf(_.get(record, 'data_source', null)) > -1) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "DISABLED_SOURCE")
            return callback(`record data source is ${_.get(record, 'data_source', null)} and is disabled`, record);
        }
        else if (_.get(record, 'is_automatic', 0) != 0) {
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "AUTOMATIC")
            return callback('Subscription exists');
        }
        else if (notificationType == 'BILLGEN') {
            // check for notification eligibility
            let amount = _.get(record, 'amount', null);
            if (amount !== null && amount <= _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)) {
                utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "MIN_NOTIFY_AMOUNT")
                return callback(`amount <= defined amount ${_.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)}`, record);
            }
            else if (_.get(record, 'paytype', null) == 'prepaid') {
                utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "PREPAID")
                return callback(`prepaid service id `, record);
            }
        }
        else if (notificationType == 'BILLDUE' || notificationType == 'DUEDATE') {
            let
                currDate = MOMENT().startOf('day'),
                dueDate = MOMENT(_.get(record, 'due_date')).utc().startOf('day'),
                dueDate_currDate_diff = dueDate.diff(currDate, 'days'),
                setDueDateNull = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', record.operator, 'ALLOW_NULL_DUE_DATE'], false);

            // setting due date format to avoid further conversion
            _.set(record, 'dueDate', dueDate.format('YYYY-MM-DD'));

            if ((setDueDateNull && !dueDate.isValid()) || _.get(record, 'due_date', null) === null) {
                return callback(null, record);
            } else if (dueDate_currDate_diff > -1 || dueDate_currDate_diff < 0) {
                return callback(null, record);
            } else {
                utility.sendNotificationMetricsFromCustomNotificationsCreate(record, {}, "INVALID_DUE_DATE")
                return callback(`Invalid diff dueDate_currDate_diff : ${dueDate_currDate_diff}`, record);
            }



        }
        return callback(null, record);
    }

    //file didn't marked for done
    //not commiting in case of incative pid

    convertKafkaPayloadToRecord(kafkaPayload) {

        let
            self = this,
            data;

        try {
            data = JSON.parse(_.get(kafkaPayload, 'value', null));

        } catch (error) {
            if (error) {
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, kafkaPayload);
                return null;
            }
        }

        if (_.get(data, 'paytype', null) == 'credit card') { // for credit card send only last 4 digit in  payload
            data.recharge_number = self.smsParsingSyncCCBillLib.createMaskedCCBasedOnCCLen(data.recharge_number, data.recharge_number.slice(-4))
        }
        let payloadService = _.get(data, 'service', null);



        let payload = {
            customer_id: _.get(data, 'customer_id', null),
            recharge_number: _.get(data, 'recharge_number', null),
            old_product_id: _.get(data, 'product_id', null),
            is_automatic: _.get(data, 'is_automatic'),
            product_id: self.activePidLib.getActivePID(_.get(data, 'product_id', null)),
            operator: _.get(data, 'operator', null),
            amount: _.get(data, 'due_amount', null),
            bill_date: _.get(data, 'bill_date', null),
            recon_id: _.get(data, 'recon_id', null),
            due_date: _.get(data, 'due_date', null),
            bill_fetch_date: _.get(data, 'bill_fetch_date', null),
            next_bill_fetch_date: _.get(data, 'next_bill_fetch_date', null),
            gateway: _.get(data, 'gateway', null),
            paytype: _.get(data, 'paytype', null),
            service: _.get(data, 'service', null),
            circle: _.get(data, 'circle', null),
            data_consumed: _.get(data, 'data_consumed', null),
            customer_mobile: _.get(data, 'customer_mobile', null),
            customer_email: _.get(data, 'customer_email', null),
            user_data: _.get(data, 'userData', null),
            payment_date: _.get(data, 'payment_date', null),
            source: _.get(data, 'source', null),
            notificationType: _.get(data, 'notif_type', null),
            template_id: _.get(data, 'template_id', null),
            template_name: _.get(data, 'template_name', null),
            bank_name: _.get(data, 'bank_name', null),
            card_network: _.get(data, 'card_network', null),
            customerOtherInfo: _.get(data, 'customerOtherInfo', null),
            time_interval: _.get(data, 'time_intervals', null),
            promocode: _.get(data, 'promocode'),
            campaign_id: _.get(data, 'campaign_id'),
            timestamps: {
                customNotifications_acknowledgeTime: new Date().getTime(),
                customNotifications_onBoardTime: _.get(data, 'customNotifications_onBoardTime', null)
            },
            start_date: _.get(data, 'start_date', null),
            end_date: _.get(data, 'end_date', null),
            campaign_name: _.get(data, 'campaign_name', null),
            debugKey: `customer_id-${_.get(data, 'customer_id')},recharge_number-${payloadService == 'financial services' ? this.encryptionDecryptioinHelper.encryptData(_.get(data, 'recharge_number')) : _.get(data, 'recharge_number')},operator-${_.get(data, 'operator')}`,

        };
        for (let key in data) {
            if (data.hasOwnProperty(key) && key.startsWith('param-')) {
                payload[key] = data[key];
            }
        }
        return payload;

    }

    async prepareNotification(callback, record) {
        let self = this,
            productInfo = self.cvrData[_.get(record, 'product_id', null)];

        self.L.log('3. prepareNotification :: fetch templates and prepare notification records');

        if (productInfo) {
            let dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let billDate = MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let nextBillFetchDate = MOMENT(_.get(record, 'next_bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'next_bill_fetch_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;

            let payLoad = {
                amount: _.get(record, 'amount', null),
                dataConsumed: _.get(record, 'data_consumed', null),
                recharge_number: _.get(record, 'recharge_number', null),
                operator: _.get(productInfo, 'operator'),
                operator_label: _.get(productInfo, 'operator_label'),
                brand: _.get(productInfo, 'brand'),
                thumbnail: null,
                category_id: _.get(productInfo, 'category_id'),
                service: _.get(productInfo, 'service'),
                customer_id: _.get(record, 'customer_id', null),
                bank_name: _.get(record, 'bank_name', null),
                card_network: _.get(record, 'card_network', null),
                paytype: _.get(productInfo, 'paytype'),
                time_interval: _.get(record, 'time_interval', null),//what time_interval does
                start_date: _.get(record, 'start_date', null),
                end_date: _.get(record, 'end_date', null),
                template_name: _.get(record, 'template_name', null),
                promocode: _.get(record, 'promocode'),
                gateway: _.get(productInfo, 'gateway'),
                circle: _.get(productInfo, 'circle'),
                customer_mobile: _.get(record, 'customer_mobile'),
                customer_email: _.get(record, 'customer_email'),
                payment_date: _.get(record, 'payment_date'),
                bank_name: _.get(record, 'bank_name'),
                card_network: _.get(record, 'card_network'),
                campaign_id: _.get(record, 'campaign_id'),
                is_automatic: _.get(record, 'is_automatic'),
                campaign_name: _.get(record, 'campaign_name', null),
                bill_date: billDate,
                bill_fetch_date: billFetchDate,
                next_bill_fetch_date: nextBillFetchDate

            },
                notificationType;
            dueDate ? _.set(payLoad, 'due_date', dueDate) : '';
            let emojiData = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES'], null);//should i put custom notification
            _.extend(payLoad, emojiData);

            if (_.get(payLoad, 'service').toLowerCase() == 'financial services') {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'last_four_digits', self.getLast4digitsOfCC(_.get(record, 'recharge_number', null)));
                _.set(payLoad, 'minimum_due', _.get(record, 'amount', null));
            } else if (_.get(this.config, ['COMMON', 'EMI_DUE_CONSUMER_CONFIG', env, _.get(record, 'operator', null), 'notificationCreateSendMinDueAmount'], null)) {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'minimum_due', _.get(record, 'amount', null));
            }

            for (let key in record) {
                if (record.hasOwnProperty(key) && key.startsWith('param-')) {
                    payLoad[key] = record[key];
                }
            }



            let notificationRecords = [

                {
                    type: 'PUSH',
                    recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                    notificationType: notificationType,
                    template_id: _.get(record, 'template_id', null),
                }
            ],
                short_operator_name = null;



            try {
                short_operator_name = _.get(JSON.parse(productInfo.attributes), 'short_operator_name', null);
            }
            catch (error) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_PID_PARSE', 'STATUS:ERROR']);
                self.L.error('Error while parsing attributes for PID', _.get(record, 'product_id', null), error);
            }

            short_operator_name = short_operator_name || _.get(productInfo, 'brand');
            _.set(payLoad, 'short_operator_name', short_operator_name);

            self.createShortUnsubscribeUrl(function (err, short_url) {
                if (err) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_SHORT_UNSUBURL', 'STATUS:ERROR']);
                    self.L.error('Error creating unsubscribe url ::', err);
                } else {
                    _.set(payLoad, 'unsubscribe_url', short_url);
                }
                ASYNC.map(
                    notificationRecords,
                    (notificationRecord, next) => {
                        self.sendNotification(record, notificationRecord, payLoad, () => {
                            next();
                        });
                    },
                    err => {
                        callback();
                    }
                )
            }, _.get(productInfo, 'operator'), _.get(record, 'recharge_number', null));


        } else {
            let errorMsg = `Product info does not exist for the product id: ${_.get(record, 'product_id', null)}`;
            self.L.error(errorMsg);
            callback(errorMsg);
        }
    }




    getLast4digitsOfCC(rechargeNumber = "") {
        let self = this;
        try {
            let last4Digits = rechargeNumber.replace(/ /g, "");
            last4Digits = last4Digits.substring(last4Digits.length - 4);
            return last4Digits;
        }
        catch (err) {
            self.L.error('billReminder::getLast4digitsOfCC', 'Error while finding last 4 digits of CC', err);
            return null;
        }
    }

    getMinDueAmount(record) {//check this
        let self = this;
        try {
            let customerOtherInfo = JSON.parse(_.get(record, 'customerOtherInfo', null));
            let currentMinBillAmount = _.get(customerOtherInfo, 'currentMinBillAmount', null)
            return currentMinBillAmount;
        }
        catch (err) {
            self.L.error('billReminder::getMinDueAmount', 'Error while parsing customerOtherInfo', err);
            return null;
        }
    }

    createShortUnsubscribeUrl(cb, operator, recharge_number) {
        let self = this,
            unsubscribe_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null);

        unsubscribe_url = unsubscribe_url + `/unsubscribe?operator=${operator}&recharge_number=${recharge_number}`;
        let object = new utility.TinyUrl();
        object.createShortUrl(cb, _.get(self.config, ['TINYURL_CONFIG'], null), unsubscribe_url);
    }

    getTopicToPublish(start_date) {
        let notif_date = new Date(start_date);
        let startHour = notif_date.getUTCHours();
        const minutes = notif_date.getUTCMinutes();
        if (minutes > 0) {
            startHour += 1;
        }
        return `NOTIFICATION_${startHour}`;

    }


    getTopicToPublish(category, source) {
        let self = this,
            topicToPublish = _.get(self.notificationConfig, ['categoryTopic', `${category}_${source}`], null) ||
                _.get(self.notificationConfig, ['categoryTopic', category], null);
        return topicToPublish;
    }


    sendProcessedNotification(record, notificationData, notificationRecord, callback) {
        let self = this;
        try {
            let
                source = _.get(record, 'source', null),
                sourceIdMapping = _.invert(_.get(self.notificationConfig, 'source'), {}),
                source_id = _.get(sourceIdMapping, source, self.categoryId);

            let additionalData = self.getDataForMeasurementAndTracking(record, notificationRecord, notificationData);
            _.set(notificationData, 'additional_data', additionalData);

            let send_at = MOMENT(_.get(record, ['timestamps', 'customNotifications_onBoardTime'], null)).isValid() ? MOMENT(_.get(record, ['timestamps', 'customNotifications_onBoardTime'], null)).format('YYYY-MM-DD HH:mm:ss.SSS') : MOMENT().format('YYYY-MM-DD HH:mm:ss.SSS');

            try {
                let hourTosendNotification = null;
                let topicToPublish = self.getTopicToPublish(record.start_date)
                if (self.categoryId == 13) {
                    hourTosendNotification = topicToPublish.includes('_NOTIFICATION') ? null : topicToPublish.split('_')[1];
                }




                let
                    currentDate = MOMENT().format('YYYY-MM-DD'),
                    startDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_START_TIME'], '09:00:00')).format('YYYY-MM-DD HH:mm:ss'),
                    endDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
                    dueDateExpiryTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'DUE_DATE_NOTIFICATOION_EXPIRY_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
                    startOfDay = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    startOfHour = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss')
                if (self.categoryId == 13) {
                    startOfHour = hourTosendNotification ? MOMENT().startOf('day').add(hourTosendNotification, 'hours').format('YYYY-MM-DD HH:mm:ss') : null
                }





                if (!MOMENT(send_at).isBetween(startOfHour, endDateTime)) { //3pm-10pm
                    if (MOMENT(send_at).isBetween(startOfDay, startOfHour)) {
                        send_at = startOfHour;
                    } else {
                        send_at = MOMENT(startOfHour).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
                    }
                }


                if (MOMENT().isAfter(dueDateExpiryTime) && _.get(record, 'notificationType', null) == 'DUEDATE' && topicToPublish != 'RT_NOTIFICATION') {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_CREATE_NOTIFICATION', 'STATUS:ERROR', 'TYPE:DUEDATE_NOTIFICATOION_EXPIRY_TIME']);
                    self.L.log(`sendProcessedNotification:: DUEDATE notification for ${_.get(record, 'debugKey')} is not sent because of DUEDATE_NOTIFICATOION_EXPIRY_TIME`);
                    return callback();
                }
            } catch (e) {
                self.L.error(`Error in calculating send at:: setting default send_at as now()`, e);
            }

            if (_.get(record, 'recharge_number', null)) {
                record.recharge_number = _.get(record, 'recharge_number').toString().replace(/'/g, '');
            }
            let body = {
                "source_id": source_id,
                "category_id": self.categoryId,
                "recharge_number": _.get(record, 'recharge_number', null),
                "product_id": _.get(record, 'product_id', null),
                "max_retry_count": 2,
                "retry_interval": 30,
                "type": _.get(notificationRecord, 'type', null),
                "template_id": _.get(notificationRecord, 'template_id', null),
                "recipient": _.get(notificationRecord, 'recipients', null),
                "send_at": send_at,
                "data": notificationData,
                "rules": {
                    "condition": `category_id=${self.categoryId} and source_id=${source_id} and recharge_number='${_.get(record, 'recharge_number', null)}' and product_id=${_.get(record, 'product_id', null)} 
                                    and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)}`,
                    "actions": [
                        {
                            "status": "pending",
                            "action": "drop"
                        },
                        {
                            "status": "sent",
                            "action": "drop"
                        }
                    ]
                }
            };

            if (_.get(record, 'time_interval', null)) {
                body['time_interval'] = _.get(record, 'time_interval', null);
            }

            _.set(body, 'timestamps', _.get(record, 'timestamps', {}));



            // Calling notify operation internally instead of API hit
            self.notify.__createNotification(function (error, data) {
                if (error) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_CREATE_NOTIFICATION', 'STATUS:ERROR']);
                    utility.sendNotificationMetricsFromCustomNotificationsCreate(record, notificationRecord, "ERROR_IN_CREATE")
                    self.L.error('sendProcessedNotification', 'createNotification', 'Error :', error, `for ${_.get(notificationRecord, 'type', null)}-${_.get(record, 'debugKey')}`);
                    return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
                } else {
                    utility.sendNotificationMetricsFromCustomNotificationsCreate(record, notificationRecord, "CREATED")
                    self.L.log('sendProcessedNotification', `${_.get(notificationRecord, 'type', null)} successful for ${_.get(record, 'debugKey')}`);
                    return callback();
                }

            }, body);

        } catch (error) {
            self.L.error('sendProcessedNotification::Error sending notification::Err:Msg', error);
            return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
        }
    }

    sendNotification(record, notificationRecord, data, callback) {

        if (_.get(notificationRecord, 'recipients', null) && _.get(notificationRecord, 'template_id', null)) {
            let self = this,
                paramsForUrl,
                payLoad = _.clone(data);

            if (_.get(notificationRecord, 'type', null) == 'PUSH') {
                let deepLinkData = {};
                let url_type = "external";
                let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
                let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
                let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
                let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
                let paytype = _.get(payLoad, 'paytype', null) != null ? _.get(payLoad, 'paytype', null).toLowerCase() : null;
                let short_operator_name = _.get(payLoad, "short_operator_name", null);
                let template_id = _.get(notificationRecord, "template_id", null);
                let operator = _.get(payLoad, "operator", null);
                //let utm = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);

                if (!landing_path) {
                    if (product_service === 'mobile') {
                        landing_path = "mobile_postpaid";
                    } else if (product_service === 'datacard') {
                        landing_path = "datacard_postpaid";
                    } else if (product_service === 'dth') {
                        landing_path = "dth";
                    } else {
                        landing_path = 'utility';
                    }
                }
                //isRealTimeDataExhausted

                let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
                if (_.toLower(product_service) == 'paytm postpaid') {
                    let deeplinkForPaytmPostpaid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE', `${product_service}::${paytype}`], []);
                    if (deeplinkForPaytmPostpaid && deeplinkForPaytmPostpaid.length) {
                        url = deeplinkForPaytmPostpaid[0];
                    }
                }
                paramsForUrl = self.getParamsForChatAndPush(record, payLoad);
                let completeUrl = null;
                if (product_service == 'mobile') {
                    completeUrl = url + "?$" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNum(record, '$');
                } else {
                    completeUrl = url + "?" + self.getQueryParams(paramsForUrl, '$') + self.getExtraRechargeNum(record, '$');
                }

                deepLinkData = {
                    "payLoad": payLoad,
                    "extra": {
                        "url": completeUrl,
                        "url_type": url_type
                    }
                }

                if (_.get(payLoad, 'param-deeplink', null)) {
                    deepLinkData.extra.url = _.get(payLoad, 'param-deeplink', null);
                }

                let pushNotificationData = self.notificationLibrary.getPushNotiData(deepLinkData, notificationRecord, 13);
                self.sendProcessedNotification(record, pushNotificationData, notificationRecord, callback);

            }
        } else {
            let self = this;
            utility.sendNotificationMetricsFromCustomNotificationsCreate(record, notificationRecord, "INVALID_RECP_OR_TEMPLATE_ID")
            self.L.error('sendNotification', `Invalid Recipient:${_.get(notificationRecord, 'recipients', null)} or templateId:${_.get(notificationRecord, 'template_id', null)} for ${_.get(record, 'debugKey', null)}`);
            return self.notify.insertRejectedNotifications(callback, 'Invalid recepient or template', record, notificationRecord);
        }
    }






    getDataForMeasurementAndTracking(record, notificationRecord, notificationData) {
        let self = this;
        let raw_expiry_date = _.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.due_date', null) : _.get(notificationData, 'options.data.due_date', null);
        if (raw_expiry_date && MOMENT(raw_expiry_date, 'Do MMM YYYY').isValid()) {
            raw_expiry_date = MOMENT(raw_expiry_date, 'Do MMM YYYY').format('YYYY-MM-DD');
        }

        return {
            recon_id: _.get(record, 'recon_id', null),
            type: _.get(notificationRecord, 'type', null),
            notif_type: _.get(record, 'notificationType', null),
            promocode: _.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.promo_code', null) : _.get(notificationData, 'options.data.promo_code', null),
            msg_type: null,
            timepoint: _.get(record, 'timepoint', null),
            templateName: _.get(notificationData, 'templateName', null),
            operator: _.toLower(_.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.operator', null) : _.get(notificationData, 'options.data.operator', null)),
            amount: _.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.amount', null) : _.get(notificationData, 'options.data.amount', null),
            service: _.toLower(_.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.service', null) : _.get(notificationData, 'options.data.service', null)),
            due_date: raw_expiry_date,
            customer_id: _.get(notificationData, 'dynamicParams', null) ? _.get(notificationData, 'dynamicParams.customer_id', null) : _.get(notificationData, 'options.data.customer_id', null),
            bill_source: 'RU'
        }
    }

    getQueryParams(data, delimiter = '&') {
        let self = this;
        try {
            let
                operator = _.toLower(_.get(data, 'operator', null)),
                amount = _.get(data, 'amount', null),
                product_id = _.get(data, 'product_id', null),
                recharge_number = _.get(data, 'recharge_number', ''),
                bank_name = _.get(data, 'bank_name', ''),
                card_network = _.get(data, 'card_network', ''),
                category_id = _.get(data, 'category_id', null),
                notificationRecord = _.get(data, 'notificationRecord', null),
                product_service = _.get(data, 'product_service', null),
                paytype = _.get(data, 'paytype', null),
                type = _.get(notificationRecord, 'type', null);

            if (_.toLower(product_service) == 'financial services') {
                let enableNewFormatMCN = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'DEEP_LINK'], 0)
                if (enableNewFormatMCN) {
                    let lastIndex = recharge_number.lastIndexOf('X');
                    recharge_number = recharge_number.substring(0, lastIndex).replace(/[0-9]/g, "X") + recharge_number.substring(lastIndex) // ensuring only last 4 digits remain in MCN
                }
                return `recharge_number=${recharge_number.replace(/ /g, '')}${delimiter}bank_name=${bank_name}${delimiter}card_network=${card_network}`;
            } else if (_.toLower(product_service) == 'paytm postpaid') {
                let aid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'AID_BY_SERVICE_PAYTYPE', `${_.toLower(product_service)}::${_.toLower(paytype)}`], '');
                let dynamicObject = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'DYNAMIC_PARAMS_BY_SERVICE_PAYTYPE', `${_.toLower(product_service)}::${_.toLower(paytype)}`], {});
                _.set(dynamicObject, ['sparams', 'product_id'], product_id);
                _.set(dynamicObject, ['sparams', 'recharge_number'], recharge_number);
                _.set(dynamicObject, ['sparams', 'price'], amount);
                return `aId=${aid}&data=${new Buffer.from(JSON.stringify(dynamicObject), 'utf-8').toString('base64')}`;
            } else {
                if (data.ignoreAmountInDeeplink || type == 'EMAIL') {
                    return `product_id=${product_id}${delimiter}price=${delimiter}recharge_number=${recharge_number}`;
                } else if (category_id === 17 && _.get(self.ignoreAmountInURLForMobilePrepaidOperators, operator, null)) {
                    // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
                    return `product_id=${product_id}${delimiter}recharge_number=${recharge_number}${delimiter}expandBrowsePlan=true`;
                } else if (_.get(data, 'is_automatic', null) == 5) {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}${delimiter}toAmount=true${delimiter}isRenewSubscription=true`;
                }
                else {
                    return `product_id=${product_id}${delimiter}price=${amount}${delimiter}recharge_number=${recharge_number}`;
                }
            }
        } catch (error) {
            self.L.error("Error in getQueryParams of data fetched: ", error, JSON.stringify(data));
            return null;
        }
    }

    getExtraRechargeNum(record, operator = '&') {
        let self = this;
        let user_data = _.get(record, "user_data", null);
        let extra_recharge_num_url = "";
        if (user_data) {
            try {
                user_data = JSON.parse(user_data);
                for (var i = 2; i < 10; i++) {
                    var key = 'recharge_number_' + i,
                        value = '';

                    if (!_.has(user_data, [key], null)) break;

                    value = _.get(user_data, [key], '');
                    extra_recharge_num_url = extra_recharge_num_url + `${operator}${key}=${value}`;
                }
            } catch (error) {
                self.L.error("Error in parsing of data fetched: ", error);
                return extra_recharge_num_url;
            }
        }

        return extra_recharge_num_url;
    }



    getParamsForChatAndPush(record, payLoad) {
        let paramsForUrl = {
            operator: _.toLower(_.get(record, 'operator', null)),
            amount: _.get(record, 'amount', null),
            product_id: _.get(record, 'product_id', null),
            recharge_number: _.get(record, 'recharge_number', ''),
            category_id: _.get(payLoad, 'category_id', null),
            bank_name: _.get(record, 'bank_name', null),
            card_network: _.get(record, 'card_network', null),
            paytype: _.get(record, 'paytype', null),
            product_service: _.get(record, 'service', null),
        };
        if (_.get(record, 'is_automatic', null) == 5) {
            _.set(paramsForUrl, 'is_automatic', _.get(record, 'is_automatic', 5));
        }
        return paramsForUrl;
    }



    decideNextDueDate(record, operator, tableName) {
        var
            self = this,
            service_id = _.get(record, 'service_id', null),
            paymentDueDate = _.get(record, 'payment_date', null);
        if (operator && service_id == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
            var
                daysToExpireReminder = _.get(self.notificationExpiryPeriod, operator, null),
                daysToNextDueDate = _.get(self.operatorsBillsConfig, [operator, 'daysToNextDueDate'], null);

            if (daysToExpireReminder && paymentDueDate && MOMENT().diff(MOMENT(paymentDueDate, 'YYYY-MM-DD 00:00:00'), 'days') >= daysToExpireReminder) {
                let params = {
                    customerId: _.get(record, 'customer_id', null),
                    rechargeNumber: _.get(record, 'recharge_number', null),
                    notificationStatus: 0
                };
                self.bills.updateNotificationStatus(function (error, data) {
                    if (error) {
                        self.L.critical('decideNextDueDate :: Unable to update Notification status for  rechargeNumber :: ' + params.rechargeNumber + " customerId :: " + params.customerId);
                    }
                }, tableName, params);
            } else if (daysToNextDueDate) {
                let params = {
                    customerId: _.get(record, 'customer_id', null),
                    rechargeNumber: _.get(record, 'recharge_number', null),
                    productId: _.get(record, 'product_id', null),
                    operator: _.get(record, 'operator', null),
                    service: _.get(record, 'service', null),
                    dueDate: MOMENT().add(daysToNextDueDate, 'days').format('YYYY-MM-DD 00:00:00')
                };
                self.bills.updateDueDateInReminder(function (error, data) {
                    if (error) {
                        self.L.critical('decideNextDueDate :: Unable to update due date for  rechargeNumber :: ' + params.rechargeNumber + " customerId :: " + params.customerId);
                    }
                }, tableName, params);
            }
        }
    }

    _utmByTemplateId(template_id, short_operator_name, operator = '&', product_service, operator_name) {
        let self = this;
        //fetch utm details by notificationType
        //utm_source, utm_medium, utm_campaign
        let utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', template_id]);
        if (!utm) {
            self.L.critical(`UTM config not found for template: ${template_id}`);
            utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', 'notFound'], {});
        }

        if (short_operator_name) {
            short_operator_name = short_operator_name.toLowerCase();
        } else if (operator_name) {
            short_operator_name = operator_name.toLowerCase();
        } else {
            short_operator_name = "default";
        }

        let utmCampaign = (product_service ? product_service.toString().toLowerCase() : "default") + "_" + short_operator_name + "_" + (template_id ? template_id : "default");
        return `${operator}utm_source=${self.getUtmParam(utm.utm_source)}${operator}utm_medium=${self.getUtmParam(utm.utm_medium)}${operator}utm_campaign=${self.commonLib.getUtmParam(utmCampaign, "_")}`;
    }

    getUtmParam(utmparam) {
        if (!utmparam) return '';
        let utmProcessed = encodeURIComponent(utmparam.replace(/[^a-zA-Z0-9 ]/g, ''));
        return utmProcessed;
    }


    suspendOperations() {
        var self = this,
            deferred = Q.defer();
        self.L.log(`billReminderNotification::suspendOperations kafka consumer shutdown initiated`);
        Q()
            .then(function () {
                self.kafkaBillFetchConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`billReminderNotification::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`billReminderNotification::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`billReminderNotification::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`billReminderNotification::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
}


export default customNotificationCreateConsumer;


// paytmmp://mobile_postpaid?url=https://digitalcatalog.paytm.com/v1/mobile/getproductlist/21?$product_id=192$recharge_number=9094807879$price=219.53$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Airtel
// curl -X POST -H 'content-type: application/json' "http://notifypanel-mum.paytm.com/v1/admin/notification/async/send" -d '{"template_type":"push","template_id":4753,"options":{"notificationOpts":{"recipients":"23716219","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://digitalcatalog.paytm.com/v1/mobile/getproductlist/21?$product_id=192$recharge_number=9094807879$price=219.53$utm_source=billDueDateReminder$utm_medium=push$utm_campaign=Airtel","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":219.53,"recharge_number":"9094807879","operator":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/1555311132380.png","category_id":21,"service":"Mobile","due_date":"5th Mar 2020","short_operator_name":"Airtel","unsubscribe_url":"https://p-y.tm/BH-pUIk"}}}' 