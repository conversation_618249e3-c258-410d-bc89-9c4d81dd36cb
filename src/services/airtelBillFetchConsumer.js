import MOMENT from 'moment'
import _ from 'lodash'
import <PERSON>Y<PERSON> from 'async'
import REQUEST from 'request'
import BILLS from '../models/bills'
import PLAN_VALIDITY from '../models/planValidity'
import BILL_SUBSCRIBER from './billSubscriber'
import utility from '../lib'
import throttledRequest from 'throttled-request'
import events from 'events'
import SCHEDULE from 'node-schedule'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import CASSANDRA_MODEL from '../models/cassandraBills';
import OS from 'os';
import EncryptorDecryptor from 'encrypt_decrypt';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class AirtelPublisher {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.bills = new BILLS(options);
        this.planValidityModel = new PLAN_VALIDITY(options);
        this.cassandraModel = new CASSANDRA_MODEL(options);
        this.dbBatchSize = 1000;
        this.servicePeriod = 2 * 60; //time in seconds
        this.tableName = 'bills_airtelprepaid'
        this.operator = 'airtel'
        this.service = 'mobile'
        this.recordsPublished = 0
        this.billSubscriber = new BILL_SUBSCRIBER(options);
        this.commonLib = new utility.commonLib(options);
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? 10 : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? 5 * 60 * 1000 : 0;
        this.infraUtils = options.INFRAUTILS;
        this.dry_run = _.get(options, 'dry_run', 0); // If dry_run = 1, dont alter/push in DB/kafka just log it
        this.publishedInSession = {};
        this.MAX_RETRY_COUNT = 3;
        this.billFetchIntervals = _.get(options.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'BILL_FETCH_INTERVAL', 'dayValueArray'], [-7, -5, -3, -2 -1, 0, 1]);
        this.lastNotificationDay = _.get(options.config ,['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'LAST_NOTIFICATION_DAY', 'dayValue'], 1); // last notificationat D + 3
        this.firstBillFetchToDueDateInterval = _.get(options.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'FIRST_BILL_FETCH_DUE_DATE_INTERVAL', 'days'], 7);
        this.daystoAddForNBFD = _.get(options.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'NEXT_BILL_FETCH_DAYS', 'days'], 2);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.cassandraDBClient = options.cassandraDbClient;
        this.cryptr = new EncryptorDecryptor();
        this.tpsForPV = 100;
        this.tps = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', `airtel_prepaid`, 'PARALLEL_BILL_FETCH_HITS'], 30);
        this.throttledRequest = new throttledRequest(REQUEST);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.throttledRequest.configure({
            requests: this.tps,
            milliseconds: 1000
        });

        this.eventEmitter = new events.EventEmitter();
        this.eventEmitter.setMaxListeners(this.tps * 2);
        this.fetchTokenLocked = false;
        this.apiTimeout = 2 * 60 * 1000; // 2 min timeout
        this.stopService = false;
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

        this.L.info('Publisher::', this.tableName, this.operator, 'initialising Publisher...');
    }

    initializeVariable(){
        this.tps = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', `airtel_prepaid`, 'PARALLEL_BILL_FETCH_HITS'], 30);
        this.billFetchIntervals = _.get(this.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'BILL_FETCH_INTERVAL', 'dayValueArray'], [-7, -5, -3, -2 -1, 0, 1]);
        this.lastNotificationDay = _.get(this.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'LAST_NOTIFICATION_DAY', 'dayValue'], 1); // last notificationat D + 3
        this.firstBillFetchToDueDateInterval = _.get(this.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'FIRST_BILL_FETCH_DUE_DATE_INTERVAL', 'days'], 7);
        this.daystoAddForNBFD = _.get(this.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'NEXT_BILL_FETCH_DAYS', 'days'], 2);

        this.throttledRequest.configure({
            requests: this.tps,
            milliseconds: 1000
        });
    }

    /*
      Function which will start the publisher for a table
    */
    start() {
        let self = this;
        self.L.info('Publisher::', self.operator, 'starting the service loop');
        ASYNC.waterfall([
            next => {
                if (self.token) { // To avoid token generation continuously
                    return next();
                } else {
                    return self.fetchToken(next);
                }
            },
            next => {
                self.billSubscriber._configureKafkaBillFetchPublisher((error) => {
                    if(error) {
                        self.L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. REMINDER_BILL_FETCH with error', error);
                    }else {
                        self.L.log('Publisher :: start', 'Kafka Publisher configured REMINDER_BILL_FETCH');
                    }
                    next(error)
                });
            },
            next => {
                self.billSubscriber._configureKafkaBillFetchPublisherRealtime((error) => {
                    if(error) {
                        self.L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. REMINDER_BILL_FETCH_REALTIME with error', error);
                    }else {
                        self.L.log('Publisher :: start', 'Kafka Publisher configured REMINDER_BILL_FETCH_REALTIME');
                    }
                    next(error)
                });
            },
            next => {
                self.billSubscriber._configureCtPublisher((error) => {
                    if(error) {
                        self.L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. CT_KAFKA with error', error);
                    }else {
                        self.L.log('Publisher :: start', 'Kafka Publisher configured CT_KAFKA');
                    }
                    next(error)
                });
            },
            next => {
                let self = this;
                self.airtelPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.AIRTEL_BILL_FETCH.HOSTS'),
                });
                self.airtelPublisher.initProducer('high', function (error) {
                    if(error) {
                        self.L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. AIRTEL_PREPAID_RECORDS with error', error);
                    }else {
                        self.L.log('Publisher :: start', 'Kafka Publisher configured AIRTEL_PREPAID_RECORDS');
                    }
                    return next(error);
                });
            },
            next => {
                /** Initialize consumer */
                self.kafkaConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.AIRTEL_BILL_FETCH.HOSTS'),
                    "groupId": "reminderAirtelBillFetchConsumer-v2",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.AIRTEL_BILL_FETCH.TOPIC'),
                    "id": `reminderAirtelBillFetchConsumer_${OS.hostname()}_${process.pid}`,
                    "fromOffset": "latest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });
                self.kafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (error) {
                        self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                    }
                    self.L.log("configureKafka", "consumer of topic : AIRTEL_PREPAID_RECORDS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.error('Error in _execSteps', error);
            }
        });
    }


    execSteps(records) {
        let self = this,
            chunkSize = 50,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;
       
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} Airtel Publisher Bills data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:AIRTEL_PREPAID_CONSUMER']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 10);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("airtelBillFetchConsumer", records);
                
                self.kafkaConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ', records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:AIRTEL_PREPAID_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    // Resume consumer now
                    setTimeout(function () {
                        self.kafkaConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    validate(callback,payload) {
        let self=this;
        if(!payload) return callback('Payload is empty');

        let publishedTime = _.get(payload, 'publishedTime', null);
        if(publishedTime){
            if(MOMENT(publishedTime).isBefore(MOMENT().startOf('day'))){
                return callback('publishedTime is not of today');
            }else{
                return callback(null);
            }
        } 
        else{
            return callback('publishedTime is not present');
        }
    }

    processBatch(records, done) {
        let
            self = this,
            data = [];

        records.forEach(row => {
            try {
                let value = _.get(row, 'value', {}),
                content = typeof value === 'string' ? JSON.parse(value) : value;
                self.validate(function (error) {
                    if (error) {
                        self.L.error('_processKafkaData:: ', `Validation failed-${error}`, 'for offset:', _.get(row, 'offset'), ` topic : ${_.get(row, 'topic')} , partition : ${_.get(row, 'partition')} , timestamp : ${_.get(row, 'timestamp')} `, 'DataObj :', JSON.stringify(content));
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:AIRTEL_PREPAID_CONSUMER", 
                            'STATUS:VALIDATION_FAILED',
                            'TYPE:PUBLISHED_TIME_OLDER',
                        ]);
                    } else{
                        data.push(content);
                    }
                }, content);
            }
            catch (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:AIRTEL_PREPAID_CONSUMER", 'STATUS:ERROR','TYPE:PARSING_ERROR']);
                self.L.error('_processKafkaData::', `Error while parsing ${error} for data ${JSON.stringify(row)}`);
            }
        });

        ASYNC.waterfall([
            (next) => {
                self.L.log("fetchRecords", `Fetched ${data.length} data from kafka`);
                let recharge_numbers = _.map(data, 'recharge_number');
                let recharge_numbers_str = [];
                for (var i = 0; i < recharge_numbers.length; i += self.tpsForPV) {
                    recharge_numbers_str.push(recharge_numbers.slice(i, i + self.tpsForPV));
                }
                self.validityMap = {}; // Resetting map to release previous map memory
                ASYNC.eachSeries(recharge_numbers_str, self.fetchValidity.bind(self), function (error) {
                    return next(error, data);
                })
            },
            (data, next) => {
                ASYNC.each(data, self._processRecords.bind(self), function (err) {
                    if (err) {
                        self.L.error('_processRecordsInBatch', `error encountered while publishing records in batch ${err}`);
                    }
                    next()
                });
            }
        ], function (error) {
            done()
        })

    }

    /*
       Going recursively for this function instead with Async lib's method, because we need to have a provision of halting the process for just a small moment, in cases when the queue is full
    */
    _processRecords(currentRecord, done) {

        // Replacing product_id with active PID
        let
            self = this,
            activePid = self.activePidLib.getActivePID(currentRecord.product_id);

        self.L.verbose('_processRecords', `Found active Pid ${activePid} against PID ${currentRecord.product_id}`);
        currentRecord.old_product_id = currentRecord.product_id; // Keeping track of original PID
        currentRecord.product_id = activePid;                // Replacing active PID

        currentRecord.traceKey = `Id:${currentRecord.id}_RN:${currentRecord.recharge_number}_custId:${currentRecord.customer_id}_pId:${currentRecord.product_id}`;;
        self.L.log('_processRecords', `Processing record for ${currentRecord.traceKey}`);

        // All the updated date needs to be updated in table will he held here
        let updatedRecord = _.clone(currentRecord);
        
        updatedRecord = self.refereshData(updatedRecord)
        ASYNC.waterfall([
            next => {
                // check if validity exists for this number by fetching it from ES
                return self.checkValidityActivationStatus(next, updatedRecord);
            },
            (activeValidity, next) => {
                if (activeValidity === true) {
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_PAID_ON_PAYTM', 137);
                    updatedRecord.reason = `ACTIVE_VALIDITY_EXISTS`;
                    return next('Active validity exists in our system');
                }

                self.L.log('_processRecords', `Active validity do not exists in our system, now going to fetch validity from Airtel API for ${updatedRecord.traceKey}`);
                //Lets cache this, to prevent ourselves from sending same bill record (rechargeNumber:ProductId combinatiion) again to gw Service
                //self.publishedInSession[`${currentRecord.recharge_number}:${currentRecord.product_id}`] = true

                return self.checkBlacklist(next, updatedRecord);
            },
            (blackListedCustomer, next)=>{
                if (blackListedCustomer === true) {
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_BLACKLISTED', 138);
                    updatedRecord.reason = `BLACKLISTED`;
                    return next('blackListedCustomer found in our system');
                }
                // Hit Airtel API and parse response
                return self.checkForParsedSms(next,updatedRecord);
            },
            (isSmsParsed, next)=>{
                if (isSmsParsed === true) {
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_SMS_PARSED', 139);
                    updatedRecord.reason = `SMS_PARSED`;
                    return next('sms parsed found in our system');
                }
                // Hit Airtel API and parse response
                return self.fetchBill(next, currentRecord, updatedRecord);
            },
            (next) => {
                // Check for notification eligibility
                if (_.get(updatedRecord, 'rePushInkafka', false) === true && _.get(updatedRecord, 'retry_count', 10) < self.MAX_RETRY_COUNT) {
                    updatedRecord.retry_count++;
                    self.publishInUpstream(function(err){
                        if(err){
                            self.handleResponse(updatedRecord, _.get(updatedRecord, 'response', null));
                            return next('Error in api and error in upstream publish')
                        }else{
                            return next(null);
                        }
                    }, updatedRecord);
                } else if(_.get(updatedRecord, 'rePushInkafka', false) === true && _.get(updatedRecord, 'retry_count', 10) >= self.MAX_RETRY_COUNT){
                    self.handleResponse(updatedRecord, _.get(updatedRecord, 'response', null));
                    return next('Error in api and max retries reached')
                } else{
                    //other cases
                    return next(null);
                }
            },
            (next) => {
                    
               
                /** Setting recon_id here only so that the same is being passed in notification pipeline as well */
                let recon_id = utility.generateReconID(updatedRecord.recharge_number , updatedRecord.operator , null , MOMENT(updatedRecord.due_date).isValid()? MOMENT(updatedRecord.due_date):null   )
                _.set(updatedRecord , 'extra.recon_id',recon_id)
                
                // Check for notification eligibility
                if (_.get(updatedRecord, 'sendNotification', false) === true) {
                    return self.sendNotification(next, updatedRecord);
                } else {
                    return next(null);
                }
            },
            (next) => {
                if (_.get(updatedRecord, 'sendNotification', false) === true) {
                    return self.publishCtEvents(next, updatedRecord);
                } else {
                    return next(null);
                }
            },
            (next) => {
                if (_.get(updatedRecord, 'status', null) === _.get(self, 'config.COMMON.bills_status.NOT_IN_USE_INVALID_RECHARGE_NUMBER', 131)) {
                    return self.updateBlacklist(next, updatedRecord);
                } else {
                    return next(null);
                }
            }
        ], (err) => {

            if (err) {
                self.L.error('_processRecords', `Error for ${currentRecord.traceKey} - ${err}`);
            }

            // Update final table data
            self.updateBillsRecord(done, updatedRecord);
        });
    }

    /**
     * Resetting record data
     * @param {*} updatedRecord 
     */
    refereshData(updatedRecord) {
        let self = this;
        updatedRecord.amount = 79; // amount =0 was creating validation faluire 

        updatedRecord.reason = '';
        try{
            let extra = updatedRecord.extra ? JSON.parse(updatedRecord.extra) : {};
            updatedRecord.extra = extra;
        }catch(err){
            self.L.info("refreshData","Error while parsing extra")
            updatedRecord.extra = {}
        }
        return updatedRecord;
    }

    /**
     * Fetch validity for all records.recharge_number
     * @param {*} done 
     * @param {*} records 
     */
    fetchValidity(recharge_numbers_str, done) {
        let self = this;

        self.planValidityModel.getValidity(function (error, data) {
            self.L.verbose('fetchValidity', data);

            if (error) {
                self.L.critical('fetchValidity', error);
            } else {
                if (data && data.length > 0) {
                    data.forEach(row => {
                        if (_.get(row, 'operator') == self.operator && _.get(row, 'service') == self.service) {
                            self.validityMap[_.get(row, 'recharge_number')] = true;
                        }
                    });
                }
            }

            self.L.verbose('fetchValidity', 'validityMap', self.validityMap);
            return done(error);
        }, recharge_numbers_str);
    }

    /**
     * Fetch latest recharge from ES for queried number
     * @param {*} done 
     * @param {*} currentRecord 
     * @param {*} updatedRecord 
     */

    checkValidityActivationStatus(done, updatedRecord) {
        let self = this,
            rechargeNumber = _.get(updatedRecord, 'recharge_number');

        if (self.validityMap[rechargeNumber] === true) {
            self.L.log('checkValidityActivationStatus', `Active validity exists for : ${_.get(updatedRecord, 'traceKey')}`);
            return done(null, true);
        } else {
            self.L.log('checkValidityActivationStatus', `Active validity do not exists for : ${_.get(updatedRecord, 'traceKey')}`);
            return done(null, false);
        }
    }


    /**
     * Take decision based on current response
     * @param {*} updatedRecord 
     * @param {*} response 
     */
    handleResponse(updatedRecord, response) {
        let self = this,
            currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
            prevDate = MOMENT().startOf('day').add(-self.daystoAddForNBFD, 'days').format('YYYY-MM-DD'),
            prevResponse = _.get(updatedRecord, ['extra','last_response', prevDate], null),
            extra = _.clone(_.get(updatedRecord, 'extra', null)), // Using it for checking First time Fetch
            status = _.get(updatedRecord, 'status', null),
            next_bill_fetch_date = _.get(updatedRecord, 'next_bill_fetch_date', null),
            isNbfdToday = MOMENT().startOf('day').isSame(next_bill_fetch_date, 'day');

            updatedRecord.extra.last_response= { [currentDate]: response };
            

            if(status == _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)){
                updatedRecord.next_bill_fetch_date = self.addDuration(self.daystoAddForNBFD)
                if(prevResponse == true && response == true){
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                    updatedRecord.reason = `1-1 True to true`
                    updatedRecord.sendNotification = true;
                    updatedRecord.templates = self.getTemplates('GENERIC');
                } 
                else if (prevResponse == false && response == false){
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                    updatedRecord.reason = `1-1 False to false`
                }
                else if(prevResponse == true && response == false){
                    /** True to false transition i.e user has just recharged */
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14)
                    updatedRecord.payment_date = MOMENT().format('YYYY-MM-DD HH:mm:ss') // to confirm
                    updatedRecord.next_bill_fetch_date = self.addDuration(_.get(self.config,['DYNAMIC_CONFIG','AIRTEL_PREPAID_CONFIG','DAYS_TO_ADD_IN_NBFD_AFTER_PAYMENT','days'],20))
                    updatedRecord.reason = `1-14 True to false transition i.e Users has recharged there`
                    updatedRecord.sendNotification = true;
                    updatedRecord.templates = self.getTemplates('RECHARGE_DONE');
                    _.set(updatedRecord, ['extra', 'last_transition'], currentDate);

                }
                else if(prevResponse == false  && response == true){
                    /** False to true transition i.e Users plan has expired there  */
                    updatedRecord.bill_fetch_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                    updatedRecord.due_date = self.addDuration(self.firstBillFetchToDueDateInterval);
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
                    updatedRecord.reason = `1-4 False to true transition i.e Users plan has expired there`
                    _.set(updatedRecord, ['extra', 'last_transition'], currentDate);
                    this.setNextBillFetchDateForSuccessResponse(updatedRecord)
                }
                else if(response == null){
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PENDING', 0);
                    updatedRecord.reason = `1-0 Error case`
                }
                else if(prevResponse == null && response == true){
                    updatedRecord.sendNotification = true;
                    updatedRecord.templates = self.getTemplates('GENERIC');
                    updatedRecord.status= _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)   
                    updatedRecord.reason = `1-1 null to true`       
                }
                else if(prevResponse == null && response == false){
                    updatedRecord.status= _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)  
                    updatedRecord.reason = `1-1 null to false`           
                }
            }
            else if(status == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)){ //false-true transition happened                
                if(isNbfdToday && response == false){
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14)
                    updatedRecord.payment_date = MOMENT().format('YYYY-MM-DD HH:mm:ss') // to confirm
                    updatedRecord.next_bill_fetch_date = self.addDuration(_.get(self.config,['DYNAMIC_CONFIG','AIRTEL_PREPAID_CONFIG','DAYS_TO_ADD_IN_NBFD_AFTER_PAYMENT','days'],20))
                    updatedRecord.reason = `4-14 True to false transition i.e Users has recharged there`
                    _.set(updatedRecord, ['extra', 'last_transition'], currentDate);
                    updatedRecord.sendNotification = true;
                    updatedRecord.templates = self.getTemplates('RECHARGE_DONE');

                }
                else if(response==true){ 
                    this.setNextBillFetchDateForSuccessResponse(updatedRecord)
                    updatedRecord.reason = `4-4 True to true with known duedate`
                } else {
                    updatedRecord.next_bill_fetch_date = self.addDuration(self.daystoAddForNBFD)
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                    updatedRecord.reason = `4-1 True to published (error case or missed nbfd + false case) with known duedate`
                }
            }
            else if(status == _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14)){
                if(isNbfdToday && response==true){
                    updatedRecord.bill_fetch_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                    updatedRecord.due_date = self.addDuration(self.firstBillFetchToDueDateInterval);
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
                    updatedRecord.reason = `14-4 False to true transition i.e Users plan has expired there`
                    _.set(updatedRecord, ['extra', 'last_transition'], currentDate);
                    this.setNextBillFetchDateForSuccessResponse(updatedRecord)
                }
                else {
                    updatedRecord.next_bill_fetch_date = self.addDuration(self.daystoAddForNBFD)
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                    updatedRecord.reason = `14-1 False to published (error case or missed nbfd + true case)`
                }
            }
            else{
                updatedRecord.next_bill_fetch_date = self.addDuration(self.daystoAddForNBFD)
                if(response== true){
                    updatedRecord.sendNotification = true;
                    updatedRecord.templates = self.getTemplates('GENERIC');
                    updatedRecord.reason = `${_.get(updatedRecord, 'status',null)}-1 first bill fetch`
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                } 
                else if (response==false){
                    updatedRecord.reason = `${_.get(updatedRecord, 'status',null)}-1 first bill fetch`
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1)
                }
                else if(response==null){
                    updatedRecord.reason = `${_.get(updatedRecord, 'status',null)}-0 first bill fetch with error`
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PENDING', 0)
                }
            } 



            /*
            //31oct

            last transisition: 20oct
            last response: {
                29oct: true,
            }

            //prevResponse -> 29oct
            //extra={

            }

         * Status Meanings
         * 4 - Users whose validity is expired and due date is known , we have to send them notification
         * 
         * 14 - Users who have undergone a true->false transition and we know that they have recharged somewhere
         * 
         * 1 - Users for which we know the previous day(D-2) status and they have not undergone a true->false or false->true transition yet
         * 
         * 
         * 0 - New users or  users whose previous Day(D-2) status is not known
         * 
         * 
         * 
            */
    }

    addDuration(days) {
        return MOMENT().add(days, 'days').format('YYYY-MM-DD 00:00:00');
    }

   

    setNextBillFetchDateForSuccessResponse(updatedRecord) {
        let self = this,
            dueDate = MOMENT(updatedRecord.due_date).startOf('day'), 
            currDate_dueDate_diff = MOMENT().startOf('day').diff(dueDate.startOf('day'), 'days'), 
            daysToAdd = self.daystoAddForNBFD;

        for (let i in self.billFetchIntervals) { //[-6, -3, -1, 0, 1, 3];
            if (self.billFetchIntervals[i] > currDate_dueDate_diff) {
                daysToAdd = self.billFetchIntervals[i];
                break;
            }
        }


        if (self.billFetchIntervals.indexOf(currDate_dueDate_diff) > -1) {
            updatedRecord.sendNotification = true;
            updatedRecord.templates = self.getTemplates(currDate_dueDate_diff);
        }

        // End of bill fetch after D+2 
        if (currDate_dueDate_diff > self.lastNotificationDay) {
            updatedRecord.sendNotification = true;
            updatedRecord.templates = self.getTemplates('GENERIC');
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1);
        } else {
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);
            updatedRecord.next_bill_fetch_date = MOMENT(updatedRecord.due_date).add(daysToAdd, 'days').format('YYYY-MM-DD 00:00:00');
        }
    }

    getTemplates(templateType) {
        let self = this;
        return {
            PUSH: _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'TEMPLATE_BY_AIRTEL_KEY', `AIRTEL_PREPAID_TEMPLATE_${templateType}_PUSH`], null),
            SMS: _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'TEMPLATE_BY_AIRTEL_KEY', `AIRTEL_PREPAID_TEMPLATE_${templateType}_SMS`], null),
            EMAIL: _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'TEMPLATE_BY_AIRTEL_KEY', `AIRTEL_PREPAID_TEMPLATE_${templateType}_EMAIL`], null),
            CHAT: _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'TEMPLATE_BY_AIRTEL_KEY', `AIRTEL_PREPAID_TEMPLATE_${templateType}_CHAT`], null),
            WHATSAPP: _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'TEMPLATE_BY_AIRTEL_KEY', `AIRTEL_PREPAID_TEMPLATE_${templateType}_WHATSAPP`], null)
        };
    }

    publishInUpstream(done, updatedRecord) {
        let
            self = this;
        if(updatedRecord.rePushInkafka) delete updatedRecord.rePushInkafka;
        self.airtelPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.AIRTEL_BILL_FETCH.TOPIC'),
            messages: JSON.stringify(updatedRecord)
        }], function (error) {
            if (error) {
                self.L.critical('sendNotification :: AIRTEL_BILL_FETCH', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(updatedRecord), error);
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.AIRTEL_BILL_FETCH.TOPIC'),
                    'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                    'OPERATOR:AIRTEL_PREPAID'
                ])
            } else {
                self.L.log('sendNotification :: AIRTEL_BILL_FETCH', 'Message published successfully in Kafka', ' on topic AIRTEL_BILL_FETCH', JSON.stringify(updatedRecord));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.AIRTEL_BILL_FETCH.TOPIC'),
                    'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                    'OPERATOR:AIRTEL_PREPAID'
                ])
            }
            return done(error);
        }, [200, 800]);
    }

    sendNotification(done, updatedRecord) {
        let
            self = this,
            toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
            let payload = {
                source: toBeNotifiedRealtime?"airtelBillFetchRealtime": "airtelBillFetch",
                notificationType: "DUEDATE",
                data: self.commonLib.mapBillsTableColumns(updatedRecord)
            };

        // Setting status = 4 because in case of T+2 notification we are updating status=13 in table, In this case next pipe line will discard this record. To avoid this we are setting status=4 explicitly
        _.set(payload, 'data.status', 4);

        if (self.dry_run == 1) {
            self.L.log('dry_run :: sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
            return done();
        }

        if(toBeNotifiedRealtime){
            utility.sendNotificationMetricsFromSource(payload)
            self.billSubscriber.billFetchKafkaRealtime.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                messages: JSON.stringify(payload)
            }], function (error) {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    self.L.critical('sendNotification :: billFetchKafkaRealtime', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    utility._sendMetricsToDD(1, [
                        'STATUS:PUBLISH_ERROR',
                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                        'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                        'OPERATOR:AIRTEL_PREPAID'
                    ])
                } else {
                    self.L.log('sendNotification :: billFetchKafkaRealtime', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
                    utility._sendMetricsToDD(1, [
                        'STATUS:PUBLISHED',
                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                        'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                        'OPERATOR:AIRTEL_PREPAID'
                    ])
                }
                return done(error);
            }, [200, 800]);
        }else{
            utility.sendNotificationMetricsFromSource(payload)
            self.billSubscriber.kafkaBillFetchPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                messages: JSON.stringify(payload)
            }], function (error) {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    self.L.critical('sendNotification :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    utility._sendMetricsToDD(1, [
                        'STATUS:PUBLISH_ERROR',
                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                        'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                        'OPERATOR:AIRTEL_PREPAID'
                    ])
                } else {
                    self.L.log('sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                    utility._sendMetricsToDD(1, [
                        'STATUS:PUBLISHED',
                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                        'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                        'OPERATOR:AIRTEL_PREPAID'
                    ])
                }
                return done(error);
            }, [200, 800]);
        }
    }

    /**
     * Update Record in Table
     */
    updateBillsRecord(done, updatedRecord) {
        let self = this;
        let tableName = _.get(updatedRecord, 'tableName', null)

        let reason = _.get(updatedRecord, 'reason', {});
        if (reason && reason.length && reason.length > 0) {
            reason = reason.split(' ')[0];
        }
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_RESULT', `RESULT:${reason}`]);

    
        _.set(updatedRecord, 'extra.user_type', 'NON_RU');
        _.set(updatedRecord, 'extra.source_subtype_2', 'PARTIAL_BILL');
        _.set(updatedRecord, 'extra.updated_data_source', 'AIRTEL_PREPAID_BILL_FETCH');
        self.bills.updateAirtelRecord(err => {
            if (err) {
                self.L.critical('updateBillsRecord', `error occurred while updating record for ${updatedRecord.traceKey}`, err);
            }
            return done();
        }, tableName, {
            id: updatedRecord.id,
            status: _.get(updatedRecord, 'status', 0),
            retryCount: 0,
            billDueDate: MOMENT(updatedRecord.due_date).isValid()? MOMENT(updatedRecord.due_date).format('YYYY-MM-DD HH:mm:ss'):null,
            operator: updatedRecord.operator,
            service: updatedRecord.service,
            nextBillFetchDate: MOMENT(updatedRecord.next_bill_fetch_date).isValid() ? MOMENT(updatedRecord.next_bill_fetch_date).format('YYYY-MM-DD HH:mm:ss'): MOMENT().add(self.daystoAddForNBFD, 'days').format('YYYY-MM-DD HH:mm:ss'),
            paymentDate : MOMENT(updatedRecord.payment_date).isValid() ?  MOMENT(updatedRecord.payment_date).format('YYYY-MM-DD HH:mm:ss'):null, 
            billFetchDate: MOMENT(updatedRecord.bill_fetch_date).isValid() ? MOMENT(updatedRecord.bill_fetch_date).format('YYYY-MM-DD HH:mm:ss'):MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            extra: JSON.stringify(_.get(updatedRecord, 'extra', {})),
            reason: _.get(updatedRecord, 'reason', ''),
            publishedDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            customerOtherInfo: JSON.stringify(_.get(updatedRecord, 'customerOtherInfo', {})).slice(0, 200),
            traceKey: updatedRecord.traceKey
        }, self.dry_run, true);
    }

    /**
     * Fetch JWT Token from Airtel API
     * @param {*} done 
     */
    fetchToken(done) {
        let self = this,
            apiOpts = {
                url: 'https://zapi.airtel.in/as/airtel-oauth/v1/oauth/token',
                method: 'POST',
                timeout: self.apiTimeout,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `username=${_.get(self.config, ['AIRTEL_PUBLISHER_CONFIG', 'FETCH_TOKEN_API', 'username'], '')}&password=${_.get(self.config, ['AIRTEL_PUBLISHER_CONFIG', 'FETCH_TOKEN_API', 'password'], '')}&client_id=PAYTM&grant_type=access_token`  /** VAULT integration required  */
            };
        // Locking it to have singleton behaviour
        self.fetchTokenLocked = true;

        var latencyStart = new Date().getTime();
        REQUEST(apiOpts, function (error, response, body) {

            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'AIRTEL_PREPAID_FETCH_TOKEN',
                'URL': _.get(apiOpts, 'url', null)
            });
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_FETCH_TOKEN', `STATCODE:${_.get(response, 'statusCode', _.get(error, 'code', '5XX'))}`]);
        // let error=null,response={statusCode:200},body={token: "abc"} //for local testing

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    self.L.error("fetchToken", `Error parsing token response body`, e);
                    body = null;
                }
            }

            if (response && _.get(response, 'statusCode') == 200 && body && _.get(body, 'token', null)) {
                self.token = _.get(body, 'token', null);
                self.L.log('fetchToken', `Token received successfully...`);
                self.L.verbose('fetchToken', `Token received : ${self.token}`);

                self.L.log('Emitting token fetched event');
                self.eventEmitter.emit('TOKEN_FETCHED');
                self.fetchTokenLocked = false;
                return done(null);
            } else {
                self.L.critical('fetchToken', `Request failed with statusCode:${_.get(response, 'statusCode')},error-${error},body-${JSON.stringify(body)}`);
                setTimeout(function () {
                    self.L.log('fetchToken', 'Retrying to fetch token...');
                    return self.fetchToken(done);
                }, 1000);
            }
        });
    }

    checkForParsedSms(done,updatedRecord){
        let self=this;
        self.L.log('checkForParsedSms', `Checking for parsed sms for ${updatedRecord.traceKey}`);
        try{
            let recharge_number = self.cryptr.encrypt(_.get(updatedRecord, 'recharge_number',null));
            self.cassandraModel.getDataFromNonPaytm(function (error, data) {
                if (error) {
                    self.L.critical('checkForParsedSms', error);
                    return done(null, false);
                } else {
                    if (data && data.length > 0) {
                        self.L.log('checkForParsedSms', `Parsed sms found in cassandra for ${updatedRecord.traceKey}`);
                        return done(null, true);
                    } else {
                        self.L.log('checkForParsedSms', `Parsed sms do not exists in cassandra for ${updatedRecord.traceKey}`);
                        return done(null, false);
                    }
                }
            }, {
                recharge_number: recharge_number,
                customer_id: _.get(updatedRecord, 'customer_id',null),
                operator: _.get(updatedRecord, 'operator',null),
                service: _.get(updatedRecord, 'service',null)
            })
        }catch(e){
            self.L.error('checkForParsedSms', `Error in encrypting recharge_number for ${updatedRecord.traceKey}`);
            return done(null, false);
        }
    }

    updateBlacklist(done, updatedRecord) {
        let self = this;
        self.L.log('updateBlacklist', `Updating blacklist for ${updatedRecord.traceKey}`);
        self.cassandraModel.updateBlacklist(function (error) {
            if (error) {
                self.L.critical('updateBlacklist', error);
            } else {
                self.L.log('updateBlacklist', `Blacklist updated successfully for ${updatedRecord.traceKey}`);
            }
            return done(error);
        }, updatedRecord.recharge_number);
    }

    checkBlacklist(done, updatedRecord) {
        let self = this;
        self.L.log('checkBlacklist', `Checking RN in cassandra for ${updatedRecord.traceKey}`);
        self.cassandraModel.getRNFromBlacklist(function (error, data) {
            if (error) {
                self.L.critical('checkBlacklist', error);
                return done(null, false);
            } else {
                if (data && data.length > 0) {
                    self.L.log('checkBlacklist', `RN found in cassandra for ${updatedRecord.traceKey}`);
                    return done(null, true);
                } else {
                    self.L.log('checkBlacklist', `RN do not exists in cassandra for ${updatedRecord.traceKey}`);
                    return done(null, false);
                }
            }
        }   , updatedRecord.recharge_number);
    }

    /**
     * 
     * @param {*} done 
     * @param {*} currentRecord 
     * @param {*} updatedRecord 
     */
    fetchBill(done, currentRecord, updatedRecord) {
        let
            self = this,
            apiOpts = {
                url: `https://zapi.airtel.in/s/app/wl-service/airtel-mobility-recharge/prepaid/v1/3P/recharge?siNumber=${currentRecord.recharge_number}&liteRequest=true`,
                timeout: self.apiTimeout,
                headers: {
                    'authorization': `Bearer ${self.token}`
                }
            };

        self.L.verbose("fetchBill", `apiOpts for ${updatedRecord.traceKey}`, JSON.stringify(apiOpts));

        var latencyStart = new Date().getTime();

        self.throttledRequest(apiOpts, (error, response, body) => {
            // let error=null,response={statusCode:200},body={airtelPrepaid:true,expiryFlag:true} //for local testing
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'AIRTEL_PREPAID_FETCH_BILL'
            });

            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_FETCH_BILL', `STATCODE:${_.get(response, 'statusCode', _.get(error, 'code', '5XX'))}`]);

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    self.L.error("fetchBill", `Error parsing data for ${currentRecord.traceKey}`, e);
                    return done('Parsing Error');
                }
            }
            if (response && _.get(response, 'statusCode') == 200 && body) {
                if (_.get(body, 'airtelPrepaid', true) === false) {
                    // Number is not airtel prepaid...removing it
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_INVALID_RECHARGE_NUMBER', 131);
                    updatedRecord.reason = 'INVALID_PREPAID_NUMBER: Invalid Airtel prepaid Number';
                } else if (_.get(body, 'expiryFlag') === true || _.get(body, 'expiryFlag') === false) { // This is valid response else error case
                    self.handleResponse(updatedRecord, _.get(body, 'expiryFlag'));
                } else {
                    updatedRecord.reason = 'FETCH_BILL_API_RESPONSE_ERROR: Failed to get Plan Expiry';
                    updatedRecord.rePushInkafka = true;
                }

                updatedRecord.customerOtherInfo = body; // save response
                self.L.log('fetchBill', `Plan Validity Response ${JSON.stringify(body)} for ${currentRecord.traceKey}`);
                return done(error);
            } else if (response && (_.get(response, 'statusCode') == 401 || _.get(response, 'statusCode') == 403)) { // Invalid token

                if (self.fetchTokenLocked === true) { // fetch token already in progress...wait for the event 
                    self.L.error('fetchBill', `Token Expired....Waiting for token to be generated for ${updatedRecord.traceKey}...`);

                    self.eventEmitter.on('TOKEN_FETCHED', function retryFetchBill() {
                        self.L.log('fetchBill', `Token fetched TOKEN_FETCHED event received...processing record again for ${updatedRecord.traceKey}`);
                        self.eventEmitter.removeListener('TOKEN_FETCHED', retryFetchBill);
                        return self.fetchBill(done, currentRecord, updatedRecord);
                    });
                } else {
                    self.L.error('fetchBill', `Token Expired....Getting new token ${updatedRecord.traceKey}`);
                    self.fetchToken(function (error) {
                        if (error) {
                            self.L.error('fetchBill', `Error while fetching token for ${updatedRecord.traceKey}`);
                            updatedRecord.reason = 'FETCH_BILL_API_RESPONSE_ERROR: Failed to get Plan Expiry';
                            updatedRecord.rePushInkafka = true;
                            return done();
                        } else {
                            self.L.log('fetchBill', `Token fetched...processing record again for ${updatedRecord.traceKey}`);
                            return self.fetchBill(done, currentRecord, updatedRecord);
                        }
                    });
                }
            } else {
                updatedRecord.reason = `FETCH_BILL_API_ERROR`;
                updatedRecord.rePushInkafka = true;                
                self.L.error('fetchBill', `Request failed with statusCode:${_.get(response, 'statusCode')},error-${error},body-${JSON.stringify(body)}`);
                return done();
            }
        });
    }

    promoteForRetry(updatedRecord) {
        let self = this;
        updatedRecord.next_bill_fetch_date = MOMENT().add(2, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
    }


    /**
     * 
     * @param {*} done 
     * @param {*} dbRecordResp 
     */
    publishCtEvents(done, dbRecordResp) {
        let self = this;

        const customerId = _.get(dbRecordResp, 'customer_id', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
        const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
        const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

        if(self.commonLib.isCTEventBlocked(eventName)){
            self.L.info(`Blocking CT event ${eventName}`)
            return done()
        }

        let productId = _.get(dbRecordResp, 'product_id', '');
        productId = self.activePidLib.getActivePID(productId);

        // reminder util expects customerOtherInfo as JSON string
        let customerOtherInfo = _.get(dbRecordResp, 'customerOtherInfo', '');
        customerOtherInfo = customerOtherInfo ? JSON.stringify(customerOtherInfo) : {};
        _.set(dbRecordResp, 'customerOtherInfo', customerOtherInfo)

        if (!_.get(dbRecordResp, 'notification_status', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            return done(null)
        }
        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if (error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if (error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                self.billSubscriber.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AIRTEL_PREPAID", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.critical('publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AIRTEL_PREPAID", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator,`EVENT_NAME:${eventName}`]);
                        self.L.log('publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ], error => {
            if (error) {
                self.L.error('publishCtEvents', `Exception occured Error Msg:: ${error} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtEvents`, `Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`airtel bill fetch consumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.kafkaConsumer.close(function(error, res){
                if(error){
                    self.L.error(`airtel bill fetch consumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`airtel bill fetch consumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`airtel bill fetch consumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`airtel bill fetch consumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default AirtelPublisher