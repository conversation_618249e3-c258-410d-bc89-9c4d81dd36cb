const crypto = require('crypto');
const _ = require('lodash');
class AES256Encryption {
    
  constructor(options) {
    this.config = _.get(options,'config');
    this.key = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'
    this.algorithm = 'aes-256-ctr';
    // this.iv = Buffer.from(_.get(this.config, ['ENCRYPTION_CONFIG', 'DEFAULT', 'IV'], ''), 'hex');
    this.iv = Buffer.from('68e232d2639555a0cf08aaed9d50a025', 'hex');
  }
  encrypt(text) {
    if (text === null || text === undefined) {
      return text;
    }
    if (typeof text !== 'string') {
      text = JSON.stringify(text);
    }
    const cipher = crypto.createCipheriv(this.algorithm, this.key, this.iv);
    const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);
    return encrypted.toString('base64');
  }
  
  decrypt(encryptedText) {
    try {
      if (typeof encryptedText !== 'string' || !encryptedText) {
        return encryptedText;
      }

      const decipher = crypto.createDecipheriv(this.algorithm, this.key, this.iv);
      const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptedText, 'base64')),
        decipher.final()
      ]);

      return decrypted.toString();
    } catch (error) {
      console.error('Decryption error:', error.message);
      return null;
    }
  }
}

module.exports = AES256Encryption;