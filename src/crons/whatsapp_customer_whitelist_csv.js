import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
let serviceName = "WHATSAPP_CUSTOMER_WHITELIST_INGESTOR" 
let progressFilePath = `/var/log/digital-reminder/whatsapp_whitelist_csv_ingestor.json`
let progressTracker = {}
let todayDate = null;
let whatsappWhitelistIngestorProducer = null;

class WHATSAPP_CUSTOMER_WHITELIST_INGESTOR {

    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress)
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],5)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.infraUtils = options.INFRAUTILS;
        this.productidMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {};
        this.currentFile=""
        todayDate = MOMENT().format('YYYY-MM-DD');
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG',serviceName,'path','value'],`digital-reminder/WHATSAPP_WHITELIST_INGEST/`)}${todayDate}`
        this.files = []
    }
    getProgressObject(){
        let self = this;
        let progress = {}
        progressFilePath = `/var/log/digital-reminder/whatsapp_whitelist_csv_ingestor-${MOMENT().format('YYYY-MM')}.json`
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
            // progress = progress[todayDate] ? progress[todayDate] : {};
        }
        this.L.info("Loaded",progress)
        return progress
    } 
    updateProgress(filename ,count ){
        let self = this;
        if(_.get(progressTracker , [todayDate,filename] , 0 ) == -1)return;
        _.set(progressTracker , [todayDate,filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker[todayDate]),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }


    start(callback){
        let self = this;
        progressTracker = this.getProgressObject()
        try{
            this.csvIngester.configure(this.bucketName, this.logPrefix,this.batchSize)
        }catch(error){
            self.L.critical(this.logPrefix , "Cannot initialize AWS")
            return callback(error)
        }
        self.L.info("Getting Files in the folder")
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:PROCESSING', `TYPE:CRON_TRIGGERED`]); 
       
        // eachSeries(data , self.processEachFile.bind(self) , callback)
        self.startKafkaProducer((error) => {
            if(error) {
                return callback(error);
            }  
            this.csvIngester.getFileNames(this.folderPath,function(err , data){
                if(err){
                    self.L.error("Error while getting files", err);
                    return callback(err)
                }else{
                    // data = _.filter(data , self.filterFileByMonth)
                    return eachSeries(data , self.processEachFile.bind(self) , callback)
                }
            })
        })
      
    }

    startKafkaProducer(cb){
        let self = this;
        whatsappWhitelistIngestorProducer = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
        });
        self.L.log("whatsappWhitelistIngestorProducer :: whatsappWhitelistIngestorProducer KAFKA PRODUCER STARTED");
        whatsappWhitelistIngestorProducer.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising whatsappWhitelistIngestorProducer :: ', error);
            self.L.log("whatsappWhitelistIngestorProducer :: whatsappWhitelistIngestorProducer KAFKA PRODUCER STARTED....");
            return cb(error);
        });
        // cb();
    }
    processEachFile(filename , callback){
        let self = this;
        if(_.get(progressTracker , [todayDate, filename], null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:PROCESSING', `TYPE:FILE_RECEIVED`]); 
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [todayDate, filename], 0) 
        this.csvIngester.start(this.processRecords.bind(this, filename),filename , function(error , data){
            return callback()
        },skipRows)
    }

    async processRecordinBatch( filename, data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecords(function(){
                    cb()
                } ,record, filename)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }

    async processRecords(fileName, data) {
        let self=this;
    
        return new Promise((resolve, reject) => {    
            let recordsToPublish= [];
        
            for(let params of data) {    
                let records = JSON.stringify(params);
                recordsToPublish.push(records);
                self.L.info("processRecords:: ","record processed:",records);
            }
            if(recordsToPublish.length == 0) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR_PROCESSING_EMPTY_ARRAY", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                self.L.critical('publishInKafka :: WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', `fileName: ${fileName}`,'Error: PROCESSING EMPTY ARRAY. ArraySize:' + recordsToPublish.length);
                resolve();
            }else{
                whatsappWhitelistIngestorProducer.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.WHATSAPP_CUSTOMER_WHITELIST_INGESTOR.TOPIC', ''),
                    messages: recordsToPublish
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR_KAFKA_PUBLISH", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                        self.L.critical('publishInKafka :: WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', `fileName: ${fileName}`,'Error while publishing message in Kafka - MSG:- ' + recordsToPublish.length, error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:PUBLISHED', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_INGESTOR_KAFKA_PUBLISH", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]); 
                        self.L.log('publishInKafka :: WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', `fileName: ${fileName}`, 'Message published successfully in Kafka', ' on topic WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', recordsToPublish.length);
                    }
                    resolve();
                }, [200, 800]);
                resolve();
            }
        })    
        
    }

}

    (function main() {
        if (require.main === module) {
            startup.init({
                  
            }, function (err, options) {
                let script;
                try{
                script = new WHATSAPP_WHITELIST_CSV_INGEST(options);
                script.start(
                    function (err) {
                        setTimeout(function () {
                            if (err) {
                                console.log("main::Error" + err);
                                process.exit(1);
                            } else {
                                console.log("main::completed");
                                process.exit(0);
                            }
                        }, 1000);
                    })
                }catch(err){
                    options.L.error(err)
                    process.exit(1)
                }
              
            });
        }
    })();
    export default WHATSAPP_CUSTOMER_WHITELIST_INGESTOR    