/*
    Author: <PERSON><PERSON><PERSON>
    to start the cron locally execute following command
    VAULT=0 node dist/app.js --userScoreCSVIngest
*/

import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
let serviceName = "USER_SCORE_CSV_INGEST" 
let progressFilePath = `/var/log/digital-notification/progress-user-score-ingestor.json`
let progressTracker = {}
let todayDate = null;
let userScoreIngestorProducer = null;
/** Maintain offset and processed files in a path */
class USER_SCORE_CSV_INGEST {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress)
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],5)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.infraUtils = options.INFRAUTILS;
        this.productidMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {};
        this.currentFile=""
        todayDate = MOMENT().format('YYYY-MM-DD');
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG','USER_SCORE_CSV_INGEST','path','value'],`digital-reminder/USER_SCORE_INGEST/`)}${todayDate}` 
        this.files = []
        this.isScoringDisabled = _.get(options.config , ['DYNAMIC_CONFIG','USER_SCORE_CSV_INGEST','COMMON','IS_DISABLED'],0);
    }
    getProgressObject(){
        let self = this;
        let progress = {}
        progressFilePath = `/var/log/digital-notification/progress-user-score-ingestor-${MOMENT().format('YYYY-MM')}.json`
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
            // progress = progress[todayDate] ? progress[todayDate] : {};
        }
        this.L.info("Loaded",progress)
        return progress
    }   
    updateProgress(filename ,count ){
        let self = this;
        if(_.get(progressTracker , [todayDate,filename] , 0 ) == -1)return;
        _.set(progressTracker , [todayDate,filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker[todayDate]),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }
    // filterFileByMonth(filename){
    //     try{
    //        let date = filename.split('$')[1].split('.')[0].slice(0,7)
    //        if(date == MOMENT().format('YYYY-MM'))return true
    //     }catch(err){
    //         return false
    //     }
    //     return false
    // }
    start(callback){
        let self = this;
        if(self.isScoringDisabled){
            //Disabled due to prepaid reminders usecases, need to handle these cases in future before enabling.
            //-- https://wiki.mypaytm.com/display/RECHBP/Low+Balance+Reminder+-+Prepaid+Identification
            self.L.info("Scoring is disabled due to prepaid reminders usecases");
            callback();
        } else {
            progressTracker = this.getProgressObject()
            try{
                this.csvIngester.configure(this.bucketName, this.logPrefix,this.batchSize)
            }catch(error){
                self.L.critical(this.logPrefix , "Cannot initialize AWS")
                return callback(error)
            }
            self.L.info("Getting Files in the folder")
           
            // eachSeries(data , self.processEachFile.bind(self) , callback)
            self.startKafkaProducer((error) => {
                if(error) {
                    return callback(error);
                }  
                this.csvIngester.getFileNames(this.folderPath,function(err , data){
                    if(err){
                        self.L.error("Error while getting files", err);
                        return callback(err)
                    }else{
                        // data = _.filter(data , self.filterFileByMonth)
                        return eachSeries(data , self.processEachFile.bind(self) , callback)
                    }
                })
            })
        }
      
    }

    startKafkaProducer(cb){
        let self = this;
        userScoreIngestorProducer = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
        });
        self.L.log("userScoreIngestorProducer :: userScoreIngestorProducer KAFKA PRODUCER STARTED");
        userScoreIngestorProducer.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising userScoreIngestorProducer :: ', error);
            self.L.log("userScoreIngestorProducer :: userScoreIngestorProducer KAFKA PRODUCER STARTED....");
            return cb(error);
        });
        // cb();
    }

    processEachFile(filename , callback){
        let self = this;
        if(_.get(progressTracker , [todayDate, filename], null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [todayDate, filename], 0) 
        this.csvIngester.start(this.processRecords.bind(this, filename),filename , function(error , data){
            return callback()
        },skipRows)
    }
    async processRecordinBatch( filename, data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecords(function(){
                    cb()
                } ,record, filename)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }
    
    async processRecords(fileName, data) {
        let self=this;
    
        return new Promise((resolve, reject) => {    
            let recordsToPublish= [];
        
            for(let params of data) {    
                self.L.info("processRecords:: ","processing record:",JSON.stringify(params));
                let cust_id = Number(_.get(params,'user_id',null)),
                    category    = _.get(params,'category',null),
                    paytype     = _.get(params,'paytype',null),
                    score       = parseFloat(_.get(params,'score',null));

                if(!cust_id || !category || _.isEmpty(category) || !paytype || _.isEmpty(paytype) || (!score && score !=0)) {
                    self.L.error("processRecords:: ","error while processing record:",JSON.stringify(params));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:ERROR', "TYPE:USER_SCORE_CSV_INGESTOR_PAYLOAD_PROCESSING", `SERVICE:${category}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                    continue;
                }

                if(category.toLowerCase() == "ru" && _.get(self.config , ['DYNAMIC_CONFIG','USER_SCORE_CSV_INGEST','SKIP_RU_CATEGORY','value'],1) == 1) {
                    self.L.info("processRecords:: ","skipping processing record:",JSON.stringify(params));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:ERROR', "TYPE:INVALID_CATEGORY_RU", `SERVICE:${category}`,`FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                    continue;
                }
            
                let finalRecord = {
                    cust_id,
                    category,
                    paytype,
                    score
                }
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:PROCESSING', `SERVICE:${category}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`, `DATE:${todayDate}`]); 
                recordsToPublish.push(JSON.stringify(finalRecord));
                self.L.info("processRecords:: ","record processed:",JSON.stringify(params));
            }
            if(recordsToPublish.length == 0) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:ERROR', "TYPE:USER_SCORE_CSV_INGESTOR_PROCESSING_EMPTY_ARRAY", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                self.L.critical('publishInKafka :: USER_SCORE_INGESTOR', `fileName: ${fileName}`,'Error: PROCESSING EMPTY ARRAY. ArraySize:' + recordsToPublish.length);
                resolve();
            }else{
                userScoreIngestorProducer.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CRON_USER_SCORE_INGESTOR.TOPIC', ''),
                    messages: recordsToPublish
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:ERROR', "TYPE:USER_SCORE_CSV_INGESTOR_KAFKA_PUBLISH", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]);
                        self.L.critical('publishInKafka :: USER_SCORE_INGESTOR', `fileName: ${fileName}`,'Error while publishing message in Kafka - MSG:- ' + recordsToPublish.length, error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_CSV_INGESTOR", 'STATUS:PUBLISHED', "TYPE:USER_SCORE_CSV_INGESTOR_KAFKA_PUBLISH", `FILE_NAME:${fileName}`, `DATE:${todayDate}`]); 
                        self.L.log('publishInKafka :: USER_SCORE_INGESTOR', `fileName: ${fileName}`, 'Message published successfully in Kafka', ' on topic USER_SCORE_INGESTOR', recordsToPublish.length);
                    }
                    resolve();
                }, [200, 800]);
            }
        })    
        
    }
}
(function main() {
    if (require.main === module) {
        startup.init({
              
        }, function (err, options) {
            let script;
            try{
            script = new USER_SCORE_CSV_INGEST(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
            }catch(err){
                options.L.error(err)
                process.exit(1)
            }
          
        });
    }
})();
export default USER_SCORE_CSV_INGEST
