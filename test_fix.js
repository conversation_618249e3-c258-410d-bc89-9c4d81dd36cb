// Test script to verify the fix for sendNonPaytmBillsMetrics
const _ = require('lodash');

// Mock logger
const L = {
    error: (msg, data) => console.log('ERROR:', msg, data),
    verbose: (msg) => console.log('VERBOSE:', msg)
};

// Mock _sendMetricsToDD function
const _sendMetricsToDD = (value, tags) => {
    console.log('Metrics sent:', { value, tags });
};

// Fixed sendNonPaytmBillsMetrics function
let sendNonPaytmBillsMetrics = (status, type, source, payload, error = null) => {
    // Validate required parameters to prevent undefined access
    if (!status || !type || !source) {
        L.error('sendNonPaytmBillsMetrics: Missing required parameters', { status, type, source });
        return;
    }

    // Ensure payload is an object to prevent undefined access
    if (!payload || typeof payload !== 'object') {
        L.error('sendNonPaytmBillsMetrics: Invalid payload parameter', { payload });
        return;
    }

    let tags = [
        'REQUEST_TYPE:NON_PAYTM_BILLS',
        'FLOW:NEW_NON_PAYTM_BILLS',
        'STATUS:' + status,
        'TYPE:' + type,
        'SOURCE:' + source,
        'SERVICE:' + _.get(payload, 'service', 'UNKNOWN'),
        'OPERATOR:' + _.get(payload, 'operator', 'UNKNOWN'),
        'ORIGIN:' + _.get(payload, 'source', 'UNKNOWN')
    ];

    const partialBillState = _.get(payload, 'partialBillState');
    if (partialBillState) {
        tags.push('PARTIAL_BILL:' + partialBillState);
    }
    const dataExhaust = _.get(payload, 'isRealTimeDataExhausted', false);
    if (dataExhaust) {
        tags.push('DATA_EXHAUST:' + dataExhaust);
    }
    const isValidityExpired = _.get(payload, 'isValidityExpired', false);
    if (isValidityExpired) {
        tags.push('IS_VALIDITY_EXPIRED:' + isValidityExpired);
    }
    if (error) {
        // Safely handle error parameter - it could be a string, Error object, or other type
        const errorMessage = typeof error === 'string' ? error : 
                            (error && error.message) ? error.message : 
                            (error && error.toString) ? error.toString() : 
                            'UNKNOWN_ERROR';
        tags.push('ERROR:' + errorMessage);
    }
    
    try {
        _sendMetricsToDD(1, tags);
    } catch (metricsError) {
        L.error('sendNonPaytmBillsMetrics: Error sending metrics to DataDog', metricsError);
    }
};

// Test cases
console.log('=== Test Case 1: Normal Error object ===');
const normalError = new Error('Test error message');
const params1 = {
    service: 'electricity',
    operator: 'bses yamuna',
    debugKey: 'rech_num:100233542::operator:bses yamuna::productId:465642456::custId:1002335434::service:electricity'
};
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params1, normalError.message);

console.log('\n=== Test Case 2: Undefined error.message ===');
const undefinedError = { someProperty: 'value' }; // No message property
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params1, undefinedError.message);

console.log('\n=== Test Case 3: Null error ===');
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params1, null);

console.log('\n=== Test Case 4: String error ===');
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params1, 'String error message');

console.log('\n=== Test Case 5: Object without message but with toString ===');
const objectError = { toString: () => 'Custom toString error' };
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params1, objectError);

console.log('\n=== Test Case 6: Invalid payload ===');
sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', null, 'error message');

console.log('\n=== Test Case 7: Missing required parameters ===');
sendNonPaytmBillsMetrics('', '', '', params1, 'error message');

console.log('\n=== All tests completed ===');
